package com.shunwang.marketing.query.marketing;

import com.shunwang.marketing.query.BaseQuery;
public class BussMemberRelationQuery extends BaseQuery {

	private Integer id;
	private Integer memberId;
	private Integer bussType;
	private Integer bussId;
	private String remark;
	public Integer getId() {
		return id;
	}
	public void setId(Integer id) {
		this.id = id;
	}
	public Integer getMemberId() {
		return memberId;
	}
	public void setMemberId(Integer memberId) {
		this.memberId = memberId;
	}
	public Integer getBussType() {
		return bussType;
	}
	public void setBussType(Integer bussType) {
		this.bussType = bussType;
	}
	public Integer getBussId() {
		return bussId;
	}
	public void setBussId(Integer bussId) {
		this.bussId = bussId;
	}
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}


}
