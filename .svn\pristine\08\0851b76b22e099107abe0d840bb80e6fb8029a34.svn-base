package com.shunwang.marketing.enums.marketing;

import lombok.Getter;
public interface ResourcePrivilegeEnum {
    @Getter
     enum GradingStateEnum {

        GRADING(1, "分级"),
        NOT_GRADING(0, "不分级")
;
        GradingStateEnum(int value, String name) {
            this.value = value;
            this.name = name;
        }

        private  int value;
        private  String name;
    }

    @Getter
     enum OnlineStateEnum {

        ONLINE(1, "上线"),
        OFFLINE(0, "下线")
;
        OnlineStateEnum(int value, String name) {
            this.value = value;
            this.name = name;
        }

        private  int value;
        private String name;
    }

    @Getter
    enum TypeEnum {

        ICON(1, "特权图标"),
        CARD(2, "特权卡牌");

        TypeEnum(int value, String name) {
            this.value = value;
            this.name = name;
        }

        private Integer value;
        private String name;
    }

    @Getter
    enum SourceTypeEnum {

        SHUNWANG(1, "顺网"),
        QQ(2, "qq网吧");

        SourceTypeEnum(int value, String name) {
            this.value = value;
            this.name = name;
        }

        private Integer value;
        private String name;
    }



}
