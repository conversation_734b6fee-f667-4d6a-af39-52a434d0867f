package com.shunwang.marketing.pojo.marketing;

import com.shunwang.marketing.constant.UploadContstant;
import com.shunwang.marketing.context.DomainContext;
import com.shunwang.marketing.enums.marketing.ResourcePrivilegeContentEnum;
import com.shunwang.marketing.pojo.BaseObject;
import com.shunwang.util.lang.StringUtil;
import lombok.Data;

import java.util.Objects;

@Data
public class ResourcePrivilegeContent extends BaseObject {
	private static final long serialVersionUID = 1L;
	private Integer id;
	private Integer level;
	private Integer privilegeType;
	private Integer privilegeRelateId;
	private Integer resourcePrivilegeId;

	private String privilegeName;
	private String hoverImg; // hover图
	private String description; // 描述
	private Integer type; // 类型 1 图标 2卡牌
	private Integer openType; // 打开方式
	private String openUrl; // 打开链接
	private String detail; // 具体介绍
	private String showImg; // 展示图片
	private String args; // 启动参数
	private java.util.Date startTime; // 开始时间
	private java.util.Date endTime; // 结束时间
	private Integer sourceType;
	private Integer markType;
	private String resourceName;
	private String resourceAlias;
	private Integer resourceId;
	private Integer sort;
	private String extInfo;
	private Integer gameLevel;
	private Integer buss; // 业务归属 1云海菜单 2云菜单
	private String remark; // 备注

	public String getPrivilegeTypeName(){
		for(ResourcePrivilegeContentEnum.PrivilegeTypeEnum privilegeTypeEnum : ResourcePrivilegeContentEnum.PrivilegeTypeEnum.values()){
			if(Objects.equals(privilegeTypeEnum.getValue(), privilegeType)){
				return privilegeTypeEnum.getName();
			}
		}
		return "";
	}

	public String getHoverImg() {
		if (StringUtil.isNotBlank(hoverImg)){
			return DomainContext.getBackStaticServer() + UploadContstant.UPLOAD_DIRECTORY + hoverImg;
		}
		return null;
	}
	public String getShowImg() {
		if (StringUtil.isNotBlank(showImg)){
			return DomainContext.getBackStaticServer() + UploadContstant.UPLOAD_DIRECTORY + showImg;
		}
		return null;
	}
}
