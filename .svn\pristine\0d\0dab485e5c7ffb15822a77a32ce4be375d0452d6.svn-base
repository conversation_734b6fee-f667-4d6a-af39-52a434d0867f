package com.shunwang.marketing.service.marketing.impl;

import com.shunwang.marketing.dao.BaseMapper;
import com.shunwang.marketing.dao.marketing.BarLevelBarMapper;
import com.shunwang.marketing.pojo.marketing.BarLevelBar;
import com.shunwang.marketing.query.marketing.BarLevelBarQuery;
import com.shunwang.marketing.service.impl.BaseServiceImpl;
import com.shunwang.marketing.service.marketing.BarLevelBarService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class BarLevelBarServiceImpl extends BaseServiceImpl<BarLevelBar> implements BarLevelBarService {
    @Autowired
    private BarLevelBarMapper barLevelBarMapper;
    @Override
    public BaseMapper<BarLevelBar> getDao() {
        return barLevelBarMapper;
    }


    @Override
    public List<BarLevelBar> findByBarId(Integer barId) {
        BarLevelBarQuery barLevelBarQuery = new BarLevelBarQuery();
        barLevelBarQuery.setBarId(barId);
        return barLevelBarMapper.find(barLevelBarQuery);
    }
}
