package com.shunwang.marketing.cacheService;

import com.google.gson.Gson;
import com.shunwang.util.lang.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.cache.CacheKeyPrefix;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

@Component
public class RedisOperation {

    private final Logger logger = LoggerFactory.getLogger(RedisOperation.class);

    @Autowired
    CacheKeyPrefix cacheKeyPrefix;

    Gson gson = new Gson();

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    public String get(String key) {
        key = getKeyName(key);
        return redisTemplate.opsForValue().get(key);
    }

    public <T> T get(String key, Class<T> t) {
        String json = get(key);
        if (StringUtil.isNotBlank(json)) {
            return gson.fromJson(json, t);
        }
        return null;
    }

    public boolean set(String key, String value) {
        key = getKeyName(key);

        boolean result = false;
        try {
            redisTemplate.opsForValue().set(key, value);
            result = true;
        } catch (Exception e) {
            logger.error("设置缓存Key[{}]值[{}]失败", key, value, e);
        }
        return result;
    }

    public boolean set(String key, String value, long exp) {
        key = getKeyName(key);

        boolean result = false;
        try {
            redisTemplate.opsForValue().set(key, value, exp, TimeUnit.SECONDS);
            result = true;
        } catch (Exception e) {
            logger.error("设置缓存Key[{}]值[{}]失败", key, value, e);
        }
        return result;
    }

    public boolean set(String key, Object value, long exp, TimeUnit timeUnit) {
        key = getKeyName(key);

        boolean result = false;
        try {
            String val;
            if (isBaseType(value)) {
                val = value.toString();
            } else {
                val = gson.toJson(value);
            }
            redisTemplate.opsForValue().set(key, val, exp, timeUnit);
            result = true;
        } catch (Exception e) {
            logger.error("设置缓存Key[{}]值[{}]失败", key, value, e);
        }
        return result;
    }

    public boolean setIfAbsent(String key, Object value, long exp, TimeUnit timeUnit) {
        key = getKeyName(key);

        boolean result = false;
        try {
            String val;
            if (isBaseType(value)) {
                val = value.toString();
            } else {
                val = gson.toJson(value);
            }
            Boolean b = redisTemplate.opsForValue().setIfAbsent(key, val, exp, timeUnit);
            result = b == null ? false : b;
        } catch (Exception e) {
            logger.error("设置缓存Key[{}]值[{}]失败", key, value, e);
        }
        return result;
    }

    public boolean getAndSet(String key, String value) {
        key = getKeyName(key);

        boolean result = false;
        try {
            redisTemplate.opsForValue().getAndSet(key, value);
            result = true;
        } catch (Exception e) {
            logger.error("获取并设置缓存Key[{}]值[{}]失败", key, value, e);
        }
        return result;
    }

    public boolean delete(String key) {
        key = getKeyName(key);

        boolean result = false;
        try {
            Boolean b = redisTemplate.delete(key);
            result = b == null ? false : b;
        } catch (Exception e) {
            logger.error("删除缓存Key[{}]失败", key, e);
        }
        return result;
    }

    /**
     * 判断object是否为基本类型
     *
     * @param obj
     * @return
     */
    private boolean isBaseType(Object obj) {
        Class<?> clazz = obj.getClass();
        if (clazz.equals(Integer.class)
                || clazz.equals(Byte.class)
                || clazz.equals(Short.class)
                || clazz.equals(Double.class)
                || clazz.equals(Float.class)
                || clazz.equals(Long.class)
                || clazz.equals(Character.class)
                || clazz.equals(String.class)
                || clazz.equals(Boolean.class)) {
            return true;
        }
        return false;
    }


    protected String getKeyName(String cacheName) {
        return cacheKeyPrefix.compute(cacheName);
    }
}
