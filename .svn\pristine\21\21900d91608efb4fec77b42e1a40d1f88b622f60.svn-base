package com.shunwang.marketing.service.marketing.impl;

import com.shunwang.marketing.aspect.asynctask.AsyncTaskExecutor;
import com.shunwang.marketing.enums.marketing.TencentOrderEnum;
import com.shunwang.marketing.enums.marketing.TencentOrderNoticeEnum;
import com.shunwang.marketing.exception.CommonException;
import com.shunwang.marketing.pojo.marketing.TencentGoodsWelfare;
import com.shunwang.marketing.pojo.marketing.TencentOrderNotice;
import com.shunwang.marketing.query.marketing.TencentOrderQuery;
import com.shunwang.marketing.service.marketing.TencentGoodsService;
import com.shunwang.marketing.service.marketing.TencentGoodsWelfareService;
import com.shunwang.marketing.service.marketing.TencentOrderNoticeService;
import com.shunwang.marketing.service.marketing.TencentOrderService;
import com.shunwang.marketing.dao.marketing.TencentOrderMapper;
import com.shunwang.marketing.dao.BaseMapper;
import com.shunwang.marketing.task.job.TencentGameGiftJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import com.shunwang.marketing.service.impl.BaseServiceImpl;
import com.shunwang.marketing.pojo.marketing.TencentOrder;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
public class TencentOrderServiceImpl extends BaseServiceImpl<TencentOrder> implements TencentOrderService {
    @Autowired
    private TencentOrderMapper tencentOrderMapper;
    @Autowired
    TencentOrderNoticeService tencentOrderNoticeService;
    @Autowired
    TencentGoodsWelfareService tencentGoodsWelfareService;
    @Autowired
    TencentGoodsService tencentGoodsService;

    @Override
    public BaseMapper<TencentOrder> getDao() {
        return tencentOrderMapper;
    }


    @Override
    public TencentOrder getByOrderNo(String orderNo) {
        Assert.hasText(orderNo, "订单号不能为空");
        TencentOrderQuery query = new TencentOrderQuery();
        query.setOrderNo(orderNo);
        return tencentOrderMapper.findOne(query);
    }

    @Override
    public TencentOrder getByOrderNoAndCheckInit(String orderNo) {
        TencentOrder order = getByOrderNo(orderNo);
        if (order == null) {
            log.error("腾讯订单不存在:{}", orderNo);
            throw new CommonException("订单不存在");
        }
        if (!TencentOrderEnum.PayStateEnum.CREATE.getValue().equals(order.getPayState())) {
            throw new CommonException("订单状态不允许支付");
        }
        return order;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void finishOrder(String orderNo,String disburseNo) {
        TencentOrder order = getByOrderNoForUpdate(orderNo);
        if (!TencentOrderEnum.PayStateEnum.CREATE.getValue().equals(order.getPayState())) {
            log.info("腾讯订单:{}已处理,状态:{}", orderNo, order.getPayState());
            return;
        }
        order.setPayOrderNo(disburseNo);

        //商品单用户购买限制
        if (tencentGoodsService.isReachUserLimit(order.getGoodsId(), order.getMemberId())) {
            log.info("用户:{}已经达到商品:{}购买次数限制", order.getMemberId(), order.getGoodsId());
            order.setPayState(TencentOrderEnum.PayStateEnum.FAIL.getValue());
            order.setRemark("用户达到购买限制");
            update(order);
            return;
        }

        //保存权益通知记录
        List<TencentGoodsWelfare> welfareList = tencentGoodsWelfareService.findByGoodsId(order.getGoodsId());
        List<TencentOrderNotice> orderNoticeList = welfareList.stream().map((tencentWelfare) -> buildOrderNotice(tencentWelfare, order)).collect(Collectors.toList());
        tencentOrderNoticeService.batchSave(orderNoticeList);

        //商品库存,需要上锁最后处理
        if (tencentGoodsService.isReachCntLimit(order.getGoodsId())) {
            log.info("商品:{}达到购买次数限制", order.getGoodsId());
            order.setPayState(TencentOrderEnum.PayStateEnum.FAIL.getValue());
            order.setRemark("商品达到购买限制");
            update(order);
            return;
        }

        //订单成功
        order.setPaySuccessTime(new Date());
        order.setPayState(TencentOrderEnum.PayStateEnum.SUCCESS.getValue());
        update(order);

        log.info("订单:{}成功,异步发放权益", orderNo);
        AsyncTaskExecutor.submit(new TencentGameGiftJob(orderNo, tencentOrderNoticeService));
    }

    private TencentOrderNotice buildOrderNotice(TencentGoodsWelfare tencentWelfare, TencentOrder order) {
        TencentOrderNotice orderNotice = new TencentOrderNotice();
        orderNotice.setGoodsId(tencentWelfare.getGoodsId());
        orderNotice.setGoodsName(tencentWelfare.getGoodsName());
        orderNotice.setWelfareId(tencentWelfare.getWelfareId());
        orderNotice.setWelfareName(tencentWelfare.getWelfareName());
        orderNotice.setMemberId(order.getMemberId());
        orderNotice.setMemberName(order.getMemberName());
        orderNotice.setOrderNo(order.getOrderNo());
        orderNotice.setNoticeNo("NT" + order.getOrderNo());
        orderNotice.setState(TencentOrderNoticeEnum.StateEnum.CREATE.getValue());
        orderNotice.setSendCnt(0);
        orderNotice.setThirdActivityId(tencentWelfare.getThirdActivityId());
        orderNotice.setAddTime(new Date());
        orderNotice.setSendTime(new Date());
        return orderNotice;
    }

    @Override
    public TencentOrder getByOrderNoForUpdate(String orderNo) {
        Assert.hasText(orderNo, "订单号不能为空");
        return tencentOrderMapper.getByOrderNoForUpdate(orderNo);
    }
}
