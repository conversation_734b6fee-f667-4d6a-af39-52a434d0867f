package com.shunwang.marketing.controller.api;

import com.shunwang.marketing.aspect.asynctask.AsyncTaskExecutor;
import com.shunwang.marketing.cacheService.CacheKeyConstant;
import com.shunwang.marketing.cacheService.RedisOperation;
import com.shunwang.marketing.context.ThreadLocalContext;
import com.shunwang.marketing.enums.marketing.OutRequestEnum;
import com.shunwang.marketing.pojo.marketing.vo.BarPrivilegeVO;
import com.shunwang.marketing.pojo.marketing.vo.ExtensionVo;
import com.shunwang.marketing.result.Result;
import com.shunwang.marketing.result.ResultGenerator;
import com.shunwang.marketing.service.config.ResourcesService;
import com.shunwang.marketing.service.marketing.ExtensionService;
import com.shunwang.marketing.service.marketing.MemberTaskService;
import com.shunwang.marketing.service.marketing.OutRequestService;
import com.shunwang.marketing.service.privilege.PrivilegeService;
import com.shunwang.marketing.util.DateUtil;
import com.shunwang.sms.utils.SMSInnerSenderUtil;
import com.shunwang.util.net.IpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Controller
@Slf4j
@Validated
public class OutSiteController {
    @Value("${weixin.jscode2session.appId}")
    private String appId;
    @Value("${marketing.login.pay.siteId}")
    private String siteId;
    @Autowired
    private ExtensionService extensionService;
    @Autowired
    private MemberTaskService memberTaskService;
    @Autowired
    OutRequestService outRequestService;
    @Autowired
    private PrivilegeService privilegeService;
    @Autowired
    RedisOperation redisOperation;
    @Autowired
    ResourcesService resourcesService;
    @Value("${privilege.exception.num}")
    List<String> nums;


    @ResponseBody
    @RequestMapping("/barPrivilege/promotionData")
    public Result promotionData(
            @RequestParam(value = "barid") String barid,
            @RequestParam("guid") String guid,
            @RequestParam(value = "ip", required = false) String ip,
            @RequestParam("location") Integer location,
            @RequestParam("themeId") String themeId,
            @RequestParam("menuVersion") String menuVersion,
            @RequestParam(value = "ipMode",defaultValue = "1")Integer ipMode, //1 使用请求解析的ip 2使用传入的ip
            HttpServletRequest request
    ) {
        ThreadLocalContext.setTempData(ThreadLocalContext.BAR_ID, barid);
        ThreadLocalContext.setTempData(ThreadLocalContext.GU_ID, guid);
        if (ipMode == 1) {
            ip = IpUtil.getIpAddress(request);
            log.debug("请求解析的ip为:{}", ip);
        }
        ExtensionVo result = extensionService.promotionData(ip, barid, location);
        return ResultGenerator.newSuccessResult(result);

    }

    @RequestMapping("/barPrivilege/toPage")
    public String toBarPrivilege(@RequestParam(value = "id",defaultValue = "1")Integer id){
        return "cafePrivilegeHub/cafePrivilegeHub";
    }

    @ResponseBody
    @RequestMapping("/aiTask/receiptNotify")
    public Result promotionData(
            @RequestParam(value = "unionId") String unionId,
            @RequestParam(value = "key") String key){
        boolean result = memberTaskService.toFinishTask(unionId, key, appId, siteId);
        return result ? ResultGenerator.newEmptySuccessResult() : ResultGenerator.newSystemErrorResult("服务方发生异常，调用失败");
    }

    /**
     * 广告网吧等级推送
     * @param type 1新增 2更新 3删除
     * @param data 推送数据
     */
    @ResponseBody
    @RequestMapping("/ad/barPush")
    public Result adBarPush(@NotNull(message = "操作类型不能为空") Integer type,
                            @NotBlank(message = "推送数据不能为空")  String data) {
        outRequestService.push(OutRequestEnum.TypeEnum.AD_BAR.getValue(), type, data);
        return ResultGenerator.newEmptySuccessResult();
    }


    /**
     * 广告游戏等级推送
     * @param type 1新增 2更新 3删除
     * @param data 推送数据
     */
    @ResponseBody
    @RequestMapping("/ad/gamePush")
    public Result adGamePush(@NotNull(message = "操作类型不能为空") Integer type,
                             @NotBlank(message = "推送数据不能为空") String data) {
        outRequestService.push(OutRequestEnum.TypeEnum.AD_GAME.getValue(), type, data);
        return ResultGenerator.newEmptySuccessResult();
    }

    @RequestMapping("/wangwei/queryPrivilegeContent")
    @ResponseBody
    public Result<?> queryPrivilegeContent(@NotNull Integer barId, @NotEmpty Integer[] resourceIds){

        BarPrivilegeVO barPrivilegeVO = privilegeService.queryPrivilegeContent(barId, Arrays.asList(resourceIds));
        return ResultGenerator.newSuccessResult(barPrivilegeVO);
    }

    /**
     * 网维特权数据
     * @param barId
     * @param ip
     * @return
     */
    @RequestMapping("/wangwei/privilege")
    @ResponseBody
    public Result<BarPrivilegeVO> queryPrivilegeContent(@NotNull(message = "barId不能为空") Integer barId,String ip) {
        try {
            BarPrivilegeVO barPrivilegeVO = privilegeService.queryPrivilegeContent(barId);
            return ResultGenerator.newSuccessResult(barPrivilegeVO);
        } catch (Exception e) {
            log.error("网维特权数据获取异常", e);
            AsyncTaskExecutor.submit(() -> {
                if (redisOperation.setIfAbsent(CacheKeyConstant.PRIVILEGE_ERROR, 1, 2, TimeUnit.HOURS)) {
                    for (String num : nums) {
                        SMSInnerSenderUtil.sendMsg(num, "网吧特权数据获取异常,barId:" + barId);
                    }
                }
            });
            return ResultGenerator.newEmptySystemErrorResult();
        }
    }
}
