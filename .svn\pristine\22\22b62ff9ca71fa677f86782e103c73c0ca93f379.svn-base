package com.shunwang.marketing.service.marketing.promotionPopupWindow;

import com.shunwang.marketing.context.ThreadLocalContext;
import com.shunwang.marketing.enums.marketing.PromotionPopupWindowEnum;
import com.shunwang.marketing.pojo.marketing.SignIn;
import com.shunwang.marketing.pojo.marketing.vo.PromotionPopupWindowVo;
import com.shunwang.marketing.service.marketing.SignInService;
import com.shunwang.marketing.service.marketing.SignRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class StrategyProcessor4 extends AbstractStrategyProcessor {
    @Autowired
    SignRecordService signRecordService;
    @Autowired
    SignInService signInService;

    @Override
    public boolean doProcess(PromotionPopupWindowVo ppw) {
        SignIn signIn;
        if (PromotionPopupWindowEnum.PlatformEnum.MENU.getValue().equals(ppw.getPlatform())) {
            signIn = signInService.getByPageId(Integer.parseInt(ppw.getRealPageId()));
        } else {
            signIn = signInService.getByChannelFallback(ThreadLocalContext.getChannel());
        }
        return signIn != null && !signRecordService.isSignInToday(signIn);
    }

    @Override
    String strategyValue() {
        return PromotionPopupWindowEnum.StrategyEnum.NO_SIGN.getValue();
    }

    @Override
    public boolean needLogin() {
        return true;
    }
}
