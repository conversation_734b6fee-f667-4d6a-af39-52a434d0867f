package com.shunwang.marketing.controller.pc;


import com.shunwang.marketing.pojo.Pageable;
import com.shunwang.marketing.pojo.marketing.RedPacketRecord;
import com.shunwang.marketing.result.Result;
import com.shunwang.marketing.result.ResultGenerator;
import com.shunwang.marketing.service.marketing.RedPacketRecordService;

import com.shunwang.marketing.util.MemberUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;


/**
 * 游戏优惠券相关接口
 */
@Slf4j
@Controller
@RequestMapping("/pc/redPacketRecord")
public class PcRedPacketRecordController {
    @Autowired
    private RedPacketRecordService redPacketRecordService;

    @RequestMapping("/myRedPacket")
    @ResponseBody
    public Result myRedPacket(Pageable pageable){
        Integer memberId = pageable.getMemberId();
        if (memberId == null){
            return ResultGenerator.newSystemErrorResult("请先登录");
        }
        Map<String, Object> result = new HashMap<>();
        List<RedPacketRecord> redPacketRecordList = redPacketRecordService.getUserRedPacketList(pageable);
        pageable.setCnt(redPacketRecordService.getCntByMember(memberId));
        result.put("packetList", redPacketRecordList);
        result.put("pageInfo",  pageable);
        return ResultGenerator.newSuccessResult(result);
    }


}
