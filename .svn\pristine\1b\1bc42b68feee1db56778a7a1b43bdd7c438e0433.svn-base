package com.shunwang.marketing.pojo.marketing;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.shunwang.marketing.constant.UploadContstant;
import com.shunwang.marketing.context.DomainContext;
import com.shunwang.marketing.pojo.BaseObject;
import com.shunwang.util.lang.StringUtil;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

import static com.shunwang.marketing.constant.UploadContstant.UPLOAD_DIRECTORY;

@Data
public class MoneySavingCard extends BaseObject {
	private static final long serialVersionUID = 1L;
	private Integer id;
	private String cardName;
	private String channel;
	private String tags;
	private Integer cnt;
	private BigDecimal price;
	private BigDecimal worth;
	@JsonIgnore
	private Integer sort;
	private Integer unlockedCnt;
	private String remark;
	@JsonIgnore
	private String userAdd;
	@JsonIgnore
	private Date timeAdd;
	@JsonIgnore
	private String userEdit;
	@JsonIgnore
	private Date timeEdit;
	private String rule;
	private String tipsBefore;
	private String tipsAfter;
	private String btnText;
	private String bottomText;
	private String bottomTextUnUse;
	private Integer autoRefund;
	private Integer cardType;
	private Integer taskRuleId;
	private String taskPicFirst;
	private String taskPicSecond;

	@JsonIgnore
	private Date startTime;
	@JsonIgnore
	private Date endTime;

	@JsonIgnore
	public boolean isEffective() {
		Date date = new Date();
		return startTime != null && endTime != null && endTime.after(date) && date.after(startTime);
	}

}
