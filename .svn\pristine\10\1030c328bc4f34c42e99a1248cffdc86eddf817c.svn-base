package com.shunwang.marketing.service.marketing.promotionPopupWindow;

import com.shunwang.marketing.context.ThreadLocalContext;
import com.shunwang.marketing.enums.marketing.CommonEnum;
import com.shunwang.marketing.enums.marketing.GiftBagEnum;
import com.shunwang.marketing.enums.marketing.PromotionPopupWindowEnum;
import com.shunwang.marketing.pojo.marketing.GiftBag;
import com.shunwang.marketing.pojo.marketing.vo.PromotionPopupWindowVo;
import com.shunwang.marketing.query.marketing.GiftBagQuery;
import com.shunwang.marketing.service.marketing.GiftBagService;
import com.shunwang.marketing.service.marketing.PrizeRecordService;
import com.shunwang.marketing.util.MemberUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Component
public class StrategyProcessor7 extends AbstractStrategyProcessor {
    @Autowired
    GiftBagService giftBagService;

    @Override
    public boolean doProcess(PromotionPopupWindowVo ppw) {
        if (PromotionPopupWindowEnum.PlatformEnum.MENU.getValue().equals(ppw.getPlatform())){
            GiftBagQuery query = new GiftBagQuery();
            query.setPlatform(CommonEnum.PlatformEnum.MENU.getValue());
            query.setState(GiftBagEnum.StateEnum.ONLINE.getValue());
            query.setPageId(ppw.getRealPageId());
            GiftBag bag = giftBagService.findOne(query);
            return bag != null && !giftBagService.isReachLimit(MemberUtil.getNotNullMemberId(), bag.getId(), bag);
        }else{
            List<GiftBag> bagList = giftBagService.getByChannel(ThreadLocalContext.getChannel(), MemberUtil.getNotNullMemberId());
            return !CollectionUtils.isEmpty(bagList);
        }
    }

    @Override
    String strategyValue() {
        return PromotionPopupWindowEnum.StrategyEnum.GIFT_BAG.getValue();
    }

    @Override
    public boolean needLogin() {
        return true;
    }
}
