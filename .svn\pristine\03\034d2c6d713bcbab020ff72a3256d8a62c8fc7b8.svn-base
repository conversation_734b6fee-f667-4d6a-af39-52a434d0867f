package com.shunwang.marketing.dto;


import com.shunwang.marketing.pojo.BaseObject;
import com.shunwang.marketing.util.SensitiveInfoUtil;
import com.shunwang.util.lang.StringUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

@EqualsAndHashCode(callSuper = true)
@Data
public class UserInfo extends BaseObject {
    private Integer memberId;
    private String memberName;
    /**
     * 头像
     */
    private String headImg;
    /**
     * 昵称
     */
    private String nickName;
    private String unionId;
    private String openId;
    private String mobile;

    private String appId;
    private String idCardNo;
    private Integer idCardState;
    /**
     * 今天是否登录过,false代表是第一次登录
     */
    private Boolean haveTodayLogin;

    public enum IdCardStateEnum {
        TO_IDENTIFY(1, "待认证"),
        HAVE_IDENTIFY(2, "已认证"),
        HAVE_UPDATED(3, "已修改"),

        ;

        IdCardStateEnum(int value, String name) {
            this.value = value;
            this.name = name;
        }

        private @Getter
        Integer value;
        private @Getter String name;
    }

    public String getIdCardNo(){
        if (StringUtil.isNotBlank(idCardNo)){
            return SensitiveInfoUtil.idCardNo4StarReplace(idCardNo);
        }
        return "";
    }




}
