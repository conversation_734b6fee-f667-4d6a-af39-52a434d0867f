package com.shunwang.marketing.manager.paopao;

import com.shunwang.marketing.manager.request.paopao.SendGiftRequest;
import com.shunwang.marketing.manager.request.tencent.GiveGameGiftRequest;
import com.shunwang.marketing.manager.response.paopao.SendGiftResponse;
import com.shunwang.marketing.manager.response.tencent.GiveGameGiftResponse;
import com.shunwang.marketing.manager.service.CommonServiceClient;
import com.shunwang.marketing.manager.service.TencentGameServiceClient;
import com.shunwang.util.math.RandomUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@SpringBootTest
@RunWith(SpringRunner.class)
public class SendGiftTest {

    @Test
    public void test(){
        SendGiftRequest request = new SendGiftRequest();
        request.setOrderNo("test12312312312"+ RandomUtil.getRandomStr(6));
        request.setKedouId("575372999");
        request.setCommodityInfo("[{\"amount\":1,\"id\":165,\"type\":1}]");
        SendGiftResponse response = CommonServiceClient.execute(request);
        if (response.isSuccess()){
            System.out.println("发放成功");
        } else {
            System.out.println("coed:" + response.getCode()+", msg:" + response.getMsg());
        }
    }
}
