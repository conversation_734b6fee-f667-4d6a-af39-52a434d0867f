package com.shunwang.marketing.filter;

import com.shunwang.util.trace.Tracefilter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.*;

/**
 * Created by bao.jin on 2019/8/19.
 */
@Configuration
public class WebFilterConfig {

    @Value("${livelink.livePlatId}")
    private String livePlatId;
    @Value("${livelink.sigkey}")
    private String sigkey;
    @Value("${livelink.seckey}")
    private String seckey;

    @Bean
    public FilterRegistrationBean<LogFilter> logFilter() {
        LogFilter logFilter = new LogFilter();
        FilterRegistrationBean<LogFilter> filterRegistrationBean = new FilterRegistrationBean<>();
        filterRegistrationBean.setFilter(logFilter);
        filterRegistrationBean.setOrder(2);
        List<String> urls = new ArrayList<>();
        urls.add("/*");
        filterRegistrationBean.setUrlPatterns(urls);
        return filterRegistrationBean;
    }

    @Bean
    public FilterRegistrationBean<SessionFilter> sessionFilter() {
        FilterRegistrationBean<SessionFilter> registration = new FilterRegistrationBean<>();
        registration.setFilter(new SessionFilter());
        registration.setUrlPatterns(Arrays.asList("/*"));
        registration.setOrder(3);
        return registration;
    }



    @Bean
    public FilterRegistrationBean<UserLoginCheckFilter> userLoginCheckFilter() {
        FilterRegistrationBean<UserLoginCheckFilter> registration = new FilterRegistrationBean<>();
        UserLoginCheckFilter filter = new UserLoginCheckFilter();
        registration.setFilter(filter);
        registration.setUrlPatterns(Arrays.asList(
                "/forward",
//                "/weixin/*",
                "/h5/*",
                "/redPacketRecord/*",
                "/moneySavingCard/*",
                "/giftBag/*",
                "/weixin/user/*",
                "/upload/*",
                "/popupWindow/*",
                "/ppw/*",
                "/lastLogin/*",
                "/Lotteries/*",
//                "/barPrivilege/*",
                "/member/*",
                "/survey/*",
                "/score/*",
//                "/noLogin/*",
                "/barPrivilege/toPage",
                "/tab/mini/*",
                "/subRecord/*",
                "/contentRecord/*",
                "/pondOrder/*",
                "/taskCenter/*"
        ));
        return registration;
    }


    @Bean
    public FilterRegistrationBean<XssFilter> xssFilter() {
        FilterRegistrationBean<XssFilter> registration = new FilterRegistrationBean<>();
        registration.setFilter(new XssFilter());
        return registration;
    }

    @Bean
    public FilterRegistrationBean<Tracefilter> traceFilter() {
        FilterRegistrationBean<Tracefilter> registration = new FilterRegistrationBean<>();
        registration.setFilter(new Tracefilter());
        registration.setOrder(1);
        return registration;
    }

    @Bean
    public FilterRegistrationBean<LivelinkValidateFilter> livelinkValidateFilter() {
        FilterRegistrationBean<LivelinkValidateFilter> registration = new FilterRegistrationBean<>();
        registration.setFilter(new LivelinkValidateFilter());
        registration.setUrlPatterns(Arrays.asList("/tencent/livelink/user/*","/tencent/getPayUrl"));
        Map<String, String> initParameters = new HashMap<>();
        initParameters.put("livePlatId", livePlatId);
        initParameters.put("livelinkMd5key", sigkey);
        initParameters.put("expireTime", "300");
        registration.setInitParameters(initParameters);
        return registration;
    }
}
