# 设计文档

## 概述

将现有的 AI 控制器从火山引擎 SDK 迁移到 Spring AI 框架。Spring AI 是 Spring 生态系统中的 AI 集成框架，提供了统一的抽象层来与各种 AI 服务提供商集成。迁移将保持现有的 API 接口不变，但底层实现将使用 Spring AI 的标准化组件。

## 架构

### 当前架构
```
Controller -> AiService -> 火山引擎 SDK -> AI 服务
```

### 目标架构
```
Controller -> Spring AI ChatClient -> AI 服务提供商 (OpenAI/Azure/等)
```

### 核心组件关系图

```mermaid
graph TB
    A[AiController] --> B[Spring AI ChatClient]
    A --> C[AiRecordService]
    A --> D[BussMemberRelationService]
    A --> E[ContentService]
    A --> F[ResourcesService]
    
    B --> G[AI Provider]
    B --> H[Prompt Template]
    B --> I[Response Handler]
    
    C --> J[Database]
    D --> J
    E --> J
    F --> J
    
    K[Redis] --> L[Rate Limiting]
    A --> L
```

## 组件和接口

### 1. Spring AI 集成层

#### ChatClient 配置
- **职责**: 配置和管理 Spring AI 的 ChatClient
- **接口**: 
  - `ChatClient.Builder` 用于构建客户端
  - 支持多种 AI 提供商的配置
- **实现**: 通过 Spring Boot 自动配置和自定义配置类

#### AI 提供商适配器
- **职责**: 适配不同的 AI 服务提供商
- **支持的提供商**:
  - OpenAI (GPT-3.5, GPT-4)
  - Azure OpenAI
  - 其他兼容 OpenAI API 的服务
- **配置方式**: 通过 application.yml 配置

### 2. 控制器层重构

#### AiController 改造
- **保持现有接口**: 所有现有的 REST 端点保持不变
- **内部实现变更**: 
  - 移除对 `AiService` 的依赖
  - 直接使用 Spring AI 的 `ChatClient`
  - 保持 SSE 流式响应机制

#### 新增配置类
```java
@Configuration
public class SpringAiConfig {
    @Bean
    public ChatClient chatClient(ChatModel chatModel) {
        return ChatClient.builder(chatModel).build();
    }
}
```

### 3. 数据流处理

#### 请求处理流程
1. 用户发送聊天请求
2. 验证用户授权状态
3. 检查频率限制
4. 构建 Spring AI 的 Prompt
5. 调用 ChatClient 获取响应
6. 通过 SSE 流式返回响应
7. 保存对话记录到数据库

#### 响应流处理
- 使用 Spring AI 的 `Flux<String>` 支持流式响应
- 保持现有的 `SseEmitter` 机制
- 确保错误处理和超时管理

## 数据模型

### 现有数据模型保持不变
- `AiRecord`: 对话记录实体
- `BussMemberRelation`: 用户授权关系
- `Content`: 推荐问题内容

### 新增配置模型
```java
@ConfigurationProperties(prefix = "spring.ai")
public class SpringAiProperties {
    private String provider = "openai";
    private String apiKey;
    private String baseUrl;
    private ChatOptions defaultOptions;
    // getter/setter
}
```

### Prompt 模板管理
```java
public class PromptTemplateManager {
    private final String systemPrompt;
    private final PromptTemplate promptTemplate;
    
    public Prompt buildPrompt(String userMessage) {
        return promptTemplate.create(Map.of("message", userMessage));
    }
}
```

## 错误处理

### 异常层次结构
```java
public class SpringAiException extends RuntimeException {
    // 基础 AI 异常
}

public class AiProviderException extends SpringAiException {
    // AI 提供商相关异常
}

public class RateLimitException extends SpringAiException {
    // 频率限制异常
}
```

### 错误处理策略
1. **网络异常**: 重试机制，最多重试 3 次
2. **API 限制**: 返回友好的错误信息
3. **认证失败**: 记录日志并返回配置错误提示
4. **超时处理**: 设置合理的超时时间，避免长时间等待

### 降级机制
- 当 AI 服务不可用时，返回预设的友好提示
- 保存用户问题到队列，待服务恢复后处理
- 提供服务状态检查端点

## 测试策略

### 单元测试
- **Controller 测试**: 使用 MockMvc 测试所有端点
- **Service 测试**: Mock Spring AI 组件进行业务逻辑测试
- **配置测试**: 验证不同配置下的 ChatClient 创建

### 集成测试
- **AI 提供商集成**: 使用测试 API Key 进行真实调用测试
- **数据库集成**: 验证对话记录的正确保存和查询
- **缓存集成**: 测试 Redis 频率限制功能

### 性能测试
- **并发测试**: 模拟多用户同时聊天的场景
- **流式响应测试**: 验证 SSE 在高并发下的表现
- **内存使用测试**: 确保长时间运行不会出现内存泄漏

### 测试数据管理
- 使用 TestContainers 进行数据库测试
- 创建测试专用的 AI 配置
- 模拟不同的 AI 响应场景

## 配置管理

### application.yml 配置示例
```yaml
spring:
  ai:
    openai:
      api-key: ${OPENAI_API_KEY}
      base-url: https://api.openai.com
      chat:
        options:
          model: gpt-3.5-turbo
          temperature: 0.7
          max-tokens: 1000
    retry:
      max-attempts: 3
      delay: 1000ms
```

### 环境特定配置
- **开发环境**: 使用测试 API Key，启用详细日志
- **测试环境**: 使用 Mock 服务，快速响应
- **生产环境**: 使用生产 API Key，启用监控和告警

## 迁移策略

### 渐进式迁移
1. **第一阶段**: 添加 Spring AI 依赖，保持现有功能
2. **第二阶段**: 实现新的 ChatClient 集成
3. **第三阶段**: 切换聊天功能到新实现
4. **第四阶段**: 移除旧的 AI 服务依赖

### 兼容性保证
- 保持所有现有 API 接口不变
- 响应格式完全兼容
- 数据库结构无需变更
- 前端代码无需修改

### 回滚计划
- 保留原有 AiService 实现作为备份
- 通过配置开关控制使用新旧实现
- 监控关键指标，异常时快速回滚