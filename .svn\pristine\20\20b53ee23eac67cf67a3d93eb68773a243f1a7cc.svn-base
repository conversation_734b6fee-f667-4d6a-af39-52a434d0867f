package com.shunwang.marketing.controller;

import com.shunwang.marketing.context.DomainContext;
import com.shunwang.marketing.enums.SmsSendDetailsEnum;
import com.shunwang.marketing.pojo.marketing.Scheme;
import com.shunwang.marketing.pojo.sms.FunctionPage;
import com.shunwang.marketing.pojo.sms.ShortUrl;
import com.shunwang.marketing.result.Result;
import com.shunwang.marketing.result.ResultGenerator;
import com.shunwang.marketing.service.marketing.SchemeService;
import com.shunwang.marketing.service.sms.FunctionPageService;
import com.shunwang.marketing.service.sms.ShortUrlService;
import com.shunwang.marketing.service.sms.SmsSendDetailsService;
import com.shunwang.marketing.util.Long62Util;
import com.shunwang.util.date.DateUtil;
import com.shunwang.util.lang.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.xml.crypto.Data;
import java.text.ParseException;
import java.util.Date;

@Controller
@Slf4j
public class ShortUrlController {

    @Autowired
    private ShortUrlService shortUrlService;
    @Autowired
    private FunctionPageService functionPageService;
    @Autowired
    private SmsSendDetailsService smsSendDetailsService;
    @Autowired
    SchemeService schemeService;



    @RequestMapping("/s/**")
    public String jumpLongLink(HttpServletRequest request) {
        String shortUrl = request.getRequestURI();
        if (StringUtil.isBlank(shortUrl)){
            return "error" ;
        }
        shortUrl = shortUrl.replace("/s/", "");
        Long id = Long62Util.longValue(shortUrl);
        if (StringUtil.isBlank(shortUrl)){
            return "error" ;
        }

        ShortUrl pojo = shortUrlService.get(id);
        if (pojo == null){
            return "error";
        }

        return "redirect:" + pojo.getLongUrl();
    }

    /**
     * 触达
     * @param memberId
     * @param smsId
     * @return
     */
    @RequestMapping("reach")
    @ResponseBody
    public Result reach(Integer memberId, Integer smsId, Integer pageId, String channel) throws ParseException{
        if (smsId == null || memberId == null || pageId == null){
            return ResultGenerator.newSystemErrorResult("参数错误");
        }
        if (StringUtil.isBlank(channel)){
            channel = SmsSendDetailsEnum.ChannelEnum.POINT.getValue();
        }
        FunctionPage page = functionPageService.get(pageId);
        if (page == null || DateUtil.compare(DateUtil.praseDate(page.getStartTime()), new Date() , 1) > 0){
            return ResultGenerator.newSystemErrorResult("领取失败，活动未上线");
        }
        if (page == null || DateUtil.compare(new Date(), DateUtil.praseDate(page.getEndTime()), 1) > 0){
            return ResultGenerator.newSystemErrorResult("领取失败，活动已结束");
        }
        boolean result = smsSendDetailsService.reach(smsId, memberId, channel);
        if (!result){
            return ResultGenerator.newSystemErrorResult("领取失败，已经领取过了");
        }
        return ResultGenerator.newSuccessResult("");
    }

    /**
     * 领取
     * @param memberId
     * @param smsId
     * @return
     */
    @RequestMapping("receive")
    @ResponseBody
    public Result receive(Integer memberId, Integer smsId, Integer pageId, String channel) throws ParseException {
        if (smsId == null || memberId == null || pageId == null){
            return ResultGenerator.newSystemErrorResult("参数错误");
        }
        if (StringUtil.isBlank(channel)){
            channel = SmsSendDetailsEnum.ChannelEnum.POINT.getValue();
        }
        FunctionPage page = functionPageService.get(pageId);
        if (page == null || DateUtil.compare(DateUtil.praseDate(page.getStartTime()), new Date() , 1) > 0){
            return ResultGenerator.newSystemErrorResult("领取失败，活动未上线");
        }
        if (page == null || DateUtil.compare(new Date(), DateUtil.praseDate(page.getEndTime()), 1) > 0){
            return ResultGenerator.newSystemErrorResult("领取失败，活动已结束");
        }

        boolean result = smsSendDetailsService.receive(smsId, memberId, channel);
        if (!result){
            return ResultGenerator.newSystemErrorResult("领取失败，已经领取过了");
        }
        return ResultGenerator.newSuccessResult("");
    }

    /**
     * 抵达页
     * @param model
     * @param memberId
     * @param smsId
     * @param pageId
     * @return
     */
    @RequestMapping("reachPage")
    public String reachPage(Model model, Integer memberId, Integer smsId, Integer pageId){
        FunctionPage functionPage = functionPageService.get(pageId);
        if (functionPage == null){
            model.addAttribute("page", new FunctionPage());
        } else {
            model.addAttribute("page", functionPage);
        }
        model.addAttribute("isReach", smsSendDetailsService.getIsReach(smsId, memberId));
        model.addAttribute("pageId", pageId);
        model.addAttribute("memberId", memberId);
        model.addAttribute("smsId", smsId);
        model.addAttribute("appServer", DomainContext.getAppServer());
        model.addAttribute("bigDataReportUrl", DomainContext.getBigDataReportUrl());
        model.addAttribute("staticServer", DomainContext.getStaticServer());
        model.addAttribute("backStaticServer", DomainContext.getBackStaticServer());

        return "reachPage";
    }

    /**
     * 微信scheme跳转中转页面
     * @return
     */
    @RequestMapping("/ws{schemeId}")
    public String jumpMini(@PathVariable Integer schemeId,Model model) {
        Scheme scheme = schemeService.get(schemeId);
        if (scheme == null || StringUtil.isBlank(scheme.getScheme())) {
            log.error("scheme数据异常:{}",schemeId);
            return "error";
        }
        model.addAttribute("scheme",scheme.getScheme());
        return "wxScheme/page";

    }
}
