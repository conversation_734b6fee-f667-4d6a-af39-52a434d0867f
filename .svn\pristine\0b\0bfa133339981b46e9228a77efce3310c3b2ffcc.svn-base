package com.shunwang.marketing.pojo.marketing;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.shunwang.marketing.context.DomainContext;
import com.shunwang.marketing.pojo.BasePageable;
import com.shunwang.util.lang.StringUtil;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
@Data
public class StoreOrder extends BasePageable {
	private static final long serialVersionUID = 1L;
	private Integer id;
	private Integer memberId;
	private Integer moduleDetailId;
	private String name;
	private String orderNo;
	private String payOrderNo;
	private BigDecimal price;
	private Integer score;
	/**
	 * 状态（1：创建订单，2：成功，3：失败 4：关闭 5：退款中 6退款完成 7退款失败）
	 */
	private Integer state;
	@JsonIgnore
	private String remark;
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date timeAdd;
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date timeEdit;


	/*非本表数据*/
	@JsonIgnore
	private Integer productImgId;
	/**
	 * 商品图
	 */
	private String productImg;

	public String getProductImg() {
		return StringUtil.isBlank(productImg) ? "" : DomainContext.getBackStaticUploadServer() + productImg;
	}
}
