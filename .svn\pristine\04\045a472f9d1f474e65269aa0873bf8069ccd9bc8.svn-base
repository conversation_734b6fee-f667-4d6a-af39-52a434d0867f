package com.shunwang.marketing.service.marketing.impl;

import com.shunwang.marketing.service.marketing.TencentOrderService;
import com.shunwang.marketing.dao.marketing.TencentOrderMapper;
import com.shunwang.marketing.dao.BaseMapper;
import org.springframework.beans.factory.annotation.Autowired;
import com.shunwang.marketing.service.impl.BaseServiceImpl;
import com.shunwang.marketing.pojo.marketing.TencentOrder;
import org.springframework.stereotype.Component;
@Component
public class TencentOrderServiceImpl extends BaseServiceImpl<TencentOrder> implements TencentOrderService {
    @Autowired
    private TencentOrderMapper tencentOrderMapper;
    @Override
    public BaseMapper<TencentOrder> getDao() {
        return tencentOrderMapper;
    }



}
