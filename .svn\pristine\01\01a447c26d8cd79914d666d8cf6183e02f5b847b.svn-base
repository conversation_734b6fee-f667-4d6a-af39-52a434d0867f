package com.shunwang.marketing.manager.request.inter;

import com.shunwang.marketing.constant.InterfacesConstant;
import com.shunwang.marketing.manager.HttpMethod;
import com.shunwang.marketing.manager.response.inter.GetTicketWithoutLoginResponse;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

@Data
public class GetTicketWithoutLoginRequest extends BaseInterfaceRequest<GetTicketWithoutLoginResponse> {
    private String memberId;
    @Override
    public Map<String, String> buildParams() {
        Map<String,String> map=new HashMap<>();
        map.put("memberId",memberId);
        return map;
    }

    @Override
    public Class<GetTicketWithoutLoginResponse> getResponseClass() {
        return GetTicketWithoutLoginResponse.class;
    }

    @Override
    public Integer getInterfaceKey() {
        return InterfacesConstant.INTERFACE_KEY_GET_TICKET_WHITOUT_LOGIN;
    }

    @Override
    public HttpMethod getHttpMethod() {
        return HttpMethod.POST;
    }
}
