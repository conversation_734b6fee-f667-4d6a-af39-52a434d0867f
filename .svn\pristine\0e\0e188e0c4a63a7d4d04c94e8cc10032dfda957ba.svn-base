package com.shunwang.marketing.controller;

import com.shunwang.api.core.CommonResponse;
import com.shunwang.marketing.manager.service.UserApiServiceClient;
import com.shunwang.marketing.util.SensitiveInfoUtil;
import com.shunwang.userapi.client.model.common.*;
import com.shunwang.userapi.client.model.user.SingleAccountQueryRequest;
import com.shunwang.userapi.client.model.user.SingleAccountQueryResponse;
import com.shunwang.userapi.client.model.user.SingleAccountUnbindRequest;
import com.shunwang.util.lang.StringUtil;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

@Controller
@Slf4j
@RequestMapping("/netBar")
public class NetBarController {
    @Autowired
    private UserApiServiceClient userApiServiceClient;

    @ResponseBody
    @RequestMapping("/authUpdate")
    public CommonResponse authUpdate(Integer id, Integer state, Integer memberId) {
        NetBarAuthUpdateRequest request = new NetBarAuthUpdateRequest();
        request.setId(id);
        request.setState(state);
        request.setMemberId(memberId);
        return userApiServiceClient.authUpdate(request);
    }

    @ResponseBody
    @RequestMapping("/infoQuery")
    public CommonResponse infoQuery(String barid) {
        NetBarInfoQueryRequest request = new NetBarInfoQueryRequest();
        request.setBarid(barid);
        return userApiServiceClient.infoQuery(request);
    }

    @ResponseBody
    @RequestMapping("/authQuery")
    public CommonResponse authQuery(Integer memberId) {
        NetBarAuthQueryRequest request = new NetBarAuthQueryRequest();
        request.setWxMemberId(memberId);
        CommonResponse<List<NetBarAuthQueryResponse>> authQueryResponse = userApiServiceClient.authQuery(request);
        if(authQueryResponse.getCode() != 0){
            return authQueryResponse;
        }
        List<NetBarAuthQueryResponse> list = authQueryResponse.getData();
        for (NetBarAuthQueryResponse response : list){
            NetBarInfoQueryRequest netBarInfoQueryRequest = new NetBarInfoQueryRequest();
            netBarInfoQueryRequest.setBarid(response.getSnbid());
            CommonResponse<NetBarInfoQueryResponse> barResponse = userApiServiceClient.infoQuery(netBarInfoQueryRequest);
            if (barResponse == null || barResponse.getData() == null || StringUtil.isBlank(barResponse.getData().getsNBName())){
                continue;
            }
            response.setsNBName(barResponse.getData().getsNBName());
        }
        authQueryResponse.setData(list);
        return authQueryResponse;
    }

    @ResponseBody
    @RequestMapping("/single/query")
    public CommonResponse singleQuery(Integer memberId, Integer type) {
        SingleAccountQueryRequest request = new SingleAccountQueryRequest();
        request.setMemberId(memberId);
        request.setType(type);
        CommonResponse<SingleAccountQueryResponse> response = userApiServiceClient.singleQuery(request);
        if (response.getData() != null){
            response.getData().setMobile(SensitiveInfoUtil.mobileStarReplace(response.getData().getMobile()));
        }

        return response;
    }

    @ResponseBody
    @RequestMapping("/single/unbind")
    public CommonResponse singleUnbind(Integer memberId, Integer type) {
        SingleAccountUnbindRequest request = new SingleAccountUnbindRequest();
        request.setMemberId(memberId);
        request.setType(type);
        return userApiServiceClient.singleUnbind(request);
    }
}
