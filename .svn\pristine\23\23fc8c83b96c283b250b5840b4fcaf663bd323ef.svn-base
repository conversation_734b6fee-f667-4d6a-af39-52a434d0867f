package com.shunwang.marketing.pojo.marketing;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.shunwang.marketing.context.DomainContext;
import com.shunwang.marketing.enums.marketing.PrizeRecordEnum;
import com.shunwang.marketing.pojo.BaseObject;
import com.shunwang.marketing.util.DateUtil;
import com.shunwang.util.lang.StringUtil;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.util.Assert;

import java.util.Date;
@Data
@Accessors(chain = true)
public class RedPacketRecord extends BaseObject {
	private static final long serialVersionUID = 1L;
	private Integer id;
	@JsonIgnore
	private Integer savingCardId;
	private Integer welfareId;
	@JsonIgnore
	private String code;
	@JsonIgnore
	private Integer memberId;
	@JsonIgnore
	private String source;
	private Integer state;
	@JsonIgnore
	private String orderNo;
	@JsonIgnore
	private String mscOrderNo;
	@JsonIgnore
	private String bagOrderNo;
	@JsonIgnore
	private Date sendTime;
	@JsonIgnore
	private Date useTime;
	@JsonIgnore
	private Integer bagId;
	@JsonIgnore
	private Integer moduleDetailId;
	private Date expireTime;

	private Integer needUnlocked;

	private String name;
	/** 描述 **/
	private String description;
	/** 说明 **/
	private String explain;
	/** 内容 **/
	private String contents;
	/** 详情 **/
	private String details;
	private String img;
	private String url;
	/** 备注 **/
	private String remark;
	/** 面值 **/
	private Integer faceValue;
	/** 满多少可用 **/
	private Integer limiting;
	/** 是否过期 **/
	private Integer isExpired;
	/** 业务归属(1:游戏，2：网吧特权，3：泡泡，4：商城，5：珑腾，6：云电脑，7：其他) **/
	private Integer businessLine;

	private String gameId;
	private String webDetail;
	/**
	 * 打开方式，1：启动菜单详情页，2：浏览器，3：菜单内部浏览器 4:小程序h5页面 5:小程序内部页 6:外部小程序 7:外部小程序短链
	 */
	private Integer openType;

	public String getImg() {
		return DomainContext.getBackStaticServer() +  "/upload/images/welfare/" + img;
	}

	/**
	 * 是否过期
	 */
	public boolean judgeExpired(Welfare welfare){
		Date now = new Date();
		//已过期：1.未关联到权益 2.expireTime过期时间为不为null并且当前时间在过期时间之后 3.expireTime为null，过期时间取权益的effectiveDays字段+sendTime
		if (welfare == null
				|| (expireTime != null && now.after(expireTime))
				|| (expireTime == null && DateUtil.compare(now, DateUtil.addDay(sendTime, welfare.getEffectiveDays()), 1) > 0)) {
			isExpired = PrizeRecordEnum.IsExpiredEnum.EXPIRED.getValue();
			return true;
		}
		isExpired = PrizeRecordEnum.IsExpiredEnum.NOT_EXPIRED.getValue();
		return false;
	}

	/**
	 * 回跳地址
	 */
	private String backUrl;

	/**
	 * 落地页ID
	 */
	public String getLandingPageId() {
		if (StringUtil.isNotBlank(url) && url.contains("https://m.kedou.com/landingPage/goPage")) {
			return url.replaceFirst("https://m.kedou.com/landingPage/goPage\\?id=", "");
		}
		return "";
	}
}
