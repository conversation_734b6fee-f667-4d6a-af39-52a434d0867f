package com.shunwang.marketing.controller;


import com.shunwang.marketing.cacheService.CacheKeyConstant;
import com.shunwang.marketing.cacheService.RedisOperation;
import com.shunwang.marketing.enums.marketing.AiRecordEnum;
import com.shunwang.marketing.exception.CommonException;
import com.shunwang.marketing.pojo.ai.AiReplyMessage;
import com.shunwang.marketing.pojo.marketing.AiRecord;
import com.shunwang.marketing.pojo.marketing.AiSession;
import com.shunwang.marketing.pojo.marketing.vo.AiGameInfoVo;
import com.shunwang.marketing.pojo.marketing.vo.RewardVo;
import com.shunwang.marketing.result.Result;
import com.shunwang.marketing.result.ResultGenerator;
import com.shunwang.marketing.service.marketing.AiGameService;
import com.shunwang.marketing.service.marketing.AiRecordService;
import com.shunwang.marketing.service.marketing.AiService;
import com.shunwang.marketing.service.marketing.AiSessionService;
import com.shunwang.marketing.util.MemberUtil;
import com.shunwang.util.json.GsonUtil;
import com.shunwang.util.lang.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * ai游戏
 */
@Validated
@Controller
@RequestMapping("/aiGame")
public class AiGameController {
    @Autowired
    AiRecordService aiRecordService;
    @Autowired
    AiSessionService aiSessionService;
    @Autowired
    AiGameService aiGameService;
    @Autowired
    RedisOperation redisOperation;
    @Autowired
    AiService aiService;

    /**
     * 游戏/会话信息
     */
    @ResponseBody
    @RequestMapping("/info")
    public Result<AiGameInfoVo> info(@NotNull Integer gameId) {
        return ResultGenerator.newSuccessResult(aiGameService.getGameAndSessionInfo(gameId));
    }

    /**
     * 创建会话，用户第一次发送消息创建
     */
    @ResponseBody
    @RequestMapping("/create")
    public Result<String> createSession(@NotNull Integer gameId) {
        if (!redisOperation.setIfAbsent("ai_session_create_" + MemberUtil.getNotNullMemberId(), 1, 3, TimeUnit.SECONDS)) {
            throw new CommonException("操作频繁");
        }
        AiSession session = aiSessionService.createGameSession(gameId);
        return session == null ? ResultGenerator.newEmptySystemErrorResult() : ResultGenerator.newSuccessResult(session.getSessionId());
    }

    /**
     * 对话历史
     */
    @ResponseBody
    @RequestMapping("/history")
    public Result history(AiRecord pageable) {
        if (StringUtil.isBlank(pageable.getSessionId())) {
            return ResultGenerator.newSystemErrorResult("sessionId必填");
        }
        AiSession session = aiSessionService.getBySessionId(pageable.getSessionId());
        pageable.setSession(session.getId());
        pageable.setMemberId(MemberUtil.getNotNullMemberId());
        Map<String, Object> result = aiRecordService.getPageMap(pageable);
        List<AiRecord> list = (List<AiRecord>) result.get("list");
        list.forEach((record) -> {
            if (AiRecordEnum.TypeEnum.ANSWER.getValue().equals(record.getType())) {
                String content = record.getContent();
                AiReplyMessage replyMessage = GsonUtil.fromJson(content, AiReplyMessage.class);
                record.setContent(replyMessage.getContent());
            }
        });
        return ResultGenerator.newSuccessResult(result);
    }

    /**
     * 聊天
     *
     * @param sessionId
     * @return
     */
    @ResponseBody
    @RequestMapping("/chat")
    public Result<AiReplyMessage> chat(String sessionId, String question) {
        if (!redisOperation.setIfAbsent(CacheKeyConstant.AI_CHAT + MemberUtil.getNotNullMemberId(), 1, 3, TimeUnit.SECONDS)) {
            throw new CommonException("操作频繁");
        }
        return ResultGenerator.newSuccessResult(aiService.gameAiBuss(question, sessionId));
    }


    /**
     * 领奖
     *
     * @return
     */
    @ResponseBody
    @RequestMapping("/reward")
    public Result<RewardVo> receiveReward(String sessionId) {
        if (!redisOperation.setIfAbsent("ai_game_receive_" + MemberUtil.getNotNullMemberId(), 1, 3, TimeUnit.SECONDS)) {
            throw new CommonException("操作频繁");
        }
        return ResultGenerator.newSuccessResult(aiGameService.receiveReward(sessionId));
    }

    @RequestMapping("/goPage")
    public String goPage(@NotNull Integer gameId){
        return "aiGame/index";
    }

}
