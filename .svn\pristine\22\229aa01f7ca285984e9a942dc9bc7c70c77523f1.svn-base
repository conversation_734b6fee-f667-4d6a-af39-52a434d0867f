package com.shunwang.marketing.pojo.marketing;

import com.shunwang.marketing.pojo.BaseObject;
import java.math.BigDecimal;
import java.util.Date;
public class TencentGoods extends BaseObject {
	private static final long serialVersionUID = 1L;
	private Integer id;
	private String goodsName;
	private BigDecimal price;
	private String specification;
	private String description;
	private String payPageBackground;
	private Integer userCntLimit;
	private Integer cntLimt;
	private Long payCopywriting;
	private String remark;
	private Date addTime;
	private String addUser;
	private Date editTime;
	private String editUser;
	public Integer getId() {
		return id;
	}
	public void setId(Integer id) {
		this.id = id;
	}
	public String getGoodsName() {
		return goodsName;
	}
	public void setGoodsName(String goodsName) {
		this.goodsName = goodsName;
	}
	public BigDecimal getPrice() {
		return price;
	}
	public void setPrice(BigDecimal price) {
		this.price = price;
	}
	public String getSpecification() {
		return specification;
	}
	public void setSpecification(String specification) {
		this.specification = specification;
	}
	public String getDescription() {
		return description;
	}
	public void setDescription(String description) {
		this.description = description;
	}
	public String getPayPageBackground() {
		return payPageBackground;
	}
	public void setPayPageBackground(String payPageBackground) {
		this.payPageBackground = payPageBackground;
	}
	public Integer getUserCntLimit() {
		return userCntLimit;
	}
	public void setUserCntLimit(Integer userCntLimit) {
		this.userCntLimit = userCntLimit;
	}
	public Integer getCntLimt() {
		return cntLimt;
	}
	public void setCntLimt(Integer cntLimt) {
		this.cntLimt = cntLimt;
	}
	public Long getPayCopywriting() {
		return payCopywriting;
	}
	public void setPayCopywriting(Long payCopywriting) {
		this.payCopywriting = payCopywriting;
	}
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	public Date getAddTime() {
		return addTime;
	}
	public void setAddTime(Date addTime) {
		this.addTime = addTime;
	}
	public String getAddUser() {
		return addUser;
	}
	public void setAddUser(String addUser) {
		this.addUser = addUser;
	}
	public Date getEditTime() {
		return editTime;
	}
	public void setEditTime(Date editTime) {
		this.editTime = editTime;
	}
	public String getEditUser() {
		return editUser;
	}
	public void setEditUser(String editUser) {
		this.editUser = editUser;
	}
}
