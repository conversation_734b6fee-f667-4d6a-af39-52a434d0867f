package com.shunwang.marketing.service.marketing.impl;

import cn.hutool.core.util.RandomUtil;
import com.shunwang.marketing.cacheService.RedisOperation;
import com.shunwang.marketing.enums.ResourcesEnum;
import com.shunwang.marketing.enums.marketing.AiRecordEnum;
import com.shunwang.marketing.pojo.ai.AiReceiveMessage;
import com.shunwang.marketing.pojo.ai.AiReplyMessage;
import com.shunwang.marketing.pojo.marketing.AiModel;
import com.shunwang.marketing.pojo.marketing.AiRecord;
import com.shunwang.marketing.pojo.util.SendMsg;
import com.shunwang.marketing.query.marketing.AiModelQuery;
import com.shunwang.marketing.send.email.EmailSender;
import com.shunwang.marketing.service.ai.AiSessionWrapper;
import com.shunwang.marketing.service.config.ResourcesService;
import com.shunwang.marketing.service.marketing.AiModelService;
import com.shunwang.marketing.service.marketing.AiRecordService;
import com.shunwang.marketing.service.marketing.AiService;
import com.shunwang.sms.utils.SMSInnerSenderUtil;
import com.shunwang.util.json.GsonUtil;
import com.shunwang.util.lang.StringUtil;
import com.volcengine.ark.runtime.model.completion.chat.ChatCompletionRequest;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessage;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessageRole;
import com.volcengine.ark.runtime.service.ArkService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.shunwang.marketing.enums.marketing.AiModelEnum.PlatformEnum.AI_DEPARTMENT;
import static com.shunwang.marketing.enums.marketing.AiModelEnum.PlatformEnum.HUOSHAN;

@Service
@Slf4j
public class AiServiceimpl implements AiService {
    @Autowired
    AiRecordService aiRecordService;
    @Autowired
    AiModelService aiModelService;
    Map<String, ArkService> arkServiceMap = new ConcurrentHashMap<>();
    @Autowired
    RedisOperation redisOperation;
    @Autowired
    ResourcesService resourcesService;
    @Autowired
    EmailSender emailSender;

    private AiModel getModel(Integer modelId) {
        if (modelId != null) {
            AiModel model = aiModelService.get(modelId);
            if (model.isEffective()) {
                return model;
            }
        }
        List<AiModel> aiModelList = aiModelService.find(new AiModelQuery());
        if (CollectionUtils.isEmpty(aiModelList)) {
            return null;
        }
        aiModelList = aiModelList.stream().filter(AiModel::isEffective).collect(Collectors.toList());
        //优先使用外部模型，外部模型失效 使用ai部门模型
        Map<Boolean, List<AiModel>> isAiDepartment = aiModelList.stream().collect(Collectors.groupingBy((aiModel -> AI_DEPARTMENT.getValue().equals(aiModel.getPlatform()))));
        if (CollectionUtils.isEmpty(isAiDepartment.get(false))) {
            return RandomUtil.randomEle(isAiDepartment.get(true));
        } else {
            return RandomUtil.randomEle(isAiDepartment.get(false));
        }
    }

    public void checkToken() {
        List<AiModel> aiModelList = aiModelService.find(new AiModelQuery());
        int sumRemainToken = aiModelList.stream()
                .filter((aiModel -> !AI_DEPARTMENT.getValue().equals(aiModel.getPlatform()) || aiModel.getRemainToken() == null))
                .mapToInt(AiModel::getRemainToken)
                .sum();
        if (sumRemainToken < 50000) {
            log.info("ai总token小于50000");
            if (redisOperation.setIfAbsent("ai_token_check", 1, 3, TimeUnit.HOURS)) {
                log.info("ai总token小于50000，发送提醒");
                String emailReceiver = resourcesService.getStringValueByTypeAndNameEhCache(ResourcesEnum.TypeName.EMAIL_RECEIVER);
                if (StringUtil.isNotBlank(emailReceiver)) {
                    for (String receiver : emailReceiver.split(",")) {
                        SendMsg sendMsg = new SendMsg();
                        sendMsg.setTitle("AI Token余量不足");
                        sendMsg.setContent("AI总token数小于50000");
                        sendMsg.setNumber(receiver);
                        emailSender.sendEmail(sendMsg);
                    }
                }

                String smsReceiver = resourcesService.getStringValueByTypeAndNameEhCache(ResourcesEnum.TypeName.SMS_RECEIVER);
                if (StringUtil.isNotBlank(smsReceiver)) {
                    for (String receiver : smsReceiver.split(",")) {
                        SMSInnerSenderUtil.sendMsg(receiver, "AI总token数小于50000");
                    }
                }
            }
        }
    }

    @Override
    public void chat(AiSessionWrapper session, String message) throws IOException {
        AiReceiveMessage receiveMessage;
        try {
            receiveMessage = GsonUtil.fromJson(message, AiReceiveMessage.class);
        } catch (Exception e) {
            log.info("会话:{}:ai对话消息格式异常", session.getSessionId());
            session.getSession().getBasicRemote().sendText(AiReplyMessage.newErrorJsonMsg("消息格式错误"));
            return;
        }

        if (StringUtil.isBlank(receiveMessage.getQuestion())) {
            session.getSession().getBasicRemote().sendText(AiReplyMessage.newErrorJsonMsg("问题内容为空"));
        }

        session.setModel(getModel(session.getModel() == null ? null : session.getModel().getId()));
        AiModel model = session.getModel();
        if (model == null) {
            log.info("会话:{}:未获取到模型", session.getSessionId());
            session.getSession().getBasicRemote().sendText(AiReplyMessage.newErrorJsonMsg("未获取到模型"));
            return;
        }

        AiRecord questionRecord = new AiRecord();
        questionRecord.setMemberId(session.getMemberId());
        questionRecord.setType(AiRecordEnum.TypeEnum.USER.getValue());
        questionRecord.setContent(receiveMessage.getQuestion());
        questionRecord.setTimeAdd(new Date());
        questionRecord.setConsumeToken(0);
        questionRecord.setModelId(model.getId());
        aiRecordService.save(questionRecord);
        Integer platform = model.getPlatform();
        String messageId = UUID.randomUUID().toString();
        if (HUOSHAN.getValue().equals(platform)) {
            callHuoShanModel(session, receiveMessage.getQuestion(), questionRecord, messageId);
        } else if (AI_DEPARTMENT.getValue().equals(platform)) {
            callAIDepartmentModel(session, receiveMessage.getQuestion(), questionRecord);
        }
        checkToken();
    }

    private void callAIDepartmentModel(AiSessionWrapper session, String question, AiRecord questionRecord) {

    }

    private void callHuoShanModel(AiSessionWrapper session, String question, AiRecord questionRecord, String messageId) {
        AiModel model = session.getModel();
        ArkService service = getArkService(model);
        StringBuilder answer = new StringBuilder();
        AiRecord answerRecord = new AiRecord();
        answerRecord.setMemberId(session.getMemberId());
        answerRecord.setType(AiRecordEnum.TypeEnum.SYSTEM.getValue());
        answerRecord.setModelId(model.getId());
        answerRecord.setParentId(questionRecord.getId());
        final List<ChatMessage> streamMessages = new ArrayList<>();
        final ChatMessage streamUserMessage = ChatMessage.builder().role(ChatMessageRole.USER).content(question).build();
        streamMessages.add(streamUserMessage);

        ChatCompletionRequest streamChatCompletionRequest = ChatCompletionRequest.builder()
                .model(model.getEndPoint())
                .messages(streamMessages)
                .maxTokens(4096)
                .streamOptions(new ChatCompletionRequest.ChatCompletionRequestStreamOptions(true))
                .build();
        service.streamChatCompletion(streamChatCompletionRequest)
                .doOnError((throwable -> {
                    log.error("火山大模型调用异常:{}", session.getSessionId(), throwable);
                    session.getSession().getBasicRemote().sendText(AiReplyMessage.newErrorJsonMsg("火山大模型调用异常"));
                }))
                .blockingForEach(
                        choice -> {
                            if (!choice.getChoices().isEmpty()) {
                                String content = choice.getChoices().get(0).getMessage().getContent().toString();
                                //deepSeek深度思考存在很多为null的消息,content为null时，只发送finishReason不为空的消息
                                if (StringUtil.isNotEmpty(content) || StringUtil.isNotBlank(choice.getChoices().get(0).getFinishReason())) {
                                    answer.append(content);
                                    AiReplyMessage aiReplyMessage = new AiReplyMessage();
                                    aiReplyMessage.setCode(0);
                                    aiReplyMessage.setMessageId(messageId);
                                    aiReplyMessage.setFinish(StringUtil.isNotBlank(choice.getChoices().get(0).getFinishReason()));
                                    aiReplyMessage.setContent(content);
                                    session.getSession().getBasicRemote().sendText(GsonUtil.toJson(aiReplyMessage));
                                }
                            }
                            if (choice.getUsage() != null) {
                                answerRecord.setConsumeToken((int) choice.getUsage().getCompletionTokens());
                                questionRecord.setConsumeToken((int) choice.getUsage().getPromptTokens());
                            }
                        }
                );
        //回答记录
        answerRecord.setContent(answer.toString());
        answerRecord.setTimeAdd(new Date());
        aiRecordService.save(answerRecord);

        aiRecordService.update(questionRecord);

        //减少模型的token
        aiModelService.decToken(model.getId(), answerRecord.getConsumeToken() + questionRecord.getConsumeToken());
    }

    private ArkService getArkService(AiModel model) {
        ArkService service = arkServiceMap.get(model.getApiKey());
        if (service == null) {
            synchronized (model.getApiKey().intern()) {
                service = arkServiceMap.get(model.getApiKey());
                if (service == null) {
                    service = ArkService.builder()
                            .apiKey(model.getApiKey())
                            .timeout(Duration.ofSeconds(120))
                            .connectTimeout(Duration.ofSeconds(20))
                            .baseUrl(model.getUrl())
                            .build();
                }
                arkServiceMap.put(model.getApiKey(), service);
            }
        }
        return service;
    }
}
