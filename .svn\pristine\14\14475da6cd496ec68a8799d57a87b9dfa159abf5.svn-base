package com.shunwang.marketing.controller;

import com.shunwang.marketing.constant.SignInConstant;
import com.shunwang.marketing.pojo.marketing.SignIn;
import com.shunwang.marketing.result.Result;
import com.shunwang.marketing.result.ResultGenerator;
import com.shunwang.marketing.service.marketing.SignInService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 签到
 */
@Slf4j
@RestController
@RequestMapping("/h5/signIn")
public class SignInController {
    @Autowired
    private SignInService signInService;

    @GetMapping("/config")
    public Result config(@RequestParam(value = "channel", defaultValue = SignInConstant.DEFAULT_CHANNEL, required = false) String channel) {
        SignIn signIn = signInService.getByChannelFallback(channel);
        if (signIn == null) {
            return ResultGenerator.newEmptySystemErrorResult();
        }
        signInService.populate(signIn);
        Map<String, Object> result = new HashMap<>();
        result.put("isSignToday", signIn.isHaveSign());
        result.put("bgImg", signIn.getBgImagePath());
        result.put("signConfigList", signIn.getConfigList());

        return ResultGenerator.newSuccessResult(result);


    }

    /**
     * 签到
     *
     * @param channel
     * @return
     */
    @PostMapping("/doSignIn")
    public Result doSignIn(
            @RequestParam(value = "channel", defaultValue = SignInConstant.DEFAULT_CHANNEL, required = false) String channel) {
        SignIn signIn = signInService.getByChannelFallback(channel);
        if (signIn == null) {
            return ResultGenerator.newEmptySystemErrorResult();
        }
        signInService.signIn(signIn);
        return ResultGenerator.newSuccessResult("签到成功");

    }


}
