<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shunwang.marketing.dao.marketing.ContentInfoMapper">
    <resultMap id="BaseResultMap" type="contentInfo">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="configId" column="config_id" jdbcType="INTEGER"/>
        <result property="contentId" column="content_id" jdbcType="INTEGER"/>
        <result property="proportion" column="proportion" jdbcType="DOUBLE"/>
        <result property="tags" column="tags" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="baseColumn">
        id,
        config_id,
        content_id,
        proportion,
        tags
    </sql>
    <sql id="base_where" >
        <trim prefix="where" prefixOverrides="and|or">
            <if test="id != null ">
     	        and id = #{id,jdbcType=INTEGER}
            </if>
            <if test="configId != null ">
     	        and config_id = #{configId,jdbcType=INTEGER}
            </if>
            <if test="infoId != null ">
     	        and content_id = #{contentId,jdbcType=INTEGER}
            </if>
        </trim>
    </sql>
	<select id="find" parameterType="com.shunwang.marketing.query.BaseQuery" resultMap="BaseResultMap" >
		select 
        <include refid="baseColumn" />
		from marketing_content_info t 
		<include refid="base_where" />
	</select>
    <select id="findCnt" parameterType="com.shunwang.marketing.query.BaseQuery" resultType="java.lang.Integer" >
        select
            count(*)
        from marketing_content_info t
		<include refid="base_where" />
    </select>
	<insert id="save" parameterType="contentInfo" >
		insert into marketing_content_info 
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null">
                id,
            </if>
            <if test="configId != null">
                config_id,
            </if>
            <if test="contentId != null">
                content_id,
            </if>
            <if test="proportion != null">
                proportion,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="id != null">
                #{id,jdbcType=INTEGER},
            </if>
            <if test="configId != null">
                #{configId,jdbcType=INTEGER},
            </if>
            <if test="contentId != null">
                #{contentId,jdbcType=INTEGER},
            </if>
            <if test="proportion != null">
                #{proportion,jdbcType=DOUBLE},
            </if>
        </trim>
	</insert>
	<update id="update" parameterType="contentInfo" >
		update marketing_content_info
        <set>
            <if test="id != null">
     	        id = #{id,jdbcType=INTEGER},
            </if>
            <if test="configId != null">
     	        config_id = #{configId,jdbcType=INTEGER},
            </if>
            <if test="contentId != null">
                content_id = #{contentId,jdbcType=INTEGER},
            </if>
            <if test="proportion != null">
                proportion = #{proportion,jdbcType=DOUBLE},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
	</update>
</mapper>
