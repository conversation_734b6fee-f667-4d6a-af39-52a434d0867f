<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ApiGeneratorConfig">
    <option name="excludeFieldNames">
      <set>
        <option value="serialVersionUID" />
      </set>
    </option>
    <option name="yApiServerUrl" value="https://yapi.swomc.net/" />
    <option name="projectToken" value="7feadaef76c4e1b8a47757624e1055ed479171f60e993531485f8cd211aa86f0" />
    <option name="projectId" value="1299" />
    <option name="defaultCat" value="1299" />
  </component>
  <component name="ExternalStorageConfigurationManager" enabled="true" />
  <component name="MavenProjectsManager">
    <option name="originalFiles">
      <list>
        <option value="$PROJECT_DIR$/pom.xml" />
      </list>
    </option>
  </component>
  <component name="ProjectRootManager" version="2" languageLevel="JDK_1_8" project-jdk-name="1.8 (2)" project-jdk-type="JavaSDK" />
  <component name="SvnBranchConfigurationManager">
    <option name="myConfigurationMap">
      <map>
        <entry key="$PROJECT_DIR$">
          <value>
            <SvnBranchConfiguration>
              <option name="branchUrls">
                <list>
                  <option value="https://svn.shunwang.com/svn/passportLine/com.shunwang.marketingWeb/backup_branches" />
                  <option value="https://svn.shunwang.com/svn/passportLine/com.shunwang.marketingWeb/branches" />
                  <option value="https://svn.shunwang.com/svn/passportLine/com.shunwang.marketingWeb/tags" />
                </list>
              </option>
              <option name="trunkUrl" value="https://svn.shunwang.com/svn/passportLine/com.shunwang.marketingWeb/trunk" />
            </SvnBranchConfiguration>
          </value>
        </entry>
      </map>
    </option>
  </component>
</project>