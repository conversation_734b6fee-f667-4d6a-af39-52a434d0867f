package com.shunwang.marketing.dao.marketing;

import com.shunwang.marketing.dao.BaseMapper;
import com.shunwang.marketing.pojo.marketing.GiftBag;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface GiftBagMapper extends BaseMapper<GiftBag> {

    /**
     * 获取用户的礼包列表
     * @param memberId
     * @return
     */
    List<GiftBag> getUserGiftBagList(Integer memberId);

    List<GiftBag> findNoTagsData(String channel);

    int decStock(Integer bagId);

    List<GiftBag> findOnlineByIds(@Param("ids") List<Integer> ids);
}
