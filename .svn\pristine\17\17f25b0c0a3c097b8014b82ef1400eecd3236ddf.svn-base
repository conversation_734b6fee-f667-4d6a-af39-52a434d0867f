package com.shunwang.marketing.pojo.marketing.vo;

import com.shunwang.marketing.context.DomainContext;
import com.shunwang.marketing.enums.marketing.MscOrderEnum;
import com.shunwang.marketing.enums.marketing.WelfareEnum;
import com.shunwang.marketing.pojo.marketing.MscOrder;
import com.shunwang.util.lang.StringUtil;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class WelfareMscVo {
    private static final long serialVersionUID = 1L;
    private Integer welfareId;
    private String name;
    private Integer type;
    /** 描述 **/
    private String description;
    /** 说明 **/
    private String explain;
    /** 内容 **/
    private String contents;
    /** 详情 **/
    private String details;
    private String img;
    private String url;
    /** 备注 **/
    private String remark;
    /** 面值 **/
    private Integer faceValue;
    /** 满多少可用 **/
    private Integer limiting;

    /** 是否需要解锁 **/
    private Integer needUnlocked;
    /** 券类型（2：满减券，1：现金券） **/
    private Integer businessType;
    private Integer businessLine;
    private BigDecimal worth;

    private Integer recordId;
    /** 状态（0：未领取，1：未使用，2：已核销，3：已锁定） **/
    private Integer state;
    /** 是否过期（0：未过期，1：已过期） **/
    private Integer isExpired;

    private String bagOrderNo;

    /**
     * true 锁定 false 解锁，默认解锁，调用lock()方法上锁
     */
    private boolean isLock=false;
    /**
     * 打开方式，1：启动菜单详情页，2：浏览器，3：菜单内部浏览器 4:小程序h5页面 5:小程序内部页 6:外部小程序 7:外部小程序短链
     */
    private Integer openType;
    /**
     * 回跳地址
     */
    private String backUrl;

    /**
     * 落地页ID
     */
    public String getLandingPageId() {
        if (StringUtil.isNotBlank(url) && url.contains("https://m.kedou.com/landingPage/goPage")) {
            return url.replaceFirst("https://m.kedou.com/landingPage/goPage\\?id=", "");
        }
        return "";
    }

    public String getImg() {
        return DomainContext.getBackStaticServer() + "/upload/images/prize/" + img;
    }

    public Integer getState(){
        if (state == null){
            return 1;
        }
        return state;
    }

    public void lock(MscOrder mscOrder){
        if (WelfareEnum.NeedUnLocdkEnum.not_need.getValue().equals(needUnlocked)||MscOrderEnum.LockStateEnum.LOCKED.getValue().equals(mscOrder.getLockState())){
            return;
        }
        setLock(true);
    }
}
