package com.shunwang.marketing.service.api.impl;

import com.shunwang.marketing.context.UserContext;
import com.shunwang.marketing.exception.NoLoginFailException;
import com.shunwang.marketing.manager.request.inter.GetTicketWithoutLoginRequest;
import com.shunwang.marketing.manager.request.inter.VerifyLoginStatusRequest;
import com.shunwang.marketing.manager.request.sso.InterLoginAuthorizeRequest;
import com.shunwang.marketing.manager.response.inter.GetTicketWithoutLoginResponse;
import com.shunwang.marketing.manager.response.inter.VerifyLoginStatusResponse;
import com.shunwang.marketing.manager.service.InterfaceServiceClient;
import com.shunwang.marketing.pojo.config.Interfaces;
import com.shunwang.marketing.service.api.NoLoginService;
import com.shunwang.marketing.service.config.InterfacesService;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.utils.URIBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.net.URI;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Service
public class NoLoginServiceImpl implements NoLoginService {
    @Autowired
    private InterfacesService interfacesService;

    @Override
    public URI toIdentityIndex()  {
        return baseNoLogin(Objects.requireNonNull(UserContext.getUserId()).toString(), "https://i.kedou.com/index?partner=marketing" , "identity");
    }

    @Override
    public URI toSuggestPage()  {
        return baseNoLogin(Objects.requireNonNull(UserContext.getUserId()).toString(), "https://i.kedou.com/suggest/marketing/goPage", "identity");
    }

    @Override
    public URI toNetar()  {
        return baseNoLogin(Objects.requireNonNull(UserContext.getUserId()).toString(), "https://i.kedou.com/netbar/index", "identity");
    }

    private URI baseNoLogin(String memberId, String redirectUri, String targetSiteId) {
        try {
            //1.获取ticket
            log.debug("开始免登跳转：{}",redirectUri);
            GetTicketWithoutLoginRequest getTicketWithoutLoginRequest = new GetTicketWithoutLoginRequest();
            getTicketWithoutLoginRequest.setMemberId(memberId);
            GetTicketWithoutLoginResponse getTicketWithoutLoginResponse = InterfaceServiceClient.executeForJson(getTicketWithoutLoginRequest);
            if (getTicketWithoutLoginResponse.isSuccess()) {
                log.debug("获取ticket成功");
                //2.ticket换取token
                VerifyLoginStatusRequest verifyLoginStatusRequest = new VerifyLoginStatusRequest();
                verifyLoginStatusRequest.setMemberId(memberId);
                verifyLoginStatusRequest.setTicket(getTicketWithoutLoginResponse.getTicket());
                VerifyLoginStatusResponse verifyLoginStatusResponse = InterfaceServiceClient.executeForJson(verifyLoginStatusRequest);
                if (verifyLoginStatusResponse.isSuccess()) {
                    log.debug("获取token成功");
                    //3.使用token重定向到目标站点
                    InterLoginAuthorizeRequest interLoginAuthorizeRequest = new InterLoginAuthorizeRequest();
                    interLoginAuthorizeRequest.setMemberId(memberId);
                    interLoginAuthorizeRequest.setAccessToken(verifyLoginStatusResponse.getLoginStatusResult().getAccessToken());
                    interLoginAuthorizeRequest.setRedirectUri(redirectUri);
                    interLoginAuthorizeRequest.setTargetSiteId(targetSiteId);
                    Interfaces setting = interfacesService.selectByPrimaryKeyEnabled(interLoginAuthorizeRequest.getInterfaceKey());
                    interLoginAuthorizeRequest.doInterfaceSetting(setting);

                    String url = interLoginAuthorizeRequest.buildUrl();
                    Map<String, String> requestParams = interLoginAuthorizeRequest.buildCommonParams();
                    URIBuilder uriBuilder = new URIBuilder(url);
                    requestParams.forEach(uriBuilder::addParameter);
                    URI uri = uriBuilder.build();
                    log.debug("免登地址：{}", uri);
                    return uri;
                }else{
                    log.error("获取token失败,msgId:{},msg:{}", verifyLoginStatusResponse.getMsgId(), verifyLoginStatusResponse.getMsg());
                }
            } else {
                log.error("获取ticket失败,msgId:{},msg:{}", getTicketWithoutLoginResponse.getMsgId(), getTicketWithoutLoginResponse.getMsg());
            }
        } catch (Exception e) {
            throw new NoLoginFailException(e.getMessage());
        }
        throw new NoLoginFailException("");
    }

}
