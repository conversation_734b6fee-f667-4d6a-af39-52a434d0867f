package com.shunwang.marketing.query.marketing;

import com.shunwang.marketing.query.BaseQuery;

import java.util.Date;
public class TagQuery extends BaseQuery {

	private Integer id;
	private String tagName;
	private String partner;
	private String activeTag;
	private String interestTag;
	private String rfmTag;
	private String remark;
	private Date timeAdd;
	private String userAdd;
	private Date timeEdit;
	private String userEdit;
	public Integer getId() {
		return id;
	}
	public void setId(Integer id) {
		this.id = id;
	}
	public String getTagName() {
		return tagName;
	}
	public void setTagName(String tagName) {
		this.tagName = tagName;
	}
	public String getPartner() {
		return partner;
	}
	public void setPartner(String partner) {
		this.partner = partner;
	}
	public String getActiveTag() {
		return activeTag;
	}
	public void setActiveTag(String activeTag) {
		this.activeTag = activeTag;
	}
	public String getInterestTag() {
		return interestTag;
	}
	public void setInterestTag(String interestTag) {
		this.interestTag = interestTag;
	}
	public String getRfmTag() {
		return rfmTag;
	}
	public void setRfmTag(String rfmTag) {
		this.rfmTag = rfmTag;
	}
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	public Date getTimeAdd() {
		return timeAdd;
	}
	public void setTimeAdd(Date timeAdd) {
		this.timeAdd = timeAdd;
	}
	public String getUserAdd() {
		return userAdd;
	}
	public void setUserAdd(String userAdd) {
		this.userAdd = userAdd;
	}
	public Date getTimeEdit() {
		return timeEdit;
	}
	public void setTimeEdit(Date timeEdit) {
		this.timeEdit = timeEdit;
	}
	public String getUserEdit() {
		return userEdit;
	}
	public void setUserEdit(String userEdit) {
		this.userEdit = userEdit;
	}


}
