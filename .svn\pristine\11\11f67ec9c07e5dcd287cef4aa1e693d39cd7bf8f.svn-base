package com.shunwang.marketing.service.privilege.impl;

import com.shunwang.marketing.enums.marketing.ExtensionEnum;
import com.shunwang.marketing.enums.marketing.ResourcePrivilegeContentEnum;
import com.shunwang.marketing.enums.marketing.ResourcePrivilegeEnum;
import com.shunwang.marketing.pojo.marketing.*;
import com.shunwang.marketing.pojo.marketing.vo.BarPrivilegeVO;
import com.shunwang.marketing.pojo.marketing.vo.BarStatisticsVo;
import com.shunwang.marketing.pojo.marketing.vo.ExtensionVo;
import com.shunwang.marketing.pojo.marketing.vo.ResourcePrivilegeVO;
import com.shunwang.marketing.query.marketing.AdGameConfigQuery;
import com.shunwang.marketing.query.marketing.ResourcePrivilegeContentQuery;
import com.shunwang.marketing.service.marketing.*;
import com.shunwang.marketing.service.privilege.PrivilegeService;
import com.shunwang.util.lang.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 创建日期：2024/9/27 13:48
 * 创建作者：胡严峻 (<EMAIL>)
 * 描　　述：
 *
 * @author: 胡严峻 (<EMAIL>)
 * @date: 2024/9/27 13:48
 * @version: 1.0.1
 */
@Service
@Slf4j
public class PrivilegeServiceImpl implements PrivilegeService {

    @Autowired
    private AdBarConfigService adBarConfigService;

    @Autowired
    private AdGameConfigService adGameConfigService;

    @Autowired
    private ResourcePrivilegeService resourcePrivilegeService;

    @Autowired
    private ResourcePrivilegeContentService resourcePrivilegeContentService;

    @Autowired
    private BarLevelBarService barLevelBarService;

    @Autowired
    private BarLevelService barLevelService;

    @Autowired
    private BarLevelPrivilegeService barLevelPrivilegeService;

    @Autowired
    private WelfareService welfareService;

    @Autowired
    private GiftBagService giftBagService;
    @Autowired
    ExtensionService extensionService;
    @Autowired
    BarLevelResourceService barLevelResourceService;

    @Override
    @Cacheable(value = "cache_1_minutes", unless = "#result==null", key = "'m_bar_privilege_bar_id_'+#barId + '_resourceId_' + #resourceIdList.toString()")
    public BarPrivilegeVO queryPrivilegeContent(Integer barId, List<Integer> resourceIdList) {

        List<ResourcePrivilege> resourcePrivileges = resourcePrivilegeService.findAllOnline();
        Set<Integer> resourceIdSet = new HashSet<>(resourceIdList);
        resourcePrivileges.removeIf(resourcePrivilege -> !resourceIdSet.contains(resourcePrivilege.getResourceId()));
        if(resourcePrivileges.isEmpty()){
            return null;
        }
        //获取网吧等级
        BarLevel barLevel = null;
        AdBarConfig adBarConfig = adBarConfigService.findByBarId(barId);
        List<BarLevelBar> barLevelBars = barLevelBarService.findByBarId(barId);
        List<Integer> barLevelIds = barLevelBars.stream()
                .map(BarLevelBar::getBarLevelId).collect(Collectors.toList());
        List<BarLevel> barLevels = barLevelService.findOnlineByIds(barLevelIds);
        barLevels.sort(Comparator.comparing(BarLevel::getBarLevelKey).reversed());

        if(!barLevels.isEmpty()){
            barLevel = barLevels.get(0);
        }else if(adBarConfig != null){
            barLevel = barLevelService.findOnlineByBarLevelKey(adBarConfig.getBarLevelKey());
        }else{
            return null;
        }
        if(barLevel == null){
            return null;
        }
        //网吧对应特权
        List<BarLevelPrivilege> barLevelPrivileges = barLevelPrivilegeService.findByBarLevelId(barLevel.getId());
        List<Integer> resourcePrivilegeContentIds = barLevelPrivileges.stream()
                .map(BarLevelPrivilege::getResourcePrivilegeContentId)
                .collect(Collectors.toList());

        List<Integer> resourceIds = resourcePrivileges.stream()
                .map(ResourcePrivilege::getResourceId)
                .collect(Collectors.toList());
        //游戏等级
        List<AdGameConfig> adGameConfigs = adGameConfigService.findByBarIdAndResourceIds(barId, resourceIds);
        Map<Integer, Integer> resourceIdLevelMap = adGameConfigs.stream()
                .collect(Collectors.toMap(AdGameConfig::getResourceId, AdGameConfig::getLevel));

        //特权内容
        List<ResourcePrivilegeContentQuery> resourcePrivilegeContentQueries = resourcePrivileges.stream()
                .map(resourcePrivilege -> {
            ResourcePrivilegeContentQuery resourcePrivilegeContentQuery = new ResourcePrivilegeContentQuery();
            resourcePrivilegeContentQuery.setResourcePrivilegeId(resourcePrivilege.getId());
            resourcePrivilegeContentQuery.setLevel(resourceIdLevelMap.get(resourcePrivilege.getResourceId()));
            return resourcePrivilegeContentQuery;
        }).collect(Collectors.toList());
        List<ResourcePrivilegeContent> resourcePrivilegeContents =
                resourcePrivilegeContentService.findByResourcePrivilegeIdsAndLevelsAndIds(
                        resourcePrivilegeContentQueries, resourcePrivilegeContentIds);
        //具体权益或礼包
        Map<Integer, List<Integer>> privilegeTypeRelateIdMap = resourcePrivilegeContents.stream()
                .collect(Collectors.groupingBy(ResourcePrivilegeContent::getPrivilegeType,
                        Collectors.mapping(ResourcePrivilegeContent::getPrivilegeRelateId, Collectors.toList())));
        List<Welfare> welfareList = welfareService.findOnlineByIds(
                privilegeTypeRelateIdMap.get(ResourcePrivilegeContentEnum.PrivilegeTypeEnum.WELFARE.getValue()));
        List<GiftBag> giftBagList = giftBagService.findOnlineByIds(
                privilegeTypeRelateIdMap.get(ResourcePrivilegeContentEnum.PrivilegeTypeEnum.GIFT_BAG.getValue()));
        Map<Integer, Welfare> welfareMap = welfareList.stream().collect(Collectors.toMap(Welfare::getId, o -> o));
        Map<Integer, GiftBag> giftBagMap = giftBagList.stream().collect(Collectors.toMap(GiftBag::getId, o -> o));

        Map<Integer, ResourcePrivilege> resourcePrivilegeMap = resourcePrivileges.stream()
                .collect(Collectors.toMap(ResourcePrivilege::getId, o -> o));
        List<ResourcePrivilegeVO> resourcePrivilegeVos = resourcePrivilegeContents.stream()
                .map(resourcePrivilegeContent -> {
                    ResourcePrivilegeVO resourcePrivilegeVO = new ResourcePrivilegeVO();
                    resourcePrivilegeVO.setPrivilegeType(resourcePrivilegeContent.getPrivilegeType());
                    resourcePrivilegeVO.setResourceId(
                            resourcePrivilegeMap.get(resourcePrivilegeContent.getResourcePrivilegeId()).getResourceId());
                    resourcePrivilegeVO.setHoverImg(resourcePrivilegeMap.get(
                            resourcePrivilegeContent.getResourcePrivilegeId()).getHoverImg());
                    if (Objects.equals(resourcePrivilegeContent.getPrivilegeType(),
                            ResourcePrivilegeContentEnum.PrivilegeTypeEnum.WELFARE.getValue())) {
                        Welfare welfare = welfareMap.getOrDefault(resourcePrivilegeContent.getPrivilegeRelateId(), new Welfare());
                        resourcePrivilegeVO.setName(welfare.getName());
                        resourcePrivilegeVO.setImg(welfare.getImg());
                    } else if (Objects.equals(resourcePrivilegeContent.getPrivilegeType(),
                            ResourcePrivilegeContentEnum.PrivilegeTypeEnum.GIFT_BAG.getValue())) {
                        GiftBag giftBag = giftBagMap.getOrDefault(resourcePrivilegeContent.getPrivilegeRelateId(), new GiftBag());
                        resourcePrivilegeVO.setName(giftBag.getBagName());
                        resourcePrivilegeVO.setImg(giftBag.getBagImg());
                    }
                    return resourcePrivilegeVO;
                })
                .filter(resourcePrivilegeVO -> resourcePrivilegeVO.getName() != null)
                .collect(Collectors.toList());

        BarPrivilegeVO barPrivilegeVO = new BarPrivilegeVO();
        barPrivilegeVO.setPrivileges(resourcePrivilegeVos);
        barPrivilegeVO.setBarLevelKey(adBarConfig != null ? adBarConfig.getBarLevelKey() : null);
        barPrivilegeVO.setBarLevelName(barLevel.getBarLevelName());
        barPrivilegeVO.setBarLevelImg(barLevel.getBarLevelImg());
        return barPrivilegeVO;
    }

    @Override
    @Cacheable(value = "cache_1_minutes", unless = "#result==null", key = "'m_bar_privilege_bar_id_'+#barId")
    public BarPrivilegeVO queryPrivilegeContent(Integer barId) {
        //获取网吧等级
        BarLevel barLevel = null;
        AdBarConfig adBarConfig = adBarConfigService.findByBarId(barId);
        List<BarLevelBar> barLevelBars = barLevelBarService.findByBarId(barId);
        List<Integer> barLevelIds = barLevelBars.stream()
                .map(BarLevelBar::getBarLevelId).collect(Collectors.toList());
        List<BarLevel> barLevels = barLevelService.findOnlineByIds(barLevelIds);
        barLevels.sort(Comparator.comparing(BarLevel::getBarLevelKey).reversed());

        if (!barLevels.isEmpty()) {
            barLevel = barLevels.get(0);
        } else if (adBarConfig != null) {
            barLevel = barLevelService.findOnlineByBarLevelKey(adBarConfig.getBarLevelKey());
        } else {
            return null;
        }
        log.info("{}获取网吧等级:{}", barId, barLevel == null ? "" : barLevel.getBarLevelKey());
        if (barLevel == null) {
            return null;
        }
        //网吧对应特权
        List<ResourcePrivilegeContent> barLevelContentList = resourcePrivilegeContentService.findOnlineByBarLevelId(barLevel.getId());

        List<Integer> resourceIds = barLevelContentList.stream()
                .map(ResourcePrivilegeContent::getResourceId)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(resourceIds)) {
            log.info("不存在已上线游戏或特权");
            return null;
        } ;
        //游戏等级
        List<AdGameConfig> adGameConfigs = adGameConfigService.findByBarIdAndResourceIds(barId, resourceIds);
        Map<Integer, Map<Integer, AdGameConfig>> adGameConfigMap = adGameConfigs.stream()
                .collect(Collectors.groupingBy(AdGameConfig::getResourceId, Collectors.toMap(AdGameConfig::getType, config -> config)));

        //符合广告等级的所有特权并按照资源id分组
        Map<Integer, List<ResourcePrivilegeContent>> levelMatchGroupByResourceIdMap = barLevelContentList.stream().filter(
                (content) -> {
                    Map<Integer, AdGameConfig> configMap = adGameConfigMap.get(content.getResourceId());
                    if (configMap == null) return false;

                    AdGameConfig config = configMap.get(content.getSourceType());
                    if (config == null) return false;
                    content.setExtInfo(config.getExtInfo());
                    return config.getLevel().equals(content.getLevel());
                }
        ).collect(Collectors.groupingBy(
                ResourcePrivilegeContent::getResourceId
        ));

        //一个游戏如果有多个特权，优先展示qq网吧的
        List<ResourcePrivilegeVO> resourcePrivilegeVos = levelMatchGroupByResourceIdMap.values().stream().flatMap(
                (contentList) -> contentList.stream()
                        .collect(Collectors.groupingBy(ResourcePrivilegeContent::getType))
                        .values().stream().map(
                                (groupByTypeList) -> {
                                    groupByTypeList.sort((o1, o2) -> {
                                        if (o1.getSourceType().equals(o2.getSourceType())) {
                                            return o1.getId() - o2.getId();
                                        }
                                        return o2.getSourceType() - o1.getSourceType();
                                    });
                                    ResourcePrivilegeContent content = groupByTypeList.get(0);
                                    ResourcePrivilegeVO resourcePrivilegeVO = new ResourcePrivilegeVO();
                                    BeanUtils.copyProperties(content, resourcePrivilegeVO);
                                    if (ResourcePrivilegeEnum.SourceTypeEnum.QQ.getValue().equals(resourcePrivilegeVO.getSourceType()) && StringUtil.isNotBlank(content.getExtInfo())) {
                                        resourcePrivilegeVO.setQqGameId(Integer.parseInt(content.getExtInfo()));
                                    }
                                    return resourcePrivilegeVO;
                                }
                        )
        ).collect(Collectors.toList());

        //特权图标区和特权卡牌区分类
        Map<Integer, List<ResourcePrivilegeVO>> groupByType = resourcePrivilegeVos.stream()
                .sorted(Comparator.comparing(ResourcePrivilegeVO::getSort))
                .collect(Collectors.groupingBy(ResourcePrivilegeVO::getType));

        BarPrivilegeVO barPrivilegeVO = new BarPrivilegeVO();
        barPrivilegeVO.setPrivileges(groupByType.get(ResourcePrivilegeEnum.TypeEnum.ICON.getValue()));
        barPrivilegeVO.setCardPrivileges(groupByType.get(ResourcePrivilegeEnum.TypeEnum.CARD.getValue()));
        barPrivilegeVO.setPrivilegeNum(resourcePrivilegeVos.size());
        //网维根据barLevelKey是否为空展示特权
        barPrivilegeVO.setBarLevelKey(barPrivilegeVO.getPrivilegeNum() > 0 ? barLevel.getBarLevelKey() : null);
        barPrivilegeVO.setBarLevelName(barLevel.getBarLevelName());
        barPrivilegeVO.setBarLevelImg(barLevel.getBarLevelImg());
        ExtensionVo extension = extensionService.getPromotionData(barId.toString(), ExtensionEnum.LocationEnum.MENU_RIGHT.getValue());
        barPrivilegeVO.setMorePrivilegeUrl(extension.getLinkUrl());
        return barPrivilegeVO;
    }

    @Override
    public BarStatisticsVo queryBarStatistics(Integer barId) {
        AdBarConfig adBarConfig = adBarConfigService.findByBarId(barId);
        if (adBarConfig == null) {
            return null;
        }
        BarStatisticsVo barStatistics = new BarStatisticsVo();
        barStatistics.setBarId(barId);
        barStatistics.setBarLevelKey(adBarConfig.getBarLevelKey());

        AdGameConfigQuery query = new AdGameConfigQuery();
        query.setBarId(barId);
        List<AdGameConfig> adGameConfigs = adGameConfigService.find(query);

        if (!CollectionUtils.isEmpty(adGameConfigs)) {
            barStatistics.setResourceList(adGameConfigs.stream().map(AdGameConfig::getResourceId).distinct().collect(Collectors.toList()));
        }
        return barStatistics;
    }

    @Override
    public List<String> imageResources() {
        List<BarLevel> barLevelList = barLevelService.findAll();
        List<String> imageResources = new ArrayList<>();
        imageResources.addAll(
                barLevelList.stream().map(BarLevel::getBarLevelImg).collect(Collectors.toList())
        );

        List<ResourcePrivilegeContent> resourcePrivilegeContentList = resourcePrivilegeContentService.find(new ResourcePrivilegeContentQuery());
        imageResources.addAll(
                resourcePrivilegeContentList.stream().map(ResourcePrivilegeContent::getHoverImg).distinct().collect(Collectors.toList())
        );
        imageResources.addAll(
                resourcePrivilegeContentList.stream().map(ResourcePrivilegeContent::getShowImg).distinct().collect(Collectors.toList())
        );

        imageResources = imageResources.stream().filter(StringUtil::isNotBlank).collect(Collectors.toList());

        return imageResources;
    }
}
