package com.shunwang.marketing.query.marketing;

import com.shunwang.marketing.query.BaseQuery;
import lombok.Data;

import java.util.Date;
@Data
public class ResourcePrivilegeQuery extends BaseQuery {

	private Integer id;
	private String resourceName;
	private Integer resourceId;
	private Integer gradingState;
	private Integer onlineState;
	private String userAdd;
	private Date timeAdd;
	private String userEdit;
	private Date timeEdit;

	private String resourceIdOrName;

}
