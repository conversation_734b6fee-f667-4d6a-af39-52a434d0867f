package com.shunwang.marketing.service.marketing.tencent.impl;

import com.shunwang.marketing.context.DomainContext;
import com.shunwang.marketing.enums.marketing.TencentOrderEnum;
import com.shunwang.marketing.enums.marketing.WelfareEnum;
import com.shunwang.marketing.exception.CommonException;
import com.shunwang.marketing.manager.request.tencent.TokenToUidRequest;
import com.shunwang.marketing.manager.response.tencent.TokenToUidResponse;
import com.shunwang.marketing.manager.service.TencentGameServiceClient;
import com.shunwang.marketing.manager.service.UserApiServiceClient;
import com.shunwang.marketing.pojo.marketing.TencentActivity;
import com.shunwang.marketing.pojo.marketing.TencentGoods;
import com.shunwang.marketing.pojo.marketing.TencentOrder;
import com.shunwang.marketing.pojo.tencent.GoPayUrlVo;
import com.shunwang.marketing.service.marketing.TencentActivityService;
import com.shunwang.marketing.service.marketing.TencentGoodsService;
import com.shunwang.marketing.service.marketing.TencentOrderService;
import com.shunwang.marketing.service.marketing.tencent.TencentService;
import com.shunwang.userapi.client.model.user.UserQueryRequest;
import com.shunwang.userapi.client.model.user.UserQueryResponse;
import com.shunwang.util.date.DateUtil;
import com.shunwang.util.lang.StringUtil;
import com.shunwang.util.math.RandomUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.utils.URIBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
@Slf4j
public class TencentServiceImpl implements TencentService {
    @Autowired
    TencentOrderService tencentOrderService;
    @Autowired
    TencentGoodsService tencentGoodsService;
    @Autowired
    UserApiServiceClient userApiServiceClient;
    @Autowired
    TencentActivityService tencentActivityService;

    @Override
    public String getPayUrl(String token, Integer goodsId, String redirectUrl) {
        TencentGoods goods = tencentGoodsService.get(goodsId);
        if (goods == null) {
            throw new CommonException("商品不存在");
        }

        //token换用户Id
        Integer memberId = tokenToUserId(token, goods);

        //获取memberName
        UserQueryResponse user;
        try {
            UserQueryRequest request = new UserQueryRequest();
            request.setMemberId(memberId);
            user = userApiServiceClient.getUser(request);
        } catch (Exception e) {
            log.error("userapi获取用户异常", e);
            throw new CommonException("系统异常");
        }

        if (user == null) {
            log.error("用户不存在:{}", memberId);
            throw new CommonException("用户不存在");
        }

        //保存订单
        TencentOrder order = new TencentOrder();
        order.setOrderNo("TX" + DateUtil.getCurrentDateStamp() + RandomUtil.getRandomStr(10));
        order.setAmount(goods.getPrice());
        order.setGoodsName(goods.getGoodsName());
        order.setGoodsId(goodsId);
        order.setMemberId(memberId);
        order.setMemberName(user.getMemberName());
        order.setPayState(TencentOrderEnum.PayStateEnum.CREATE.getValue());
        order.setToken(token);
        order.setAddTime(new Date());
        tencentOrderService.save(order);

        try {
            URIBuilder builder = new URIBuilder(DomainContext.getAppServer() + "tencent/goPayUrl");
            builder.addParameter("orderNo", order.getOrderNo());
            builder.addParameter("redirectUrl", redirectUrl);
            return builder.build().toString();
        } catch (Exception e) {
            log.error("支付页面地址构建失败", e);
            throw new CommonException("支付页面地址构建失败");
        }
    }

    private Integer tokenToUserId(String token, TencentGoods goods) {
        TokenToUidRequest tokenToUidRequest = new TokenToUidRequest();
        tokenToUidRequest.setToken(token);
        String flowId = tencentGoodsService.getFlowId(goods.getId(), WelfareEnum.TypeEnum.TENCENT.getValue());
        if (StringUtil.isBlank(flowId)) {
            log.error("商品未配置腾讯商品或者未配置flowId:{}", goods.getId());
            throw new CommonException("flowId未配置");
        }
        tokenToUidRequest.setFlowId(flowId);

        TencentActivity activity = tencentActivityService.get(goods.getActivityId());
        tokenToUidRequest.setThirdActiveId(activity.getThirdActiveId());
        tokenToUidRequest.setThirdGameCode(activity.getGameId());
        TokenToUidResponse tokenToUidResponse = TencentGameServiceClient.execute(tokenToUidRequest);
        if (!tokenToUidResponse.isSuccess()) {
            log.error("腾讯token换用户Id失败:{}", tokenToUidResponse.getSMsg());
            throw new CommonException("系统异常");
        }
        return Integer.parseInt(tokenToUidResponse.getV7());
    }

    @Override
    public GoPayUrlVo goPayUrl(String orderNo) {
        TencentOrder order = tencentOrderService.getByOrderNoAndCheckInit(orderNo);
        GoPayUrlVo result = new GoPayUrlVo();

        TencentGoods goods = tencentGoodsService.get(order.getGoodsId());
        result.setPrice(goods.getPrice());
        result.setSpecification(goods.getSpecification());
        result.setGoodsName(goods.getGoodsName());
        result.setDescription(goods.getDescription());
        result.setPayPageBackground(goods.getPayPageBackground());
        result.setQrCodeUrl(DomainContext.getAppServer() + "tencentOrder/jumpPay?orderNo=" + orderNo);
        result.setOrderNo(orderNo);
        return result;
    }
}
