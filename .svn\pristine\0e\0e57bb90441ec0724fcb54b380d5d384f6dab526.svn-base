package com.shunwang.marketing.result;


public class ResultGenerator {

    public static <T> Result<T> newEmptySuccessResult() {
        return new Result<>(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMsg(), null);
    }

    public static <T> Result<T> newEmptySystemErrorResult() {
        return new Result<>(ResultCode.SYSTEM_ERROR.getCode(), ResultCode.SYSTEM_ERROR.getMsg(), null);
    }

    public static <T> Result<T> newSystemErrorResult(String msg) {
        return new Result<>(ResultCode.SYSTEM_ERROR.getCode(), msg, null);
    }

    public static <T> Result<T> newSuccessResult(T data) {
        return new Result<>(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMsg(), data);
    }

    public static <T> Result<T> newResult(ResultCode resultCode, T data) {
        return new Result<>(resultCode.getCode(), resultCode.getMsg(), data);
    }

    public static <T> Result<T> newResult(ResultCode resultCode, String msg, T data) {
        return new Result<>(resultCode.getCode(), msg, data);
    }


    public static <T> Result<T> newResult(int resultCode, String msg, T data) {
        return new Result<>(resultCode, msg, data);
    }


}
