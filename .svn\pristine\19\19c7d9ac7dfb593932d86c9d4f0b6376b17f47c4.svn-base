package com.shunwang.marketing.service.weixin.impl;

import com.shunwang.marketing.enums.marketing.SubMsgEnum;
import com.shunwang.marketing.enums.marketing.SubRecordEnum;
import com.shunwang.marketing.manager.response.weixin.WxMessage;
import com.shunwang.marketing.pojo.marketing.SubRecord;
import com.shunwang.marketing.query.marketing.SubRecordQuery;
import com.shunwang.marketing.service.bigData.BigDataService;
import com.shunwang.marketing.service.marketing.SubRecordService;
import com.shunwang.marketing.service.weixin.WxService;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.StringJoiner;

@Component
@Slf4j
public class WxServiceImpl implements WxService {
    @Autowired
    SubRecordService subRecordService;
    @Autowired
    BigDataService bigDataService;
    @Value("${bigDataReport.bussId.mini}")
    private String businessIdMini;

    @Override
    public void process(WxMessage wxMessage) {
        if (WxMessage.EVENT_SUB_MSG_POPUP.equals(wxMessage.getEvent())) { //订阅消息事件
            //订阅弹窗事件接受能发送次数加1，拒绝不会影响能发送的次数
            processMsgPopupEvent(wxMessage);
        } else if (WxMessage.EVENT_MSG_CHANGE.equals(wxMessage.getEvent())) {
            //改变事件，事件只会接收到用户拒绝接收消息的信息，只有用户选择不再询问后才能触发此事件
            //用户选择不接收之后次数清零
            processMsgChangeEvent(wxMessage);
        } else if (WxMessage.EVENT_MSG_SENT.equals(wxMessage.getEvent())) { //发送通知成功事件，不做操作
            processMsgSentEvent(wxMessage);
        }
    }

    private void processMsgSentEvent(WxMessage wxMessage) {
        if (!CollectionUtils.isEmpty(wxMessage.getList())) {
            wxMessage.getList().forEach((subMsgState -> {
                String templateId = subMsgState.getTemplateId();
                bigDataService.bigDataReport(buildSendBody(templateId, wxMessage.getFromUserName()), businessIdMini);
            }));
        }
    }

    private String buildSendBody(String templateId, String openId) {
        StringJoiner joiner = new StringJoiner("\t");
        joiner.add("submsg-send").add("click").add(templateId).add("").add(openId).add("");
        return joiner.toString();
    }

    private void processMsgPopupEvent(WxMessage wxMessage) {
        if (!CollectionUtils.isEmpty(wxMessage.getList())) {
            wxMessage.getList().forEach((subMsgState -> {
                String templateId = subMsgState.getTemplateId();
                String status = subMsgState.getSubscribeStatusString();
                SubRecordQuery query = new SubRecordQuery();
                query.setOpenId(wxMessage.getFromUserName());
                query.setTemplateId(templateId);
                SubRecord exist = subRecordService.findOne(query);
                if (exist == null) {
                    SubRecord toSave = buildRecord(wxMessage, templateId, status);
                    subRecordService.save(toSave);
                } else {
                    if (WxMessage.SubMsgState.SUB_STATUS_ACCEPT.equals(status)) {
                        subRecordService.incTimes(exist.getId());
                    }
                }
                bigDataService.bigDataReport(buildPopupBody(status, templateId, wxMessage.getFromUserName()), businessIdMini);
            }));
        }
    }

    private void processMsgChangeEvent(WxMessage wxMessage) {
        if (!CollectionUtils.isEmpty(wxMessage.getList())) {
            wxMessage.getList().forEach((subMsgState -> {
                String templateId = subMsgState.getTemplateId();
                String status = subMsgState.getSubscribeStatusString();
                SubRecordQuery query = new SubRecordQuery();
                query.setOpenId(wxMessage.getFromUserName());
                query.setTemplateId(templateId);
                SubRecord exist = subRecordService.findOne(query);
                if (exist == null) { //正常来讲不会不存在记录
                    SubRecord toSave = buildRecord(wxMessage, templateId, status);
                    subRecordService.save(toSave);
                } else {
                    //目前只有拒绝事件，将状态改为拒绝并且次数清零
                    if (WxMessage.SubMsgState.SUB_STATUS_REJECT.equals(status)) {
                        exist.setState(SubRecordEnum.StateEnum.REJECT.getValue());
                        exist.setTimes(0);
                        exist.setTimeEdit(new Date());
                        subRecordService.update(exist);
                    } else {
                        log.info("改变事件收到接受请求");
                    }
                }
            }));
        }
    }

    private String buildPopupBody(String status, String templateId, String openId) {
        StringJoiner joiner = new StringJoiner("\t");
        joiner.add(WxMessage.SubMsgState.SUB_STATUS_ACCEPT.equals(status) ? "submsg-agree" : "submsg-reject").add("click").add(templateId).add("").add(openId).add("");
        return joiner.toString();
    }

    @NotNull
    private SubRecord buildRecord(WxMessage wxMessage, String templateId, String status) {
        SubRecord subRecord = new SubRecord();
        subRecord.setOpenId(wxMessage.getFromUserName());
        subRecord.setTemplateId(templateId);
        subRecord.setTimeAdd(new Date());
        subRecord.setTimeEdit(new Date());
        subRecord.setSubType(SubMsgEnum.SubTypeEnum.ONCE.getValue()); //目前只有这个
        //状态字段感觉没有用，发送只跟次数挂钩即可，因此只要用户允许次数设置成1，用户不允许次数为0
        if (WxMessage.SubMsgState.SUB_STATUS_ACCEPT.equals(status)) {
            subRecord.setState(SubRecordEnum.StateEnum.ACCEPT.getValue());
            subRecord.setTimes(1);
        } else {
            subRecord.setState(SubRecordEnum.StateEnum.REJECT.getValue());
            subRecord.setTimes(0);
        }
        return subRecord;
    }
}
