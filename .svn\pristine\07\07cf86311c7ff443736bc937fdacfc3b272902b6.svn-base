<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shunwang.marketing.dao.marketing.CdkeyMapper">
    <resultMap id="BaseResultMap" type="cdkey">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="contents" column="contents" jdbcType="VARCHAR"/>
        <result property="fileName" column="file_name" jdbcType="VARCHAR"/>
        <result property="totalCnt" column="total_cnt" jdbcType="INTEGER"/>
        <result property="sendCnt" column="send_cnt" jdbcType="INTEGER"/>
        <result property="startTime" column="start_time" jdbcType="TIMESTAMP"/>
        <result property="endTime" column="end_time" jdbcType="TIMESTAMP"/>
        <result property="userAdd" column="user_add" jdbcType="VARCHAR"/>
        <result property="timeAdd" column="time_add" jdbcType="TIMESTAMP"/>
        <result property="userEdit" column="user_edit" jdbcType="VARCHAR"/>
        <result property="timeEdit" column="time_edit" jdbcType="TIMESTAMP"/>
    </resultMap>
    <sql id="baseColumn">
        id,
        `name`,
        contents,
        file_name,
        total_cnt,
        send_cnt,
        start_time,
        end_time,
        user_add,
        time_add,
        user_edit,
        time_edit
    </sql>
    <sql id="base_where" >
        <trim prefix="where" prefixOverrides="and|or">
            <if test="id != null ">
     	        and id = #{id,jdbcType=INTEGER}
            </if>
            <if test="name != null ">
     	        and `name` = #{name,jdbcType=VARCHAR}
            </if>
            <if test="contents != null ">
     	        and contents = #{contents,jdbcType=VARCHAR}
            </if>
            <if test="fileName != null ">
     	        and file_name = #{fileName,jdbcType=VARCHAR}
            </if>
            <if test="totalCnt != null ">
     	        and total_cnt = #{totalCnt,jdbcType=INTEGER}
            </if>
            <if test="sendCnt != null ">
     	        and send_cnt = #{sendCnt,jdbcType=INTEGER}
            </if>
            <if test="startTime != null ">
     	        and start_time = #{startTime,jdbcType=TIMESTAMP}
            </if>
            <if test="endTime != null ">
     	        and end_time = #{endTime,jdbcType=TIMESTAMP}
            </if>
            <if test="userAdd != null ">
     	        and user_add = #{userAdd,jdbcType=VARCHAR}
            </if>
            <if test="timeAdd != null ">
     	        and time_add = #{timeAdd,jdbcType=TIMESTAMP}
            </if>
            <if test="userEdit != null ">
     	        and user_edit = #{userEdit,jdbcType=VARCHAR}
            </if>
            <if test="timeEdit != null ">
     	        and time_edit = #{timeEdit,jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>

    <sql id="base_query_where" >
        <trim prefix="where" prefixOverrides="and|or">
            <if test="id != null ">
                and id = #{id,jdbcType=INTEGER}
            </if>
            <if test="name != null and name!='' ">
                and `name` = #{name,jdbcType=VARCHAR}
            </if>
            <if test="contents != null and contents!=''">
                and contents = #{contents,jdbcType=VARCHAR}
            </if>
            <if test="fileName != null and fileName!='' ">
                and file_name = #{fileName,jdbcType=VARCHAR}
            </if>
            <if test="totalCnt != null ">
                and total_cnt = #{totalCnt,jdbcType=INTEGER}
            </if>
            <if test="sendCnt != null ">
                and send_cnt = #{sendCnt,jdbcType=INTEGER}
            </if>
            <if test="startTime != null ">
                and start_time = #{startTime,jdbcType=TIMESTAMP}
            </if>
            <if test="endTime != null ">
                and end_time = #{endTime,jdbcType=TIMESTAMP}
            </if>
            <if test="userAdd != null and userAdd!='' ">
                and user_add = #{userAdd,jdbcType=VARCHAR}
            </if>
            <if test="timeAdd != null ">
                and time_add = #{timeAdd,jdbcType=TIMESTAMP}
            </if>
            <if test="userEdit != null and userEdit!=''">
                and user_edit = #{userEdit,jdbcType=VARCHAR}
            </if>
            <if test="timeEdit != null and timeEdit!='' ">
                and time_edit = #{timeEdit,jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>
	<select id="find" parameterType="com.shunwang.marketing.query.BaseQuery" resultMap="BaseResultMap" >
		select 
        <include refid="baseColumn" />
		from marketing_cdkey t 
		<include refid="base_where" />
	</select>
    <select id="findCnt" parameterType="com.shunwang.marketing.query.BaseQuery" resultType="java.lang.Integer" >
        select
            count(*)
        from marketing_cdkey t
		<include refid="base_where" />
    </select>
    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.Integer">
		select
        <include refid="baseColumn" />
		from marketing_cdkey
		where id = #{id,jdbcType=INTEGER}
    </select>
    <select id="getForUpdate" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select
        <include refid="baseColumn" />
        from marketing_cdkey
        where id = #{id,jdbcType=INTEGER}
        for update
    </select>
	<insert id="save" parameterType="cdkey" >
		insert into marketing_cdkey
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="name != null">
                `name`,
            </if>
            <if test="contents != null">
                contents,
            </if>
            <if test="fileName != null">
                file_name,
            </if>
            <if test="totalCnt != null">
                total_cnt,
            </if>
            <if test="sendCnt != null">
                send_cnt,
            </if>
            <if test="startTime != null">
                start_time,
            </if>
            <if test="endTime != null">
                end_time,
            </if>
            <if test="userAdd != null">
                user_add,
            </if>
            <if test="timeAdd != null">
                time_add,
            </if>
            <if test="userEdit != null">
                user_edit,
            </if>
            <if test="timeEdit != null">
                time_edit,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="contents != null">
                #{contents,jdbcType=VARCHAR},
            </if>
            <if test="fileName != null">
                #{fileName,jdbcType=VARCHAR},
            </if>
            <if test="totalCnt != null">
                #{totalCnt,jdbcType=INTEGER},
            </if>
            <if test="sendCnt != null">
                #{sendCnt,jdbcType=INTEGER},
            </if>
            <if test="startTime != null">
                #{startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="endTime != null">
                #{endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="userAdd != null">
                #{userAdd,jdbcType=VARCHAR},
            </if>
            <if test="timeAdd != null">
                #{timeAdd,jdbcType=TIMESTAMP},
            </if>
            <if test="userEdit != null">
                #{userEdit,jdbcType=VARCHAR},
            </if>
            <if test="timeEdit != null">
                #{timeEdit,jdbcType=TIMESTAMP},
            </if>
        </trim>
	</insert>
	<update id="update" parameterType="cdkey" >
		update marketing_cdkey
        <set>
            <if test="id != null">
     	        id = #{id,jdbcType=INTEGER},
            </if>
            <if test="name != null">
     	        `name` = #{name,jdbcType=VARCHAR},
            </if>
            <if test="contents != null">
     	        contents = #{contents,jdbcType=VARCHAR},
            </if>
            <if test="fileName != null">
     	        file_name = #{fileName,jdbcType=VARCHAR},
            </if>
            <if test="totalCnt != null">
     	        total_cnt = #{totalCnt,jdbcType=INTEGER},
            </if>
            <if test="sendCnt != null">
     	        send_cnt = #{sendCnt,jdbcType=INTEGER},
            </if>
            <if test="startTime != null">
     	        start_time = #{startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="endTime != null">
     	        end_time = #{endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="userAdd != null">
     	        user_add = #{userAdd,jdbcType=VARCHAR},
            </if>
            <if test="timeAdd != null">
     	        time_add = #{timeAdd,jdbcType=TIMESTAMP},
            </if>
            <if test="userEdit != null">
     	        user_edit = #{userEdit,jdbcType=VARCHAR},
            </if>
            <if test="timeEdit != null">
     	        time_edit = #{timeEdit,jdbcType=TIMESTAMP},
            </if>
        </set>
		<trim prefix="where" prefixOverrides="and|or"> 
            id = #{id,jdbcType=INTEGER}
        </trim>
	</update>
    <select id="findByWelfare" resultMap="BaseResultMap" >
        select
        <include refid="baseColumn" />
        from marketing_cdkey mc
        left join marketing_welfare_cdkey t on t.cdkey_id = mc.id
        where t.welfare_id = #{welfareId,jdbcType=INTEGER}
        and mc.start_time &lt; NOW() and mc.end_time &gt; NOW() and mc.send_cnt &lt; mc.total_cnt
    </select>
    <update id="updSendCnt" >
        update marketing_cdkey
            set send_cnt = send_cnt + 1
        where id = #{id,jdbcType=INTEGER}
    </update>
</mapper>
