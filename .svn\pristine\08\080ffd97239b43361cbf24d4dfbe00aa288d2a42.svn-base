<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shunwang.marketing.dao.marketing.TaskRuleTaskMapper">
    <resultMap id="BaseResultMap" type="taskRuleTask">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="taskId" column="task_id" jdbcType="INTEGER"/>
        <result property="ruleId" column="rule_id" jdbcType="INTEGER"/>
        <result property="showTitle" column="show_title" jdbcType="VARCHAR"/>
        <result property="explain" column="explain" jdbcType="VARCHAR"/>
        <result property="sort" column="sort" jdbcType="INTEGER"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="content" column="content" jdbcType="VARCHAR"/>
        <result property="jumpType" column="jump_type" jdbcType="INTEGER"/>
        <result property="appId" column="app_id" jdbcType="VARCHAR"/>
        <result property="backUrl" column="back_url" jdbcType="VARCHAR"/>
        <result property="key" column="key" jdbcType="VARCHAR"/>
        <result property="timeAdd" column="time_add" jdbcType="TIMESTAMP"/>
        <result property="timeEdit" column="time_edit" jdbcType="TIMESTAMP"/>
        <result property="userAdd" column="user_add" jdbcType="VARCHAR"/>
        <result property="userEdit" column="user_edit" jdbcType="VARCHAR"/>
    </resultMap>
    <resultMap id="TaskResultMap" type="com.shunwang.marketing.pojo.marketing.vo.UserTaskVo">
        <result property="ruleTaskId" column="ruleTaskId" jdbcType="INTEGER"/>
        <result property="showTitle" column="show_title" jdbcType="VARCHAR"/>
        <result property="explain" column="explain" jdbcType="VARCHAR"/>
        <result property="type" column="type" jdbcType="INTEGER"/>
        <result property="jumpType" column="jump_type" jdbcType="INTEGER"/>
        <result property="backUrl" column="back_url" jdbcType="VARCHAR"/>
        <result property="content" column="content" jdbcType="VARCHAR"/>
        <result property="appId" column="app_id" jdbcType="VARCHAR"/>
        <result property="key" column="key" jdbcType="VARCHAR"/>
        <result property="cntLimit" column="cnt_limit" jdbcType="INTEGER"/>
        <result property="condition" column="condition" jdbcType="INTEGER"/>
    </resultMap>
    <sql id="baseColumn">
        id,
        task_id,
        rule_id,
        show_title,
        `explain`,
        sort,
        remark,
        content,
        jump_type,
        app_id,
        back_url,
        `key`,
        time_add,
        time_edit,
        user_add,
        user_edit
    </sql>
    <sql id="base_where" >
        <trim prefix="where" prefixOverrides="and|or">
            <if test="id != null ">
     	        and id = #{id,jdbcType=INTEGER}
            </if>
            <if test="taskId != null ">
     	        and task_id = #{taskId,jdbcType=INTEGER}
            </if>
            <if test="ruleId != null ">
     	        and rule_id = #{ruleId,jdbcType=INTEGER}
            </if>
            <if test="showTitle != null ">
     	        and show_title = #{showTitle,jdbcType=VARCHAR}
            </if>
            <if test="explain != null ">
     	        and `explain` = #{explain,jdbcType=VARCHAR}
            </if>
            <if test="sort != null ">
     	        and sort = #{sort,jdbcType=INTEGER}
            </if>
            <if test="remark != null ">
     	        and remark = #{remark,jdbcType=VARCHAR}
            </if>
            <if test="content != null ">
     	        and content = #{content,jdbcType=VARCHAR}
            </if>
            <if test="jumpType != null ">
     	        and jump_type = #{jumpType,jdbcType=INTEGER}
            </if>
            <if test="appId != null ">
     	        and app_id = #{appId,jdbcType=VARCHAR}
            </if>
            <if test="key != null ">
     	        and `key` = #{key,jdbcType=VARCHAR}
            </if>
        </trim>
    </sql>
	<select id="find" parameterType="com.shunwang.marketing.query.BaseQuery" resultMap="BaseResultMap" >
		select 
        <include refid="baseColumn" />
		from marketing_task_rule_task t 
		<include refid="base_where" />
	</select>
    <select id="findCnt" parameterType="com.shunwang.marketing.query.BaseQuery" resultType="java.lang.Integer" >
        select
            count(*)
        from marketing_task_rule_task t
		<include refid="base_where" />
    </select>
    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.Integer">
		select
        <include refid="baseColumn" />
		from marketing_task_rule_task
		where id = #{id,jdbcType=INTEGER}
    </select>
	<insert id="save" parameterType="taskRuleTask" >
		insert into marketing_task_rule_task
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="taskId != null">
                task_id,
            </if>
            <if test="ruleId != null">
                rule_id,
            </if>
            <if test="showTitle != null">
                show_title,
            </if>
            <if test="explain != null">
                `explain`,
            </if>
            <if test="sort != null">
                sort,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="content != null">
                content,
            </if>
            <if test="jumpType != null">
                jump_type,
            </if>
            <if test="appId != null">
                app_id,
            </if>
            <if test="key != null">
                key,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="taskId != null">
                #{taskId,jdbcType=INTEGER},
            </if>
            <if test="ruleId != null">
                #{ruleId,jdbcType=INTEGER},
            </if>
            <if test="showTitle != null">
                #{showTitle,jdbcType=VARCHAR},
            </if>
            <if test="explain != null">
                #{explain,jdbcType=VARCHAR},
            </if>
            <if test="sort != null">
                #{sort,jdbcType=INTEGER},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="content != null">
                #{content,jdbcType=VARCHAR},
            </if>
            <if test="jumpType != null">
                #{jumpType,jdbcType=INTEGER},
            </if>
            <if test="appId != null">
                #{appId,jdbcType=VARCHAR},
            </if>
            <if test="key != null">
                #{key,jdbcType=VARCHAR},
            </if>
        </trim>
	</insert>
	<update id="update" parameterType="taskRuleTask" >
		update marketing_task_rule_task
        <set>
            <if test="id != null">
     	        id = #{id,jdbcType=INTEGER},
            </if>
            <if test="taskId != null">
     	        task_id = #{taskId,jdbcType=INTEGER},
            </if>
            <if test="ruleId != null">
     	        rule_id = #{ruleId,jdbcType=INTEGER},
            </if>
            <if test="showTitle != null">
     	        show_title = #{showTitle,jdbcType=VARCHAR},
            </if>
            <if test="explain != null">
     	        `explain` = #{explain,jdbcType=VARCHAR},
            </if>
            <if test="sort != null">
     	        sort = #{sort,jdbcType=INTEGER},
            </if>
            <if test="remark != null">
     	        remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="content != null">
     	        content = #{content,jdbcType=VARCHAR},
            </if>
            <if test="jumpType != null">
     	        jump_type = #{jumpType,jdbcType=INTEGER},
            </if>
            <if test="appId != null">
     	        app_id = #{appId,jdbcType=VARCHAR},
            </if>
            <if test="key != null">
     	        key = #{key,jdbcType=VARCHAR},
            </if>
        </set>
		<trim prefix="where" prefixOverrides="and|or"> 
            id = #{id,jdbcType=INTEGER}
        </trim>
	</update>
    <select id="getPondTaskList" resultMap="TaskResultMap" parameterType="java.lang.Integer">
        select trt.id as ruleTaskId, trt.show_title, trt.explain, t.type, trt.jump_type, trt.content, trt.app_id, trt.back_url, trt.key, t.cnt_limit, t.condition
        from marketing_task_rule_task trt
        left join marketing_task t on trt.task_id = t.id
        left join marketing_task_rule tr on trt.rule_id = tr.id
        where tr.id = #{taskRuleId}
        order by trt.sort desc
    </select>
</mapper>
