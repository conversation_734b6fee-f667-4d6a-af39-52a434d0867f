package com.shunwang.marketing.enums.marketing;

import lombok.Getter;
import lombok.Setter;

public interface BussMemberRelationEnum {

    enum BussTypeEnum {
        PRIZE(1, "奖品"),
        SUB_MSG(2, "订阅消息"),
        CONTENT(3, "摘星星"),
        ;

        BussTypeEnum(int value, String name) {
            this.value = value;
            this.name = name;
        }

        private @Getter Integer value;
        private @Getter String name;
    }


}
