package com.shunwang.marketing.pojo.marketing;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.shunwang.marketing.pojo.BaseObject;
import lombok.Data;

import java.util.Date;

@Data
public class BannerDetails extends BaseObject {
	private static final long serialVersionUID = 1L;
	private Integer id;
	private String name;
	/**
	 * 打开方式（1：启动菜单详情页，2：外部浏览器，3：菜单浏览器）
	 */
	private Integer openType;
	/**
	 * 游戏ID
	 */
	private String gameId;
	@JsonIgnore
	private Integer bannerId;
	@JsonIgnore
	private Integer imgId;
	/**
	 * 链接跳转地址
	 */
	private String linkUrl;
	@JsonIgnore
	private Integer state;
	@JsonIgnore
	private Integer sort;
	@JsonIgnore
	private String remark;

}
