package com.shunwang.marketing.service.marketing.impl;

import com.shunwang.marketing.context.UserContext;
import com.shunwang.marketing.dao.marketing.TaskRuleTaskMapper;
import com.shunwang.marketing.enums.marketing.MemberTaskEnum;
import com.shunwang.marketing.enums.marketing.PrizeEnum;
import com.shunwang.marketing.enums.marketing.TaskEnum;
import com.shunwang.marketing.enums.marketing.WelfareEnum;
import com.shunwang.marketing.pojo.marketing.*;
import com.shunwang.marketing.pojo.marketing.vo.UserTaskVo;
import com.shunwang.marketing.service.marketing.*;
import com.shunwang.marketing.dao.BaseMapper;
import org.springframework.beans.factory.annotation.Autowired;
import com.shunwang.marketing.dao.marketing.TaskCenterMapper;
import com.shunwang.marketing.service.impl.BaseServiceImpl;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class TaskCenterServiceImpl extends BaseServiceImpl<TaskCenter> implements TaskCenterService {
    @Autowired
    private TaskCenterMapper taskCenterMapper;
    @Autowired
    private TaskRuleTaskService taskRuleTaskService;
    @Autowired
    private TaskService taskService;
    @Autowired
    private MemberTaskService memberTaskService;
    @Autowired
    private TaskRuleTaskMapper taskRuleTaskMapper;
    @Autowired
    private WelfareService welfareService;
    @Autowired
    private GiftBagService giftBagService;


    @Override
    public BaseMapper<TaskCenter> getDao() {
        return taskCenterMapper;
    }


    @Override
    public List<TaskCenter> findOnline(String channel) {
        List<TaskCenter> taskCenterList = taskCenterMapper.findOnline(channel);
        for (TaskCenter taskCenter : taskCenterList){
            buildTaskCenter(taskCenter);
        }
        return taskCenterList;
    }

    public TaskCenter buildTaskCenter(TaskCenter taskCenter) {
        List<UserTaskVo> userTaskVoList = taskRuleTaskMapper.getPondTaskList(taskCenter.getRuleId());
        for (UserTaskVo vo : userTaskVoList){
            TaskRuleTask taskRuleTask = taskRuleTaskMapper.get(vo.getRuleTaskId());
            Task task = taskService.get(taskRuleTask.getTaskId());
            MemberTask memberTask = memberTaskService.toStartMemberTask(task, UserContext.getUserId(), vo.getRuleTaskId(), null);
            vo.setRewardType(task.getRewardType());
            vo.setRewardName(getRewardName(task.getRewardType(), task.getReward()));
            if (TaskEnum.ToolEnum.BUY_GIFT_BAG.getValue().equals(task.getTool())){
                vo.setTool(task.getTool());
                vo.setConditionId(task.getToolCondition());
            }
            vo.setFinishCnt(memberTask.getFinishCnt());
            vo.setState(vo.getCntLimit().intValue() == vo.getFinishCnt().intValue() ? MemberTaskEnum.StateEnum.FINISHED.getValue() : MemberTaskEnum.StateEnum.NOT_FINISHED.getValue());
            if (TaskEnum.RewardTypeEnum.WELFARE.getValue().equals(task.getRewardType())) {
                Welfare welfare = welfareService.get(Integer.parseInt(task.getReward()));
                vo.setWelfareType(welfare.getType());
                vo.setRewardRecordId(memberTask.getRecordId());
            }
            if (TaskEnum.RewardTypeEnum.SCORE.getValue().equals(task.getRewardType())) {
                vo.setWelfareType(WelfareEnum.TypeEnum.SCORE.getValue()); //前端拉详情用
            } else if (TaskEnum.RewardTypeEnum.GIFT_BAG.getValue().equals(task.getRewardType())) {
                vo.setWelfareType(PrizeEnum.PrizeTypeEnum.GIFT_BAG.getValue()); //前端拉详情用
            }
        }
        taskCenter.setTaskList(userTaskVoList);
        return taskCenter;
    }

    private String getRewardName(Integer rewardType, String reward) {
        if (rewardType.intValue() == TaskEnum.RewardTypeEnum.CNT.getValue()) {
            return "抽奖次数+" + reward;
        } else if (rewardType.intValue() == TaskEnum.RewardTypeEnum.WELFARE.getValue()) {
            Welfare welfare = welfareService.get(Integer.valueOf(reward));
            return welfare == null ? "" : welfare.getName() + "+1";
        } else if (rewardType.intValue() == TaskEnum.RewardTypeEnum.SCORE.getValue()) {
            return "网费通+" + reward;
        } else if (rewardType.intValue() == TaskEnum.RewardTypeEnum.GIFT_BAG.getValue()) {
            GiftBag giftBag = giftBagService.get(Integer.valueOf(reward));
            return giftBag == null ? "" : giftBag.getBagName() + "+1";
        }

        return "";
    }
}
