package com.shunwang.marketing.service.marketing.impl;

import com.shunwang.marketing.dao.BaseMapper;
import com.shunwang.marketing.enums.marketing.MemberSurveyEnum;
import com.shunwang.marketing.enums.marketing.ScoreRecordEnum;
import com.shunwang.marketing.enums.marketing.SurveyEnum;
import com.shunwang.marketing.enums.marketing.WelfareEnum;
import com.shunwang.marketing.exception.CommonException;
import com.shunwang.marketing.pojo.marketing.MemberSurveyOption;
import com.shunwang.marketing.pojo.marketing.Survey;
import com.shunwang.marketing.pojo.marketing.Welfare;
import com.shunwang.marketing.pojo.marketing.vo.MemberSurveyVo;
import com.shunwang.marketing.query.marketing.MemberSurveyQuery;
import com.shunwang.marketing.service.marketing.*;
import com.shunwang.marketing.util.MemberUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import com.shunwang.marketing.dao.marketing.MemberSurveyMapper;
import com.shunwang.marketing.pojo.marketing.MemberSurvey;
import com.shunwang.marketing.service.impl.BaseServiceImpl;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

@Slf4j
@Component
public class MemberSurveyServiceImpl extends BaseServiceImpl<MemberSurvey> implements MemberSurveyService {
    @Autowired
    private MemberSurveyMapper memberSurveyMapper;
    @Autowired
    private MemberSurveyOptionService memberSurveyOptionService;
    @Autowired
    WelfareService welfareService;
    @Autowired
    SurveyService surveyService;
    @Autowired
    GiftBagService giftBagService;
    /** 红包 **/
    private final Integer TYPE_RED_PACKET = 2;
    /** 券 **/
    private final Integer TYPE_COUPON = 1;
    /** 礼包 **/
    private final Integer TYPE_BAG = 3;

    @Override
    public BaseMapper getDao() {
        return memberSurveyMapper;
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public MemberSurvey save(MemberSurvey memberSurvey) {
        memberSurvey.setTimeAdd(new Date());
        memberSurvey.setMemberId(MemberUtil.getNotNullMemberId());
        memberSurvey.setPrizeReceived(MemberSurveyEnum.PrizeReceivedEnum.NO.getValue());
        memberSurveyMapper.save(memberSurvey);
        List<MemberSurveyOption> optionList = ((MemberSurveyVo) memberSurvey).getOptionList();
        optionList.forEach((option) -> option.setMemberSurveyId(memberSurvey.getId()));
        memberSurveyOptionService.batchSave(optionList);
        return memberSurvey;
    }


    @Override
    public MemberSurvey getByMIdAndSurveyId(Integer memberId, Integer surveyId) {
        MemberSurveyQuery query = new MemberSurveyQuery();
        query.setMemberId(MemberUtil.getNotNullMemberId());
        query.setSurveyId(surveyId);
        return findOne(query);
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public Integer getReward(Integer surveyId) {
        Integer type = 0;
        Survey survey = surveyService.getIgnoreState(surveyId);
        if (survey == null) {
            log.error("问卷{}不存在", surveyId);
            throw new CommonException(-5, "问卷不存在");
        }
        MemberSurvey memberSurvey = getByMIdAndSurveyId(MemberUtil.getNotNullMemberId(), surveyId);
        if (memberSurvey == null) {
            log.error("用户{}尚未填写过问卷{}", MemberUtil.getNotNullMemberId(), surveyId);
            throw new CommonException(-3, "用户尚未填写过问卷");
        }
        memberSurvey = memberSurveyMapper.getForUpdate(memberSurvey.getId());
        if (MemberSurveyEnum.PrizeReceivedEnum.YES.getValue().equals(memberSurvey.getPrizeReceived())) {
            log.error("用户{}已领取过奖励,问卷ID{}", MemberUtil.getNotNullMemberId(), surveyId);
            throw new CommonException(-4, "用户已领取过奖励");
        }

        if (SurveyEnum.RewardTypeEnum.WELFARE.getValue().equals(survey.getRewardType())) {
            Welfare welfare = welfareService.get(survey.getRewardId());
            welfareService.sendWelfare(welfareService.get(survey.getRewardId()), "问卷", ScoreRecordEnum.SourceTypeEnum.SURVEY,true);
            type = WelfareEnum.TypeEnum.RED_PACKET.getValue().intValue() == welfare.getType() ? TYPE_RED_PACKET : TYPE_COUPON;
        } else if (SurveyEnum.RewardTypeEnum.GIFT.getValue().equals(survey.getRewardType())) {
            giftBagService.receiveGiftBag(survey.getRewardId());
            type = TYPE_BAG;
        }
        memberSurvey.setPrizeReceived(MemberSurveyEnum.PrizeReceivedEnum.YES.getValue());
        update(memberSurvey);
        return type;
    }
}
