package com.shunwang.marketing.service.marketing.impl;

import com.shunwang.marketing.dao.BaseMapper;
import com.shunwang.marketing.enums.marketing.BannerDetailsEnum;
import com.shunwang.marketing.pojo.marketing.Image;
import com.shunwang.marketing.pojo.marketing.vo.BannerDetailVo;
import com.shunwang.marketing.query.marketing.BannerDetailsQuery;
import com.shunwang.marketing.service.marketing.ImageService;
import org.springframework.beans.factory.annotation.Autowired;
import com.shunwang.marketing.dao.marketing.BannerDetailsMapper;
import com.shunwang.marketing.service.marketing.BannerDetailsService;
import com.shunwang.marketing.service.impl.BaseServiceImpl;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;
import com.shunwang.marketing.pojo.marketing.BannerDetails;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Component
public class BannerDetailsServiceImpl extends BaseServiceImpl<BannerDetails> implements BannerDetailsService {
    @Autowired
    private BannerDetailsMapper bannerDetailsMapper;
    @Autowired
    ImageService imageService;
    @Override
    public BaseMapper<BannerDetails> getDao() {
        return bannerDetailsMapper;
    }


    @Cacheable(value = "cache_10_minutes", unless = "#result==null or #result.size()==0", key = "'m_bannerDetail_'+#bannerId")
    @Override
    public List<BannerDetailVo> findByBannerId(Integer bannerId) {
        BannerDetailsQuery query = new BannerDetailsQuery();
        query.setBannerId(bannerId);
        query.setState(BannerDetailsEnum.StateEnum.OPEN.getValue());
        return bannerDetailsMapper.findBannerDetailVo(query);
    }

    @Override
    public void populate(BannerDetailVo bannerDetail) {
        imageService.populate(bannerDetail.getImgId(), bannerDetail::setImagePath, bannerDetail::setInformation);
    }
}
