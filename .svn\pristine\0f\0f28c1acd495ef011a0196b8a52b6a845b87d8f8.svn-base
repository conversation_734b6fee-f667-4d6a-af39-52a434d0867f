<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shunwang.marketing.dao.marketing.ContentMapper">
    <resultMap id="BaseResultMap" type="content">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="type" column="type" jdbcType="INTEGER"/>
        <result property="title" column="title" jdbcType="VARCHAR"/>
        <result property="welfare" column="welfare" jdbcType="INTEGER"/>
        <result property="userName" column="user_name" jdbcType="VARCHAR"/>
        <result property="detail" column="detail" jdbcType="LONGVARCHAR"/>
        <result property="timeAdd" column="time_add" jdbcType="TIMESTAMP"/>
        <result property="timeEdit" column="time_edit" jdbcType="TIMESTAMP"/>
        <result property="userAdd" column="user_add" jdbcType="VARCHAR"/>
        <result property="userEdit" column="user_edit" jdbcType="VARCHAR"/>

        <result property="jumpType" column="jump_type" jdbcType="INTEGER"/>
        <result property="jumpUrl" column="jump_url" jdbcType="VARCHAR"/>
        <result property="imgId" column="img_id" jdbcType="INTEGER"/>
    </resultMap>
    <sql id="baseColumn">
        id,
        type,
        title,
        welfare,
        user_name,
        detail,
        jump_type,
        jump_url,
        img_id,
        time_add,
        time_edit,
        user_add,
        user_edit
    </sql>
    <sql id="base_where" >
        <trim prefix="where" prefixOverrides="and|or">
            <if test="id != null ">
     	        and id = #{id,jdbcType=INTEGER}
            </if>
            <if test="type != null ">
                and type = #{type,jdbcType=INTEGER}
            </if>
            <if test="title != null ">
     	        and title = #{title,jdbcType=VARCHAR}
            </if>
            <if test="welfare != null ">
     	        and welfare = #{welfare,jdbcType=INTEGER}
            </if>
            <if test="userName != null ">
     	        and user_name = #{userName,jdbcType=VARCHAR}
            </if>
            <if test="detail != null ">
     	        and detail = #{detail,jdbcType=LONGVARCHAR}
            </if>
            <if test="timeAdd != null ">
     	        and time_add = #{timeAdd,jdbcType=TIMESTAMP}
            </if>
            <if test="timeEdit != null ">
     	        and time_edit = #{timeEdit,jdbcType=TIMESTAMP}
            </if>
            <if test="userAdd != null ">
     	        and user_add = #{userAdd,jdbcType=VARCHAR}
            </if>
            <if test="userEdit != null ">
     	        and user_edit = #{userEdit,jdbcType=VARCHAR}
            </if>
        </trim>
    </sql>
	<select id="find" parameterType="com.shunwang.marketing.query.BaseQuery" resultMap="BaseResultMap" >
		select t.*
		from marketing_content t
		<include refid="base_where" />
	</select>
    <select id="findOne" parameterType="com.shunwang.marketing.query.BaseQuery" resultMap="BaseResultMap" >
        select t.*
        from marketing_content t
        <include refid="base_where" />
        limit 1
    </select>
    <select id="findCnt" parameterType="com.shunwang.marketing.query.BaseQuery" resultType="java.lang.Integer" >
        select
            count(*)
        from marketing_content t
		<include refid="base_where" />
    </select>
    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select t.*
        from marketing_content t
        where t.id = #{id,jdbcType=INTEGER}
    </select>

</mapper>
