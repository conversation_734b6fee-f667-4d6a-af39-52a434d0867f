package com.shunwang.marketing.service.marketing;

import com.shunwang.marketing.enums.marketing.PromotionPopupWindowEnum;
import com.shunwang.marketing.pojo.marketing.vo.PromotionPopupWindowVo;
import com.shunwang.marketing.service.marketing.promotionPopupWindow.WindowChain;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@SpringBootTest
@RunWith(SpringRunner.class)
public class PromotionPopupWindowTest {
    @Test
    public void TestContext(){
        PromotionPopupWindowVo promotionPopupWindow = new PromotionPopupWindowVo();
        promotionPopupWindow.setType(PromotionPopupWindowEnum.TypeEnum.PRIZE.getValue());
        boolean b = new WindowChain().checkStrategy(promotionPopupWindow);
        System.out.println(b);
    }
}
