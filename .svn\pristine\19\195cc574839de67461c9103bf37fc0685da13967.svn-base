package com.shunwang.marketing.service.marketing;

import com.shunwang.marketing.pojo.marketing.LotteriesCnt;
import com.shunwang.marketing.service.BaseService;
public interface LotteriesCntService extends BaseService<LotteriesCnt> {

    /**
     * 根据支付订单添加抽奖次数
     * @param memberId
     * @param orderNo
     */
    void addPayOrderCnt(Integer memberId, Integer pondId,String orderNo);

    LotteriesCnt getByMemberIdAndPondId(Integer memberId, Integer pondId);

    /**
     * 获取当日抽奖次数
     * @param memberId
     * @return
     */
    Integer getLuckyDrawCnt(Integer memberId,Integer pondId);

    /**
     * 增加抽奖次数，id必填 调用前需保证已经更新当天的抽奖数
     * @param lotteriesCnt
     */
    void incCnt(LotteriesCnt lotteriesCnt);

    void subtractCnt(Integer memberId,Integer pondId);

    /**
     * 添加用户点击广告抽奖次数
     * @param memberId
     * @param prizeAdLimit
     */
    LotteriesCnt addPrizeAdCnt(Integer memberId,Integer pondId, Integer prizeAdLimit);

    /**
     * 添加用户任务抽奖次数
     * @param memberId
     * @param cnt
     */
    LotteriesCnt addTaskCnt(Integer memberId, Integer pondId,Integer cnt);

    /**
     * 初始化
     * @param memberId
     * @return
     */
    LotteriesCnt init(Integer memberId, Integer pondId,String today);

    void addOtherCnt(Integer memberId,Integer pondId,Integer cnt);
}
