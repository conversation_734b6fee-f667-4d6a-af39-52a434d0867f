package com.shunwang.marketing.query.marketing;

import com.shunwang.marketing.query.BaseQuery;
import lombok.Data;
import java.util.Date;
import java.util.List;

@Data
public class AiGameQuery extends BaseQuery {
    private Integer id;
    private String name;
    private String rule;
    private Integer type;
    private Integer rewardType;
    private String reward;
    private String shareTitle;
    private Integer shareImg;
    private String sharePath;
    private Date addTime;
    private Date editTime;
    private String addUser;
    private String editUser;
    private String remark;

    private List<Integer> idList;
} 