package com.shunwang.marketing.manager.request.sso;


import com.shunwang.marketing.manager.IResponse;
import com.shunwang.marketing.manager.request.BaseRequest;
import com.shunwang.marketing.util.SignTool;
import com.shunwang.util.date.DateUtil;
import com.shunwang.util.encrypt.Md5Encrypt;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.generator.api.dom.java.Interface;

import java.io.UnsupportedEncodingException;
import java.util.Map;

@Getter
@Setter
@Slf4j
public abstract class BaseSsoRequest<T extends IResponse> extends BaseRequest<T> {
    private String siteId;
    private String time;
    private String sign;
    private String md5Key;

    @Override
    public String buildUrl() {
        return getUrl();
    }

    public abstract String buildSource(Map<String, String> paramMap, String md5Key);

    public Map<String, String> buildCommonParams() throws UnsupportedEncodingException {
        Map<String, String> params = buildParams();
        params.put("siteId", getSiteId());
        params.put("timestamp", DateUtil.getCurrentDateStamp());
        params.put("signVersion", "1.0");

        String signSource = SignTool.buildSignStringSortedMap(params, "sign", md5Key);


//        String signSource = buildSource(params, md5Key);
//        String sign = Md5Encrypt.encrypt(URLEncoder.encode(signSource, "UTF-8").toUpperCase(), "UTF-8").toUpperCase();
        String sign = Md5Encrypt.encrypt(signSource).toUpperCase();
        params.put("sign", sign);
        log.info("签名原串[{}]", signSource);
        return params;
    }


    /**
     * 设置配置
     */
    public void doInterfaceSetting(Interface setting) {

    }

}
