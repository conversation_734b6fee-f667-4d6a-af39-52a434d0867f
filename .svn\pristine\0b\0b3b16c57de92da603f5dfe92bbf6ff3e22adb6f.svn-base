package com.shunwang.marketing.manager.request.swpay;

import com.shunwang.marketing.constant.InterfacesConstant;
import com.shunwang.marketing.manager.HttpMethod;
import com.shunwang.marketing.manager.response.swpay.UnifiedOrderResponse;
import com.shunwang.marketing.pojo.config.Interfaces;
import com.shunwang.util.date.DateUtil;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Getter
@Setter
public class MobilePayRequest extends BaseSwpayRequest {

    private String payoutMemberName;
    private String receiptMemberName;
    private Integer disburseType=1;
    private Integer frostType=0;
    private String outTime;
    private String produceName;
    private BigDecimal producePrice;
    private Integer produceAmount;
    private BigDecimal money;
    private String orderNo;
    private String callbackUrl;

    @Override
    public Map<String, String> buildParams() {
        Map<String, String> map = new HashMap<>();
        map.put("payoutName", payoutMemberName);
        map.put("receiptName", receiptMemberName);
        map.put("disburseType", "1");
        map.put("frostType", "0");
        map.put("outTime", DateUtil.getDateStamp(DateUtil.addDate(Calendar.MINUTE, new Date(), 30)));
        map.put("produceName", produceName);
        map.put("producePrice", producePrice.toString());
        map.put("produceAmount", "1");
        map.put("money", money.toString());
        map.put("orderNo", orderNo);
        map.put("callbackUrl", getCallbackUrl());
        return map;
    }

    @Override
    public Class<UnifiedOrderResponse> getResponseClass() {
        return UnifiedOrderResponse.class;
    }

    @Override
    public Integer getInterfaceKey() {
        return InterfacesConstant.INTERFACE_KEY_TENCENT_MOBILE_PAY;
    }

    @Override
    public HttpMethod getHttpMethod() {
        return HttpMethod.POST;
    }

    @Override
    public void doInterfaceSetting(Interfaces setting) {
        setUrl(setting.getInterfaceUrl1());
        setPartner(setting.getInterfacePartnerId());
        setService(setting.getInterfaceName());
        setMd5Key(setting.getInterfaceMd5Key());

        this.callbackUrl = setting.getInterfaceReturnUrl1();
        this.receiptMemberName = setting.getInterfaceEmail();
    }
}
