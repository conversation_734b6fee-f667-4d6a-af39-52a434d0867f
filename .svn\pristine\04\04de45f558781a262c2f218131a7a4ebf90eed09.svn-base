package com.shunwang.marketing.aspect;

import com.shunwang.marketing.aspect.annotation.ThreadLocalParam;
import com.shunwang.marketing.context.ThreadLocalContext;
import com.shunwang.marketing.util.MemberUtil;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.lang.annotation.Annotation;
import java.lang.reflect.Method;

@Component
@Aspect
public class ThreadLocalParamAspect {
    @Around("execution(* *(.., @com.shunwang.marketing.aspect.annotation.ThreadLocalParam (*), ..))")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        // 获取方法参数
        Object[] args = joinPoint.getArgs();
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        Method method = methodSignature.getMethod();
        Annotation[][] parameterAnnotations = method.getParameterAnnotations();

        for (int i = 0; i < parameterAnnotations.length; i++) {
            for (Annotation annotation : parameterAnnotations[i]) {
                if (annotation instanceof ThreadLocalParam) {
                    if (args[i] != null) {
                        ThreadLocalContext.setTempData(((ThreadLocalParam) annotation).value(), args[i]);
                    }
                }
            }
        }

        return joinPoint.proceed(args);
    }
}
