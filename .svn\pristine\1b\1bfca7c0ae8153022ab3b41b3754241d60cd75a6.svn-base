package com.shunwang.marketing.service.marketing.impl;

import com.shunwang.marketing.dao.BaseMapper;
import com.shunwang.marketing.dao.marketing.BannerDetailsMapper;
import com.shunwang.marketing.pojo.marketing.Image;
import com.shunwang.marketing.pojo.marketing.vo.BannerDetailVo;
import com.shunwang.marketing.pojo.marketing.vo.BannerVo;
import com.shunwang.marketing.query.marketing.BannerDetailsQuery;
import com.shunwang.marketing.service.marketing.BannerDetailsService;
import com.shunwang.marketing.service.marketing.ImageService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import com.shunwang.marketing.pojo.marketing.Banner;
import com.shunwang.marketing.service.marketing.BannerService;
import com.shunwang.marketing.service.impl.BaseServiceImpl;
import org.springframework.stereotype.Component;
import com.shunwang.marketing.dao.marketing.BannerMapper;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Component
public class BannerServiceImpl extends BaseServiceImpl<Banner> implements BannerService {
    @Autowired
    private BannerMapper bannerMapper;
    @Autowired
    ImageService imageService;
    @Autowired
    BannerDetailsService bannerDetailsService;
    @Autowired
    BannerDetailsMapper bannerDetailsMapper;
    @Override
    public BaseMapper<Banner> getDao() {
        return bannerMapper;
    }


    @Override
    public BannerVo getBannerVoById(Integer bannerId) {
        Banner banner = bannerMapper.get(bannerId);
        BannerVo bannerVo = new BannerVo();
        BeanUtils.copyProperties(banner, bannerVo);
        List<BannerDetailVo> bannerDetailList = bannerDetailsService.findByBannerId(banner.getId());
        for (BannerDetailVo item : bannerDetailList) {
            bannerDetailsService.populate(item);
        }
        bannerVo.setBannerDetailList(bannerDetailList);
        return bannerVo;
    }

}
