package com.shunwang.marketing.pojo.marketing;

import com.shunwang.marketing.pojo.BaseObject;
import java.util.Date;
public class OutRequest extends BaseObject {
	private static final long serialVersionUID = 1L;
	private Integer id;
	private Integer type;
	private String request;
	private Integer state;
	private Date timeAdd;
	private Date finishTime;
	private String remark;
	private Integer addition;
	public Integer getId() {
		return id;
	}
	public void setId(Integer id) {
		this.id = id;
	}
	public Integer getType() {
		return type;
	}
	public void setType(Integer type) {
		this.type = type;
	}
	public String getRequest() {
		return request;
	}
	public void setRequest(String request) {
		this.request = request;
	}
	public Integer getState() {
		return state;
	}
	public void setState(Integer state) {
		this.state = state;
	}
	public Date getTimeAdd() {
		return timeAdd;
	}
	public void setTimeAdd(Date timeAdd) {
		this.timeAdd = timeAdd;
	}
	public Date getFinishTime() {
		return finishTime;
	}
	public void setFinishTime(Date finishTime) {
		this.finishTime = finishTime;
	}
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	public Integer getAddition() {
		return addition;
	}
	public void setAddition(Integer addition) {
		this.addition = addition;
	}
}
