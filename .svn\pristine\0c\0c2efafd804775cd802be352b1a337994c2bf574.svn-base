package com.shunwang.marketing.send.email;

import com.shunwang.marketing.cacheService.RedisOperation;
import com.shunwang.marketing.pojo.util.SendMsg;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Component;

import javax.mail.internet.MimeMessage;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;


@Component
@Data
public class EmailSender implements Runnable{
	private final static Logger log = LoggerFactory.getLogger(EmailSender.class);
	private BlockingQueue<SendMsg> queue = new LinkedBlockingQueue<>(20);
	@Value("${mail.to.username}")
	private String toUser;
	@Autowired
	private RedisOperation redisOperation;


	public void addToSendQueue(SendMsg sendMsg){
		queue.offer(sendMsg);//放入队列中
	}
	/**
	 * 从队列中取出sendMsg，进行发送
	 * @param sendMsg
	 */
	public void sendEmail(SendMsg sendMsg){
		if(sendMsg == null) {
			log.error("sendMsg is null");
			return;
		}

		try {
			JavaMailBaseSender javaMailSender =  EmailSenderGenerator.buildJavaMailSender();
			MimeMessage mimeMsg = javaMailSender.createMimeMessage();

			MimeMessageHelper helper = new MimeMessageHelper(mimeMsg, true, "UTF-8");
			helper.setTo(sendMsg.getNumber());
			helper.setSubject(sendMsg.getTitle());
			helper.setFrom(javaMailSender.getFrom(),"顺网会员中心");
			helper.setText(sendMsg.getContent(), true);
			javaMailSender.send(mimeMsg);
		} catch (Exception e) {
			log.error("sendNumber:["+sendMsg.getNumber()+"]," +
					"sendTitle:["+sendMsg.getTitle()+"]," +
					"sendText:["+sendMsg.getContent()+"],邮件发送异常",e);
		}
	}

	public void buildStockWarnEmail(Integer cnt, String msg){

		if (cnt < 2 || cnt == 50 || cnt == 10){
			SendMsg sendMsg = new SendMsg();
			sendMsg.setTitle("库存不足");
			log.info("权益库存不足预警:" + msg);
			String[] users = toUser.split("\\|");
			for (int i = 0; i < users.length; i++){
				String key = "sendEmail_" + users[i] + "_" + msg;
				if (redisOperation.setIfAbsent(key, 0, 24, TimeUnit.HOURS)){
					sendMsg.setContent(msg);
					sendMsg.setNumber(users[i]);
					sendEmail(sendMsg);
					log.info("权益库存不足预警邮件已发送，接收人:{}" + users[i]);
				}
			}

		}
	}

	public void buildTencentWarnEmail(Long cnt){
		SendMsg sendMsg = new SendMsg();
		sendMsg.setTitle("腾讯活动接口调用失败警告");
		log.info("腾讯活动业务相关接口在规定时间内已失败:" + cnt + "次");
		String[] users = toUser.split("\\|");
		for (int i = 0; i < users.length; i++){
			String key = "sendEmail_" + users[i] + "_tencentWarnEmail";
			if (redisOperation.setIfAbsent(key, 0, 24, TimeUnit.HOURS)){
				sendMsg.setContent("腾讯活动业务相关接口在规定时间内已失败:" + cnt + "次");
				sendMsg.setNumber(users[i]);
				sendEmail(sendMsg);
				log.info("发送腾讯活动业务相关接口在规定时间内失败预警邮件已发送，接收人:{}", users[i]);
			}
		}
	}


	@Override
	public void run() {
		while(true){
			try {
				SendMsg sendMsg = queue.take();//从队列中取出
				sendEmail(sendMsg);
			} catch (Exception e) {
				log.error("邮件发送异常",e);
			}
		}
	}

}
