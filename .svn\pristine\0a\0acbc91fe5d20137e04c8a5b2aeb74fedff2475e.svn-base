package com.shunwang.marketing.service.marketing.impl;

import com.shunwang.marketing.context.ThreadLocalContext;
import com.shunwang.marketing.dao.BaseMapper;
import com.shunwang.marketing.dao.marketing.AiGameMapper;
import com.shunwang.marketing.enums.marketing.*;
import com.shunwang.marketing.exception.CommonException;
import com.shunwang.marketing.pojo.marketing.*;
import com.shunwang.marketing.pojo.marketing.vo.AiGameInfoVo;
import com.shunwang.marketing.pojo.marketing.vo.RewardVo;
import com.shunwang.marketing.query.marketing.AiGameItemQuery;
import com.shunwang.marketing.service.impl.BaseServiceImpl;
import com.shunwang.marketing.service.marketing.*;
import com.shunwang.util.lang.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Component
@Slf4j
public class AiGameServiceImpl extends BaseServiceImpl<AiGame> implements AiGameService {
    @Autowired
    private AiGameMapper aiGameMapper;
    @Autowired
    AiGameItemService aiGameItemService;
    @Autowired
    ImageService imageService;
    @Autowired
    AiSessionService aiSessionService;
    @Autowired
    AiRecordService aiRecordService;
    @Autowired
    LotteriesCntService lotteriesCntService;
    @Autowired
    PondService pondService;
    @Autowired
    ScoreService scoreService;
    @Autowired
    GiftBagService giftBagService;
    @Autowired
    WelfareService welfareService;

    @Override
    public BaseMapper getDao() {
        return aiGameMapper;
    }

    @Override
    public AiGameInfoVo getGameAndSessionInfo(Integer gameId) {
        AiGameInfoVo result = new AiGameInfoVo();
        AiGameItem period = aiGameItemService.getPeriod();
        if (period == null) {
            return result;
        }
        AiGame aiGame = get(gameId);
        if (AiGameEnum.StateEnum.OFFLINE.getValue().equals(aiGame.getState())) {
            return result;
        }
        AiGameItemQuery aiGameItemQuery = new AiGameItemQuery();
        aiGameItemQuery.setGameItemConfig(aiGame.getGameItemConfig());
        AiGameItem aiGameItem = aiGameItemService.findOnline(aiGameItemQuery);
        if (aiGameItem == null) {
            log.info("{}不存在游戏项目", gameId);
            return result;
        }
        imageService.populate(aiGame.getShareImg(), result::setShareImg);
        imageService.populate(aiGameItem.getRewardImg(), result::setRewardImgPath);
        result.setGameId(aiGame.getId());
        result.setGameItemId(aiGameItem.getId());
        BeanUtils.copyProperties(aiGame, result);
        result.setKeyword(aiGameItem.getKeyword());
        result.setOpen(true);

        //会话信息
        AiSession session = aiSessionService.getSessionInPeriod(gameId, aiGameItem.getId(), period.getStartTime(), period.getEndTime());
        if (session == null) {
            return result;
        }
        String remark = session.getRemark();
        result.setFinish(AiSessionEnum.AiGameRemarkEnum.FINISH.getValue().equals(remark) || AiSessionEnum.AiGameRemarkEnum.REWARD.getValue().equals(remark));
        List<AiRecord> record = aiRecordService.findBySession(session.getId());
        result.setRewardReceived(AiSessionEnum.AiGameRemarkEnum.REWARD.getValue().equals(remark));
        result.setTimes((int) record.stream().filter((item) -> AiRecordEnum.TypeEnum.ANSWER.getValue().equals(item.getType())).count());
        return result;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public RewardVo receiveReward(String sessionId) {
        RewardVo result = new RewardVo();
        AiSession session = aiSessionService.getBySessionId(sessionId);
        if (AiSessionEnum.AiGameRemarkEnum.REWARD.getValue().equals(session.getRemark())) {
            throw new CommonException("已经领取过奖励");
        } else if (StringUtil.isBlank(session.getRemark())) {
            throw new CommonException("尚未完成任务");
        }
        AiGameItem aiGameItem = aiGameItemService.get(session.getExt2());

        if (AiGameItemEnum.RewardTypeEnum.LOTTERY_TIMES.getValue().equals(aiGameItem.getRewardType())) {
            Pond pond = new Pond();
            pond.setType(PondEnum.TypeEnum.FREE.getValue());
            pond.setChannel(ThreadLocalContext.getChannel());
            lotteriesCntService.addOtherCnt(session.getMemberId(), pondService.getRandomPondFallback(pond).getId(), Integer.parseInt(aiGameItem.getReward()));
            result.setReward(Integer.parseInt(aiGameItem.getReward()));
        } else if (AiGameItemEnum.RewardTypeEnum.RIGHTS.getValue().equals(aiGameItem.getRewardType())) {
            Integer prizeRecordId = welfareService.sendWelfare(welfareService.get(Integer.parseInt(aiGameItem.getReward())), "猜角色", ScoreRecordEnum.SourceTypeEnum.GAME, true);
            result.setReward(prizeRecordId);
        } else if (AiGameItemEnum.RewardTypeEnum.SCORE.getValue().equals(aiGameItem.getRewardType())) {
            scoreService.updateScoreAndTakeRecord(Integer.parseInt(aiGameItem.getReward()), ScoreRecordEnum.TypeEnum.ADD, ScoreRecordEnum.SourceTypeEnum.GAME.getValue(), "猜角色");
            result.setReward(Integer.parseInt(aiGameItem.getReward()));
        } else {
            giftBagService.receiveGiftBag(Integer.parseInt(aiGameItem.getReward()));
            result.setReward(Integer.parseInt(aiGameItem.getReward()));
        }

        result.setType(aiGameItem.getRewardType());
        session.setRemark(AiSessionEnum.AiGameRemarkEnum.REWARD.getValue());
        aiSessionService.update(session);
        return result;
    }


}
