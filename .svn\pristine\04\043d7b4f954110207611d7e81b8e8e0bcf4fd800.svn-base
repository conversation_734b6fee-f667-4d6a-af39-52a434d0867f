package com.shunwang.marketing.service.marketing.impl;

import com.shunwang.marketing.dao.BaseMapper;
import com.shunwang.marketing.dao.marketing.SignConfigMapper;
import com.shunwang.marketing.pojo.marketing.SignConfig;
import com.shunwang.marketing.pojo.marketing.SignConfigResult;
import com.shunwang.marketing.service.impl.BaseServiceImpl;
import com.shunwang.marketing.service.marketing.SignConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class SignConfigServiceImpl extends BaseServiceImpl<SignConfig> implements SignConfigService {
    @Autowired
    private SignConfigMapper signConfigMapper;


    @Override
    public BaseMapper getDao() {
        return signConfigMapper;
    }


    public List<SignConfigResult> getBySignInId(Integer signInId) {
        return signConfigMapper.getBySignInId(signInId);
    }

}
