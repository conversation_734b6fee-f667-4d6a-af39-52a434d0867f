package com.shunwang.marketing.pojo.marketing;

import com.shunwang.marketing.pojo.BaseObject;
import java.util.Date;
public class ModuleReceiveRecord extends BaseObject {
	private static final long serialVersionUID = 1L;
	private Integer id;
	private Integer moduleDetailId;
	private Integer memberId;
	private Date timeAdd;
	public Integer getId() {
		return id;
	}
	public void setId(Integer id) {
		this.id = id;
	}
	public Integer getModuleDetailId() {
		return moduleDetailId;
	}
	public void setModuleDetailId(Integer moduleDetailId) {
		this.moduleDetailId = moduleDetailId;
	}
	public Integer getMemberId() {
		return memberId;
	}
	public void setMemberId(Integer memberId) {
		this.memberId = memberId;
	}
	public Date getTimeAdd() {
		return timeAdd;
	}
	public void setTimeAdd(Date timeAdd) {
		this.timeAdd = timeAdd;
	}
}
