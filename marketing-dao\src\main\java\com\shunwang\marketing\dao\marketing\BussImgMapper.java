package com.shunwang.marketing.dao.marketing;

import com.shunwang.marketing.dao.BaseMapper;
import com.shunwang.marketing.pojo.marketing.BussImg;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BussImgMapper extends BaseMapper<BussImg> {

	/**
	 * 根据业务类型和业务ID查询图片列表
	 * @param bussType 业务类型
	 * @param bussId 业务ID
	 * @return 图片列表
	 */
	List<BussImg> findByBussTypeAndBussId(@Param("bussType") Integer bussType, @Param("bussId") Integer bussId);

	/**
	 * 根据业务类型查询图片列表
	 * @param bussType 业务类型
	 * @return 图片列表
	 */
	List<BussImg> findByBussType(@Param("bussType") Integer bussType);

}
