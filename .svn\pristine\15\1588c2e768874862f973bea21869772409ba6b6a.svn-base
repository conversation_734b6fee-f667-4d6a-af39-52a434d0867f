package com.shunwang.marketing.service.marketing.impl;

import com.shunwang.marketing.context.ThreadLocalContext;
import com.shunwang.marketing.dao.BaseMapper;
import com.shunwang.marketing.dao.marketing.MemberTagMapper;
import com.shunwang.marketing.manager.bigData.UserTagService;
import com.shunwang.marketing.manager.response.bigData.UserTagResponse;
import com.shunwang.marketing.pojo.marketing.MemberTag;
import com.shunwang.marketing.pojo.marketing.Tag;
import com.shunwang.marketing.query.marketing.MemberTagQuery;
import com.shunwang.marketing.service.bigData.BigDataService;
import com.shunwang.marketing.service.impl.BaseServiceImpl;
import com.shunwang.marketing.service.marketing.MemberTagService;
import com.shunwang.marketing.service.marketing.TagService;
import com.shunwang.util.lang.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.Date;
import java.util.List;
import java.util.StringJoiner;

@Component
public class MemberTagServiceImpl extends BaseServiceImpl<MemberTag> implements MemberTagService {
    @Autowired
    private MemberTagMapper memberTagMapper;
    @Autowired
    private UserTagService userTagService;
    @Autowired
    private BigDataService bigDataService;
    @Value("${bigDataReport.bussId.mini}")
    private String businessIdMini;
    @Autowired
    private TagService tagService;
    @Override
    public BaseMapper<MemberTag> getDao() {
        return memberTagMapper;
    }


    @Override
    public void processUserTag(Integer memberId) {
        List<UserTagResponse> userTagList = userTagService.execute(memberId);
        if (userTagList == null || userTagList.size() < 1){
            return;
        }

        for (UserTagResponse response : userTagList){
            MemberTag memberTag = findByPartnerName(memberId, response.getPartnerName());
            if (memberTag == null){
                memberTag = new MemberTag();
                memberTag.setMemberId(memberId);
                memberTag.setActiveTag(response.getActiveTag());
                memberTag.setInterestTag(response.getInterestTag());
                memberTag.setRfmTag(response.getRfmTag());
                memberTag.setCategoryName(response.getCategoryName());
                memberTag.setPartnerName(response.getPartnerName());
                memberTag.setTimeAdd(new Date());
                memberTagMapper.save(memberTag);
            } else {
                memberTag.setActiveTag(response.getActiveTag());
                memberTag.setInterestTag(response.getInterestTag());
                memberTag.setRfmTag(response.getRfmTag());
                memberTag.setTimeEdit(new Date());
                memberTagMapper.update(memberTag);
            }
        }

    }

    /**
     * 用户的标签是否匹配目标标签
     *
     * @param tags     目标标签
     * @param memberId
     * @return
     */
    @Override
    public boolean isMatching(String tags, Integer memberId) {
        if (StringUtil.isBlank(tags)){
            return false;
        }
        String[] tagArr = tags.split(",");
        List<MemberTag> memberTagList = findByMemberId(memberId);
        for (int i = 0; i < tagArr.length; i++){
            Tag tag = tagService.get(Integer.parseInt(tagArr[i]));
            if (tag == null) {
                continue;
            }
            if (matching(tag, memberTagList)){
                bigDataService.bigDataReport(buildReportBody(memberId, tag), businessIdMini);
                return true;
            }
        }
        return false;
    }

    private boolean matching(Tag tag, List<MemberTag> memberTagList){
        for (MemberTag memberTag : memberTagList) {
            //业务线标签
            if (StringUtil.isNotBlank(tag.getPartner()) && !tag.getPartner().contains(memberTag.getCategoryName())){
                continue;
            }
            //活跃标签
            if (StringUtil.isNotBlank(tag.getActiveTag()) && !tag.getActiveTag().contains(memberTag.getActiveTag()) ){
                continue;
            }
            //rfm标签
            if (StringUtil.isNotBlank(tag.getRfmTag()) && !tag.getRfmTag().contains(memberTag.getRfmTag())){
                continue;
            }
            //比对兴趣标签
            String userTag = memberTag.getInterestTag();
            String interestTag = tag.getInterestTag();
            if (StringUtil.isBlank(userTag)){
                continue;
            }
            if (StringUtil.isBlank(interestTag)){
                return true;
            }
            String[] userTagArr = userTag.split(",");
            for (int i = 0; i < userTagArr.length; i++){
                if (interestTag.contains(userTagArr[i])){
                    return true;
                }
            }
        }

        return false;
    }

    public MemberTag findByPartnerName(Integer memberId, String partnerName){
        MemberTagQuery query = new MemberTagQuery();
        query.setMemberId(memberId);
        query.setPartnerName(partnerName);
        List<MemberTag> memberTagList = memberTagMapper.find(query);
        if (memberTagList != null && memberTagList.size() > 0){
            return memberTagList.get(0);
        }
        return null;
    }

    public List<MemberTag> findByMemberId(Integer memberId){
        Assert.notNull(memberId);
        MemberTagQuery query = new MemberTagQuery();
        query.setMemberId(memberId);
        return memberTagMapper.find(query);
    }

    private String buildReportBody(Integer memberId, Tag tag) {
        StringJoiner joiner = new StringJoiner("\t");
        joiner.add("jz").add("jz_matching").add("").add("").add(memberId.toString()).add(tag.getId().toString()).add(ThreadLocalContext.getChannel());
        return joiner.toString();
    }
}
