<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shunwang.marketing.dao.marketing.ResourcePrivilegeMapper">
    <resultMap id="BaseResultMap" type="resourcePrivilege">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="resourceName" column="resource_name" jdbcType="VARCHAR"/>
        <result property="resourceId" column="resource_id" jdbcType="INTEGER"/>
        <result property="gradingState" column="grading_state" jdbcType="TINYINT"/>
        <result property="onlineState" column="online_state" jdbcType="TINYINT"/>
        <result property="hoverImg" column="hover_img" jdbcType="VARCHAR"/>
        <result property="userAdd" column="user_add" jdbcType="VARCHAR"/>
        <result property="timeAdd" column="time_add" jdbcType="TIMESTAMP"/>
        <result property="userEdit" column="user_edit" jdbcType="VARCHAR"/>
        <result property="timeEdit" column="time_edit" jdbcType="TIMESTAMP"/>
        <result property="openType" column="open_type" jdbcType="INTEGER"/>
        <result property="openUrl" column="open_url" jdbcType="VARCHAR"/>
        <result property="type" column="type" jdbcType="INTEGER"/>
        <result property="description" column="description" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="baseColumn">
        id,
        resource_name,
        resource_id,
        grading_state,
        online_state,
        hover_img,
        user_add,
        time_add,
        user_edit,
        time_edit,
        open_type,
          open_url,
          type,
          description
    </sql>
    <sql id="base_where" >
        <trim prefix="where" prefixOverrides="and|or">
            <if test="id != null ">
     	        and id = #{id,jdbcType=INTEGER}
            </if>
            <if test="resourceName != null ">
     	        and resource_name = #{resourceName,jdbcType=VARCHAR}
            </if>
            <if test="resourceId != null ">
     	        and resource_id = #{resourceId,jdbcType=INTEGER}
            </if>
            <if test="gradingState != null ">
     	        and grading_state = #{gradingState,jdbcType=TINYINT}
            </if>
            <if test="onlineState != null ">
     	        and online_state = #{onlineState,jdbcType=TINYINT}
            </if>
            <if test="hoverImg != null ">
                and hover_img = #{hoverImg,jdbcType=VARCHAR}
            </if>
            <if test="userAdd != null ">
     	        and user_add = #{userAdd,jdbcType=VARCHAR}
            </if>
            <if test="timeAdd != null ">
     	        and time_add = #{timeAdd,jdbcType=TIMESTAMP}
            </if>
            <if test="userEdit != null ">
     	        and user_edit = #{userEdit,jdbcType=VARCHAR}
            </if>
            <if test="timeEdit != null ">
     	        and time_edit = #{timeEdit,jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>
	<select id="find" parameterType="com.shunwang.marketing.query.BaseQuery" resultMap="BaseResultMap" >
		select 
        <include refid="baseColumn" />
		from marketing_resource_privilege t 
		<include refid="base_where" />
	</select>

    <select id="findAll" parameterType="com.shunwang.marketing.query.BaseQuery" resultMap="BaseResultMap" >
        select
        <include refid="baseColumn" />
        from marketing_resource_privilege t
    </select>
    <select id="findCnt" parameterType="com.shunwang.marketing.query.BaseQuery" resultType="java.lang.Integer" >
        select
            count(*)
        from marketing_resource_privilege t
		<include refid="base_where" />
    </select>
    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.Integer">
		select
        <include refid="baseColumn" />
		from marketing_resource_privilege
		where id = #{id,jdbcType=INTEGER}
    </select>
	<insert id="save" parameterType="resourcePrivilege" keyProperty="id" useGeneratedKeys="true">
		insert into marketing_resource_privilege 
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null">
                id,
            </if>
            <if test="resourceName != null">
                resource_name,
            </if>
            <if test="resourceId != null">
                resource_id,
            </if>
            <if test="gradingState != null">
                grading_state,
            </if>
            <if test="onlineState != null">
                online_state,
            </if>
            <if test="hoverImg != null ">
                hover_img,
            </if>
            <if test="userAdd != null">
                user_add,
            </if>
            <if test="timeAdd != null">
                time_add,
            </if>
            <if test="userEdit != null">
                user_edit,
            </if>
            <if test="timeEdit != null">
                time_edit,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="id != null">
                #{id,jdbcType=INTEGER},
            </if>
            <if test="resourceName != null">
                #{resourceName,jdbcType=VARCHAR},
            </if>
            <if test="resourceId != null">
                #{resourceId,jdbcType=INTEGER},
            </if>
            <if test="gradingState != null">
                #{gradingState,jdbcType=TINYINT},
            </if>
            <if test="onlineState != null">
                #{onlineState,jdbcType=TINYINT},
            </if>
            <if test="hoverImg != null ">
                #{hoverImg,jdbcType=VARCHAR},
            </if>
            <if test="userAdd != null">
                #{userAdd,jdbcType=VARCHAR},
            </if>
            <if test="timeAdd != null">
                #{timeAdd,jdbcType=TIMESTAMP},
            </if>
            <if test="userEdit != null">
                #{userEdit,jdbcType=VARCHAR},
            </if>
            <if test="timeEdit != null">
                #{timeEdit,jdbcType=TIMESTAMP},
            </if>
        </trim>
	</insert>
	<update id="update" parameterType="resourcePrivilege" >
		update marketing_resource_privilege
        <set>
            <if test="id != null">
     	        id = #{id,jdbcType=INTEGER},
            </if>
            <if test="resourceName != null">
     	        resource_name = #{resourceName,jdbcType=VARCHAR},
            </if>
            <if test="resourceId != null">
     	        resource_id = #{resourceId,jdbcType=INTEGER},
            </if>
            <if test="gradingState != null">
     	        grading_state = #{gradingState,jdbcType=TINYINT},
            </if>
            <if test="onlineState != null">
     	        online_state = #{onlineState,jdbcType=TINYINT},
            </if>
            <if test="hoverImg != null ">
                hover_img = #{hoverImg,jdbcType=VARCHAR},
            </if>
            <if test="userAdd != null">
     	        user_add = #{userAdd,jdbcType=VARCHAR},
            </if>
            <if test="timeAdd != null">
     	        time_add = #{timeAdd,jdbcType=TIMESTAMP},
            </if>
            <if test="userEdit != null">
     	        user_edit = #{userEdit,jdbcType=VARCHAR},
            </if>
            <if test="timeEdit != null">
     	        time_edit = #{timeEdit,jdbcType=TIMESTAMP},
            </if>
        </set>
		<trim prefix="where" prefixOverrides="and|or"> 
            id = #{id,jdbcType=INTEGER}
        </trim>
	</update>

</mapper>
