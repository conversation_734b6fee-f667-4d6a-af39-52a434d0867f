package com.shunwang.marketing.manager.request.paopao;

import com.shunwang.marketing.constant.InterfacesConstant;
import com.shunwang.marketing.manager.HttpMethod;
import com.shunwang.marketing.manager.request.BaseRequest;
import com.shunwang.marketing.manager.response.paopao.SendGiftResponse;
import com.shunwang.marketing.pojo.config.Interfaces;
import com.shunwang.util.encrypt.Md5Encrypt;
import com.shunwang.util.encrypt.SignTool;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Getter
@Setter
public class SendGiftRequest extends BaseRequest<SendGiftResponse> {
    /**
     *  用户id
     */
    private String kedouId;
    /**
     * 商品信息，由后台配置
     */
    private String commodityInfo;
    /**
     * 订单号
     */
    private String orderNo;

    private String app;
    private String md5Key;
    private String platform = "1";

    @Override
    public Map<String, String> buildParams() {
        Map<String, String> params = new HashMap<>();
        params.put("kedouId", kedouId);
        params.put("commodityInfo", commodityInfo);
        params.put("orderNo", orderNo);
        params.put("app", app);
        params.put("platform", platform);
        params.put("time", getUnixTimestamp());
        String signSource = SignTool.buildSignStringSorted(params, "sign", md5Key);
        String sign = Md5Encrypt.encrypt(signSource).toLowerCase();
        params.put("sign", sign);
        return params;
    }

    @Override
    public Class<SendGiftResponse> getResponseClass() {
        return SendGiftResponse.class;
    }

    @Override
    public Integer getInterfaceKey() {
        return InterfacesConstant.PAOPAO_SEND_GIFT;
    }

    @Override
    public void doInterfaceSetting(Interfaces setting) {
        setUrl(setting.getInterfaceUrl1());
        setApp(setting.getInterfacePartnerId());
        setMd5Key(setting.getInterfaceMd5Key());
    }
    public String getUnixTimestamp(){
        return String.valueOf(System.currentTimeMillis() / 1000L);
    }
    @Override
    public HttpMethod getHttpMethod() {
        return HttpMethod.POST;
    }
}
