var replaceUrl = function () {
  if (isH5) return;
  var _originalUrl = location.origin + location.pathname;

  history.replaceState("", "", _originalUrl);
};

function getUrlParams(key) {
  var url = window.location.search.substr(1);
  if (url == "") {
    return;
  }
  var paramsArr = url.split("&");
  for (var i = 0; i < paramsArr.length; i++) {
    var combina = paramsArr[i].split("=");
    if (combina[0] == key) {
      return combina[1];
    }
  }

  return;
}

var arrayFilter = function (array, value) {
  for (var i = 0; i < array.length; i++) {
    if (array[i].id == value) {
      return array[i];
    }
  }
};

var toast = function (text) {
  var $toast = $("<div class='toast'>" + text + "</div>");

  $toast.appendTo($(document.body));
  $toast.fadeIn();
  setTimeout(function () {
    $toast.fadeOut();

    $toast.remove();
  }, 2000);
};

var transformUrl = function (url) {
  return staticServer + url;
};

var resolveStaticPath = function () {
  $.each($("source[data-path='relative']"), function (index, item) {
    $(item).attr("srcset", transformUrl($(item).data("srcset")));
    $(item).removeAttr("data-path");
    $(item).removeAttr("data-srcset");
  });

  $.each($("img[data-path='relative']"), function (index, item) {
    $(item).attr("src", transformUrl($(item).data("src")));
    $(item).removeAttr("data-path");
    $(item).removeAttr("data-src");
  });
};

var getLaunchUrl = function (gameId, gameName) {
  var _params = ["gid=" + gameId, "gname=" + encodeURIComponent(gameName)];
  return launchPage + "?" + _params.join("&");
};

var openLink = function (url) {
  var $link = $("<a id='alink' class='link-url' target='_blank'></a>");

  $link.attr("href", url);

  $("body").append($link);

  var $linkUrl = document.querySelector("#alink");
  if ($linkUrl) $linkUrl.click();

  setTimeout(function () {
    $link.remove();
  });
};

var dataReport = function (data) {
  var _note = [isH5 ? "H5" : "Web"];

  if (!!data.note) _note.push(data.note);

  var data = {
    projectId: "privilege",
    businessId: "esporthotel",
    data: [
      data.moduleId,
      data.evt,
      data.id || "",
      !!UserInfoObj.userInfo ? UserInfoObj.userInfo.memberId : "",
      encodeURI(_note.join(";")),
      sessionStorage.getItem("channel"),
    ].join("\t"),
  };

  $.ajax({
    type: "get",
    dataType: "JSONP",
    url: bigDataReportUrl,
    data: data,
    jsonp: "callback",
  });
};

var UserInfoObj = {
  sso_container: $(".sso-section"),
  userInfo_container: $(".userinfo-section"),
  userInfo: null,
  isLogin: false,
  show: function () {
    this.sso_container.remove();
    this.userInfo_container.show();
    this.userInfo_container.find(".nickname").html(this.userInfo.nickName);
    this.userInfo_container
      .find(".avatar img")
      .attr("src", this.userInfo.headImg);
  },
  hide: function () {
    this.userInfo_container.hide();
  },
  init: function (res) {
    if (
      this.userInfo &&
      this.userInfo.nickName === res.userInfo.nickName &&
      this.userInfo.headImg === res.userInfo.headImg
    ) {
      return;
    }
    this.userInfo = res.userInfo;
    this.isLogin = res.isLogin;

    sessionStorage.setItem("memberId", this.userInfo.memberId);
    console.log(this.userInfo, "uesrinfo");
    if (!isH5) {
      if (res.isLogin) {
        this.show();
      } else {
        this.sso_container.show();
        this.appendSSOQrCode($('[data-target="sso-login"]'));
      }
    } else {
      this.hide();
    }
  },
  clear: function () {
    this.userInfo = null;
    this.isLogin = false;
    if (!isH5) {
      $(".user-nick").html("");
      $(".user-avatar").attr("src", "");
      this.userInfo_container.hide();
      this.sso_container.show();
    }
  },
  appendSSOQrCode: function (el, id) {
    if (!el || el.length === 0) return;

    var params = {
      callbackUrl: "https://m.kedou.com/h5/welfare/indexPage",
      msg: "",
      site_id: "marketing-welfare",
      loginType: 1008,
      login_rbt: "n",
    };

    if (!!id)
      params.callbackUrl = encodeURIComponent(
        params.callbackUrl + "?" + ["s=" + 1, "t=" + id].join("&")
      );

    var sso_url = "https://sso.kedou.com/login.do";

    var urlParmas = [];

    for (var key in params) {
      urlParmas.push(key + "=" + params[key]);
    }

    $(el).attr("src", sso_url + "?" + urlParmas.join("&"));
  },
};

var bodyScroll = {
  auto: function () {
    $("body").css({
      overflow: "auto",
      height: "auto",
    });
  },
  hide: function () {
    $("body").css({
      overflow: "hidden",
    });
  },
};

var successModal = function (itemdata, result) {
  var $clipboard = null;

  var _tplMessage = $("#tpl_success").html();

  var $successDialog = bootbox.dialog({
    message: _tplMessage,
    title: "领取成功",

    onHide: function () {
      bodyScroll.auto();
      if ($clipboard) $clipboard.destroy();
    },
  });

  $successDialog
    .find(".desc-section-inner")
    .on("mousewheel touchmove", function (evt) {
      console.log("mouse wheel");
      evt.stopPropagation();
      evt.stopImmediatePropagation();
    });

  $successDialog.init(function () {
    bodyScroll.hide();

    $successDialog.find(".content-section>*").hide();

    if (!UserInfoObj.isLogin) {
      if (!isH5) {
        UserInfoObj.appendSSOQrCode(
          $successDialog.find(".sso-login"),
          itemdata.id
        );

        $successDialog.find(".sso-section").css("display", "flex");
      }
      $(".qrcode-section").remove();
    } else {
      var _isFailed = [0, 2].indexOf(result.code) === -1;
      var _clsName =
        result.code === 0 ? "success-modal" : _isFailed ? "failed-modal" : "";

      $successDialog.addClass(_clsName);

      $successDialog.find(".sso-section").remove();

      if (_isFailed) {
        $successDialog.find(".failed-section").css("display", "flex");
        $successDialog
          .find(".failed-message")
          .text(result.msg || "权益太火爆了，已经被抢光了！");
      } else {
        var _rightType = itemdata.type;

        if (_rightType == 2) {
          $clipboard = new ClipboardJS(".btn-apply");

          $clipboard.off("success").on("success", function (res) {
            dataReport({
              moduleId: "btn_copy_cdkey",
              evt: "click",
              id: itemdata.id,
            });
            toast("复制成功");
          });

          $clipboard.off("error").on("error", function (res) {
            toast("复制失败");
          });

          $successDialog.find(".cdkey-section").css("display", "flex");

          if (!!result.data) {
            var $cdkey = $successDialog.find("[data-map='cdkey']");

            $cdkey.html(result.data);

            if (!isH5 && (itemdata["gameId"] || itemdata["url"])) {
              $cdkey.addClass("section-type-2");

              var $btnLaunch = $successDialog.find(".btn-apply");
              var _url = "";

              if (itemdata["gameId"]) {
                _url = getLaunchUrl(itemdata["gameId"], itemdata["gameName"]);
              } else _url = itemdata["url"];

              $btnLaunch
                .text("复制并使用")
                .attr("target", "_blank")
                .attr("href", _url)
                .fadeIn();

              $btnLaunch.off("click").on("click", function (evt) {
                dataReport({
                  moduleId: "btn_launch",
                  evt: "click",
                  id: itemdata.id,
                  note: itemdata["gameId"]
                    ? "game:" + itemdata["gameId"]
                    : "link",
                });
              });
            }
          }
        } else if (_rightType == 1) {
          if (!isH5) {
            $successDialog.find(".qrcode-section").css("display", "flex");
            var qrcode = new QRCode(document.getElementById("qrcode1"), {
              text: itemdata["url"],
              width: 120,
              height: 120,
              colorDark: "#000000",
              colorLight: "#ffffff",
              correctLevel: QRCode.CorrectLevel.H,
            });
          } else {
            $successDialog.find(".h5-link-section").css("display", "flex");

            $successDialog
              .find(".h5-link-section>a")
              .attr("href", itemdata["url"]);
          }
        }
      }
    }

    $.each(Object.keys(itemdata), function (idx, subItem) {
      if (subItem === "type") return;

      var $matchEl = $successDialog.find("[data-map='" + subItem + "']");

      if (subItem == "img") {
        $matchEl.css("background-image", "url(" + itemdata[subItem] + ")");
      } else if (subItem == "details") {
        $matchEl.html(itemdata[subItem]);
      } else {
        $matchEl.text(itemdata[subItem]);
      }
    });

    resolveStaticPath();
  });
};

var _countHandler = null;
var warningModal = function (itemdata) {
  if (_countHandler) clearInterval(_countHandler);
  var _warningModal = bootbox.dialog({
    message: $("#tpl_warning").html(),

    onHide: function () {
      bodyScroll.auto();
      if (_countHandler) clearInterval(_countHandler);
    },
  });

  var goto = function () {
    if (itemdata["gameId"])
      openLink(
        getLaunchUrl(itemdata["gameId"], itemdata["gameName"]),
        "_blank"
      );
    else openLink(itemdata["url"], "_blank");
  };

  _warningModal.init(function () {
    bodyScroll.hide();
    _warningModal.addClass("warning-modal");

    _warningModal
      .find("#btn-cancel")
      .off("click")
      .on("click", function () {
        _warningModal.modal("hide");
      });

    _warningModal
      .find("#btn-apply")
      .off("click")
      .on("click", function () {
        if (_countHandler) clearInterval(_countHandler);
        goto();
        _warningModal.modal("hide");
      });

    var _count = 5;

    _countHandler = setInterval(function () {
      if (_count < 0) {
        clearInterval(_countHandler);
        goto();
        _warningModal.find("#btn-apply").text("点击继续");
        return;
      }
      _warningModal.find("#btn-apply").text("继续（" + _count + "s）");
      _count--;
    }, 1000);
  });
};


var rightsListObj = {
  container: $(".rights-list"),
  userInfo: {},
  isLogin: false,
  welfareGroupList: [],
  currentProperty: 1,
  init: function (option) {
    var $self = this;
    this.currentProperty = option.property;
    var _initList = function (data) {
      UserInfoObj.init(data);

      $self.render();
    };
    this.load(option, _initList);

    $(".tabs .tab").on("click", function () {
      if ($(this).hasClass("active")) return;

      $(".tab.active").removeClass("active");

      $(this).addClass("active");

      _property = parseInt($(this).data("property"));

      $self.currentProperty = _property;
      $self.render();

      dataReport({
        moduleId: "tab",
        evt: "click",
        id: parseInt($(this).data("property")),
      });
    });
  },
  refresh: function () {
    var $self = this;
    this.load(
      {
        property: this.currentProperty,
      },
      function () {
        $self.render();
      }
    );
  },
  load: function (option, callback) {
    var $self = this;
    var loadData = function () {
      $.ajax({
        type: "get",
        url: isDev ? "../data/index.json" : "/h5/welfare/indexData",
        success: function (res) {
          if (res.code === 0) {
            if (
              typeof option.onSuccess !== "undefined" &&
              option.onSuccess(res)
            ) {
              return;
            }

            var data = res.data;
            $self.isLogin = data.isLogin;
            $self.userInfo = data.userInfo;
            $self.welfareGroupList = data.welfareGroupList;

            callback && callback(res.data);

            if (typeof option.afterRender !== "undefined") {
              option.afterRender(res);
            }
          }
        },
        fail: function (res) {
          console.log(res, "fail");
        },
      });
    };

    loadData();
  },
  setButtonText: function (el, state) {
    if (!UserInfoObj.isLogin) {
      el.text("免费领取");
      return;
    }

    if (state === 1) {
      el.text("立即使用").addClass("btn-primary-bordered");
    } else {
      el.text("免费领取");
    }
  },
  render: function () {
    var $self = this;

    if (!this.welfareGroupList || this.welfareGroupList.length === 0) {
      return;
    }

    var $fragment = document.createDocumentFragment();
    var _groupList = [];

    $.each(this.welfareGroupList, function (index, item) {
      if (!item.welfareList || item.welfareList.length === 0) return;

      var $rightGroupList = $($("#tpl_right_list").html());
      var _groupId = "group-" + index;
      _groupList.push({ id: item.groupId, groupName: item.groupName });

      $rightGroupList.find("[data-map='groupName']").text(item.groupName);
      $rightGroupList.attr("id", item.groupId);

      $.each(item.welfareList, function (subIndex, subItem) {
        var $rightItem = $($("#tpl_right_item").html());

        $self.setButtonText(
          $rightItem.find("[data-toggle='btn-apply']"),
          subItem.receiveState
        );
        $.each(Object.keys(subItem), function (idx, key) {
          var $matchItem = $rightItem.find("[data-map='" + key + "']");
          if ($matchItem.length === 0) return;
          $matchItem.html(subItem[key]);

          if ($matchItem.attr("data-toggle") === "popover") {
            if (isH5) {
              $matchItem.attr("data-content", subItem[key]);
            } else {
              $matchItem.attr("title", subItem[key]);
              $matchItem.removeAttr("data-toggle");
            }
          }
        });

        $rightItem.data("itemdata", subItem);

        $rightItem.appendTo($rightGroupList.find(".card-list-body"));
      });

      $rightGroupList.appendTo($fragment);
    });

    $self.container.html("");
    $self.container.append($fragment);
    $self.renderNav(_groupList);

    var $popoverEl = $self.container.find("[data-toggle='popover']");
    if ($popoverEl) {
      $popoverEl.popover({
        placement: "top",
        trigger: "focus tap hover",
      });
    }


    $self.container
      .off("click", ".card-list-item")
      .on("click", ".card-list-item", function (evt) {
        var $item = $(this);

        if ($(evt.target).data("toggle") === "popover") {
          return;
        }

        if ($item.hasClass("on-apply")) return;

        $($item).addClass("on-apply");

        var _itemData = $(this).data("itemdata");

        if (_itemData.type === 3 && UserInfoObj.isLogin) {
          var _rptType = null;

          if (_itemData["gameId"]) {
            _rptType = "btn_launch_game";
            openLink(
              getLaunchUrl(_itemData["gameId"], _itemData["gameName"]),
              "_blank"
            );
          } else {
            _rptType = "btn_open_link";
            openLink(_itemData["url"], "_blank");
          }
          $item.removeClass("on-apply");
          dataReport({
            moduleId: _rptType,
            evt: "click",
            id: _itemData.id,
          });
          return;
        } else {
          dataReport({
            moduleId:
            _itemData.receiveState === 1
                ? "btn_view_privilege"
                : "btn_get_privilege",
            evt: "click",
            id: _itemData.id,
          });
          $self.getRights(
            _itemData,
            function () {
              $item.removeClass("on-apply");
            },
            function () {
              rightsListObj.refresh();
            }
          );
        }
      });
  },
  renderNav: function (data) {
    var $navSection = $(".nav-section ul");

    $navSection.empty();
    $.each(data, function (index, item) {
      var $li = $("<li class='nav-item'><a></a></li>");

      $li
        .find("a")
        .attr("href", "#" + item.id)
        .text(item.groupName);
      $li.data("id", item.id);

      $navSection.append($li);
    });

    $navSection.find(".nav-item").first().addClass("active");

    $navSection
      .find(".nav-item")
      .off("click")
      .on("click", function () {
        $(".nav-item.active").removeClass("active");
        $(this).addClass("active");

        dataReport({
          moduleId: "anchor-nav",
          evt: "click",
          id: parseInt($(this).data("id")),
        });
      });

    $("body").scrollspy("refresh");
  },
  getRights: function (itemdata, callback, onSuccess) {
    var $self = this;

    var loadData = function () {
      $.ajax({
        type: "get",
        url: isDev ? "../data/receive_success.json" : "/h5/welfare/receive",
        data: {
          welfareId: itemdata.id,
        },
        success: function (res) {
          successModal(itemdata, res);

          if (onSuccess) {
            onSuccess(res);
          }
        },
        complete: function () {
          callback && callback();
        },
      });
    };

    loadData();
  },
};

var baseUrl = "";
var isDev = !!sessionStorage.getItem("dev");


var calcAnchorPosition = function () {
  var $anchor = $(".anchor");
  var $cardSection = $(".card-list");

  if ($cardSection.length === 0) return;

  var _offset = $cardSection.offset();

  var _left = _offset.left + $cardSection.width() + 80;

  if (_left + $anchor.width() > $(window).width()) {
    _left = $(window).width() - 20 - $anchor.width();
  }

  $anchor.css("left", _left + "px");
};

var init = function (onSuccess) {
  var s = getUrlParams("s");
  var t = getUrlParams("t");
  var channel = getUrlParams("channel");
  if (channel) sessionStorage.setItem("channel", channel);

  if (typeof s == "undefined" && typeof t == "undefined") {
    replaceUrl();
  }

  rightsListObj.init({
    property: env === 1 ? 1 : 2,
    afterRender: function (res) {
      if (s == 1) {
        var _items = [];

        $.each(res.data.welfareGroupList, function (index, item) {
          if (!item.welfareList || item.welfareList.length === 0) return;

          _items = _items.concat(item.welfareList);
        });

        var matchItem = arrayFilter(_items, t);

        if (!!matchItem) {
          if (matchItem.type === 3) {
            warningModal(matchItem);
            replaceUrl();
            return;
          }

          dataReport({
            moduleId: "btn_get_privilege",
            evt: "auto",
            id: matchItem.id,
          });

                    rightsListObj.getRights(
            matchItem,
            function () {
              replaceUrl();
            },
            function () {
              rightsListObj.refresh();
            }
          );
        } else replaceUrl();
      }

      calcAnchorPosition();
    },
    onSuccess: onSuccess,
  });

  resolveStaticPath();

  dataReport({
    moduleId: "index",
    evt: "pv",
  });

  $(".user-avatar").on("error", function () {
    if ($(this).hasClass("resolved")) return;

    $(this)
      .attr("src", transformUrl("/images/web/icon-avatar-default.png"))
      .addClass("resolved");
  });

  $(window).on("resize", function () {
    if (!isH5) calcAnchorPosition();
  });

  if (isH5) {
    $("body").addClass("mobile");
    $(".anchor").hide();
    $(".anchor-h5 .menu-trigger").on("click", function () {
      $(".menu-section").css("transform", "translateX(0)");
      $(".menu-section-backdrop").css("transform", "scale(1)");
    });
    var _closeMenu = function () {
      $(".menu-section").css("transform", "translateX(100%)");
      $(".menu-section-backdrop").css("transform", "scale(0)");
    };
    $(".anchor-h5 .menu-section .close").on("click", function () {
      _closeMenu();
    });
    $(".menu-section-backdrop").on("click", function () {
      _closeMenu();
    });
    $("body").scrollspy({ target: ".anchor-h5 ul" });
  } else {
    $("body").removeClass("mobile");
    $(".anchor").fadeIn();
    $("body").scrollspy({ target: ".anchor .nav-section" });
  }

  $(document.documentElement).scrollTop();
};
