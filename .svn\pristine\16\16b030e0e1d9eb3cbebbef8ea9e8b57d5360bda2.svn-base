package com.shunwang.marketing.service.marketing.impl;

import com.shunwang.marketing.context.ThreadLocalContext;
import com.shunwang.marketing.context.UserContext;
import com.shunwang.marketing.dao.BaseMapper;
import com.shunwang.marketing.dao.marketing.WelfareMapper;
import com.shunwang.marketing.dto.UserInfo;
import com.shunwang.marketing.enums.marketing.CdkeyRecordEnum;
import com.shunwang.marketing.enums.marketing.RedPacketRecordEnum;
import com.shunwang.marketing.enums.marketing.ScoreRecordEnum;
import com.shunwang.marketing.enums.marketing.WelfareEnum;
import com.shunwang.marketing.exception.CommonException;
import com.shunwang.marketing.exception.NoStockException;
import com.shunwang.marketing.manager.paopao.SendGiftService;
import com.shunwang.marketing.manager.request.gameBar.CheckIsEsHotelRequest;
import com.shunwang.marketing.manager.response.gameBar.CheckIsEsHotelResponse;
import com.shunwang.marketing.pojo.marketing.*;
import com.shunwang.marketing.pojo.marketing.other.SendWelfareParam;
import com.shunwang.marketing.pojo.marketing.vo.WelfareMscVo;
import com.shunwang.marketing.pojo.personal.Member;
import com.shunwang.marketing.query.marketing.CdkeyRecordQuery;
import com.shunwang.marketing.send.email.EmailSender;
import com.shunwang.marketing.service.impl.BaseServiceImpl;
import com.shunwang.marketing.service.marketing.*;
import com.shunwang.marketing.util.MemberUtil;
import com.shunwang.util.date.DateUtil;
import com.shunwang.util.json.GsonUtil;
import com.shunwang.util.lang.StringUtil;
import com.shunwang.util.net.throwing.HttpClientUtils;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Slf4j
@Component
public class WelfareServiceImpl extends BaseServiceImpl<Welfare> implements WelfareService {
    private static final Logger LOGGER = LoggerFactory.getLogger(WelfareServiceImpl.class);

    @Autowired
    private WelfareMapper welfareMapper;
    @Autowired
    private CdkeyRecordService cdkeyRecordService;
    @Autowired
    private CdkeyService cdkeyService;
    @Autowired
    private WelfareCdkeyService welfareCdkeyService;
    @Autowired
    private GroupService groupService;
    @Autowired
    private PrizeRecordService prizeRecordService;
    @Value("${checkIsEsHotelUrl}")
    private String checkIsEsHotelUrl;
    @Autowired
    private PrizeService prizeService;
    @Autowired
    private RedPacketRecordService redPacketRecordService;
    @Autowired
    SendGiftService sendGiftService;
    @Autowired
    private EmailSender emailSender;
    @Autowired
    ScoreRecordService scoreRecordService;
    @Autowired
    ScoreService scoreService;
    @Lazy
    @Autowired
    WelfareService welfareService;

    @Override
    public BaseMapper getDao() {
        return welfareMapper;
    }

    /**
     * 获取在线的权益数据
     * @return
     */
    @Cacheable(cacheNames = "cache_5_minutes", key = "'m_'+#root.methodName", unless = "#result == null")
    public Map<String, Object> getAllWelfare(){
        Map<String, Object> resultMap = new HashMap<>();

        List<GroupWelfareResult> result = new ArrayList<>();
        List<Group> groupList = groupService.findOnline();
        if (groupList.isEmpty()){
            resultMap.put("welfareGroupList", null);
            return resultMap;
        }

        //如果不是电竞酒店环境，去除电竞酒店直领权益
        for (Group group : groupList){
            List<Welfare> welfareList = welfareMapper.findWithGroup(group.getId());
            if (welfareList.isEmpty()){
                continue;
            }

            GroupWelfareResult groupWelfareResult = new GroupWelfareResult();
            groupWelfareResult.setGroupName(group.getName());
            groupWelfareResult.setGroupId(group.getId());
            groupWelfareResult.setWelfareList(welfareList);
            result.add(groupWelfareResult);
        }
        resultMap.put("welfareGroupList", result);
        return resultMap;
    }

    /**
     * 减少库存
     * @param welfare
     * @return
     */
    @Override
    public int decreaseCnt(Welfare welfare) {
        int result = welfareMapper.decreaseCnt(welfare);
        emailSender.buildStockWarnEmail(result == 0 ? welfare.getCnt() : welfare.getCnt() - 1,
                "权益库存报警，id=" + welfare.getId() + ",数量：" + (result == 0 ? welfare.getCnt() : welfare.getCnt() - 1));
        return result;
    }

    @Override
    public int decreaseCnt(Integer id) {
        Welfare welfare = welfareMapper.get(id);
        return decreaseCnt(welfare);
    }

    @Override
    public int incCnt(Integer id) {
        return welfareMapper.incCnt(id);
    }

    /**
     * 是否已经领取过cdkey权益
     * @param welfare
     * @return
     */
    public boolean isReceivedCdkey(Welfare welfare){
        if (welfare == null || welfare.getType() == WelfareEnum.TypeEnum.FICTION.getValue()){
            return false;
        }
        UserInfo userInfo = UserContext.getUserInfo();

        CdkeyRecordQuery query = new CdkeyRecordQuery();
        query.setWelfareId(welfare.getId());
        query.setMemberId(userInfo.getMemberId());

        //如果已经领取过，返回true
        List<CdkeyRecord> records = cdkeyRecordService.find(query);
        if (!records.isEmpty()){
            return true;
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public String receiveCdkeyWelfare(Welfare welfare, UserInfo userInfo, boolean couldReceiveMore) {
        if (userInfo == null){
            userInfo = UserContext.getUserInfo();
        }

        //如果只能抽取一次
        if (!couldReceiveMore){
            //如果已经领取过，返回领取过的码
            CdkeyRecordQuery query = new CdkeyRecordQuery();
            query.setWelfareId(welfare.getId());
            query.setMemberId(userInfo.getMemberId());
            List<CdkeyRecord> records = cdkeyRecordService.find(query);
            if (!records.isEmpty()){
                return records.get(0).getCdkey();
            }
        }

        List<WelfareCdkey> list = welfareCdkeyService.getByWelfareId(welfare.getId());
        for (WelfareCdkey welfareCdkey : list){
            Date now = new Date();
            Cdkey cdkey = cdkeyService.get(welfareCdkey.getCdkeyId());
            //cdkey未上线
            if (cdkey == null || DateUtil.compare(cdkey.getStartTime(), now , 1) > 0){
                continue;
            }
            //cdkey已下线
            if (DateUtil.compare(now, cdkey.getEndTime(), 1) > 0){
                continue;
            }
            CdkeyRecordQuery notUsedQuery = new CdkeyRecordQuery();
            notUsedQuery.setCdkeyId(welfareCdkey.getCdkeyId());
            notUsedQuery.setState(CdkeyRecordEnum.StateEnum.NOT_USED.getValue());
            Integer notUsed = cdkeyRecordService.findCnt(notUsedQuery);

            if (notUsed > 0){
                CdkeyRecord notUsedRecord = cdkeyRecordService.findOne(notUsedQuery);
                if (notUsedRecord == null){
                    return null;
                }
                CdkeyRecord toSend = cdkeyRecordService.getForUpdate(notUsedRecord.getId());
                toSend.setSendTime(now);
                toSend.setWelfareId(welfareCdkey.getWelfareId());
                toSend.setState(CdkeyRecordEnum.StateEnum.USED.getValue());
                toSend.setMemberId(userInfo.getMemberId());
                toSend.setMemberName(userInfo.getMemberName());
                int result = cdkeyRecordService.update(toSend);
                if (result < 1){
                    //如果并发导致领取失败，递归调用自己
                    receiveCdkeyWelfare(welfare, userInfo, couldReceiveMore);
                }
                cdkeyService.toAddSendCnt(toSend.getCdkeyId());
                emailSender.buildStockWarnEmail(cdkey.getTotalCnt() - cdkey.getSendCnt() - 1,
                        "cdkey库存报警，id=" + cdkey.getId() + ",剩余数量："
                                + (cdkey.getTotalCnt() - cdkey.getSendCnt() - 1));

                return toSend.getCdkey();
            }
        }
        //没有抽到cdkey，发送cdkey不足邮件
        emailSender.buildStockWarnEmail(0,"兑换码库存报警，id=" + welfare.getId() + ",数量：" + 0);
        return null;
    }

    /**
     * 检查兑换码库存
     * @param welfareId
     * @return
     */
    public boolean checkCdkeyStock(Integer welfareId){
        List<WelfareCdkey> list = welfareCdkeyService.getByWelfareId(welfareId);
        for (WelfareCdkey welfareCdkey : list){
            Date now = new Date();
            Cdkey cdkey = cdkeyService.get(welfareCdkey.getCdkeyId());
            //cdkey未上线
            if (cdkey == null || DateUtil.compare(cdkey.getStartTime(), now , 1) > 0){
                continue;
            }
            //cdkey已下线
            if (DateUtil.compare(now, cdkey.getEndTime(), 1) > 0){
                continue;
            }
            if (cdkey == null){
                return false;
            }
            CdkeyRecordQuery notUsedQuery = new CdkeyRecordQuery();
            notUsedQuery.setCdkeyId(welfareCdkey.getCdkeyId());
            notUsedQuery.setState(CdkeyRecordEnum.StateEnum.NOT_USED.getValue());
            Integer notUsed = cdkeyRecordService.findCnt(notUsedQuery);
            if (notUsed > 0){
                return true;
            }
        }
        return false;
    }

    @Override
    public List<Welfare> findOnlineByIds(List<Integer> ids) {
        if(ids == null || ids.isEmpty()){
            return Collections.emptyList();
        }
        return welfareMapper.findOnlineByIds(ids);
    }

    @Override
    public String receiveCdkeyWelfare(ModuleDetails moduleDetails, Member member) {
        List<WelfareCdkey> list = welfareCdkeyService.getByWelfareId(moduleDetails.getWelfareId());
        for (WelfareCdkey welfareCdkey : list){
            Date now = new Date();
            Cdkey cdkey = cdkeyService.get(welfareCdkey.getCdkeyId());
            //cdkey未上线
            if (cdkey == null || DateUtil.compare(cdkey.getStartTime(), now , 1) > 0){
                continue;
            }
            //cdkey已下线
            if (DateUtil.compare(now, cdkey.getEndTime(), 1) > 0){
                continue;
            }
            CdkeyRecordQuery notUsedQuery = new CdkeyRecordQuery();
            notUsedQuery.setCdkeyId(welfareCdkey.getCdkeyId());
            notUsedQuery.setState(CdkeyRecordEnum.StateEnum.NOT_USED.getValue());

            List<CdkeyRecord> notUsedRecord = cdkeyRecordService.find(notUsedQuery);
            if (!notUsedRecord.isEmpty()){
                CdkeyRecord toSend = cdkeyRecordService.getForUpdate(notUsedRecord.get(0).getId());
                toSend.setSendTime(now);
                toSend.setWelfareId(welfareCdkey.getWelfareId());
                toSend.setState(CdkeyRecordEnum.StateEnum.USED.getValue());
                toSend.setMemberId(member.getMemberId());
                toSend.setMemberName(member.getMemberName());
                toSend.setModuleDetailId(moduleDetails.getId());
                int result = cdkeyRecordService.update(toSend);
                if (result < 1){
                    //如果并发导致领取失败，递归调用自己
                    receiveCdkeyWelfare(moduleDetails,member);
                }
                cdkeyService.toAddSendCnt(toSend.getCdkeyId());

                return toSend.getCdkey();
            }
        }

        return null;
    }

    /**
     * 根据ip判断是否是电竞酒店环境
     * @param ip
     * @return
     */
    public boolean checkIsEsHotel(String ip){
        if (StringUtil.isBlank(ip)){
            return false;
        }
        try {
            CheckIsEsHotelRequest request = new CheckIsEsHotelRequest();
            request.setIp(ip);

            String response = HttpClientUtils.doPostJson(checkIsEsHotelUrl, request.buildParams());
            if (StringUtil.isBlank(response)){
                return false;
            }
            CheckIsEsHotelResponse checkIsEsHotelResponse = GsonUtil.jsonToBean(response, CheckIsEsHotelResponse.class);
            return checkIsEsHotelResponse.isSuccess() ? Boolean.parseBoolean(checkIsEsHotelResponse.getResult_content()) : false;
        } catch (Exception e){
            LOGGER.error("据ip判断是否是电竞酒店环境异常：" + e.getMessage());
        }

        return false;
    }

    /**
     * 获取省钱卡配置的权益列表
     * @param cardId
     * @return
     */
    public List<Welfare> getWelfareBySavingCard(Integer cardId){

        return welfareMapper.getWelfareBySavingCard(cardId);
    }

    /**
     * 获取省钱卡未领取和已锁定的红包列表
     * @param cardId
     * @return
     */
    public List<Welfare> getCardNotReceive(Integer cardId){
        return welfareMapper.getCardNotReceive(cardId, UserContext.getUserId());
    }

    /**
     * 获取用户省钱卡下的所有奖品
     *
     * @param cardId
     * @return
     */
    @Override
    public List<WelfareMscVo> getUserRedPacketByCard(Integer cardId, String orderNo) {
        //红包类权益
        List<WelfareMscVo> redPacketMscVos = welfareMapper.getUserRedPacketByCard(cardId, UserContext.getUserId());
        //非红包类权益
        List<WelfareMscVo> welfareMscVos = welfareMapper.getUserWelfareByCard(cardId);

        for (WelfareMscVo welfareMscVo : welfareMscVos){
            //如果权益是cdkey类型的，需要返回cdkey码
            if (welfareMscVo.getType().intValue() != WelfareEnum.TypeEnum.PC_CDKEY.getValue()){
                continue;
            }
            if (StringUtil.isNotBlank(welfareMscVo.getRemark())){
                continue;
            }
            List<PrizeRecord> records = prizeRecordService.getMscCardCdkeyPrize(UserContext.getUserId(), welfareMscVo.getWelfareId(), orderNo);
            if (records != null && !records.isEmpty()){
                welfareMscVo.setRemark(records.get(0).getRemark());
            }

        }
        redPacketMscVos.addAll(welfareMscVos);
        return redPacketMscVos;
    }

    public List<WelfareMscVo> getUserWelfareByCard(Integer cardId){
        return welfareMapper.getUserWelfareByCard(cardId);
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public Integer sendWelfare(Welfare welfare, String source, ScoreRecordEnum.SourceTypeEnum sourceType, boolean decStock) {
        return welfareService.sendWelfare(welfare, source, sourceType, decStock, null, null);
    }

    private Integer sendWelfare(SendWelfareParam param) {
        Welfare welfare = param.getWelfare();
        if (param.isDecStock()){
            //库存不足抛异常，奖品发放失败联系客服处理
            int welfareResult = decreaseCnt(welfare);
            if (welfareResult < 1) {
                log.info("权益库存不足,WelfareId=[{}]", welfare.getId());
                throw new NoStockException("权益库存不足,权益ID:" + welfare.getId());
            }
        }
        String cdkey = null;
        if (WelfareEnum.TypeEnum.PC_CDKEY.getValue().equals(welfare.getType())) {
            cdkey = receiveCdkeyWelfare(welfare, MemberUtil.getNotNullUserInfo(), true);
            if (StringUtil.isBlank(cdkey)) {
                log.info("权益兑换码库存不足,WelfareId=[{}]", welfare.getId());
                throw new NoStockException("权益兑换码库存不足,兑换码Id:" + welfare.getId());
            }
        } else if (WelfareEnum.TypeEnum.RED_PACKET.getValue().equals(welfare.getType())) {
            Integer redPacket = receiveRedPacket(welfare, param.getSource());
            savePrizeRecord(welfare, param, null);
            return redPacket;
        } else if (WelfareEnum.TypeEnum.SCORE.getValue().equals(welfare.getType())){ //网费通
            receiveScore(welfare,param.getSourceType().getValue(), param.getSource());
            return null;
        }
        PrizeRecord prizeRecord = savePrizeRecord(welfare, param, cdkey);
        return prizeRecord.getId();
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public Integer sendWelfare(Welfare welfare, String source, ScoreRecordEnum.SourceTypeEnum sourceType, boolean decStock, String orderNo, String payOrderNo) {
        SendWelfareParam param = new SendWelfareParam();
        param.setWelfare(welfare);
        param.setSource(source);
        param.setSourceType(sourceType);
        param.setDecStock(decStock);
        param.setOrderNo(orderNo);
        param.setPayOrderNo(payOrderNo);
        return sendWelfare(param);
    }

    @NotNull
    private PrizeRecord savePrizeRecord(Welfare welfare,SendWelfareParam param, String cdkey) {
        PrizeRecord prizeRecord = new PrizeRecord();
        prizeRecord.setMemberId(MemberUtil.getNotNullMemberId());
        prizeRecord.setOrderNo(param.getOrderNo());
        prizeRecord.setPayOrderNo(param.getPayOrderNo());
        prizeRecord.setPrizeType(welfare.getType());
        prizeRecord.setWelfareId(welfare.getId());
        prizeRecord.setPrizeName(welfare.getName());
        prizeRecord.setWelfareName(welfare.getName());
        prizeRecord.setSendTime(new Date());
        prizeRecord.setRemark(cdkey);
        prizeRecord.setSource(param.getSource());
        prizeRecord.setChannel("" + ThreadLocalContext.getTempData(ThreadLocalContext.CHANNEL));
        prizeRecordService.save(prizeRecord);
        return prizeRecord;
    }

    /**
     * 领取网费通类型的权益
     * @param welfare
     * @param source
     */
    private void receiveScore(Welfare welfare, Integer sourceType, String source) {
        scoreService.updateScoreAndTakeRecord(welfare.getScoreCnt(), ScoreRecordEnum.TypeEnum.ADD, sourceType, source);
    }

    /**
     * 领取红包类型的权益
     * @param welfare
     */
    private Integer receiveRedPacket(Welfare welfare,String source){
        RedPacketRecord redPacketRecord = new RedPacketRecord();
        BeanUtils.copyProperties(welfare, redPacketRecord);

        String orderNo = "";
        //游戏发券
        if (WelfareEnum.BussinessLineEnum.GAME.getValue() == welfare.getBusinessLine()) {
            orderNo = prizeService.sendCoupon(welfare, source, MemberUtil.getNotNullMemberId(), MemberUtil.getNotNullMemberName());
            redPacketRecord.setState(RedPacketRecordEnum.StateEnum.NOT_USED.getValue());
        }
        //泡泡发券
        else if (WelfareEnum.BussinessLineEnum.PAOPAO.getValue() == welfare.getBusinessLine()) {
            orderNo = sendGiftService.buildOrderNo();
            Integer giftType = WelfareEnum.BussinessTypeEnum.TIME.getValue() == welfare.getBusinessType() ? 1 : 2;
            boolean result = sendGiftService.executeSendGift(MemberUtil.getNotNullMemberId(), giftType, welfare.getGoodsId(), orderNo);
            if (result) {
                redPacketRecord.setState(RedPacketRecordEnum.StateEnum.NOT_USED.getValue());
            } else {
                redPacketRecord.setState(RedPacketRecordEnum.StateEnum.NOT_SEND.getValue());
            }
        }
        if (StringUtil.isBlank(orderNo)) {
            log.error("发放优惠券失败：WelfareId:{}", welfare.getId());
            throw new CommonException("发放优惠券失败");
        }
        Date now = new Date();
        redPacketRecord.setMemberId(MemberUtil.getNotNullMemberId())
                .setWelfareId(welfare.getId())
                .setSource(source)
                .setOrderNo(orderNo)
                .setSendTime(now)
                .setExpireTime(com.shunwang.marketing.util.DateUtil.addDay(now, welfare.getEffectiveDays()));
        redPacketRecordService.save(redPacketRecord);
        return redPacketRecord.getId();
    }
}
