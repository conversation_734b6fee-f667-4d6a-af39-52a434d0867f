package com.shunwang.marketing.service.marketing.impl;

import cn.hutool.core.util.RandomUtil;
import com.shunwang.marketing.cacheService.CacheKeyConstant;
import com.shunwang.marketing.cacheService.RedisOperation;
import com.shunwang.marketing.enums.ResourcesEnum;
import com.shunwang.marketing.enums.marketing.AiModelEnum;
import com.shunwang.marketing.enums.marketing.AiRecordEnum;
import com.shunwang.marketing.enums.marketing.ContentEnum;
import com.shunwang.marketing.pojo.ai.AiReplyMessage;
import com.shunwang.marketing.pojo.marketing.AiModel;
import com.shunwang.marketing.pojo.marketing.AiRecord;
import com.shunwang.marketing.pojo.util.SendMsg;
import com.shunwang.marketing.query.marketing.AiModelQuery;
import com.shunwang.marketing.query.marketing.AiRecordQuery;
import com.shunwang.marketing.query.marketing.ContentQuery;
import com.shunwang.marketing.send.email.EmailSender;
import com.shunwang.marketing.service.ai.AiSseEmitterWrapper;
import com.shunwang.marketing.service.config.ResourcesService;
import com.shunwang.marketing.service.marketing.AiModelService;
import com.shunwang.marketing.service.marketing.AiRecordService;
import com.shunwang.marketing.service.marketing.AiService;
import com.shunwang.marketing.service.marketing.ContentService;
import com.shunwang.marketing.util.DateUtil;
import com.shunwang.sms.utils.SMSInnerSenderUtil;
import com.shunwang.util.json.GsonUtil;
import com.shunwang.util.lang.StringUtil;
import com.volcengine.ark.runtime.model.completion.chat.ChatCompletionRequest;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessage;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessageRole;
import com.volcengine.ark.runtime.service.ArkService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static com.shunwang.marketing.enums.marketing.AiModelEnum.PlatformEnum.AI_DEPARTMENT;
import static com.shunwang.marketing.enums.marketing.AiModelEnum.PlatformEnum.HUOSHAN;

@Service
@Slf4j
public class AiServiceimpl implements AiService {
    @Autowired
    AiRecordService aiRecordService;
    @Autowired
    AiModelService aiModelService;
    Map<String, ArkService> arkServiceMap = new ConcurrentHashMap<>();
    @Autowired
    RedisOperation redisOperation;
    @Autowired
    ResourcesService resourcesService;
    @Autowired
    EmailSender emailSender;
    @Autowired
    @Lazy
    AiService aiService;
    @Autowired
    ContentService contentService;

    /**
     * 找当天用户使用过的大模型->没有或失效找其他模型
     *
     * @param memberId
     * @return
     */
    private AiModel getModel(Integer memberId) {
        AiRecordQuery recordQuery = new AiRecordQuery();
        recordQuery.setMemberId(memberId);
        Date today = new Date();
        recordQuery.setBeginTimeAdd(DateUtil.dateFormat(DateUtil.ymdhms_DATE_FORMAT, DateUtil.zeroConvertTime(today)));
        recordQuery.setEndTimeAdd(DateUtil.dateFormat(DateUtil.ymdhms_DATE_FORMAT, DateUtil.zeroConvertTime(DateUtil.addDay(today, 1))));
        recordQuery.setType(AiRecordEnum.TypeEnum.SYSTEM.getValue());
        List<AiRecord> todayRecordList = aiRecordService.find(recordQuery);
        if (!CollectionUtils.isEmpty(todayRecordList)) {
            AiModelQuery query = new AiModelQuery();
            query.setIdList(todayRecordList.stream().map(AiRecord::getModelId).distinct().collect(Collectors.toList()));
            List<AiModel> modelList = aiModelService.find(query);
            modelList = modelList.stream().filter(AiModel::isEffective).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(modelList)) {
                return doGetModel();
            }
            return modelList.get(0);
        }
        return doGetModel();
    }

    private AiModel doGetModel() {
        AiModelQuery query = new AiModelQuery();
        query.setState(AiModelEnum.StateEnum.ONLINE.getValue());
        List<AiModel> aiModelList = aiModelService.find(query);
        if (CollectionUtils.isEmpty(aiModelList)) {
            return null;
        }
        aiModelList = aiModelList.stream().filter(AiModel::isEffective).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(aiModelList)) {
            return null;
        }
        //优先使用外部模型，外部模型失效 使用ai部门模型
        Map<Boolean, List<AiModel>> isAiDepartment = aiModelList.stream().collect(Collectors.groupingBy((aiModel -> AI_DEPARTMENT.getValue().equals(aiModel.getPlatform()))));
        if (CollectionUtils.isEmpty(isAiDepartment.get(false))) {
            return RandomUtil.randomEle(isAiDepartment.get(true));
        } else {
            return RandomUtil.randomEle(isAiDepartment.get(false));
        }
    }

    public void checkToken() {
        AiModelQuery query = new AiModelQuery();
        query.setState(AiModelEnum.StateEnum.ONLINE.getValue());
        List<AiModel> aiModelList = aiModelService.find(query);
        int sumRemainToken = aiModelList.stream()
                .filter((aiModel -> !AI_DEPARTMENT.getValue().equals(aiModel.getPlatform()) || aiModel.getRemainToken() == null))
                .mapToInt(AiModel::getRemainToken)
                .sum();
        if (sumRemainToken < 50000) {
            log.info("ai总token小于50000");
            if (redisOperation.setIfAbsent("ai_token_check", 1, 3, TimeUnit.HOURS)) {
                log.info("ai总token小于50000，发送提醒");
                String emailReceiver = resourcesService.getStringValueByTypeAndNameEhCache(ResourcesEnum.TypeName.EMAIL_RECEIVER);
                if (StringUtil.isNotBlank(emailReceiver)) {
                    for (String receiver : emailReceiver.split(",")) {
                        SendMsg sendMsg = new SendMsg();
                        sendMsg.setTitle("AI Token余量不足");
                        sendMsg.setContent("AI总token数小于50000");
                        sendMsg.setNumber(receiver);
                        emailSender.sendEmail(sendMsg);
                    }
                }

                String smsReceiver = resourcesService.getStringValueByTypeAndNameEhCache(ResourcesEnum.TypeName.SMS_RECEIVER);
                if (StringUtil.isNotBlank(smsReceiver)) {
                    for (String receiver : smsReceiver.split(",")) {
                        SMSInnerSenderUtil.sendMsg(receiver, "AI总token数小于50000");
                    }
                }
            }
        }
    }

    @Override
    public void chat(AiSseEmitterWrapper sseEmitterWrapper) throws IOException {
        String messageId = UUID.randomUUID().toString();

        boolean isBar = judgeIsBar(sseEmitterWrapper.getQuestion());
        boolean isBasic = judgeIsBasic(sseEmitterWrapper.getQuestion());

        if (!isBar) {
            AiModel model = getModel(sseEmitterWrapper.getMemberId());
            if (model == null) {
                log.info("会话:{}未获取到模型", sseEmitterWrapper.getSessionId());
                sseEmitterWrapper.getSseEmitter().send(AiReplyMessage.newFinishMsg(getFailMsg(), messageId));
                return;
            }
            sseEmitterWrapper.setModel(model);
        }
        AiRecord questionRecord = new AiRecord();
        questionRecord.setMemberId(sseEmitterWrapper.getMemberId());
        questionRecord.setType(AiRecordEnum.TypeEnum.USER.getValue());
        questionRecord.setContent(sseEmitterWrapper.getQuestion());
        questionRecord.setTimeAdd(new Date());
        questionRecord.setConsumeToken(0);
        questionRecord.setModelId(sseEmitterWrapper.getModel() == null ? null : sseEmitterWrapper.getModel().getId());
        questionRecord.setRemark(isBasic ? "基础问题" : isBar ? "网费查询问题" : "");
        aiRecordService.save(questionRecord);


        if (isBar) {
            log.info("会话:{}问题为查询网费", sseEmitterWrapper.getSessionId());
            AiRecord answerRecord = buildAnswerRecord(sseEmitterWrapper);
            answerRecord.setRemark("网费查询回复");
            answerRecord.setContent(getBarMoneyMsg());
            aiRecordService.save(answerRecord);
            sseEmitterWrapper.getSseEmitter().send(AiReplyMessage.newFinishMsg(answerRecord.getContent(), messageId));
        } else if (isBasic) {
            log.info("会话:{}问题为基础问题", sseEmitterWrapper.getSessionId());
            AiBasicQuestionAnswer cacheAnswer = redisOperation.get(CacheKeyConstant.AI_BASIC + sseEmitterWrapper.getQuestion(), AiBasicQuestionAnswer.class);
            if (cacheAnswer == null || StringUtil.isBlank(cacheAnswer.content)) {
                String content = doChat(sseEmitterWrapper, questionRecord);
                AiBasicQuestionAnswer answer = new AiBasicQuestionAnswer();
                answer.content = content;
                answer.modelId = sseEmitterWrapper.getModel().getId();
                redisOperation.set(CacheKeyConstant.AI_BASIC + sseEmitterWrapper.getQuestion(), answer, 3, TimeUnit.HOURS);
                checkToken();
            } else {
                log.info("会话:{}缓存回复", sseEmitterWrapper.getSessionId());
                AiRecord answerRecord = buildAnswerRecord(sseEmitterWrapper);
                answerRecord.setRemark("基础问题缓存回复");
                answerRecord.setModelId(cacheAnswer.modelId);
                answerRecord.setContent(cacheAnswer.content);
                aiRecordService.save(answerRecord);
                sendInSegment(cacheAnswer.content, sseEmitterWrapper);
            }
        } else {
            doChat(sseEmitterWrapper, questionRecord);
            checkToken();
        }
    }

    class AiBasicQuestionAnswer {
        String content;
        Integer modelId;
    }

    private AiRecord buildAnswerRecord(AiSseEmitterWrapper sseEmitterWrapper) {
        AiRecord answerRecord = new AiRecord();
        answerRecord.setMemberId(sseEmitterWrapper.getMemberId());
        answerRecord.setType(AiRecordEnum.TypeEnum.SYSTEM.getValue());
        answerRecord.setContent(sseEmitterWrapper.getQuestion());
        answerRecord.setTimeAdd(new Date());
        answerRecord.setConsumeToken(0);
        return answerRecord;
    }

    public void sendInSegment(String content, AiSseEmitterWrapper sseEmitterWrapper) throws IOException {
        int partSize = 10;
        int length = content.length();
        int sendTimes = content.length() / partSize + (length % partSize == 0 ? 0 : 1);
        String messageId = UUID.randomUUID().toString();

        for (int i = 0; i < sendTimes; i++) {
            int start = i * partSize;
            int end = Math.min(start + partSize, length);

            AiReplyMessage replyMessage = new AiReplyMessage();
            replyMessage.setCode(0);
            replyMessage.setMessageId(messageId);
            replyMessage.setFinish(i == sendTimes - 1);
            replyMessage.setContent(content.substring(start, end));
            sseEmitterWrapper.getSseEmitter().send(replyMessage);
        }
    }

    private boolean judgeIsBar(String question) {
        String barMoneyKeywords = resourcesService.getStringValueByTypeAndNameEhCache(ResourcesEnum.TypeName.AI_BAR_MONEY_KEYWORDS);
        if (StringUtil.isNotBlank(barMoneyKeywords)) {
            for (String keyWord : barMoneyKeywords.split(",")) {
                if (question.contains(keyWord)) {
                    return true;
                }
            }
        }
        return false;
    }

    private boolean judgeIsBasic(String question) {
        ContentQuery query = new ContentQuery();
        query.setTitle(question.trim());
        query.setType(ContentEnum.TypeEnum.AI.getValue());
        return contentService.findCnt(query) > 0;
    }

    private String doChat(AiSseEmitterWrapper sseEmitterWrapper, AiRecord questionRecord) throws IOException {
        String answer = "";
        Integer platform = sseEmitterWrapper.getModel().getPlatform();
        if (HUOSHAN.getValue().equals(platform)) {
            answer = callHuoShanModel(sseEmitterWrapper, questionRecord);
        }

        return answer;
    }

    public String getFailMsg() {
        String msg = resourcesService.getStringValueByTypeAndNameEhCache(ResourcesEnum.TypeName.AI_FALLBACK);
        if (StringUtil.isBlank(msg)) {
            return "很抱歉，暂时无法为您提供服务，可能是由于网络不稳定或系统繁忙等原因。\n\n给您带来不便，我们深表歉意，感谢您的理解与支持！";
        }
        return msg;
    }

    public String getBarMoneyMsg() {
        String msg = resourcesService.getStringValueByTypeAndNameEhCache(ResourcesEnum.TypeName.AI_BAR_MONEY_REPLY);
        if (StringUtil.isBlank(msg)) {
            return "您好，为确保用户隐私信息安全，[网费查询]功能目前已关闭，如您需要查询网费信息，需向网吧前台直接确认，给您带来的不便非常抱歉，感谢您的支持与理解！";
        }
        return msg;
    }


    @Override
    public SseEmitter createSseEmitter(Integer memberId, String sessionId) {
        SseEmitter sseEmitter = new SseEmitter(3 * 60 * 1000L);
        sseEmitter.onCompletion(() -> {
            log.info("[{}]结束连接", sessionId);
        });
        sseEmitter.onTimeout(() -> {
            log.info("[{}]连接超时", sessionId);
        });
        sseEmitter.onError(throwable -> {
            log.info("[{}]连接异常，关闭连接", sessionId, throwable);
            sseEmitter.complete();
        });
        log.info("用户:{}创建sse连接:{}", memberId, sessionId);
        return sseEmitter;
    }

    private String callHuoShanModel(AiSseEmitterWrapper sseEmitterWrapper, AiRecord questionRecord) {
        String messageId = UUID.randomUUID().toString();
        AtomicReference<Boolean> isSseException = new AtomicReference<>(false);
        AiModel model = sseEmitterWrapper.getModel();
        ArkService service = getArkService(model);
        StringBuilder answer = new StringBuilder();
        AiRecord answerRecord = new AiRecord();
        answerRecord.setMemberId(sseEmitterWrapper.getMemberId());
        answerRecord.setType(AiRecordEnum.TypeEnum.SYSTEM.getValue());
        answerRecord.setModelId(model.getId());
        answerRecord.setParentId(questionRecord.getId());
        final List<ChatMessage> streamMessages = new ArrayList<>();
        final ChatMessage streamUserMessage = ChatMessage.builder().role(ChatMessageRole.USER).content(sseEmitterWrapper.getQuestion()).build();
        streamMessages.add(streamUserMessage);

        ChatCompletionRequest streamChatCompletionRequest = ChatCompletionRequest.builder()
                .model(model.getEndPoint())
                .messages(streamMessages)
                .maxTokens(4096)
                .streamOptions(new ChatCompletionRequest.ChatCompletionRequestStreamOptions(true))
                .build();
        service.streamChatCompletion(streamChatCompletionRequest)
                .doOnError((throwable -> {
                    log.error("火山大模型调用异常:{}", sseEmitterWrapper.getSessionId(), throwable);
                    sseEmitterWrapper.getSseEmitter().send(AiReplyMessage.newFinishMsg(getFailMsg(), messageId));
                }))
                .blockingForEach(
                        choice -> {
                            if (!choice.getChoices().isEmpty()) {
                                String content = choice.getChoices().get(0).getMessage().getContent().toString();
                                //deepSeek深度思考存在很多为null的消息,content为null时，只发送finishReason不为空的消息
                                if (StringUtil.isNotEmpty(content) || StringUtil.isNotBlank(choice.getChoices().get(0).getFinishReason())) {
                                    answer.append(content);
                                    AiReplyMessage aiReplyMessage = new AiReplyMessage();
                                    aiReplyMessage.setCode(0);
                                    aiReplyMessage.setMessageId(messageId);
                                    aiReplyMessage.setFinish(StringUtil.isNotBlank(choice.getChoices().get(0).getFinishReason()));
                                    aiReplyMessage.setContent(content);
                                    try {
                                        sseEmitterWrapper.getSseEmitter().send(GsonUtil.toJson(aiReplyMessage));
                                    } catch (Exception e) {
                                        //用户直接退出页面会导致异常，捕获异常让程序继续执行，目的是线程保存回答,并且只对于第一次异常输出日志
                                        if (isSseException.get().equals(Boolean.FALSE)) {
                                            log.info("sse发送消息失败:{}", sseEmitterWrapper.getSessionId(), e);
                                            isSseException.set(true);
                                        }
                                    }
                                }
                            }
                            if (choice.getUsage() != null) {
                                answerRecord.setConsumeToken((int) choice.getUsage().getCompletionTokens());
                                questionRecord.setConsumeToken((int) choice.getUsage().getPromptTokens());
                            }
                        }
                );
        //感觉不需要事务，尽可能去操作
        //回答记录
        answerRecord.setContent(answer.toString());
        answerRecord.setTimeAdd(new Date());
        aiRecordService.save(answerRecord);

        aiRecordService.update(questionRecord);

        //减少模型的token
        aiModelService.decToken(model.getId(), answerRecord.getConsumeToken() + questionRecord.getConsumeToken());
        return answer.toString();
    }

    private ArkService getArkService(AiModel model) {
        ArkService service = arkServiceMap.get(model.getApiKey());
        if (service == null) {
            synchronized (model.getApiKey().intern()) {
                service = arkServiceMap.get(model.getApiKey());
                if (service == null) {
                    service = ArkService.builder()
                            .apiKey(model.getApiKey())
                            .timeout(Duration.ofSeconds(120))
                            .connectTimeout(Duration.ofSeconds(10))
                            .baseUrl(model.getUrl())
                            .build();
                }
                arkServiceMap.put(model.getApiKey(), service);
            }
        }
        return service;
    }
}
