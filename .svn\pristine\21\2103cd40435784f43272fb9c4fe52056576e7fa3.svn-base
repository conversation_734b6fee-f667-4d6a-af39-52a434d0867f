package com.shunwang.marketing.pojo.marketing.vo;

import com.shunwang.marketing.context.DomainContext;
import com.shunwang.util.lang.StringUtil;
import lombok.Data;

@Data
public class AiGameInfoVo {
    private boolean open;
    private Integer gameId;
    private Integer gameItemId;
    /**
     * 规则，富文本
     */
    private String rule;
    /**
     * 用户是否已经完成
     */
    private boolean finish = false;
    /**
     * 提示
     */
    private String keyword;
    /**
     * 分享标题
     */
    private String shareTitle;
    /**
     * 分享图片
     */
    private String shareImg;
    /**
     * 分享路径
     */
    private String sharePath;
    /**
     * 会话id，若没有需调用接口创建
     */
    private String sessionId;
    /**
     * 用户猜测次数
     */
    private Integer times = 0;
    /**
     * 奖励是否领取
     */
    private boolean rewardReceived = false;
    private String rewardImgPath;

    public String getShareImg() {
        if (StringUtil.isNotEmpty(shareImg)) {
            return null;
        }
        return DomainContext.getBackStaticUploadServer() + shareImg;
    }
    public String getRewardImgPath() {
        if (StringUtil.isNotEmpty(rewardImgPath)) {
            return null;
        }
        return DomainContext.getBackStaticUploadServer() + rewardImgPath;
    }
}
