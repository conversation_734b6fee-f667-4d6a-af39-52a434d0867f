<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shunwang.marketing.dao.marketing.SignConfigMapper">
    <resultMap id="BaseResultMap" type="signConfig">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="signInId" column="sign_in_id" jdbcType="INTEGER"/>
        <result property="day" column="day" jdbcType="INTEGER"/>
        <result property="hasPrize" column="has_prize" jdbcType="INTEGER"/>
        <result property="img" column="img" jdbcType="VARCHAR"/>
        <result property="imgId" column="img_id" jdbcType="INTEGER"/>
        <result property="sort" column="sort" jdbcType="INTEGER"/>
        <result property="userAdd" column="user_add" jdbcType="VARCHAR"/>
        <result property="timeAdd" column="time_add" jdbcType="TIMESTAMP"/>
        <result property="userEdit" column="user_edit" jdbcType="VARCHAR"/>
        <result property="timeEdit" column="time_edit" jdbcType="TIMESTAMP"/>

        <result property="imagePath" column="image_path" jdbcType="VARCHAR"/>
    </resultMap>
    <resultMap id="signConfigResultMap" type="com.shunwang.marketing.pojo.marketing.SignConfigResult"
               extends="BaseResultMap">
        <collection property="welfareList" ofType="welfare"
                    select="getWelfare" column="id">

        </collection>
    </resultMap>
    <select id="getWelfare" resultMap="com.shunwang.marketing.dao.marketing.WelfareMapper.BaseResultMap">
        select w.*
        from marketing_sign_config_welfare scw LEFT JOIN marketing_welfare w ON scw.welfare_id=w.id
        where scw.sign_config_id = #{id}
    </select>
    <sql id="baseColumn">
        id
        ,
        sign_in_id,
        day,
        has_prize,
        img,
        img_id,
        image_path,
        sort,
        user_add,
        time_add,
        user_edit,
        time_edit
    </sql>
    <sql id="base_where">
        <trim prefix="where" prefixOverrides="and|or">
            <if test="id != null ">
                and id = #{id,jdbcType=INTEGER}
            </if>
            <if test="signInId != null ">
                and sign_in_id = #{signInId,jdbcType=INTEGER}
            </if>
            <if test="day != null ">
                and day = #{day,jdbcType=INTEGER}
            </if>
            <if test="hasPrize != null ">
                and has_prize = #{hasPrize,jdbcType=INTEGER}
            </if>
            <if test="img != null ">
                and img = #{img,jdbcType=VARCHAR}
            </if>
            <if test="welfareId != null ">
                and welfare_id = #{welfareId,jdbcType=INTEGER}
            </if>
            <if test="sort != null ">
                and sort = #{sort,jdbcType=INTEGER}
            </if>
            <if test="userAdd != null ">
                and user_add = #{userAdd,jdbcType=VARCHAR}
            </if>
            <if test="timeAdd != null ">
                and time_add = #{timeAdd,jdbcType=TIMESTAMP}
            </if>
            <if test="userEdit != null ">
                and user_edit = #{userEdit,jdbcType=VARCHAR}
            </if>
            <if test="timeEdit != null ">
                and time_edit = #{timeEdit,jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>
    <select id="find" parameterType="com.shunwang.marketing.query.BaseQuery" resultMap="BaseResultMap">
        select
        <include refid="baseColumn"/>
        from marketing_sign_config t
        <include refid="base_where"/>
    </select>
    <select id="findCnt" parameterType="com.shunwang.marketing.query.BaseQuery" resultType="java.lang.Integer">
        select
        count(*)
        from marketing_sign_config t
        <include refid="base_where"/>
    </select>
    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select
        <include refid="baseColumn"/>
        from marketing_sign_config
        where id = #{id,jdbcType=INTEGER}
    </select>
    <select id="getBySignInId" resultMap="signConfigResultMap">
        SELECT sc.*, i.image_path as image_path
        FROM marketing_sign_config sc
        left join marketing_image i on sc.img_id = i.id
        where sc.sign_in_id = #{signInId,jdbcType=INTEGER}
        order by sc.day
    </select>
    <insert id="save" parameterType="signConfig">
        insert into marketing_sign_config (
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="signInId != null">
                sign_in_id,
            </if>
            <if test="day != null">
                day,
            </if>
            <if test="hasPrize != null">
                has_prize,
            </if>
            <if test="img != null">
                img,
            </if>
            <if test="sort != null">
                sort,
            </if>
            <if test="userAdd != null">
                user_add,
            </if>
            <if test="timeAdd != null">
                time_add,
            </if>
            <if test="userEdit != null">
                user_edit,
            </if>
            <if test="timeEdit != null">
                time_edit,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="signInId != null">
                #{signInId,jdbcType=INTEGER},
            </if>
            <if test="day != null">
                #{day,jdbcType=INTEGER},
            </if>
            <if test="hasPrize != null">
                #{hasPrize,jdbcType=INTEGER},
            </if>
            <if test="img != null">
                #{img,jdbcType=VARCHAR},
            </if>
            <if test="sort != null">
                #{sort,jdbcType=INTEGER},
            </if>
            <if test="userAdd != null">
                #{userAdd,jdbcType=VARCHAR},
            </if>
            <if test="timeAdd != null">
                #{timeAdd,jdbcType=TIMESTAMP},
            </if>
            <if test="userEdit != null">
                #{userEdit,jdbcType=VARCHAR},
            </if>
            <if test="timeEdit != null">
                #{timeEdit,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="update" parameterType="signConfig">
        update marketing_sign_config
        <set>
            <if test="id != null">
                id = #{id,jdbcType=INTEGER},
            </if>
            <if test="signInId != null">
                sign_in_id = #{signInId,jdbcType=INTEGER},
            </if>
            <if test="day != null">
                day = #{day,jdbcType=INTEGER},
            </if>
            <if test="hasPrize != null">
                has_prize = #{hasPrize,jdbcType=INTEGER},
            </if>
            <if test="img != null">
                img = #{img,jdbcType=VARCHAR},
            </if>
            <if test="sort != null">
                sort = #{sort,jdbcType=INTEGER},
            </if>
            <if test="userAdd != null">
                user_add = #{userAdd,jdbcType=VARCHAR},
            </if>
            <if test="timeAdd != null">
                time_add = #{timeAdd,jdbcType=TIMESTAMP},
            </if>
            <if test="userEdit != null">
                user_edit = #{userEdit,jdbcType=VARCHAR},
            </if>
            <if test="timeEdit != null">
                time_edit = #{timeEdit,jdbcType=TIMESTAMP},
            </if>
        </set>
        <trim prefix="where" prefixOverrides="and|or">
            id = #{id,jdbcType=INTEGER}
        </trim>
    </update>
</mapper>
