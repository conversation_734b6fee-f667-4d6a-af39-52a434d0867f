package com.shunwang.marketing.enums.marketing;

import lombok.Getter;
import lombok.Setter;

public interface ScoreRecordEnum {

    enum TypeEnum {
        ADD(1, "添加"),
        DEC(2, "减少"),
        FROST(3, "冻结"),
        FROST_DEC(4, "解冻并扣除"),
        FROST_BACK(5, "解冻"),
        ;

        TypeEnum(int value, String name) {
            this.value = value;
            this.name = name;
        }

        private @Getter Integer value;
        private @Getter String name;
    }

    enum SourceTypeEnum {
        SIGNIN(1, "签到"),
        PRIZE(2, "抽奖"),
        GIFT_BAG(3, "礼包"),
        MSC(4, "省钱卡"),
        PAY_ORDER(5, "支付订单"),
        TASK(6, "任务"),
        STORE(7, "兑换商城"),
        SURVEY(8, "问卷"),
        STAR(9, "摘星星"),
        MODULE(10, "模块详情"),
        GAME(11, "猜角色"),
        ;

        SourceTypeEnum(int value, String name) {
            this.value = value;
            this.name = name;
        }

        private @Getter int value;
        private @Getter String name;
    }


}
