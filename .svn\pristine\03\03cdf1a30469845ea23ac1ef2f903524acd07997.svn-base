package com.shunwang.marketing.filter;

import com.shunwang.util.net.CookieUtil;
import com.shunwang.marketing.context.SessionContext;

import javax.servlet.*;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;


public class SessionFilter implements Filter {
    public static final String S_Cookie = "s.cookie";
    private static final int S_Exp = -1;


    @Override
    public void doFilter(ServletRequest req, ServletResponse resp,
                         FilterChain chain) throws IOException, ServletException {
        String sessionId = null;
        HttpServletRequest hr = (HttpServletRequest) req;
        HttpServletResponse rep = (HttpServletResponse) resp;
        Cookie cookie = CookieUtil.getCookie(hr, S_Cookie);
        if (cookie != null && cookie.getValue() != null && !"".equals(cookie.getValue())) {
            sessionId = cookie.getValue();
        } else {
            sessionId = java.util.UUID.randomUUID().toString();
            CookieUtil.setCookie(rep, S_Cookie, sessionId, "/", null, S_Exp, false);
        }
        SessionContext.setSessionId(sessionId);
        chain.doFilter(req, resp);
    }

}
