package com.shunwang.marketing.exception;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/2/6 下午5:16
 */
public class UserApiBusinessException extends RuntimeException {

	private int code;
	public UserApiBusinessException(String message) {
		super(message);
		this.code = -1;
	}

	public UserApiBusinessException(int code, String message) {
		super(message);
		this.code = code;
	}

	public UserApiBusinessException(String message, Throwable cause) {
		super(message, cause);
	}

	public UserApiBusinessException(int code, String message, Throwable cause) {
		super(message, cause);
		this.code = code;
	}

	public int getCode() {
		return code;
	}

}
