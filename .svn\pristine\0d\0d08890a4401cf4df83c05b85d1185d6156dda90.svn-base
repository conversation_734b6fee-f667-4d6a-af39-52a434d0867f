package com.shunwang.marketing.service.marketing.impl;

import com.shunwang.marketing.cacheService.CacheKeyConstant;
import com.shunwang.marketing.cacheService.RedisOperation;
import com.shunwang.marketing.context.UserContext;
import com.shunwang.marketing.dao.BaseMapper;
import com.shunwang.marketing.dao.marketing.BagOrderMapper;
import com.shunwang.marketing.dao.marketing.GiftBagMapper;
import com.shunwang.marketing.dao.marketing.PrizeRecordMapper;
import com.shunwang.marketing.dao.marketing.WelfareMapper;
import com.shunwang.marketing.enums.marketing.*;
import com.shunwang.marketing.exception.CommonException;
import com.shunwang.marketing.manager.paopao.SendGiftService;
import com.shunwang.marketing.manager.request.game.QueryCouponRequest;
import com.shunwang.marketing.manager.response.game.QueryCouponResponse;
import com.shunwang.marketing.manager.service.GameServiceClient;
import com.shunwang.marketing.pojo.marketing.*;
import com.shunwang.marketing.pojo.marketing.vo.WelfareMscVo;
import com.shunwang.marketing.query.marketing.BagOrderQuery;
import com.shunwang.marketing.query.marketing.GiftBagQuery;
import com.shunwang.marketing.send.email.EmailSender;
import com.shunwang.marketing.service.impl.BaseServiceImpl;
import com.shunwang.marketing.service.marketing.*;
import com.shunwang.marketing.service.personal.MemberAccountBindService;
import com.shunwang.marketing.util.DateUtil;
import com.shunwang.marketing.util.MemberUtil;
import com.shunwang.util.lang.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Component
public class GiftBagServiceImpl extends BaseServiceImpl<GiftBag> implements GiftBagService {
    @Autowired
    private GiftBagMapper giftBagMapper;
    @Autowired
    private WelfareMapper welfareMapper;
    @Autowired
    private BagOrderMapper bagOrderMapper;
    @Autowired
    private PrizeRecordMapper prizeRecordMapper;
    @Autowired
    private PrizeService prizeService;
    @Autowired
    private PrizeRecordService prizeRecordService;
    @Autowired
    private WelfareService welfareService;
    @Autowired
    private CdkeyService cdkeyService;
    @Autowired
    private MemberTagService memberTagService;
    @Autowired
    private RedPacketRecordService redPacketRecordService;
    @Autowired
    private SendGiftService sendGiftService;
    @Autowired
    private MemberAccountBindService memberAccountBindService;
    @Autowired
    BagOrderService bagOrderService;
    @Autowired
    private EmailSender emailSender;
    @Autowired
    RedisOperation redisOperation;
    @Autowired
    GiftBagService giftBagService;

    @Override
    public BaseMapper getDao() {
        return giftBagMapper;
    }


    /**
     * 根据渠道号获取展示给用户的礼包
     * @param channel
     * @return
     */
    public List<GiftBag> getByChannel(String channel, Integer memberId){

        //如果用户没有精准标签，返回没有配置标签的礼包
        List<MemberTag> memberTagList = memberTagService.findByMemberId(memberId);
        if (memberTagList == null || memberTagList.size() == 0) {
            return getWithNoTagsUser(channel);
        }

        //如果用户有标签，则进行标签匹配
        GiftBagQuery query = new GiftBagQuery();
        query.setPlatform(CommonEnum.PlatformEnum.MINIAPP.getValue());
        query.setChannel(channel);
        query.setState(GiftBagEnum.StateEnum.ONLINE.getValue());

        List<GiftBag> bagList = this.find(query);
        if (bagList == null || bagList.size() < 1) {
            return null;
        }
        List<GiftBag> result = bagList.stream().filter((giftBag -> {
            //判断库存是否充足||是否已经购买过
            if (!checkStock(giftBag)
                    || giftBagService.isReachLimit(UserContext.getUserId(), null, giftBag)) {
                return false;
            }

            List<Prize> prizes = prizeService.getByBagId(giftBag.getId());
            for (Prize prize : prizes) {
                //判断奖品和权益是否都数量充足，以及都在有效期内
                if (!prizeService.checkPrize(prize)) {
                    return false;
                }
            }
            //是否匹配用户精准标签
            if (!memberTagService.isMatching(giftBag.getTags(), UserContext.getUserId())) {
                return false;
            }
            giftBag.setPrizes(prizes);
            return true;
        })).sorted(Comparator.comparingInt(GiftBag::getSort)).collect(Collectors.toList());


        return CollectionUtils.isEmpty(result) ? getWithNoTagsUser(channel) : result;
    }

    /**
     * 只获取匹配用户标签的一个礼包
     * @param channel
     * @param memberId
     * @return
     */
    public GiftBag getMatchByChannel(String channel, Integer memberId, Integer platform){
        if (memberId == null) {
            return null;
        }
        //如果用户没有精准标签，返回没有配置标签的礼包
        List<MemberTag> memberTagList = memberTagService.findByMemberId(memberId);
        if (memberTagList == null || memberTagList.size() == 0) {
            return null;
        }
        //如果用户有标签，则进行标签匹配
        GiftBagQuery query = new GiftBagQuery();
        query.setPlatform(platform);
        query.setChannel(channel);
        query.setState(GiftBagEnum.StateEnum.ONLINE.getValue());

        List<GiftBag> bagList = this.find(query);
        if (bagList == null || bagList.size() < 1) {
            return null;
        }
        return bagList.stream().filter((giftBag -> {
            //判断库存是否充足
            if (!checkStock(giftBag)) {
                return false;
            }

            List<Prize> prizes = prizeService.getByBagId(giftBag.getId());
            for (Prize prize : prizes) {
                //判断奖品和权益是否都数量充足，以及都在有效期内
                if (!prizeService.checkPrize(prize)) {
                    return false;
                }
            }
            //是否匹配用户精准标签
            if (!memberTagService.isMatching(giftBag.getTags(), UserContext.getUserId())) {
                return false;
            }
            giftBag.setPrizes(prizes);
            return true;
        })).findFirst().orElse(null);
    }

    @Override
    public List<GiftBag> findOnlineByIds(List<Integer> ids) {
        if(ids == null || ids.isEmpty()){
            return Collections.emptyList();
        }
        return giftBagMapper.findOnlineByIds(ids);
    }

    @Override
    public boolean isReachLimit(Integer memberId, Integer bagId, GiftBag giftBag) {
        if ((bagId == null && giftBag == null) || memberId == null) {
            throw new CommonException("参数异常");
        }
        if (giftBag == null) {
            giftBag = giftBagService.get(bagId);
        }
        if (giftBag.getPurchaseLimit() == null) {
            return false;
        }
        BagOrderQuery query = new BagOrderQuery();
        query.setMemberId(memberId);
        query.setBagId(giftBag.getId());
        query.setState(BagOrderEnum.StateEnum.SUCCESS.getValue());
        return bagOrderMapper.findCnt(query) >= giftBag.getPurchaseLimit();
    }

    /**
     * 检查礼包以及礼包内奖品的库存
     * @param giftBag
     * @return
     */
    public boolean checkStock(GiftBag giftBag){
        emailSender.buildStockWarnEmail(giftBag.getCnt(),"礼包库存报警，id=" + giftBag.getId() + ",数量：" + giftBag.getCnt());
        //检查礼包库存
        if (giftBag.getCnt() < 1){
            log.info("礼包库存不足，礼包id：[{}]", giftBag.getId());
            return false;
        }
        //检查奖品库存
        List<Prize> prizes = prizeService.getByBagId(giftBag.getId());
        for (Prize prize : prizes){
            //检查奖品对应权益库存
            Welfare welfare = welfareService.get(prize.getWelfareId());
            emailSender.buildStockWarnEmail(welfare.getCnt(),"权益库存报警，id=" + welfare.getId() + ",数量：" + welfare.getCnt());
            if (welfare.getCnt() < 1){
                log.info("权益库存不足，权益id：[{}]", welfare.getId());
                return false;
            }
            if (welfare.getType().intValue() == WelfareEnum.TypeEnum.PC_CDKEY.getValue()){
                List<Cdkey> couldUse = cdkeyService.findByWelfare(welfare.getId());
                if (couldUse == null || couldUse.isEmpty()){
                    emailSender.buildStockWarnEmail(0,"cdkey库存报警，权益id=" + welfare.getId() + ",数量：0" );
                    log.info("权益对应的cdkey库存不足，权益id：[{}]", welfare.getId());
                    return false;
                }
            }
        }

        return true;
    }


    /**
     * 返回前端展示需要的我的礼包数据
     * @return
     */
    public List<GiftBag> myGiftBag(List<BagOrder> orderList, Integer memberId){
        if (CollectionUtils.isEmpty(orderList)){
            return new ArrayList<>();
        }
        List<GiftBag> giftBags = new ArrayList<>();
        String mobile = memberAccountBindService.findUserPhoneByWeixin(memberId);
        for (BagOrder order : orderList){
            GiftBag giftBag = giftBagMapper.get(order.getBagId());
            if (giftBag == null){
                continue;
            }
            //此礼包下的用户红包列表
            List<WelfareMscVo> userRedPacket = welfareMapper.getRedPacketByBagOrder(order.getOrderNo());
            for (WelfareMscVo vo : userRedPacket){
                Welfare welfare = welfareService.get(vo.getWelfareId());
                vo.setBusinessLine(welfare.getBusinessLine());
                if (welfare == null){
                    vo.setIsExpired(PrizeRecordEnum.IsExpiredEnum.EXPIRED.getValue());
                    continue;
                }
                if (DateUtil.compare(new Date(), welfare.getEndTime(), 1) > 0){
                    vo.setIsExpired(PrizeRecordEnum.IsExpiredEnum.EXPIRED.getValue());
                } else {
                    vo.setIsExpired(PrizeRecordEnum.IsExpiredEnum.NOT_EXPIRED.getValue());
                }

                //查询更新红包状态
                toUpdRedPacketState(vo, welfare, mobile);
            }
            giftBag.setUserRedPacket(userRedPacket);
            //此礼包下的用户卡券列表
            List<PrizeRecord> userPrize = prizeRecordMapper.getUserPrizeListByBagOrderNo(order.getOrderNo());
            for (PrizeRecord prizeRecord : userPrize){
                Welfare welfare = welfareService.get(prizeRecord.getWelfareId());
                if (welfare == null){
                    prizeRecord.setIsExpired(PrizeRecordEnum.IsExpiredEnum.EXPIRED.getValue());
                    continue;
                }
                if (DateUtil.compare(new Date(), welfare.getEndTime(), 1) > 0){
                    prizeRecord.setIsExpired(PrizeRecordEnum.IsExpiredEnum.EXPIRED.getValue());
                } else {
                    prizeRecord.setIsExpired(PrizeRecordEnum.IsExpiredEnum.NOT_EXPIRED.getValue());
                }
            }
            giftBag.setUserPrize(userPrize);

            giftBags.add(giftBag);
        }

        return giftBags;
    }

    @Override
    public List<GiftBag> findNoTagsData(String channel) {
        return giftBagMapper.findNoTagsData(channel);
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void receiveGiftBag(Integer bagId) {
        //将礼包奖品发放给用户
        GiftBag giftBag = get(bagId);

        try {
            bagOrderService.buyFreeBag(giftBag);
        } catch (Exception e) {
            log.error("礼包领取失败", e);
            throw new CommonException("礼包领取失败" + e.getMessage());
        }

    }

    @Override
    public Object buy(GiftBag bag, boolean limit) {
        boolean lock = redisOperation.setIfAbsent(CacheKeyConstant.BUY_GIFT + bag.getId() + "_" + MemberUtil.getNotNullMemberId(), "1", 5, TimeUnit.SECONDS);
        if (!lock) {
            throw new CommonException("操作频繁");
        }
        if (limit && giftBagService.isReachLimit(MemberUtil.getNotNullMemberId(), null, bag)) {
            throw new CommonException(-2, "已经达到购买限制");
        }
        giftBagService.receiveGiftBag(bag.getId());
        return null;
    }

    private List<GiftBag> getWithNoTagsUser(String channel) {
        List<GiftBag> list = findNoTagsData(channel);
        if (list == null || list.isEmpty()) {
            return null;
        }
        List<GiftBag> result = list.stream().filter((giftBag -> {
            //判断库存是否充足
            if (!checkStock(giftBag) || giftBagService.isReachLimit(UserContext.getUserId(), null,giftBag)) {
                return false;
            }


            List<Prize> prizes = prizeService.getByBagId(giftBag.getId());
            for (Prize prize : prizes) {
                //判断奖品和权益是否都数量充足，以及都在有效期内
                if (!prizeService.checkPrize(prize)) {
                    return false;
                }
            }
            giftBag.setPrizes(prizes);
            return true;
        })).sorted(Comparator.comparingInt(GiftBag::getSort)).collect(Collectors.toList());

        return result;
    }

    private WelfareMscVo toUpdRedPacketState(WelfareMscVo vo, Welfare welfare, String mobile){
        if (vo.getIsExpired() == PrizeRecordEnum.IsExpiredEnum.EXPIRED.getValue()){
            return vo;
        }
        RedPacketRecord redPacketRecord = redPacketRecordService.get(vo.getRecordId());

        if (StringUtil.isNotBlank(redPacketRecord.getOrderNo()) && redPacketRecord.getOrderNo().startsWith("GAME")){
            QueryCouponRequest request = new QueryCouponRequest(redPacketRecord.getOrderNo());
            QueryCouponResponse response = GameServiceClient.execute(request);
            //优惠券已核销
            if (QueryCouponResponse.Code.USED.getCode().equals(response.getCode())) {
                redPacketRecord.setState(RedPacketRecordEnum.StateEnum.USED.getValue());
                redPacketRecord.setUseTime(new Date());
                redPacketRecordService.update(redPacketRecord);
                vo.setState(RedPacketRecordEnum.StateEnum.USED.getValue());
            }
        }

        //泡泡券发放时，用户可能没有手机号，当发现用户有手机号时，补发红包
        if (WelfareEnum.BussinessLineEnum.PAOPAO.getValue() == welfare.getBusinessLine().intValue() &&
                redPacketRecord.getState() == RedPacketRecordEnum.StateEnum.NOT_SEND.getValue()
                && StringUtil.isNotBlank(mobile)){
            Integer giftType = WelfareEnum.BussinessTypeEnum.TIME.getValue() == welfare.getBusinessType().intValue() ? 1 : 2;
            boolean result = sendGiftService.executeSendGift(UserContext.getUserId(), giftType, welfare.getGoodsId(), redPacketRecord.getOrderNo());
            if (result){
                redPacketRecord.setState(RedPacketRecordEnum.StateEnum.NOT_USED.getValue());
                redPacketRecordService.update(redPacketRecord);
            }
        }

        return vo;
    }

    private int decStock(Integer bagId) {
        return giftBagMapper.decStock(bagId);
    }
}
