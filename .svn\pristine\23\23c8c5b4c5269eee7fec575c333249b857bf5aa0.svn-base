package com.shunwang.marketing.dao.marketing;

import com.shunwang.marketing.dao.BaseMapper;
import com.shunwang.marketing.pojo.marketing.AdGameConfig;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AdGameConfigMapper extends BaseMapper<AdGameConfig> {


    List<AdGameConfig> findByBarIdAndResourceIds(@Param("barId") Integer barId, @Param("resourceIds") List<Integer> resourceIds);
}
