package com.shunwang.marketing.controller.pc;

import com.shunwang.marketing.context.ThreadLocalContext;
import com.shunwang.marketing.enums.marketing.CommonEnum;
import com.shunwang.marketing.enums.marketing.PromotionPopupWindowEnum;
import com.shunwang.marketing.pojo.marketing.vo.PromotionPopupWindowVo;
import com.shunwang.marketing.query.marketing.PromotionPopupWindowQuery;
import com.shunwang.marketing.result.Result;
import com.shunwang.marketing.result.ResultGenerator;
import com.shunwang.marketing.service.marketing.PromotionPopupWindowService;
import com.shunwang.util.lang.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 推广弹窗接口
 */
@Controller
@Slf4j
@RequestMapping("/pc/ppw")
public class PcPromotionPopupWindowController {
    @Autowired
    PromotionPopupWindowService promotionPopupWindowService;


    /**
     * 获取推广弹窗配置
     * @param pageId 页面ID
     * @param type 类型  2抽奖类型
     * @param channel 渠道 pc端传yunhai
     * @param barId
     * @param memberId
     * @param request
     * @return
     */
    @ResponseBody
    @RequestMapping("/commend")
    public Result<PromotionPopupWindowVo> commend(
            @RequestParam(value = "pageId") String pageId,
            @RequestParam(value = "type", required = false) Integer type,
            @RequestParam(value = "channel") String channel,
            @RequestParam(value = "barId", required = false) String barId,
            @RequestParam(value = "memberId", required = false) Integer memberId,
            @RequestParam(value = "checkStrategy", defaultValue = "true") Boolean checkStrategy,
            HttpServletRequest request
    ) {
        if (StringUtil.isBlank(channel)) {
            return ResultGenerator.newSystemErrorResult("渠道不能为空");
        }
        if (StringUtil.isBlank(pageId)) {
            return ResultGenerator.newSystemErrorResult("页面ID不能为空");
        }
        ThreadLocalContext.setTempData(ThreadLocalContext.BAR_ID, barId);
        ThreadLocalContext.setTempData(ThreadLocalContext.MEMBER_ID, memberId);
        //pc端不区分渠道，仅根据页面ID
        PromotionPopupWindowQuery query = new PromotionPopupWindowQuery();
        query.setPlatform(PromotionPopupWindowEnum.PlatformEnum.MENU.getValue());
        query.setPageId(pageId);
        query.setType(type);
        query.setKind(PromotionPopupWindowEnum.KindEnum.WINDOW.getValue());
        List<PromotionPopupWindowVo> list = promotionPopupWindowService.findVo(query);
        //不校验策略
        if (!checkStrategy) {
            return CollectionUtils.isEmpty(list) ? ResultGenerator.newEmptySystemErrorResult() : ResultGenerator.newSuccessResult(list.get(0));
        }
        list.forEach((item)->{item.setChannel(channel);});

        //根据策略选择弹窗返回
        PromotionPopupWindowVo result = promotionPopupWindowService.commend(list);

        return result == null ? ResultGenerator.newEmptySystemErrorResult() : ResultGenerator.newSuccessResult(result);

    }

}
