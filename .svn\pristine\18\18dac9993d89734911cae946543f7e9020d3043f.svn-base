package com.shunwang.marketing.enums.marketing;

import lombok.Getter;

public interface AiGameEnum {

    enum TypeEnum {
        GUESS_ROLE(1, "猜角色"),
        AI_REPORT(2, "AI报告"),
        ROLE_PLAY(3, "人物扮演");

        TypeEnum(int value, String name) {
            this.value = value;
            this.name = name;
        }

        private @Getter Integer value;
        private @Getter String name;
    }

    enum RewardTypeEnum {
        LOTTERY_TIMES(1, "抽奖次数"),
        RIGHTS(2, "权益");

        RewardTypeEnum(int value, String name) {
            this.value = value;
            this.name = name;
        }

        private @Getter Integer value;
        private @Getter String name;
    }

    enum StateEnum {
        ONLINE(1, "上线"),
        OFFLINE(2, "下线"),
        ;

        StateEnum(int value, String name) {
            this.value = value;
            this.name = name;
        }

        private @Getter Integer value;
        private @Getter String name;
    }
} 
