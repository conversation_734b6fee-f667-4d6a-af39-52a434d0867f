package com.shunwang.marketing.pojo.marketing;

import com.shunwang.marketing.constant.UploadContstant;
import com.shunwang.marketing.context.DomainContext;
import com.shunwang.marketing.pojo.BaseObject;
import com.shunwang.util.lang.StringUtil;
import lombok.Data;

import java.util.Date;
@Data
public class ResourcePrivilege extends BaseObject {
	private static final long serialVersionUID = 1L;
	private Integer id;
	private String resourceName;
	private String resourceAlias;
	private Integer resourceId;
	private Integer gradingState;
	private Integer onlineState;
	private String hoverImg;
	private String userAdd;
	private Date timeAdd;
	private String userEdit;
	private Date timeEdit;
	private Integer openType;
	private String openUrl;
	private Integer type;
	private String description;
	private Integer sort;
	private String detail;
	private String showImg;
	private String args;
	private Date endTime;
	private Date startTime;


	public String getHoverImg() {
		return DomainContext.getBackStaticServer() + UploadContstant.UPLOAD_DIRECTORY + hoverImg;
	}
	public String getShowImg() {
		if (StringUtil.isNotBlank(showImg)){
			return DomainContext.getBackStaticServer() + UploadContstant.UPLOAD_DIRECTORY + showImg;
		}
		return null;
	}
}
