package com.shunwang.marketing.pojo.marketing;

import com.shunwang.marketing.pojo.BaseObject;

public class PondPrize extends BaseObject {
	private static final long serialVersionUID = 1L;
	private Integer id;
	private Integer pondId;
	private Integer prizeId;
	public Integer getId() {
		return id;
	}
	public void setId(Integer id) {
		this.id = id;
	}
	public Integer getPondId() {
		return pondId;
	}
	public void setPondId(Integer pondId) {
		this.pondId = pondId;
	}
	public Integer getPrizeId() {
		return prizeId;
	}
	public void setPrizeId(Integer prizeId) {
		this.prizeId = prizeId;
	}
}
