package com.shunwang.marketing.enums.marketing;

import lombok.Getter;

public interface OutRequestEnum {

    enum TypeEnum {
        AD_BAR(1, "广告网吧等级"),
        AD_GAME(2, "广告游戏等级"),
        ;

        TypeEnum(int value, String name) {
            this.value = value;
            this.name = name;
        }

        private @Getter Integer value;
        private @Getter String name;
    }

    enum StateEnum {
        UNDO(1, "未处理"),
        SUCCESS(2, "成功"),
        FAIL(3, "失败");

        StateEnum(int value, String name) {
            this.value = value;
            this.name = name;
        }

        private @Getter
        Integer value;
        private @Getter
        String name;
    }


}
