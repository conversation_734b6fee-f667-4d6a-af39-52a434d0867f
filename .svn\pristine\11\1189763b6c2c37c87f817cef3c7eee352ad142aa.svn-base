package com.shunwang.marketing.manager.response.swpay;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 */
@Getter
@Setter
@ToString
public class TencentAppOrderResponse extends BaseSwpayJsonResponse {

    private final BigDecimal ZERO = new BigDecimal("0");
    private String disburseNo;
    private String payNo;
    private String serverCallbackUrl;
    private String body;

    @Override
    public TencentAppOrderResponse parse() {
        super.parse();
        if (isSuccess()) {
            disburseNo = getJsonObject().get("disburseNo").getAsString();
            payNo = getJsonObject().get("payNo").getAsString();
            body = getJsonObject().get("body").getAsString();
        }
        return this;
    }

}
