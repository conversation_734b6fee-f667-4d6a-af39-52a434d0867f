package com.shunwang.marketing.service.marketing.impl;

import com.google.gson.JsonObject;
import com.shunwang.marketing.context.ThreadLocalContext;
import com.shunwang.marketing.dao.BaseMapper;
import com.shunwang.marketing.dao.marketing.AiGameMapper;
import com.shunwang.marketing.enums.marketing.*;
import com.shunwang.marketing.exception.CommonException;
import com.shunwang.marketing.pojo.marketing.*;
import com.shunwang.marketing.pojo.marketing.vo.AiGameInfoVo;
import com.shunwang.marketing.pojo.marketing.vo.RewardVo;
import com.shunwang.marketing.query.marketing.AiGameItemQuery;
import com.shunwang.marketing.service.impl.BaseServiceImpl;
import com.shunwang.marketing.service.marketing.*;
import com.shunwang.marketing.util.MemberUtil;
import com.shunwang.util.json.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;

@Component
@Slf4j
public class AiGameServiceImpl extends BaseServiceImpl<AiGame> implements AiGameService {
    @Autowired
    private AiGameMapper aiGameMapper;
    @Autowired
    AiGameItemService aiGameItemService;
    @Autowired
    ImageService imageService;
    @Autowired
    AiSessionService aiSessionService;
    @Autowired
    AiRecordService aiRecordService;
    @Autowired
    LotteriesCntService lotteriesCntService;
    @Autowired
    PondService pondService;
    @Autowired
    ScoreService scoreService;
    @Autowired
    GiftBagService giftBagService;
    @Autowired
    WelfareService welfareService;
    @Autowired
    BussMemberRelationService bussMemberRelationService;
    @Autowired
    AiService aiService;
    @Autowired
    AiModelService aiModelService;

    @Override
    public BaseMapper getDao() {
        return aiGameMapper;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public AiGameInfoVo getGameAndSessionInfo(Integer gameId) {
        AiGameInfoVo result = new AiGameInfoVo();
        AiGameItem period = aiGameItemService.getPeriod();
        if (period == null) {
            log.info("未到游戏:{}开放时间", gameId);
            return result;
        }
        AiGame aiGame = get(gameId);
        if (AiGameEnum.StateEnum.OFFLINE.getValue().equals(aiGame.getState())) {
            log.info("游戏已下线:{}", gameId);
            return result;
        }
        AiGameItemQuery aiGameItemQuery = new AiGameItemQuery();
        aiGameItemQuery.setGameItemConfig(aiGame.getGameItemConfig());
        AiGameItem aiGameItem = aiGameItemService.findOnline(aiGameItemQuery);
        if (aiGameItem == null) {
            log.info("{}不存在游戏项目", gameId);
            return result;
        }
        imageService.populate(aiGame.getShareImg(), result::setShareImg);
        imageService.populate(aiGame.getBgImg(), result::setBgImgPath);
        imageService.populate(aiGameItem.getRewardImg(), result::setRewardImgPath);
        result.setGameId(aiGame.getId());
        result.setGameItemId(aiGameItem.getId());
        BeanUtils.copyProperties(aiGame, result);
        result.setKeyword(aiGameItem.getKeyword());
        result.setOpen(true);
        result.setFirstPlay(bussMemberRelationService.findOne(MemberUtil.getNotNullMemberId(), BussMemberRelationEnum.BussTypeEnum.AI_GAME_GUIDE.getValue(), gameId) == null);
        if (result.isFirstPlay()) {
            bussMemberRelationService.save(new BussMemberRelation(MemberUtil.getNotNullMemberId(), BussMemberRelationEnum.BussTypeEnum.AI_GAME_GUIDE.getValue(), gameId));
        }


        //会话信息
        AiSession session = aiSessionService.getSessionInPeriod(gameId, aiGameItem.getId(), period.getStartTime(), period.getEndTime());
        if (session == null) {
            session = aiSessionService.createGameSession(gameId);
        }
        List<AiRecord> record = aiRecordService.findBySession(session.getId());
        if (CollectionUtils.isEmpty(record)) {
            AiRecord questionRecord = new AiRecord();
            questionRecord.setMemberId(MemberUtil.getNotNullMemberId());
            questionRecord.setType(AiRecordEnum.TypeEnum.SYSTEM.getValue());
            questionRecord.setContent(aiGameItem.getPrompt());
            questionRecord.setTimeAdd(new Date());
            questionRecord.setConsumeToken(0);
            questionRecord.setSession(session.getId());

            AiRecord answerRecord = new AiRecord();
            answerRecord.setMemberId(MemberUtil.getNotNullMemberId());
            answerRecord.setType(AiRecordEnum.TypeEnum.ANSWER.getValue());
            JsonObject jsonObject = new JsonObject();
            jsonObject.addProperty("content", aiGameItem.getFirstTalk());
            jsonObject.addProperty("right", false);
            answerRecord.setContent(GsonUtil.toJson(jsonObject));
            answerRecord.setTimeAdd(new Date());
            answerRecord.setSession(session.getId());
            answerRecord.setConsumeToken(0);
            aiRecordService.save(questionRecord);
            aiRecordService.save(answerRecord);
        }
        result.setSessionId(session.getSessionId());
        result.setFinish(session.isFinish());
        result.setRewardReceived(session.isReceiveReward());
        result.setTimes((int) record.stream().filter((item) -> AiRecordEnum.TypeEnum.USER.getValue().equals(item.getType())).count());
        return result;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public RewardVo receiveReward(String sessionId) {
        RewardVo result = new RewardVo();
        AiSession session = aiSessionService.getBySessionId(sessionId);
        if (session.isReceiveReward()) {
            throw new CommonException("已经领取过奖励");
        }
        if (!session.isFinish()) {
            throw new CommonException("尚未完成任务");
        }
        AiGameItem aiGameItem = aiGameItemService.get(session.getExt2());
        result.setType(aiGameItem.getRewardType());
        imageService.populate(aiGameItem.getRewardImg(), result::setRewardImgPath);

        if (AiGameItemEnum.RewardTypeEnum.LOTTERY_TIMES.getValue().equals(aiGameItem.getRewardType())) {
            Pond pond = new Pond();
            pond.setType(PondEnum.TypeEnum.FREE.getValue());
            pond.setChannel(ThreadLocalContext.getChannel());
            lotteriesCntService.addOtherCnt(session.getMemberId(), pondService.getRandomPondFallback(pond).getId(), Integer.parseInt(aiGameItem.getReward()));
            result.setReward(Integer.parseInt(aiGameItem.getReward()));
        } else if (AiGameItemEnum.RewardTypeEnum.RIGHTS.getValue().equals(aiGameItem.getRewardType())) {
            Welfare welfare = welfareService.get(Integer.parseInt(aiGameItem.getReward()));
            welfareService.sendWelfare(welfare, "猜角色", ScoreRecordEnum.SourceTypeEnum.GAME, true);
            result.setReward(1);
            if (WelfareEnum.TypeEnum.RED_PACKET.getValue().equals(welfare.getType())) {
                result.setType(5);
            }
        } else if (AiGameItemEnum.RewardTypeEnum.SCORE.getValue().equals(aiGameItem.getRewardType())) {
            scoreService.updateScoreAndTakeRecord(Integer.parseInt(aiGameItem.getReward()), ScoreRecordEnum.TypeEnum.ADD, ScoreRecordEnum.SourceTypeEnum.GAME.getValue(), "猜角色");
            result.setReward(Integer.parseInt(aiGameItem.getReward()));
        } else {
            giftBagService.receiveGiftBag(Integer.parseInt(aiGameItem.getReward()));
            result.setReward(1);
        }

        session.setExt3(AiSessionEnum.AiGameExt3Enum.REWARD.getValue());
        aiSessionService.update(session);
        return result;
    }


}
