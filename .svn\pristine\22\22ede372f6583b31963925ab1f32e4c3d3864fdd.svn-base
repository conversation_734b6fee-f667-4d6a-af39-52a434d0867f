package com.shunwang.marketing.enums.marketing;

import lombok.Getter;
import lombok.Setter;

public interface PondEnum {

     enum StateEnum {
         ONLINE(1, "上线"),
         OFFLINE(2, "下线"),
;
         StateEnum(int value, String name) {
            this.value = value;
            this.name = name;
        }

        private @Getter @Setter int value;
        private @Getter @Setter String name;
    }

    enum TypeEnum {
        FREE(1, "免费"),
        PAY(2, "付费"),
        ;

        TypeEnum(int value, String name) {
            this.value = value;
            this.name = name;
        }

        private @Getter Integer value;
        private @Getter String name;
    }



}
