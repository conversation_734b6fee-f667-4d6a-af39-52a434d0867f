package com.shunwang.marketing.pojo.marketing;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.shunwang.marketing.context.DomainContext;
import com.shunwang.marketing.pojo.BaseObject;
import lombok.Data;

import java.util.Date;

@Data
public class LandingPage extends BaseObject {
	private static final long serialVersionUID = 1L;
	@JsonIgnore
	private Integer id;
	private Integer type;
	private String name;
	@JsonIgnore
	private Integer state;
	private String content;
	private String background;
	private Integer icon;
	private Integer iconClick;
	private Integer iconWinId;
	@JsonIgnore
	private Date timeAdd;
	@JsonIgnore
	private Date timeEdit;
	@JsonIgnore
	private String userAdd;
	@JsonIgnore
	private String userEdit;
	private String remark;
	private String imagePath;

	public String getIcon(){
		return DomainContext.getBackStaticUploadServer() + imagePath;
	}
}
