(function(t){function e(e){for(var i,s,o=e[0],c=e[1],l=e[2],d=0,p=[];d<o.length;d++)s=o[d],Object.prototype.hasOwnProperty.call(n,s)&&n[s]&&p.push(n[s][0]),n[s]=0;for(i in c)Object.prototype.hasOwnProperty.call(c,i)&&(t[i]=c[i]);u&&u(e);while(p.length)p.shift()();return r.push.apply(r,l||[]),a()}function a(){for(var t,e=0;e<r.length;e++){for(var a=r[e],i=!0,o=1;o<a.length;o++){var c=a[o];0!==n[c]&&(i=!1)}i&&(r.splice(e--,1),t=s(s.s=a[0]))}return t}var i={},n={cafePrivilegeHub2:0},r=[];function s(e){if(i[e])return i[e].exports;var a=i[e]={i:e,l:!1,exports:{}};return t[e].call(a.exports,a,a.exports,s),a.l=!0,a.exports}s.m=t,s.c=i,s.d=function(t,e,a){s.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:a})},s.r=function(t){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},s.t=function(t,e){if(1&e&&(t=s(t)),8&e)return t;if(4&e&&"object"===typeof t&&t&&t.__esModule)return t;var a=Object.create(null);if(s.r(a),Object.defineProperty(a,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var i in t)s.d(a,i,function(e){return t[e]}.bind(null,i));return a},s.n=function(t){var e=t&&t.__esModule?function(){return t["default"]}:function(){return t};return s.d(e,"a",e),e},s.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},s.p="https://static.kedou.com/marketingWeb/static/1.4.0/cafePrivilegeHub/";var o=window["webpackJsonp"]=window["webpackJsonp"]||[],c=o.push.bind(o);o.push=e,o=o.slice();for(var l=0;l<o.length;l++)e(o[l]);var u=c;r.push([0,"chunk-vendors"]),a()})({0:function(t,e,a){t.exports=a("39a2")},"000d":function(t,e,a){},"04e4":function(t,e,a){"use strict";a("b3bc")},"0f88":function(t,e,a){"use strict";a("260b")},2147:function(t,e,a){},2331:function(t,e,a){},"260b":function(t,e,a){},2667:function(t,e,a){"use strict";a("000d")},"278f":function(t,e,a){},"286c":function(t,e,a){"use strict";a("f61f")},"29cb":function(t,e,a){},"2aec":function(t,e,a){},"2c90":function(t,e,a){"use strict";a("4305")},"2f8a":function(t,e,a){"use strict";a("602b")},"2fbf":function(t,e,a){"use strict";a("9879")},"33b6":function(t,e,a){"use strict";a("2147")},"35ce":function(t,e,a){"use strict";a("7bd8")},"39a2":function(t,e,a){"use strict";a.r(e);a("560b"),a("450d");var i=a("dcdc"),n=a.n(i),r=(a("a7cc"),a("df33")),s=a.n(r),o=(a("0fb7"),a("f529")),c=a.n(o),l=(a("e260"),a("e6cf"),a("cca6"),a("a79d"),a("2b0e")),u=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"body-wrap"},[t.externalConfig?t._e():a("div",[t.hasSwiper?a("Swiper",{attrs:{dataInfo:t.swipeData},on:{updateData:t.updateData,cdkeyShow:t.cdKeyShow}}):t._e(),t.showTop||t.hasSwiper?a("TopNav",{attrs:{userInfo:t.userInfo,mainPageOption:t.mainPageOption},on:{login:t.login,vipShow:t.vipShow,logout:t.logout,draw:t.openDrawPopup}}):t._e(),t.showTop||t.hasSwiper?a("div",{staticStyle:{"margin-top":"84px"}}):t._e(),a("div",{staticClass:"bg"},[a("FastLink",{attrs:{dataInfo:t.fastLinkData}}),t.hasSwiper?t._e():a("HeaderContainer",{attrs:{userInfo:t.userInfo,mainPageOption:t.mainPageOption,bannerInfo:t.topBanner},on:{logout:t.logout,login:t.login,vipShow:t.vipShow,draw:t.openDrawPopup}}),t.tabList.length>1?a("div",{staticClass:"tab_container"},t._l(t.tabList,(function(e,i){return a("span",{key:i,class:{active:e.id===t.activeTab},on:{click:function(a){return t.changeTab(e.id)}}},[t._v(t._s(e.tabName))])})),0):t._e(),t._l(t.allModules,(function(e,i){return a("div",{key:i},[1==e.structure?a("LRStructures",{attrs:{dataInfo:e,cIdx:i},on:{updateData:t.updateData,cdkeyShow:t.cdKeyShow}}):t._e(),3==e.structure?a("UpDownStructures",{attrs:{dataInfo:e,cIdx:i},on:{updateData:t.updateData,cdkeyShow:t.cdKeyShow}}):t._e(),4==e.structure?a("Unwind",{attrs:{dataInfo:e,cIdx:i},on:{updateData:t.updateData,cdkeyShow:t.cdKeyShow}}):t._e(),8==e.structure?a("TilingModule",{attrs:{dataInfo:e,cIdx:i},on:{updateData:t.updateData,cdkeyShow:t.cdKeyShow}}):t._e()],1)})),t.allModules.length||0!=t.loading?t._e():a("div",[a("div",{staticClass:"nodata-page"},[a("img",{staticClass:"no-data-img",attrs:{src:t.nodata,alt:""}}),a("span",[t._v("暂未找到相关特权")])])]),a("RightNav",{attrs:{bannerInfo:t.rightBanner,allModules:t.allModules,mainPageOption:t.mainPageOption,activeTab:t.activeTab}})],2)],1),t.externalConfig?a("div",{staticStyle:{height:"100vh"}},[a("div",{staticClass:"topContainer"},[a("span",{staticClass:"returnBtn",on:{click:function(e){return t.jumpExternal(null)}}},[a("img",{attrs:{src:t.returnPng}}),t._v(" 返回特权中心 ")])]),a("iframe",{attrs:{src:t.externalConfig.url,width:"100%",height:"100%",frameborder:"0"}})]):t._e(),t.loginOpen?a("Login",{attrs:{visible:t.loginOpen},on:{close:function(e){t.loginOpen=!1}}}):t._e(),t.vipDialogShow?a("VipDialog",{attrs:{visible:t.vipDialogShow,privilegeActiveTab:t.privilegeActiveTab},on:{close:function(e){t.vipDialogShow=!1}}}):t._e(),t.cdkeyDialogShow?a("CdkeyDialog",{attrs:{visible:t.cdkeyDialogShow,detail:t.cdKey},on:{close:function(e){t.cdkeyDialogShow=!1},updateData:t.updateData}}):t._e(),t.appDateInitFlag&&1==t.popupData.infoData.type?a("PpwDialog",{attrs:{popupData:t.popupData}}):t._e(),a("DrawPopup",{ref:"drawPopup",attrs:{visible:t.pondDialogShow,popupData:t.popupData},on:{vipShow:t.vipShow,close:function(e){t.pondDialogShow=!1},clickDraw:t.DrawPopupResultShow}}),t.DrawPopupResultDialogShow?a("DrawPopupResult",{ref:"DrawPopupResult",attrs:{visible:t.DrawPopupResultDialogShow,drawData:t.drawData},on:{vipShow:t.vipShow,close:function(e){t.DrawPopupResultDialogShow=!1}}}):t._e(),t.signInDialogShow?a("SignInDailogVue",{attrs:{visible:t.signInDialogShow},on:{vipShow:t.vipShow,close:function(e){t.signInDialogShow=!1}}}):t._e(),a("GiftBagDialog",{ref:"giftBagDialog",attrs:{visible:t.giftBagDialogShow},on:{vipShow:t.vipShow,close:function(e){t.giftBagDialogShow=!1}}})],1)},d=[],p=a("5530"),g=a("1da1"),m=(a("d3b7"),a("159b"),a("e9c4"),a("96cf"),function(){var t=this,e=t.$createElement,a=t._self._c||e;return 0==t.bannerInfo.state||1==t.bannerInfo.state?a("div",{staticClass:"header"},[a("div",{staticClass:"banner_container"},[0==t.bannerInfo.state?a("img",{staticClass:"defaultPng",attrs:{src:t.defaultPng}}):t._e(),1==t.bannerInfo.state?a("div",{staticClass:"content"},[a("swiper",{ref:"mySwiper",attrs:{options:t.swiperOption,id:"header_slider"}},t._l(t.bannerInfo.data,(function(e,i){return a("swiper-slide",{key:i,staticClass:"s_pic"},[a("div",{staticStyle:{cursor:"pointer"},on:{click:function(a){return t.linkClick(e)}}},[a("ImgError",{attrs:{src:e.imagePath}})],1)])})),1),a("div",{staticClass:"right",attrs:{id:"right_banner_name"}},t._l(t.bannerInfo.data,(function(e,i){var n;return a("div",{key:i},[a("div",{staticClass:"banner_name",class:(n={selected:t.sliderIdx===i},n["banner_name"+i]=!0,n),attrs:{title:e.name},on:{click:function(e){return t.changeBanner(i)}}},[t._v(" "+t._s(e.name)+" ")])])})),0)],1):t._e()]),a("div",{staticClass:"basic_container"},[a("img",{staticClass:"bg",attrs:{src:t.mainPageOption.personalCenterImgPath,alt:""}}),t.userInfo?t._e():a("div",{staticClass:"user_container",on:{click:t.login}},[a("div",{staticClass:"avatar"},[a("img",{attrs:{src:t.defaultLogin}})]),a("div",{staticClass:"text"},[t._v("请先登录")])]),t.userInfo?a("div",{staticClass:"user_container"},[a("div",{staticClass:"vip-shou",on:{click:t.vipShow}},[t._v("我的权益")]),a("span",{staticClass:"logout",on:{click:t.logout}},[t._v("退出")]),a("div",{staticClass:"avatar"},[a("img",{attrs:{src:t.userInfo.headImg||t.defaultAvatar}}),a("img",{staticClass:"vip_border",attrs:{src:t.vipBorder}})]),a("div",{staticClass:"text"},[t._v(t._s(t.userInfo.nickName))])]):t._e(),a("div",{staticClass:"link-bar"},[a("div",{staticClass:"link-signIn link",on:{click:function(e){return t.draw(1)}}}),a("div",{staticClass:"link-giftBag link",on:{click:function(e){return t.draw(2)}}}),a("div",{staticClass:"link-draw link",on:{click:function(e){return t.draw(3)}}})])])]):t._e()}),f=[],h=(a("b0c0"),a.p+"images/default.png"),v=a.p+"images/default_avatar.png",I=a.p+"images/default_user.png",w=a.p+"images/vip_border.png",b=a.p+"images/login_text.png",y=a("7212"),D=function(){var t,e=this,a=e.$createElement,i=e._self._c||a;return i("img",{staticClass:"Imgerrs",class:(t={},t[this.className]=!0,t),style:e.myStyle,attrs:{src:e.src,alt:"",referrerPolicy:"no-referrer"},on:{error:e.setErrimg}})},k=[],S=a.p+"images/opt.png",C={name:"ImgError",props:{src:{type:String,default:""},className:{type:String,default:""},myStyle:{type:Object,default:function(){return{}}}},methods:{setErrimg:function(t){var e=t.srcElement;e.src=S,this.className?e.className="img-error ".concat(this.className):e.className="img-error"}}},_=C,x=(a("d820"),a("2877")),P=Object(x["a"])(_,D,k,!1,null,"b627c00e",null),O=P.exports,B={startApp:function(t,e){try{window.external.startApp(t,e)}catch(a){return null}},getContextMenu:function(t){try{return window.external.getContextMenu(t)}catch(e){return[]}},onMenuClick:function(t,e){try{window.external.onMenuClick(t,e)}catch(a){return null}},checkResourceExistence:function(t){try{return window.external.checkResourceExistence(t)}catch(e){return t}},goToAppstoreDetailEx:function(t,e,a){try{window.external.goToAppstoreDetailEx(t,e,a)}catch(i){return null}},openInBrowser:function(t){var e=document.createElement("a");e.id="barview",e.style.display="none",e.target="_blank",e.href=t,document.querySelector("body").appendChild(e),document.getElementById("barview").click(),setTimeout((function(){document.querySelector("body").removeChild(e)}),500)},openUrlInClt:function(t){try{window.external.openUrlInClt(t.url,t.caption||"",t.id,3,t.defBrowser||0)}catch(e){this.openInBrowser(t.url)}},isAppExist:function(t){try{return window.external.isAppExist(1,t)}catch(e){return!1}},getLocalApps:function(){try{return window.external.getBarLocalApps()||[]}catch(t){return[]}},getAppIcon:function(t,e){try{return window.external.getAppIcon(t,e)}catch(a){return""}},getSearchResult:function(t,e){try{return e?window.external.getSearchResult(t,e):window.external.getSearchResult(t)}catch(a){return[]}},getGuid:function(){try{return window.external.getGuid()}catch(t){return null}},getAccurateUserId:function(){try{return window.external.getAccurateUserId()}catch(t){return""}},SsoLoginSuccessEx:function(t){try{return window.external.SsoLoginSuccessEx(JSON.stringify(t))}catch(e){return null}},login:function(){try{return window.external.login()}catch(t){return null}},logout:function(){try{return window.external.logout()}catch(t){return null}},getHotBarApps:function(t){try{return window.external.getHotBarApps(t)}catch(e){return[]}},getBarId:function(){try{return window.external.getBarId()}catch(t){return null}},openAppStoreMenu:function(t,e,a,i){try{window.external.openAppStoreMenu(t,e,a,i)}catch(n){return null}},goback:function(t){try{t?window.external.goback(t):window.external.goback()}catch(e){history.go(-1)}},reportData:function(t,e,a){try{a?window.external.ReportData(t,e,a):window.external.ReportData(t,e,"WEB_GL")}catch(i){return null}},getAppNameEx:function(t){try{return window.external.getAppNameEx(1,t)}catch(e){return""}},setPageType:function(t,e,a){try{window.external.setPageType(t,e,a)}catch(i){return null}},getIECookie:function(t,e){try{return window.external.getIECookie(t,e)}catch(a){return null}},openAppStoreUrl:function(t,e,a){try{window.external.openAppStoreUrl(t,e,a)}catch(i){window.location=e}},orderSuccess:function(t){try{window.external.orderSuccess(JSON.stringify(t))}catch(e){return null}},transParam:function(t,e,a){try{window.external.transParam(t,e,a)}catch(i){return null}},getPassportID:function(){try{return window.external.getPassportID()}catch(t){return""}},setSomeData:function(t){try{window.external.setSomeData(JSON.stringify(t))}catch(e){return null}},getSomeData:function(t){try{return window.external.getSomeData(t,"")}catch(e){return null}},openPersonCenter:function(t,e){try{window.external.openPersionCenter(t,e)}catch(a){window.location=e}},openChannel:function(t,e,a,i){try{window.external.openChannel(t,e,a,i)}catch(n){window.location=a}}},E=B,L=(a("a15b"),a("bc3a")),T=a.n(L),R=T.a.create({baseURL:"https://m.kedou.com",timeout:3e3});R.interceptors.request.use((function(t){return t}),(function(t){return Promise.reject(t)})),R.interceptors.response.use((function(t){var e=t.data;return e}),(function(t){return console.log("errr:",t.msg),Promise.reject(t)}));var j=R;function U(t){var e=t.method,a=t.params,i=t.url,n=t.data;return j("post"===e?{data:n,url:i,method:e,params:a}:{url:i,method:e,params:a})}a("25f0"),a("ac1f"),a("1276"),a("5319"),a("b680"),a("fb6a"),a("4d63"),a("c607"),a("2c3e"),a("841c"),a("5cc6"),a("907a"),a("9a8c"),a("a975"),a("735e"),a("c1ac"),a("d139"),a("3a7b"),a("d5d6"),a("82f8"),a("e91f"),a("60bd"),a("5f96"),a("3280"),a("3fcc"),a("ca91"),a("25a1"),a("cd26"),a("3c5d"),a("2954"),a("649e"),a("219c"),a("170b"),a("b39a"),a("72f7"),a("00b4");var A=a("2ef0"),M={encode:function(t){return t?(t+="",$.base64.btoa(t,!0)):""},decode:function(t){return t?$.base64.atob(t,!0):""}},N={rptType:{pv:1,pc:2},url_open:function(t){K({url:t.url,caption:t.title,id:t.id,defBrowser:1==t.type?1:0})},landing:function(t,e,a){var i=t.landing;if(1==i.type){var n=E.checkResourceExistence(i.game.game_id+";");n&&i.url?this.url_open({type:i.url_open_type,url:i.url,title:t.name,id:t.game_id}):n||G(i.game.game_id,e,a)}else i.game&&i.game.game_id&&G(i.game.game_id,e,a),this.url_open({type:i.url_open_type,url:i.url,title:t.name,id:t.game_id})}},G=function(t,e,a){a&&(e=e<10?"0"+e:e.toString(),e=parseInt(a+e)),z.getUrlVars&&z.getUrlVars()&&1==z.getUrlVars().debug&&console.log(e),E.startApp(t,e)},K=function(t){t.current?window.location=t.url:E.openUrlInClt({url:t.url,caption:M.encode(t.title),id:t.id,defBrowser:t.defBrowser||0})},V={libOpenType:{RESOURCE:1,INNER:2,GLBROWSER:3,SYSTEMBROWSER:4,GLCHANNEL:5,OPEN_RESOURCE_BEFORE_URL:6,OPEN_MENU:7,OPEN_TAG:8,NEWBROWSER:9,PERSONALBROWSER:10,SPECBROWSER:11},startLibApp:function(t,e){t=A.assignIn({},t);var a=this.libOpenType,i=e.page;if(i=i<10?"0"+i:i.toString(),t.openType||(t.openType=e.openType),t.landing&&t.isExpo)N.landing(t,e.page,e?e.type:null);else switch(t.hasOwnProperty("gameUrl")&&(t.openType=a.RESOURCE),parseInt(t.openType)){case a.RESOURCE:G(t.resourceId,e.page,e?e.type:null);break;case a.INNER:window.location=t.linkUrl;break;case a.GLBROWSER:z.isWW()?E.openInBrowser(t.linkUrl):K({url:t.linkUrl,title:e.title,id:e.id||0,defBrowser:0});break;case a.SYSTEMBROWSER:K({url:t.linkUrl,title:e.title,id:e.id||0,defBrowser:1});break;case a.GLCHANNEL:e&&e.type&&(i=parseInt(e.type+i)),E.openChannel(t.channelId,"","",i);break;case a.OPEN_RESOURCE_BEFORE_URL:!t.linkUrl||E.isAppExist(t.resourceId)?G(t.resourceId,e.page,e?e.type:null):t.linkUrl&&K({url:t.linkUrl,title:e.title,id:e.id||0,defBrowser:1});break;case a.OPEN_MENU:e&&e.type&&(i=parseInt(e.type+i)),E.openAppStoreMenu(6,t.menuId,0,i);break;case a.OPEN_TAG:this.gotoTagPage(e.page)(t.tagId);break;case a.NEWBROWSER:case a.PERSONALBROWSER:case a.SPECBROWSER:z.isWW()?E.openInBrowser(t.linkUrl):K({url:t.linkUrl,title:e.title,id:e.id||0,defBrowser:0});break}},startLibAppFn:function(t){var e=this;return function(a,i){i=i||{},i=Object(p["a"])(Object(p["a"])({},t),i),e.startLibApp(a,i)}},gotoTagPage:function(t){return function(e){var a="";E.openAppStoreUrl(!0,a,t||0)}},goback:function(){history.length>1?history.go(-1):E.goback()}},z={generateGUID:function(){var t=(new Date).getTime();return"undefined"!==typeof performance&&"function"===typeof performance.now&&(t+=performance.now()),"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx-gamelife".replace(/[xy]/g,(function(e){var a=(t+16*Math.random())%16|0;return t=Math.floor(t/16),("x"===e?a:3&a|8).toString(16)}))},eNotation:function(t){var e=parseInt(t);return isNaN(e)?0:Math.floor(e/1e8)>=1?(e/1e4).toFixed(2)+"亿":Math.floor(e/1e4)<1?e:(e/1e4).toFixed(2)+"万"},guid:function(){if(sessionStorage.getItem("guid"))return sessionStorage.getItem("guid");var t=E.getGuid();return t||(t=this.generateGUID()),sessionStorage.setItem("guid",t),t},userId:function(){var t=this.getUrlVars();if(t.userid)return t.userid;var e=this.memberId();return e||(e=""),e},accurateUserId:function(){var t=E.getAccurateUserId();return t||(t=""),t},getUserInfo:function(t){switch(t){case"token":return E.getSomeData(2);case"passport":return E.getPassportID()}},getToken:function(){var t=this.getUrlParameter("token");return t||(this.getUserInfo("token")||"")},memberId:function(){var t=this.getUrlParameter("memberid");return t||(this.getUserInfo("passport")||"")},barId:function(){var t=this.getUrlVars();return t.barid?t.barid:E.getBarId()},cityId:function(){var t=this.getUrlVars();return t.cityid?t.cityid:E.getSomeData(3,"")},getUrlVars:function(t){var e,a={},i=null;t||(t=window.location.href),i=t.slice(t.indexOf("?")+1).split("&");for(var n=0;n<i.length;n++)e=i[n].split("="),a[e[0]]=e[1];return a},getUrlParameter:function(t){t=t.replace(/[\[]/,"\\[").replace(/[\]]/,"\\]");var e=new RegExp("[\\?&]"+t+"=([^&#]*)"),a=e.exec(location.search);return null===a?"":decodeURIComponent(a[1].replace(/\+/g," "))},getBarID:function(){return E.getBarId?E.getBarId():0},isWW:function(){var t=z.getUrlVars();return!!t&&1==t.p},refreshUserInfo:function(t){E.setSomeData(t||{})},encrypt_mode1:function(t,e){for(var a=new Uint8Array(e),i=new Uint8Array([33,67,101,135,144,186,220,254]),n=0,r=0;r<e;++r){var s=t.charCodeAt(r)^i[n];a[r]=s,n=(n+1)%i.length}return a},decrypt_mode1:function(t){for(var e="",a=t.length,i=new Uint8Array([33,67,101,135,144,186,220,254]),n=0,r=0;r<a;++r){var s=t[r].charCodeAt()^i[n];e+=String.fromCharCode(s),n=(n+1)%i.length}return e},updateParamToUrl:function(t,e,a){if(!t||!e||!a)return t||"";var i=t.split("#")[0],n=t.split("#")[1];return/\?/g.test(i)?/name=[-\w]{4,25}/g.test(i)?i=i.replace(/name=[-\w]{4,25}/g,e+"="+a):i+="&"+e+"="+a:i+="?"+e+"="+a,i=n?i+"#"+n:i,i},getProductId:function(){var t=this.getUrlParameter("productId");return t||(E.getSomeData(5,0)||"")},base64:M,exposureObj:N,pageUtil:V},W=z;function F(t){return U({url:"/tab/info",method:"post",params:t})}function H(t){return U({url:"/tab/bannerInfo",method:"post",params:t})}function q(t){return U({url:"/member/get",method:"post",params:t})}function J(){return U({url:"/page/userInfo",method:"post"})}var Y=W.getUrlParameter("id");function Q(t){var e={projectId:"privilege",businessId:"menu",body:[null===W||void 0===W?void 0:W.getBarID(),W.guid(),t.moduleId,sessionStorage.getItem("memberId")||"",t.opt,sessionStorage.getItem("tabId")||"",t.ext,Y||sessionStorage.getItem("pageId")].join("\t")};return U({url:"/bigData/report",params:Object(p["a"])({},e)})}function X(t){var e;return U({url:"/module/clickBtn",method:"post",params:Object(p["a"])({barId:null!==(e=null===W||void 0===W?void 0:W.getBarID())&&void 0!==e?e:"0",guid:W.guid()},t)})}function Z(t){return U({url:"/redPacketRecord/module",method:"post",params:t})}function tt(t){return U({url:"/page/tab",method:"post",params:t})}function et(){return U({url:"/page/miniConfig",method:"post"})}function at(t){return U({url:"/pc/prize/myPrize",method:"get",params:t})}function it(t){return U({url:"/pc/giftBag/myGiftBag",method:"get",params:t})}function nt(t){return U({url:"/pc/redPacketRecord/myRedPacket",method:"get",params:t})}function rt(t){return U({url:"/module/welfare/cdkey",method:"post",params:t})}function st(t){return U({url:"/exit/rmc",method:"get",params:t})}function ot(t){var e;return U({url:"/pc/ppw/commend",params:Object(p["a"])({barId:null!==(e=null===W||void 0===W?void 0:W.getBarID())&&void 0!==e?e:"0",guid:W.guid(),channel:"yunhai",pageId:sessionStorage.getItem("pageId")},t)})}function ct(t){return U({url:"/tab/circle/info",method:"post",params:t})}function lt(t){return U({url:"/pc/prize/luckyDraw",method:"post",params:Object(p["a"])({},t)})}function ut(t){return U({url:"/tab/menu/commonStructure",method:"post",params:t})}function dt(t){return U({url:"/pc/prize/pond",method:"post",params:t})}function pt(t){return U({url:"/pc/signIn/config",method:"post",params:Object(p["a"])({memberId:sessionStorage.getItem("memberId"),pageId:W.getUrlParameter("id"),channel:"yunhai"},t)})}function gt(t){return U({url:"/pc/signIn/doSignIn",method:"post",params:Object(p["a"])({memberId:sessionStorage.getItem("memberId"),pageId:W.getUrlParameter("id"),channel:"yunhai"},t)})}function mt(t){return U({url:"/pc/giftBag/get",method:"post",params:Object(p["a"])({pageId:W.getUrlParameter("id"),channel:"yunhai"},t)})}function ft(t){return U({url:"/pc/giftBag/buy",method:"post",params:Object(p["a"])({},t)})}var ht={name:"HeaderContainer",inject:["jumpExternal","toLogin"],components:{swiper:y["swiper"],swiperSlide:y["swiperSlide"],ImgError:O},props:{userInfo:{type:Object,default:null},bannerInfo:{type:Object,default:function(){return[]}},mainPageOption:{type:Object,default:function(){return[]}}},data:function(){var t=this;return{defaultPng:h,defaultAvatar:v,defaultLogin:I,loginTextPng:b,vipBorder:w,sliderIdx:1,swiperOption:{slidesPerView:1,spaceBetween:30,stopOnLastSlide:!1,autoplay:{delay:3e3,disableOnInteraction:!1},on:{slideChange:function(){var e=document.getElementById("header_slider");t.sliderIdx=e.swiper.activeIndex,document.getElementById("right_banner_name")&&(t.sliderIdx<4?document.getElementById("right_banner_name").scrollTop=0:document.getElementById("right_banner_name").scrollTop=44*(t.sliderIdx-2))}}}}},mounted:function(){Q({moduleId:"1001",opt:"3"})},methods:{logout:function(){Q({moduleId:"1001",opt:"2"}),this.$emit("logout")},login:function(){this.$emit("login"),setTimeout((function(){Q({moduleId:"1001",opt:"1"})}),1e3)},changeBanner:function(t){this.sliderIdx=t,document.getElementById("header_slider").swiper.slideTo(t)},linkClick:function(t){switch(Q({moduleId:"1002",opt:"1",ext:t.name}),t.openType){case 1:console.log("启动游戏"),E.startApp(t.gameId,"0");break;case 2:console.log("在浏览器中打开"),E.openInBrowser(null===t||void 0===t?void 0:t.linkUrl);break;case 3:console.log("打开游戏详情页"),this.jumpExternal({url:null===t||void 0===t?void 0:t.linkUrl});break;default:break}},vipShow:function(){Q({moduleId:"1001",opt:"5"}),sessionStorage.getItem("memberId")?this.$emit("vipShow"):this.toLogin()},draw:function(t){this.$emit("draw",t)}}},vt=ht,It=(a("afb0"),Object(x["a"])(vt,m,f,!1,null,"2308b061",null)),wt=It.exports,bt=a.p+"images/return_icon.png",yt=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{attrs:{id:"LRStructures"+this.cIdx}},[t.dataInfo.imagePath?a("img",{staticClass:"title",attrs:{src:t.dataInfo.imagePath}}):t._e(),a("div",{staticClass:"first_row"},[2==t.firstDetail.type?a("Card",{staticClass:"card main",attrs:{item:t.firstDetail,moduleId:"1004"},on:{updateData:t.updateData,cdkeyShow:t.cdKeyShow}}):t._e(),1==t.firstDetail.type?a("slider",{attrs:{animation:"fade",width:"809px",height:"498px",interval:3e3}},t._l(t.firstDetail.banner.bannerDetailList,(function(e,i){return a("slider-item",{key:i,staticClass:"card",on:{click:function(a){return t.openInBrowser(e.linkUrl)}}},[a("ImgError",{attrs:{src:e.imagePath,className:"img0"}})],1)})),1):t._e(),3==t.firstDetail.type?a("iframe",{attrs:{src:t.firstDetail.linkUrl,width:"809px",height:"498px",frameborder:"0"}}):t._e(),a("div",{staticClass:"right_content"},t._l(t.restList.slice(0,2),(function(e,i){return a("Card",{key:i,staticClass:"card type1",attrs:{item:e,moduleId:"1004"},on:{updateData:t.updateData,cdkeyShow:t.cdKeyShow}})})),1)],1),a("div",{staticClass:"restCard"},t._l(t.collapsed?t.restList.slice(2,5):t.restList.slice(2),(function(e,i){return a("Card",{key:i,staticClass:"card type1",attrs:{item:e,moduleId:"1004"},on:{updateData:t.updateData,cdkeyShow:t.cdKeyShow}})})),1),t.restList.length>5?a("div",{staticClass:"more_div",on:{click:function(e){t.collapsed=!t.collapsed}}},[t._v(" 更多特权 "),a("img",{staticClass:"morePng",class:{pack_up:!t.collapsed},attrs:{src:t.morePng}})]):t._e()])},Dt=[],kt=(a("a9e3"),function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{attrs:{id:t.item.welfareId},on:{mouseleave:function(e){return t.removeClass(e)}}},[1==t.item.btnState||!t.hasMemberId&&6==!t.item.welfareType||!t.hasMemberId&&7==!t.item.welfareType?a("div",{on:{click:function(e){return t.toggleCardState(t.item)}}},[t.item.smallImgPath&&"true"==t.small?[a("ImgError",{attrs:{className:"content-img",src:t.item.smallImgPath,alt:""}})]:[a("ImgError",{attrs:{className:"content-img",src:t.item.beforeImgPath,alt:""}}),a("ImgError",{attrs:{className:"button-img",src:t.item.beforeBtnImgPath}})]],2):a("div",{on:{click:function(e){return t.linkClick(t.item)}}},[t.item.smallImgPath&&"true"==t.small?[a("ImgError",{attrs:{className:"content-img",src:t.item.smallImgPath,alt:""}})]:[a("ImgError",{attrs:{className:"content-img",src:t.item.beforeImgPath,alt:""}}),a("ImgError",{staticStyle:{"z-index":"9"},attrs:{className:"content-img",src:t.item.afterImgPath,alt:""}}),a("ImgError",{attrs:{className:"button-img",src:t.item.afterButImgPath}})]],2)])}),St=[],Ct=a("a78e"),_t=a.n(Ct),xt=a("2ef0"),Pt={props:["item","moduleId","toLogin","small"],components:{ImgError:O},inject:["jumpExternal","toLogin"],data:function(){return{messageText:{5:"已领取",6:"已领取",7:"已参与"},reportOpt:{5:[7,8],6:[3,4],7:[5,6]},cdkeyDialogShow:!1,currData:{},hasMemberId:!1}},mounted:function(){sessionStorage.getItem("memberId")?this.hasMemberId=!0:this.hasMemberId=!1},methods:{remove:xt["remove"],getRedPacket:function(t){var e=this;sessionStorage.getItem("memberId")?Z({memberId:sessionStorage.getItem("memberId"),moduleDetailId:t.id}).then((function(a){0==a.code&&(e.$message({iconClass:"mySuccess-icon",message:e.messageText[t.welfareType]||"已参与"}),Q({moduleId:e.moduleId,opt:1==t.btnState?13:8,ext:t.welfareId})),e.$emit("updateData")})):(Q({moduleId:this.moduleId,opt:7,ext:t.welfareId}),this.toLogin(t.id,{moduleId:this.moduleId,opt:13,ext:t.welfareId}))},getCdkey:function(t){_t.a.set("moduleId",this.moduleId),Q({moduleId:this.moduleId,opt:9,ext:t.welfareId}),sessionStorage.getItem("memberId")?(this.$emit("cdkeyShow",t,this.moduleId),this.$emit("updateData")):this.toLogin(t.id,{},2,t)},toggleCardState:function(t){var e=this;return Object(g["a"])(regeneratorRuntime.mark((function a(){var i;return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(5!==t.welfareType){a.next=3;break}return e.getRedPacket(t),a.abrupt("return");case 3:if(2!==t.welfareType){a.next=6;break}return e.getCdkey(t),a.abrupt("return");case 6:return a.next=8,X({moduleDetailsId:t.id});case 8:i=a.sent,0==i.code&&(e.$message({iconClass:"mySuccess-icon",message:e.messageText[t.welfareType]||"已参与"}),Q({moduleId:e.moduleId,opt:e.reportOpt[t.welfareType]?e.reportOpt[t.welfareType][0]:"",ext:t.welfareId}),e.$emit("updateData"),t.mode&&2==t.mode&&(setTimeout((function(){e.linkClick(t)}),1500),e.$nextTick()));case 10:case"end":return a.stop()}}),a)})))()},linkClick:function(t){var e,a,i,n="0",r=parseInt(null!==(e=null===t||void 0===t||null===(a=t.welfare)||void 0===a?void 0:a.gameId)&&void 0!==e?e:""),s=null!==(i=null===t||void 0===t?void 0:t.linkUrl)&&void 0!==i?i:"";if(2!==t.welfareType){switch(t.btnOpenType){case 1:console.log("启动游戏"),E.startApp(r,n);break;case 2:console.log("在浏览器中打开"),E.openInBrowser(s);break;case 3:console.log("打开游戏详情页"),this.jumpExternal({url:s});break;default:break}Q({moduleId:this.moduleId,opt:this.reportOpt[t.welfareType]?this.reportOpt[t.welfareType][1]:"",ext:t.welfareId})}else this.getCdkey(t)},removeClass:function(t){t.target.className.indexOf("hover")>-1&&t.target.classList.remove("hover")}}},Ot=Pt,Bt=(a("f641"),Object(x["a"])(Ot,kt,St,!1,null,null,null)),Et=Bt.exports,Lt=a.p+"images/more.png",Tt=a("cd9a"),Rt={name:"LRStructures",components:{Card:Et,Slider:Tt["a"],SliderItem:Tt["b"],ImgError:O},props:{dataInfo:{type:Object,default:function(){}},cIdx:{type:Number}},watch:{dataInfo:function(t){t.detailList&&t.detailList[0]?(this.firstDetail=t.detailList[0],this.restList=t.detailList.slice(1)):(this.firstDetail={},this.restList=[])}},mounted:function(){this.dataInfo.detailList&&this.dataInfo.detailList[0]?(this.firstDetail=this.dataInfo.detailList[0],this.restList=this.dataInfo.detailList.slice(1)):(this.firstDetail={},this.restList=[]),Q({moduleId:"1004",opt:"1",ext:""})},data:function(){return{morePng:Lt,firstDetail:{},restList:[],collapsed:!0}},methods:{updateData:function(){console.log("模块数据更新"),this.$emit("updateData")},openInBrowser:function(t){E.openInBrowser(t)},cdKeyShow:function(t){this.$emit("cdkeyShow",t)}}},jt=Rt,Ut=(a("04e4"),Object(x["a"])(jt,yt,Dt,!1,null,"f2b9d036",null)),$t=Ut.exports,At=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{attrs:{id:"Unwind"+t.cIdx}},[t.dataInfo.imagePath?a("img",{staticClass:"title",attrs:{src:t.dataInfo.imagePath}}):t._e(),a("div",{staticClass:"slider"},[a("swiper",{ref:"mySwiper",attrs:{options:t.swiperOption}},t._l(t.listData,(function(e,i){return a("swiper-slide",{key:i,staticClass:"one_game"},[a("div",{attrs:{id:e.welfareId}},[e.pubDate?a("div",{staticClass:"date"},[t._v(t._s(e.pubDate))]):t._e(),e.pubDate?t._e():a("div",{staticClass:"date"},[t._v(" ")]),a("div",{staticClass:"game_content"},[a("div",{staticClass:"game_bg"}),a("div",{staticClass:"game_box"},[1==e.btnState||!t.hasMemberId&&6==!e.welfareType||!t.hasMemberId&&7==!e.welfareType?a("div",{staticStyle:{position:"relative"},on:{click:function(a){return t.toggleCardState(e)}}},[a("ImgError",{attrs:{src:e.beforeImgPath,alt:"",className:"game_pic"}}),a("ImgError",{attrs:{className:"button-img",src:e.beforeBtnImgPath}})],1):a("div",{staticStyle:{position:"relative"},on:{click:function(a){return t.linkClick(e)}}},[a("ImgError",{attrs:{src:e.afterImgPath,alt:"",className:"game_pic after"}}),a("ImgError",{attrs:{src:e.beforeImgPath,alt:"",className:"game_pic"}}),a("ImgError",{attrs:{className:"button-img",src:e.afterButImgPath}})],1),a("div",{staticClass:"game_name",attrs:{title:e.name}},[t._v(t._s(e.name))]),a("div",{staticClass:"game_desc",attrs:{title:e.introduction}},[t._v(" "+t._s(e.introduction)+" ")])])])])])})),1),t.listData.length>4&&t.sliderIdx?a("div",{staticClass:"swiper-button-prev",attrs:{slot:"button-prev"},on:{click:function(e){return t.swiperPrev()}},slot:"button-prev"}):t._e(),t.listData.length>4&&t.sliderIdx+4<t.listData.length?a("div",{staticClass:"swiper-button-next",attrs:{slot:"button-next"},on:{click:function(e){return t.swiperNext()}},slot:"button-next"}):t._e()],1)])},Mt=[],Nt=(a("a7a3"),{name:"NewGame",components:{swiper:y["swiper"],swiperSlide:y["swiperSlide"],ImgError:O},inject:["jumpExternal","toLogin"],props:{dataInfo:{type:Object,default:function(){}},cIdx:{type:Number}},computed:{swiper:function(){return this.$refs.mySwiper.swiper}},watch:{dataInfo:function(t){this.listData=t.detailList}},mounted:function(){sessionStorage.getItem("memberId")?this.hasMemberId=!0:this.hasMemberId=!1,this.listData=this.dataInfo.detailList,Q({moduleId:"1005",opt:"1",ext:""})},data:function(){var t=this;return{listData:[],sliderIdx:0,hasMemberId:!1,messageText:{5:"已领取",6:"已领取",7:"已参与"},reportOpt:{5:[8,9],6:[4,5],7:[6,7]},swiperOption:{loop:!0,slidesPerView:4,stopOnLastSlide:!1,navigation:{nextEl:".swiper-button-next",prevEl:".swiper-button-prev"},on:{click:function(t,e){"swiper-button-next"!==t.target.className&&"swiper-button-prev"!==t.target.className||Q({moduleId:"1005",opt:"3"})},slideChange:function(){t.sliderIdx=t.swiper.activeIndex}}}}},methods:{swiperNext:function(){this.swiper.slideNext()},swiperPrev:function(){this.swiper.slidePrev()},getRedPacket:function(t){var e=this;sessionStorage.getItem("memberId")?Z({memberId:sessionStorage.getItem("memberId"),moduleDetailId:t.id}).then((function(a){0==a.code&&(e.$message({iconClass:"mySuccess-icon",message:e.messageText[t.welfareType]||"已参与"}),Q({moduleId:"1005",opt:1==t.btnState?13:8,ext:t.welfareId})),e.$emit("updateData")})):(Q({moduleId:"1005",opt:14,ext:t.welfareId}),this.toLogin(t.id,{moduleId:"1005",opt:13,ext:t.welfareId}))},getCdkey:function(t){_t.a.set("moduleId","1005"),Q({moduleId:"1005",opt:9,ext:t.welfareId}),sessionStorage.getItem("memberId")?(this.$emit("cdkeyShow",t),this.$emit("updateData")):this.toLogin(t.id,{},2,t)},toggleCardState:function(t){var e=this;return Object(g["a"])(regeneratorRuntime.mark((function a(){var i;return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(5!==t.welfareType){a.next=3;break}return e.getRedPacket(t),a.abrupt("return");case 3:if(2!==t.welfareType){a.next=6;break}return e.getCdkey(t),a.abrupt("return");case 6:return a.next=8,X({moduleDetailsId:t.id});case 8:i=a.sent,0==i.code&&(e.$message({iconClass:"mySuccess-icon",message:e.messageText[t.welfareType]||"已参与"}),Q({moduleId:"1005",opt:e.reportOpt[t.welfareType]?e.reportOpt[t.welfareType][0]:"",ext:t.welfareId})),e.$emit("updateData"),t.mode&&2==t.mode&&(setTimeout((function(){e.linkClick(t)}),1500),e.$nextTick());case 12:case"end":return a.stop()}}),a)})))()},linkClick:function(t){var e,a,i;if(2!==t.welfareType){var n="0",r=parseInt(null!==(e=null===t||void 0===t||null===(a=t.welfare)||void 0===a?void 0:a.gameId)&&void 0!==e?e:""),s=null!==(i=null===t||void 0===t?void 0:t.linkUrl)&&void 0!==i?i:"";switch(t.btnOpenType){case 1:console.log("启动游戏"),E.startApp(r,n);break;case 2:console.log("在浏览器中打开"),E.openInBrowser(s);break;case 3:console.log("打开游戏详情页"),this.jumpExternal({url:s});break;default:break}Q({moduleId:"1005",opt:this.reportOpt[t.welfareType]?this.reportOpt[t.welfareType][1]:"",ext:t.welfareId}),this.$emit("updateData")}else this.getCdkey(t)}}}),Gt=Nt,Kt=(a("33b6"),Object(x["a"])(Gt,At,Mt,!1,null,"d5253cc6",null)),Vt=Kt.exports,zt=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{attrs:{id:"TilingModule"+t.cIdx}},[t.dataInfo.imagePath?a("img",{staticClass:"title",attrs:{src:t.dataInfo.imagePath}}):t._e(),a("div",{staticClass:"TilingModule-box-wrapper"},t._l(t.dataInfo.detailList,(function(e,i){return a("Card",{key:i,staticClass:"card type8",attrs:{item:e,moduleId:"1010"},on:{updateData:t.updateData,cdkeyShow:t.cdKeyShow}})})),1)])},Wt=[],Ft={name:"TilingModule",inject:["jumpExternal"],components:{Card:Et},props:{dataInfo:{type:Object,default:function(){}},cIdx:{type:Number}},data:function(){return{defaultPng:h,listData:[]}},mounted:function(){Q({moduleId:"1010",opt:"1"})},methods:{updateData:function(){console.log("模块数据更新"),this.$emit("updateData")},cdKeyShow:function(t){this.$emit("cdkeyShow",t)}}},Ht=Ft,qt=(a("2667"),Object(x["a"])(Ht,zt,Wt,!1,null,"4f6d3cfa",null)),Jt=qt.exports,Yt=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{attrs:{id:"UpDownStructures"+t.cIdx}},[t.dataInfo.imagePath?a("img",{staticClass:"title",attrs:{src:t.dataInfo.imagePath}}):t._e(),a("div",{staticClass:"first_row"},[2==t.firstDetail.type?a("Card",{staticClass:"card main",attrs:{item:t.firstDetail,moduleId:"1006"},on:{updateData:t.updateData,cdkeyShow:t.cdKeyShow}}):t._e(),1==t.firstDetail.type?a("slider",{attrs:{animation:"fade",width:"100%",height:"100%",interval:3e3}},t._l(t.firstDetail.banner.bannerDetailList,(function(e,i){return a("slider-item",{key:i,staticClass:"card",on:{click:function(a){return t.openInBrowser(e)}}},[a("ImgError",{attrs:{src:e.imagePath}})],1)})),1):t._e(),3==t.firstDetail.type?a("iframe",{attrs:{src:t.firstDetail.linkUrl,width:"100%",height:"100%",frameborder:"0"}}):t._e()],1),a("div",{staticClass:"restCard"},t._l(t.restList,(function(e,i){return a("Card",{key:i,staticClass:"card type1",attrs:{item:e,moduleId:"1006"},on:{updateData:t.updateData,cdkeyShow:t.cdKeyShow}})})),1)])},Qt=[],Xt={name:"UpDownStructures",components:{Card:Et,Slider:Tt["a"],SliderItem:Tt["b"],ImgError:O},props:{dataInfo:{type:Object,default:function(){}},cIdx:{type:Number}},watch:{dataInfo:function(t){t.detailList&&t.detailList[0]?(this.firstDetail=t.detailList[0],this.restList=t.detailList.slice(1)):(this.firstDetail={},this.restList=[])}},mounted:function(){this.dataInfo.detailList&&this.dataInfo.detailList[0]?(this.firstDetail=this.dataInfo.detailList[0],this.restList=this.dataInfo.detailList.slice(1)):(this.firstDetail={},this.restList=[]),Q({moduleId:"1006",opt:"1",ext:""})},data:function(){return{firstDetail:{},restList:[]}},methods:{updateData:function(){console.log("模块数据更新"),this.$emit("updateData")},openInBrowser:function(t){E.openInBrowser(t.linkUrl),Q({moduleId:"1006",opt:"4",ext:t.welfareId})},cdKeyShow:function(t){this.$emit("cdkeyShow",t)}}},Zt=Xt,te=(a("6be1"),Object(x["a"])(Zt,Yt,Qt,!1,null,"1f39d546",null)),ee=te.exports,ae=function(){var t=this,e=t.$createElement,a=t._self._c||e;return t.allModules.length&&t.GuideState?a("div",{staticClass:"nav"},[t.bannerInfo.length?a("div",{staticClass:"top_content"},[a("swiper",{ref:"mySwiper",staticClass:"slider",attrs:{options:t.swiperOption}},[t._l(t.bannerInfo,(function(e,i){return a("swiper-slide",{key:i,staticClass:"s_pic",on:{click:function(a){return t.linkClick(e)}}},[a("div",{staticStyle:{cursor:"pointer",height:"100%"},on:{click:function(a){return t.linkClick(e)}}},[a("ImgError",{attrs:{src:e.imagePath}})],1)])})),a("div",{staticClass:"swiper-pagination",attrs:{slot:"pagination"},slot:"pagination"})],2)],1):t._e(),a("div",{staticClass:"bottom_content"},[a("ul",{staticClass:"menu_container"},t._l(t.allModules,(function(e,i){return a("li",{key:""+t.navsKeys[e.structure||"1"]+i,staticClass:"one_module",class:{active:""+t.navsKeys[e.structure||"1"]+i===t.activeKey},attrs:{title:e.name},on:{click:function(a){return t.scrollView(""+t.navsKeys[e.structure||"1"]+i,t.navsKeys[e.structure||"1"])}}},[a("img",{attrs:{src:e.navigateImgPath}})])})),0),a("div",[a("img",{staticClass:"topPic",attrs:{src:t.topPic},on:{click:function(e){return t.scrollView()}}})])])]):t._e()},ie=[],ne=a.p+"images/top.png",re={name:"RightNav",inject:["jumpExternal"],components:{swiper:y["swiper"],swiperSlide:y["swiperSlide"],ImgError:O},props:{bannerInfo:{type:Array,default:function(){return[]}},allModules:{type:Array,default:function(){return[]}},mainPageOption:{type:Object,default:function(){}},activeTab:{type:Number,default:null}},watch:{allModules:function(t){t.length&&(this.activeKey="".concat(this.navsKeys[t[0].structure],"0"))}},computed:{GuideState:function(){var t,e=this;return this.mainPageOption.tabList.forEach((function(a){a.id==e.activeTab&&(t=a.guideSwitch)})),1==t}},data:function(){return{navsKeys:{1:"LRStructures",3:"UpDownStructures",4:"Unwind",8:"TilingModule"},moduleKeys:{LRStructures:"1004",UpDownStructures:"1006",Unwind:"1005",TilingModule:"1010"},activeKey:"",topPic:ne,swiperOption:{loop:!0,autoplay:{delay:3e3,disableOnInteraction:!1},pagination:{el:".swiper-pagination"}}}},methods:{scrollView:function(t,e){this.activeKey=t,t?(document.getElementById(t).scrollIntoView({behavior:"smooth",block:"start"}),Q({moduleId:this.moduleKeys[e],opt:"2"})):window.scrollTo(0,0)},linkClick:function(t){switch(Q({moduleId:"1003",opt:"1",ext:t.name}),t.openType){case 1:console.log("启动游戏"),E.startApp(t.gameId,"0");break;case 2:console.log("在浏览器中打开"),E.openInBrowser(null===t||void 0===t?void 0:t.linkUrl);break;case 3:console.log("打开游戏详情页"),this.jumpExternal({url:null===t||void 0===t?void 0:t.linkUrl});break;default:break}}}},se=re,oe=(a("81eb"),Object(x["a"])(se,ae,ie,!1,null,"72c38c05",null)),ce=oe.exports,le=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"top_nav"},[a("img",{staticClass:"nav_title",attrs:{src:t.navTitle}}),t.mainPageOption.guideImgPath?a("div",{staticClass:"nav_link_bar"},[a("img",{attrs:{src:t.mainPageOption.guideImgPath}}),a("div",{staticClass:"signIn link",on:{click:function(e){return t.draw(1)}}}),a("div",{staticClass:"giftBag link",on:{click:function(e){return t.draw(2)}}}),a("div",{staticClass:"draw link",on:{click:function(e){return t.draw(3)}}})]):t._e(),a("div",{staticClass:"vip_hover_box"},[a("div",{staticClass:"user_container"},[t.userInfo?t._e():a("div",{staticClass:"login",on:{click:t.login}},[a("img",{attrs:{src:t.defaultLogin}}),a("img",{staticClass:"login_text",attrs:{src:t.loginTextPng}})])]),t.userInfo?a("div",{staticClass:"avatar"},[a("img",{staticClass:"avatarImg",attrs:{src:t.userInfo.headImg||t.defaultAvatar}})]):t._e(),t.userInfo?a("div",{staticClass:"top_vip_wrap"},[a("ul",{staticClass:"top_vip_list"},[a("li",{on:{click:t.vipShow}},[t._v("我的权益")]),a("li",{on:{click:t.logout}},[t._v("退出")])])]):t._e()])])},ue=[],de=a.p+"images/nav_title.png",pe={name:"TopNav",props:{userInfo:{type:Object,default:null},mainPageOption:{type:Object,default:null}},data:function(){return{navTitle:de,defaultAvatar:v,defaultLogin:I,loginTextPng:b}},mounted:function(){Q({moduleId:"1007",opt:"1",ext:""})},methods:{login:function(){this.$emit("login"),Q({moduleId:"1007",opt:"2",ext:""})},vipShow:function(){this.$emit("vipShow"),Q({moduleId:"1007",opt:"3",ext:""})},logout:function(){Q({moduleId:"1001",opt:"2"}),this.$emit("logout")},draw:function(t){this.$emit("draw",t),Q({moduleId:"1007",opt:"4",ext:""})}}},ge=pe,me=(a("4d42"),Object(x["a"])(ge,le,ue,!1,null,"72413c56",null)),fe=me.exports,he=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("el-dialog",{attrs:{visible:t.visible,width:"690px","custom-class":"login_dialog"},on:{"update:visible":function(e){t.visible=e},close:t.closeLogin}},[a("div",{staticClass:"login_container"},[a("div",{staticClass:"left_content"},[a("img",{attrs:{src:t.loginLeft}})]),a("div",{staticClass:"right_content"},[a("div",{staticClass:"right_top"},[t._v("打开微信，扫一扫登录")]),a("div",{staticClass:"qr_container"},[a("img",{attrs:{src:t.qrBg}}),a("iframe",{staticClass:"qr_block",attrs:{src:t.qr,frameborder:"no",border:"0",width:"162",height:"162",scrolling:"no"}})]),a("div",[t._v(" 扫码即表示阅读并同意"),a("span",{staticClass:"read_link",on:{click:function(e){return t.openUrl("https://i.kedou.com/agreement/register")}}},[t._v("《顺网用户协议》")]),t._v("及"),a("span",{staticClass:"read_link",on:{click:function(e){return t.openUrl("https://gact.swjoy.com/about/privacy.html")}}},[t._v("《隐私政策》")])])])])])},ve=[],Ie=(a("99af"),a.p+"images/login_left.png"),we=a.p+"images/qr_bg.png",be={name:"Login",components:{},props:{visible:{type:Boolean,default:function(){return!1}}},data:function(){return{loginLeft:Ie,qrBg:we,qr:"https://sso.kedou.com/login.do?callbackUrl=https%3A%2F%2Fm.kedou.com%2FbarPrivilege%2FtoPage%3F".concat(encodeURIComponent("id=".concat(W.getUrlParameter("id"),"&").concat(_t.a.get("unreceivedId")?"unreceivedId=".concat(_t.a.get("unreceivedId"),"&"):"")),"returnUrl%3D&msg=&site_id=marketing-welfare&loginType=1013")}},created:function(){},methods:{closeLogin:function(){this.$emit("close")},getGuid:function(){return W.guid()},openUrl:function(t){E.openInBrowser(t)}}},ye=be,De=(a("9359"),Object(x["a"])(ye,he,ve,!1,null,null,null)),ke=De.exports,Se=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"vip-dialog"},[a("el-dialog",{attrs:{visible:t.visible,width:"528px","close-on-click-modal":!1},on:{"update:visible":function(e){t.visible=e},close:t.closeVipDialog}},[a("div",{staticClass:"my-vip-box"},[a("div",{staticClass:"hd"},[a("h3",[t._v("我的权益")])]),a("div",{staticClass:"bd"},[a("div",{staticClass:"vip-tab"},[a("ul",{staticClass:"vip-tab-hd"},[a("li",{class:1==t.tabActive?"on":"",on:{click:function(e){return t.tabCardShow()}}},[a("span",[t._v("卡券")])]),a("li",{class:2==t.tabActive?"on":"",on:{click:function(e){return t.tabGiftShow()}}},[a("span",[t._v("礼包")])]),a("li",{class:3==t.tabActive?"on":"",on:{click:function(e){return t.tabPacketShow()}}},[a("span",[t._v("红包")])])]),a("div",{staticClass:"vip-tab-bd"},[1==t.tabActive?a("div",{staticClass:"vip-items vip-card"},[t.myPrizeData.length>0?a("div",t._l(t.myPrizeData,(function(e,i){return a("div",{key:i,staticClass:"item",class:0==e.isExpired?"on":""},[a("p",{staticClass:"t1"},[t._v(t._s(e.prizeName))]),a("p",{staticClass:"t2"},[t._v(t._s(e.description))]),[e.isExpired?a("div",{staticClass:"btn-2",on:{click:function(a){return t.showVipDetail(e)}}},[t._v(" 查看详情> ")]):a("div",{staticClass:"btn-1",on:{click:function(a){return t.showVipDetail(e)}}},[t._v(" 使用 ")])]],2)})),0):a("p",{staticClass:"no-data"},[t._v("暂无数据")])]):t._e(),2==t.tabActive?a("div",{staticClass:"vip-items vip-gift"},[t.myGiftBagData.length>0?a("div",t._l(t.myGiftBagData,(function(e,i){return a("div",{key:"gift"+i,staticClass:"gift-item"},[t._l(e.userRedPacket,(function(e,i){return a("div",{key:i,staticClass:"item",class:{on:!e.isExpired&&(0==e.state||1==e.state),disabled:!!e.isExpired||3==e.state}},[a("p",{staticClass:"t1"},[t._v(t._s(e.name))]),a("p",{staticClass:"t2"},[t._v(t._s(e.description))]),e.isExpired||2==e.state||3==e.state?a("div",{staticClass:"btn-2",on:{click:function(a){return t.showVipDetail(e)}}},[t._v(" 查看详情> ")]):a("div",{staticClass:"btn-1",on:{click:function(a){return t.showVipDetail(e)}}},[t._v(" 使用 ")])])})),t._l(e.userPrize,(function(e,i){return a("div",{key:i,staticClass:"item",class:{on:!e.isExpired,disabled:e.isExpired}},[a("p",{staticClass:"t1"},[t._v(t._s(e.prizeName))]),a("p",{staticClass:"t2"},[t._v(t._s(e.description))]),e.isExpired||2===e.state||3===e.state?a("div",{staticClass:"btn-2",on:{click:function(a){return t.showVipDetail(e)}}},[t._v(" 查看详情 ")]):a("div",{staticClass:"btn-1",on:{click:function(a){return t.showVipDetail(e)}}},[t._v(" 使用 ")])])}))],2)})),0):a("p",{staticClass:"no-data"},[t._v("暂无数据")])]):t._e(),3==t.tabActive?a("div",{staticClass:"vip-items vip-packet"},[t.myRedPacketData.length>0?a("div",t._l(t.myRedPacketData,(function(e,i){return a("div",{key:i,staticClass:"item",class:{on:0==e.isExpired&&1==e.state,disabled:1==e.isExpired}},[a("p",{staticClass:"t1"},[t._v(t._s(e.name))]),a("p",{staticClass:"t2"},[t._v(t._s(e.description))]),0==e.isExpired&&1==e.state?a("div",{staticClass:"btn-1",on:{click:function(a){return t.showVipDetail(e)}}},[t._v(" 使用 ")]):a("div",{staticClass:"btn-2",on:{click:function(a){return t.showVipDetail(e)}}},[t._v(" 查看详情> ")])])})),0):a("p",{staticClass:"no-data"},[t._v("暂无数据")])]):t._e()]),a("div",{staticClass:"vip-tab-ft"},[a("Pagination",{attrs:{layout:"prev, pager, next","current-page":t.page,total:t.total},on:{"current-change":function(e){return t.handleCurrentChange(e)}}})],1)])])])]),a("CodeDialog",{attrs:{visible:t.codeDialogShow,detail:t.currData},on:{"update:visible":function(e){t.codeDialogShow=e},close:t.closeVipDetail}})],1)},Ce=[],_e=(a("672e"),a("101e")),xe=a.n(_e),Pe=function(){var t=this,e=t.$createElement,a=t._self._c||e;return t.detailData?a("div",{staticClass:"vip-dialog"},[a("el-dialog",{attrs:{visible:t.visible,width:"690px","close-on-click-modal":!1},on:{"update:visible":function(e){t.visible=e},close:t.dialogClose}},[a("div",{staticClass:"vip-box"},[a("div",{staticClass:"hd"},[a("h3",[t._v("领取成功")])]),a("div",{staticClass:"bd"},[a("h5",[t._v(t._s(t.detailData.prizeName||t.detailData.name))]),a("div",{domProps:{innerHTML:t._s(t.detailData.webDetail)}})]),a("div",{staticClass:"ft"},[t.detailData.prizeType?[2==t.detailData.prizeType&&t.detailData.remark?a("div",{staticClass:"copy-box"},[a("input",{attrs:{type:"text",id:"linkCopy"},domProps:{value:t.detailData.remark}})]):t._e()]:t._e(),t.detailData.remark?a("button",{on:{click:function(e){return t.viewItem(t.detailData)}}},[t._v(" 复制并使用 ")]):!t.detailData.remark&&t.detailData.url?a("button",{on:{click:function(e){return t.viewItem(t.detailData)}}},[t._v(" 立即使用 ")]):t._e()],2)])])],1):t._e()},Oe=[],Be={name:"codeDialog",inject:["jumpExternal","vipClose"],data:function(){return{vipText:"",detailData:{}}},props:{visible:{type:Boolean,default:function(){return!1}},detail:{type:Object,default:function(){}}},watch:{detail:function(){this.detailData=this.detail}},methods:{dialogClose:function(){this.$emit("close")},copyUrl:function(t){var e=this,a=document.getElementById("linkCopy");a.select();try{document.execCommand("Copy"),this.$message({message:"复制成功",type:"success",customClass:"info-message"})}catch(i){this.$message({message:"复制失败，请手动复制",customClass:"info-message"})}setTimeout((function(){e.linkClick(t)}),1e3)},viewItem:function(t){Q({moduleId:"1008",opt:"4",ext:t.welfareId}),t.remark?this.copyUrl(t):this.linkClick(t)},linkClick:function(t){switch(null===t||void 0===t?void 0:t.openType){case 1:console.log("启动游戏"),E.startApp(t.gameId,0);break;case 2:console.log("在浏览器中打开"),E.openInBrowser(t.url);break;case 3:console.log("打开游戏详情页"),this.visible=!1,this.$emit("close"),this.vipClose(),this.jumpExternal({url:t.url});break;default:break}}}},Ee=Be,Le=(a("5ad1"),Object(x["a"])(Ee,Pe,Oe,!1,null,null,null)),Te=Le.exports,Re={name:"vipDialog",props:{visible:{type:Boolean,default:function(){return!1}},privilegeActiveTab:{type:String,default:function(){return null}}},data:function(){return{tabActive:1,myPrizeData:{},myGiftBagData:{},myRedPacketData:{},currData:null,codeDialogShow:!1,page:1,size:10,total:0}},created:function(){switch(this.$props.privilegeActiveTab){case"vip-packet":this.tabPacketShow();break;case"vip-gift":this.tabGiftShow();break;case"vip-card":this.tabCardShow();break;default:this.tabCardShow();break}},mounted:function(){Q({moduleId:"1008",opt:"1"})},components:{CodeDialog:Te,Pagination:xe.a},methods:{closeVipDialog:function(){this.$emit("close")},closeVipDetail:function(){this.codeDialogShow=!1,this.currData=null},showVipDetail:function(t){this.codeDialogShow=!0,this.currData=t,Q({moduleId:"1008",opt:"3",ext:t.welfareId})},tabCardShow:function(){this.tabActive=1,Q({moduleId:"1008",opt:"2",ext:"1"}),this.myPrizeHandle(1)},myPrizeHandle:function(t){var e=this;return Object(g["a"])(regeneratorRuntime.mark((function a(){var i;return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.next=2,at({memberId:sessionStorage.getItem("memberId")||"",page:t||e.page,size:e.size});case 2:i=a.sent,0==i.code&&(e.myPrizeData=i.data.prizeList,e.page=i.data.pageInfo.page,e.total=i.data.pageInfo.cnt);case 4:case"end":return a.stop()}}),a)})))()},tabGiftShow:function(){this.tabActive=2,Q({moduleId:"1008",opt:"2",ext:"2"}),this.myGiftBagHandle(1)},myGiftBagHandle:function(t){var e=this;return Object(g["a"])(regeneratorRuntime.mark((function a(){var i;return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.next=2,it({memberId:sessionStorage.getItem("memberId")||"",page:t||e.page,size:e.size});case 2:i=a.sent,0==i.code&&(e.myGiftBagData=i.data.bagList,e.page=i.data.pageInfo.page,e.total=i.data.pageInfo.cnt);case 4:case"end":return a.stop()}}),a)})))()},tabPacketShow:function(){this.tabActive=3,Q({moduleId:"1008",opt:"2",ext:"3"}),this.myRedPacketHandle(1)},myRedPacketHandle:function(t){var e=this;return Object(g["a"])(regeneratorRuntime.mark((function a(){var i;return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.next=2,nt({memberId:sessionStorage.getItem("memberId")||"",page:t||e.page,size:e.size});case 2:i=a.sent,0==i.code&&(e.myRedPacketData=i.data.packetList,e.page=i.data.pageInfo.page,e.total=i.data.pageInfo.cnt);case 4:case"end":return a.stop()}}),a)})))()},handleCurrentChange:function(t){console.log(t),this.page=t,1===this.tabActive&&this.myPrizeHandle(t),2===this.tabActive&&this.tabGiftShow(t),3===this.tabActive&&this.myRedPacketHandle(t)}}},je=Re,Ue=(a("b4ac"),Object(x["a"])(je,Se,Ce,!1,null,null,null)),$e=Ue.exports,Ae=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"vip-dialog"},[a("el-dialog",{attrs:{visible:t.visible,width:"690px","close-on-click-modal":!1},on:{"update:visible":function(e){t.visible=e},close:t.dialogClose}},[a("div",{staticClass:"vip-box"},[t.success?[a("div",{staticClass:"hd",class:2==t.detail.btnState?"hd2":""},[a("h3",[t._v("领取成功")])]),a("div",{staticClass:"bd"},[a("h5",[t._v(t._s(t.detail.welfare.name))]),a("div",{domProps:{innerHTML:t._s(t.detail.welfare.webDetail)}})]),a("div",{staticClass:"ft"},[t.cdkeyData?a("div",{staticClass:"copy-box"},[a("input",{attrs:{type:"text",id:"linkCopy"},domProps:{value:t.cdkeyData}}),a("button",{on:{click:function(e){return t.viewItem(t.detail.welfare,t.detail.linkUrl)}}},[t._v(" 复制并使用 ")])]):t._e()])]:[a("div",{staticClass:"error"},[t._v(" "+t._s(t.errorText)+" ")]),a("div",{staticClass:"ft"},[a("button",{on:{click:t.dialogClose}},[t._v("关闭")])])]],2)])],1)},Me=[],Ne={name:"cdkeyDialog",inject:["jumpExternal","vipClose"],data:function(){return{vipText:"",cdkeyData:"",success:!0,errorText:"领取失败",idxmoduleId:""}},props:{visible:{type:Boolean,default:function(){return!1}},detail:{type:Object,default:function(){}},moduleId:{type:String,default:""}},created:function(){this.getCdkey()},methods:{dialogClose:function(){this.$emit("close")},viewItem:function(t,e){Q({moduleId:this.idxmoduleId,opt:12,ext:this.detail.welfareId}),t.remark||this.cdkeyData?this.copyUrl(t,e):this.linkClick(t)},linkClick:function(t){switch(null===t||void 0===t?void 0:t.openType){case 1:console.log("启动游戏"),E.startApp(t.gameId,0);break;case 2:console.log("在浏览器中打开"),E.openInBrowser(t.url);break;case 3:console.log("打开游戏详情页"),this.visible=!1,this.$emit("close"),this.vipClose(),this.jumpExternal({url:t.url});break;default:break}},copyUrl:function(t,e){var a=this,i=document.getElementById("linkCopy");i.select();try{document.execCommand("Copy"),this.$message({message:"复制成功",type:"success",customClass:"info-message",onClose:function(){e&&E.openInBrowser(e)}})}catch(n){this.$message({message:"复制失败，请手动复制",customClass:"info-message"})}setTimeout((function(){a.linkClick(t)}),1e3)},getCdkey:function(){var t=this;return Object(g["a"])(regeneratorRuntime.mark((function e(){var a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,rt({memberId:sessionStorage.getItem("memberId")||"",moduleDetailId:t.detail.id});case 2:a=e.sent,0==a.code?(""!=_t.a.get("moduleId")&&(1==t.detail.btnState&&Q({moduleId:_t.a.get("moduleId")||"",opt:10,ext:t.detail.welfareId}),t.idxmoduleId=_t.a.get("moduleId"),_t.a.remove("moduleId"),t.$emit("updateData")),t.cdkeyData=a.data):(t.success=!1,t.errorText=a.msg,Q({moduleId:_t.a.get("moduleId")||"",opt:11,ext:t.detail.welfareId}));case 4:case"end":return e.stop()}}),e)})))()}}},Ge=Ne,Ke=(a("4eba"),Object(x["a"])(Ge,Ae,Me,!1,null,"c040e396",null)),Ve=Ke.exports,ze=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"ppw-dialog"},[a("el-dialog",{attrs:{visible:t.visible,width:"690px",height:"448px","close-on-click-modal":!1},on:{"update:visible":function(e){t.visible=e},close:t.closeppwDialog}},[t.popupData.infoData?a("div",{staticClass:"ppw-dialog-box",on:{click:function(e){return t.linkClick()}}},[a("img",{attrs:{src:t.popupData.infoData.imgPath}})]):t._e()])],1)},We=[],Fe={name:"ppwDialog",inject:["jumpExternal"],props:{popupData:{type:Object,default:function(){return{code:null,infoData:{}}}}},data:function(){return{visible:!1}},created:function(){this.visible=0==this.$props.popupData.code},methods:{closeppwDialog:function(){var t,e;this.visible=!1,Q({moduleId:"2002",opt:"3",ext:null===(t=this.popupData)||void 0===t||null===(e=t.infoData)||void 0===e?void 0:e.id})},linkClick:function(){var t,e,a,i,n,r,s,o,c="0",l=parseInt(null===(t=this.popupData)||void 0===t||null===(e=t.infoData)||void 0===e?void 0:e.gameId),u=null===(a=this.popupData)||void 0===a||null===(i=a.infoData)||void 0===i?void 0:i.linkUrl;switch(Q({moduleId:"2002",opt:"2",ext:null===(n=this.popupData)||void 0===n||null===(r=n.infoData)||void 0===r?void 0:r.id}),null===(s=this.popupData)||void 0===s||null===(o=s.infoData)||void 0===o?void 0:o.openType){case 1:console.log("启动游戏"),E.startApp(l,c);break;case 2:console.log("在浏览器中打开"),E.openInBrowser(u);break;case 3:console.log("打开游戏详情页"),this.visible=!1,this.jumpExternal({url:u});break;default:break}}}},He=Fe,qe=(a("2c90"),Object(x["a"])(He,ze,We,!1,null,null,null)),Je=qe.exports,Ye=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"pond-dialog"},[a("el-dialog",{attrs:{visible:t.visible,width:"580px",height:"558px","close-on-click-modal":!1},on:{"update:visible":function(e){t.visible=e},close:function(e){return t.closeDailog("defalut")}}},[t.popupData.infoData&&t.popupData.pond?a("div",{staticClass:"pond-dialog-box",on:{click:function(e){return t.draw()}}},[t.popupData.infoData.imgMsg?[a("Img2gif",{attrs:{imgUrl:t.popupData.infoData.imgPath,imgMsg:t.popupData.infoData.imgMsg}})]:[a("img",{staticClass:"img1",attrs:{src:t.popupData.infoData.imgPath}})]],2):a("div",{staticClass:"pond-dialog-box",on:{click:function(e){return t.draw()}}},[t.pond?[a("Img2gif",{attrs:{imgUrl:t.pond.imgPath,imgMsg:t.pond.information}})]:t._e()],2)])],1)},Qe=[],Xe=function(){var t=this,e=t.$createElement,a=t._self._c||e;return t.imgMsg?a("div",{staticClass:"animate-wrapper"},[a("img",{staticClass:"animate-contain rotate1",style:t.animateOptionstyle,attrs:{src:t.imgUrl}})]):a("div",{staticClass:"img-wrapper"},[a("img",{style:{width:t.imgWidth},attrs:{src:t.imgUrl}})])},Ze=[],ta=a("3835"),ea={props:{imgUrl:{type:String,default:null},imgMsg:{type:String,default:null}},data:function(){return{}},computed:{imgWidth:function(){return this.imgMsg?"".concat(this.getImageWidth(),"px"):"auto"},animateOptionstyle:function(){var t=this.imgMsg.split(","),e=Object(ta["a"])(t,3),a=e[1],i=e[2],n="\n        width: ".concat(100*i,"%;\n        animation: rotate1 ").concat(a,"ms steps(").concat(i,") infinite;\n      ");return n}},mounted:function(){},methods:{getImageWidth:function(){var t=this.imgMsg.split(","),e=Object(ta["a"])(t,3),a=e[0],i=e[2];return parseInt(a)*parseInt(i)}}},aa=ea,ia=(a("35ce"),Object(x["a"])(aa,Xe,Ze,!1,null,"75724404",null)),na=ia.exports,ra={name:"DrawPopup",inject:["toLogin"],components:{Img2gif:na},mounted:function(){var t=this;return Object(g["a"])(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:try{t.getData()}catch(a){t.loading=!1}case 1:case"end":return e.stop()}}),e)})))()},watch:{visible:function(t){t&&(this.pond?(Q({moduleId:"2003",opt:"1",ext:this.pond.id}),sessionStorage.setItem("firstEnterDraw","pond")):(this.$emit("close"),this.$message({message:"活动未上线",type:"info",customClass:"info-message"})))}},props:{visible:{type:Boolean,default:function(){return!1}},popupData:{type:Object,default:function(){return{code:null,infoData:{}}}}},data:function(){return{canDraw:!0,pond:null,loading:!0}},methods:{getData:function(){var t=this;return Object(g["a"])(regeneratorRuntime.mark((function e(){var a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,dt({pageId:sessionStorage.getItem("pageId")});case 2:a=e.sent,0===a.code&&(t.pond=a.data),t.loading=!1;case 5:case"end":return e.stop()}}),e)})))()},rpt:function(t){"defalut"==t&&Q({moduleId:"2003",opt:"3",ext:this.pond.id})},closeDailog:function(t){"defalut"==t&&this.rpt("defalut"),this.$emit("close")},draw:function(){var t,e=this;sessionStorage.getItem("memberId")?this.canDraw&&(this.canDraw=!1,console.log("点击抽奖"),Q({moduleId:"2003",opt:"2",ext:this.pond.id}),lt({pondId:this.pond.id,channel:null!==(t=this.pond.channel)&&void 0!==t?t:"yunhai",memberId:sessionStorage.getItem("memberId")}).then((function(t){0==t.code?(e.closeDailog("defalut"),e.$emit("clickDraw",t.data),e.canDraw=!0):-3==t.code?(e.$message({message:"暂不符合领取条件",customClass:"info-message",duration:2e3,onClose:function(){e.canDraw=!0}}),e.$emit("close")):(e.$message({message:"次数不足，明日再来",customClass:"info-message",duration:2e3,onClose:function(){e.canDraw=!0}}),e.$emit("close"))}))):this.toLogin(null,{},3)}}},sa=ra,oa=(a("0f88"),Object(x["a"])(sa,Ye,Qe,!1,null,null,null)),ca=oa.exports,la=function(){var t=this,e=t.$createElement,a=t._self._c||e;return t.drawData?a("div",{staticClass:"pond-dialog"},[a("el-dialog",{attrs:{visible:t.visible,width:"450px",height:"506px","close-on-click-modal":!1,"custom-class":"result"},on:{"update:visible":function(e){t.visible=e},close:function(e){return t.closeDailog("result")}}},[a("div",{staticClass:"title"},[a("img",{attrs:{src:t.titlePng,alt:""}})]),t.drawData.prizeImg?a("div",{staticClass:"content"},[a("img",{attrs:{src:t.drawData.prizeImg}})]):t._e(),a("div",{staticClass:"desc"},[t._v("明天还能继续抽奖哦！")]),a("div",{staticClass:"control",on:{click:function(e){return t.checkDrawRusult()}}},[t._v("立即查看")])])],1):t._e()},ua=[],da=a.p+"images/congratulations.png",pa={name:"DrawPopupResult",inject:["toLogin"],mounted:function(){var t=this;return Object(g["a"])(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:Q({moduleId:"2004",opt:"1",ext:t.drawData.welfareId}),console.log(t.drawData.welfareId);case 2:case"end":return e.stop()}}),e)})))()},props:{visible:{type:Boolean,default:function(){return!1}},drawData:{type:Object,default:function(){return{}}}},data:function(){return{titlePng:da}},methods:{rpt:function(t){var e;"result"==t&&Q({moduleId:"2004",opt:"3",ext:null===(e=this.drawData)||void 0===e?void 0:e.welfareId})},closeDailog:function(t){"result"==t&&(this.visible=!1,this.rpt("result")),this.$emit("close")},checkDrawRusult:function(){var t,e,a;this.closeDailog("result"),Q({moduleId:"2004",opt:"2",ext:null===this||void 0===this||null===(t=this.drawData)||void 0===t?void 0:t.welfareId});var i=null!==(e=null===this||void 0===this||null===(a=this.drawData)||void 0===a?void 0:a.prizeType)&&void 0!==e?e:0;switch(i){case 5:this.$emit("vipShow","vip-packet");break;case 10:this.$emit("vipShow","vip-gift");break;default:this.$emit("vipShow","vip-card");break}}}},ga=pa,ma=(a("f8eb"),Object(x["a"])(ga,la,ua,!1,null,null,null)),fa=ma.exports,ha=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"swiper-box"},[a("div",{staticClass:"pc-slide"},[a("div",{staticClass:"view"},[a("swiper",{ref:"viewSwiper",attrs:{options:t.swiperOption}},t._l(t.swiperInfo,(function(e,i){return a("swiper-slide",{key:i,staticClass:"swiper-item",attrs:{abc:i}},[a("div",{staticStyle:{cursor:"pointer"},on:{mouseenter:t.stopAuto,mouseleave:t.startAuto}},[1==e.type?a("div",{on:{click:function(a){return t.linkClick(e)}}},[a("ImgError",{attrs:{src:e.banner.bannerDetailList[0].imagePath}})],1):a("div",[a("Card",{key:i,staticClass:"card type3",attrs:{item:e,moduleId:"1009"},on:{updateData:t.updateData,cdkeyShow:t.cdKeyShow}})],1)])])})),1)],1)]),a("div",{staticClass:"preview"},[a("swiper",{ref:"previewSwiper",attrs:{options:t.swiperThumbs}},t._l(t.swiperInfo,(function(e,i){return a("swiper-slide",{key:i,staticClass:"swiper-item",class:t.sliderIdx===i?"active-nav":""},[a("div",{staticStyle:{cursor:"pointer"},on:{mouseenter:function(e){return t.mouseenterSwiper(i)},mouseleave:t.startAuto}},[1==e.type?a("div",{on:{click:function(a){return t.linkClick(e)}}},[a("ImgError",{attrs:{src:e.smallImgPath}})],1):a("div",[a("Card",{key:i,staticClass:"card type4",attrs:{item:e,small:"true",moduleId:"1009"},on:{updateData:t.updateData,cdkeyShow:t.cdKeyShow}})],1)])])})),1)],1),a("div",{staticClass:"arrow-left",on:{click:t.swiperLeft}}),a("div",{staticClass:"arrow-right",on:{click:t.swiperRight}})])},va=[],Ia={name:"swiperBox",inject:["jumpExternal"],components:{Card:Et,swiper:y["swiper"],swiperSlide:y["swiperSlide"],ImgError:O},props:{dataInfo:{type:Object,default:function(){}}},computed:{swiper:function(){return this.$refs.viewSwiper.swiper},thumbs:function(){return this.$refs.previewSwiper.swiper},swiperInfo:function(){return this.dataInfo.detailList||[]}},data:function(){var t=this;return{defaultPng:h,sliderIdx:0,swiperOption:{autoplay:{delay:3e3,pauseOnMouseEnter:!0,disableOnInteraction:!1},loop:!0,on:{click:function(t){console.log(t)},slideChange:function(){var e=t.swiper.activeIndex%(t.swiperInfo.length+1)-1;t.sliderIdx=e>=0?e:0,t.thumbs.slideTo(e)}}},swiperThumbs:{slidesPerView:"auto",allowTouchMove:!1}}},mounted:function(){Q({moduleId:"1009",opt:"1"}),this.swiper.params.control=this.thumbs},methods:{changeSwiper:function(t){this.sliderIdx=t,this.swiper.slideTo(t)},stopAuto:function(){this.swiper.autoplay.stop()},startAuto:function(){this.swiper.autoplay.start()},swiperLeft:function(){this.swiper.slidePrev(),Q({moduleId:"1009",opt:"2"})},swiperRight:function(){this.swiper.slideNext(),Q({moduleId:"1009",opt:"2"})},mouseenterSwiper:function(t){this.stopAuto(),this.swiper.slideTo(t+1)},updateData:function(){this.$emit("updateData")},cdKeyShow:function(t){this.$emit("cdkeyShow",t)},linkClick:function(t){switch(Q({moduleId:"1009",opt:"14",ext:t.banner.bannerId}),t.openType||t.banner.bannerDetailList[0].openType){case 1:console.log("启动游戏"),E.startApp(t.gameId||t.banner.bannerDetailList[0].gameId,"0");break;case 2:console.log("在浏览器中打开"),E.openInBrowser((null===t||void 0===t?void 0:t.linkUrl)||t.banner.bannerDetailList[0].linkUrl);break;case 3:console.log("打开游戏详情页"),this.jumpExternal({url:(null===t||void 0===t?void 0:t.linkUrl)||t.banner.bannerDetailList[0].linkUrl});break;default:break}}}},wa=Ia,ba=(a("2fbf"),Object(x["a"])(wa,ha,va,!1,null,"60ab035d",null)),ya=ba.exports,Da=function(){var t=this,e=t.$createElement,a=t._self._c||e;return t.signInConfigList.length?a("div",{staticClass:"signin-dialog"},[a("el-dialog",{attrs:{visible:t.visible,width:"450px"},on:{"update:visible":function(e){t.visible=e},close:t.closeSignInDailog}},[a("div",{staticClass:"sign-in-view"},[a("div",{staticClass:"back"},[t.bgImg?a("img",{staticClass:"back-img",attrs:{src:t.bgImg}}):t._e(),a("div",{staticClass:"sign-hd"},[t._v(t._s(t.isSignIn?"签到成功":"今日签到"))]),a("div",{staticClass:"main_container"},[a("div",{staticClass:"card_row"},t._l(t.signInConfigList.slice(0,3),(function(e){return a("div",{key:e.day,staticClass:"one_card",class:{received:e.day<t.curSignDay,receivedCard:e.day<=t.curSignDay}},[e.isSigned?a("div",{staticClass:"check_icon"}):t._e(),t.isSignIn&&t.curSignDay===e.day?a("div",{staticClass:"check_icon_bg"}):t._e(),e.welfareList&&e.welfareList.length?a("img",{staticClass:"gift-img",attrs:{src:e.img}}):t._e(),a("span",[t._v(t._s(e.isSigned?"已领取":"第"+e.day+"天"))]),e.isSigned?a("div",{staticClass:"card_day card_day_1"}):a("div",{staticClass:"card_day"})])})),0),a("div",{staticClass:"card_row"},t._l(t.signInConfigList.slice(3,6),(function(e){return a("div",{key:e.day,staticClass:"one_card",class:{received:e.day<t.curSignDay,receivedCard:e.day<=t.curSignDay}},[e.isSigned?a("div",{staticClass:"check_icon"}):t._e(),t.isSignIn&&t.curSignDay===e.day?a("div",{staticClass:"check_icon_bg"}):t._e(),e.welfareList&&e.welfareList.length?a("img",{staticClass:"gift-img",attrs:{src:e.img}}):t._e(),a("span",[t._v(t._s(e.isSigned?"已领取":"第"+e.day+"天"))]),e.isSigned?a("div",{staticClass:"card_day card_day_1"}):a("div",{staticClass:"card_day"})])})),0),a("div",{staticClass:"card_row_2"},t._l(t.signInConfigList.slice(6,7),(function(e){return a("div",{key:e.day,staticClass:"one_card_2",class:{received:e.day<t.curSignDay,receivedCard:e.day<=t.curSignDay}},[e.isSigned?a("div",{staticClass:"check_icon"}):t._e(),t.isSignIn&&t.curSignDay===e.day?a("div",{staticClass:"check_icon_bg"}):t._e(),e.welfareList&&e.welfareList.length?a("div",{staticClass:"gift_2"},[e.welfareList&&e.welfareList.length?a("img",{staticClass:"gift-img",attrs:{src:e.img}}):t._e()]):t._e(),a("span",[t._v(t._s(e.isSigned?"已领取":"第"+e.day+"天"))]),e.isSigned?a("div",{staticClass:"card_day card_last_day"}):a("div",{staticClass:"card_day card_last_day_1"})])})),0),t.isSignIn?a("div",{staticClass:"sign_btn to_Use",class:t.curWareList.length?"to_Use":"signed",on:{click:t.jumpBox}},[t._v(" "+t._s(t.curWareList.length?"去使用":"已签到")+" ")]):a("div",{staticClass:"sign_btn to_Use",on:{click:t.clickSignIn}},[a("span",[t._v("签到领奖")])]),t.isSignIn?a("div",{staticClass:"sign_tip"},[t._v("每天签到都可以免费抽奖～")]):t._e(),t.isSignIn?t._e():a("div",{staticClass:"sign_tip"},[t._v("连续7天签到奖励更丰富")])])])])])],1):t._e()},ka=[],Sa={name:"sign-in-dialog",inject:["toLogin"],props:{visible:{type:Boolean,default:function(){return!1}}},data:function(){return{signConfig:null,signPopup:null,bgImg:"",signInConfigList:[],curSignDay:0,isSignIn:!1,isSignToday:!1,curWareList:[]}},emits:["refresh"],methods:{rptData:function(t){Q(Object(p["a"])({},t))},clickSignIn:function(){var t=this;return Object(g["a"])(regeneratorRuntime.mark((function e(){var a,i,n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!sessionStorage.getItem("memberId")){e.next=7;break}return e.next=3,gt();case 3:a=e.sent,0===a.code?(t.isSignIn=!t.isSignIn,i=t.signInConfigList,i[t.curSignDay-1]&&(i[t.curSignDay-1].isSigned=!0),t.signInConfigList=i,Q({moduleId:"2006",opt:"3",ext:t.signConfig.signInId}),t.$message({iconClass:"mySuccess-icon",message:"已签到"})):t.$message({iconClass:"mySuccess-icon",message:null!==(n=String(null===a||void 0===a?void 0:a.msg))&&void 0!==n?n:"服务器异常，请稍后重试"}),e.next=9;break;case 7:t.closeSignInDailog(),t.toLogin(null,{},4);case 9:case"end":return e.stop()}}),e)})))()},closeSignInDailog:function(){this.$emit("close")},jumpBox:function(){if(Q({moduleId:"2006",opt:"4",ext:this.signConfig.signInId}),this.closeSignInDailog(),this.curWareList.length){switch(this.curWareList[0].type){case 8:this.$message({iconClass:"mySuccess-icon",message:"请前往「顺网会员中心」小程序内兑换使用"});break;case 5:"bag",this.$emit("vipShow","vip-packet");break;case 10:"gift",this.$emit("vipShow","vip-gift");break;default:this.$emit("vipShow","vip-card");break}}},initialize:function(){var t=this;return Object(g["a"])(regeneratorRuntime.mark((function e(){var a,i,n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,pt();case 2:if(a=e.sent,t.signConfig=a.data,0!==a.code){e.next=18;break}if(i=!1,t.signInConfigList=a.data.signConfigList,n=0,t.signInConfigList.forEach((function(t,e){"boolean"===typeof(null===t||void 0===t?void 0:t.isSigned)&&null!==t&&void 0!==t&&t.isSigned&&(n=t.day)})),!i){e.next=11;break}return e.abrupt("return");case 11:return t.isSignToday=a.data.isSignToday,t.isSignIn=a.data.isSignToday,t.bgImg=a.data.bgImg,t.isSignToday?(t.curSignDay=n,t.curWareList=a.data.signConfigList[n].welfareList):(t.curSignDay=n+1,t.curWareList=a.data.signConfigList[n].welfareList),e.abrupt("return",!0);case 18:if(-1!==a.code){e.next=22;break}return t.$emit("close"),t.$message({message:"活动未上线",type:"info",customClass:"info-message"}),e.abrupt("return",!1);case 22:case"end":return e.stop()}}),e)})))()}},mounted:function(){var t=this;return Object(g["a"])(regeneratorRuntime.mark((function e(){var a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.initialize();case 2:a=e.sent,Q({moduleId:"2006",opt:"1",ext:t.signConfig.signInId}),a&&sessionStorage.getItem("memberId")&&(null!==t&&void 0!==t&&t.isSignToday||t.clickSignIn()),sessionStorage.setItem("firstEnterDraw","signin");case 6:case"end":return e.stop()}}),e)})))()}},Ca=Sa,_a=(a("2f8a"),Object(x["a"])(Ca,Da,ka,!1,null,null,null)),xa=_a.exports,Pa=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"giftbag-dialog"},[a("el-dialog",{attrs:{visible:t.visible,width:"580px"},on:{"update:visible":function(e){t.visible=e},close:t.closeGiftBagDailog}},[t.isGiftBagReceive?a("div",{staticClass:"af-receive"},[a("Img2gif",{attrs:{imgUrl:t.giftBagData.prizeImg}}),a("div",{staticClass:"img-button",on:{click:t.jumpBox}},[a("Img2gif",{attrs:{imgUrl:t.giftBagData.prizeBtnImg}})],1)],1):a("div",{staticClass:"bf-receive"},[t.giftBagData&&t.giftBagData.bagImg?[a("Img2gif",{staticClass:"img-bg",attrs:{imgUrl:t.giftBagData.bagImg,imgMsg:t.giftBagData.bagImgMsg}})]:t._e(),t.giftBagData&&t.giftBagData.btnImg?a("div",{staticClass:"img-button",on:{click:t.getGiftBag}},[a("Img2gif",{attrs:{imgUrl:t.giftBagData.btnImg,imgMsg:t.giftBagData.btnImgMsg}})],1):t._e()],2)])],1)},Oa=[],Ba=(a("b64b"),{name:"giftbag-dialog",inject:["toLogin"],components:{Img2gif:na},props:{visible:{type:Boolean,default:function(){return!1}}},data:function(){return{isGiftBagReceive:!1,giftBagData:null,loading:!0}},emits:["refresh"],methods:{rptData:function(t){Q(Object(p["a"])({},t))},closeGiftBagDailog:function(){this.$emit("close")},getGiftBag:function(){var t;sessionStorage.getItem("memberId")?(Q({moduleId:"2007",opt:"3",ext:null===(t=this.giftBagData)||void 0===t?void 0:t.id}),this.toReciveGift()):this.toLogin(null,{},5)},jumpBox:function(){var t;Q({moduleId:"2007",opt:"4",ext:null===(t=this.giftBagData)||void 0===t?void 0:t.id}),this.closeGiftBagDailog(),this.$emit("vipShow","vip-gift")},toReciveGift:function(){var t=this;return Object(g["a"])(regeneratorRuntime.mark((function e(){var a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,ft({bagId:t.giftBagData.id,pageId:W.getUrlParameter("id"),memberId:sessionStorage.getItem("memberId")});case 2:a=e.sent,-3==a.code&&(t.$message({message:"暂不符合领取条件",type:"info",customClass:"info-message"}),t.closeGiftBagDailog()),0==a.code&&(t.isGiftBagReceive=!0,t.$message({message:"领取成功",type:"success",customClass:"info-message"}));case 5:case"end":return e.stop()}}),e)})))()},getData:function(){var t=this;return Object(g["a"])(regeneratorRuntime.mark((function e(){var a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,mt({pageId:W.getUrlParameter("id"),memberId:sessionStorage.getItem("memberId")});case 3:a=e.sent,t.loading=!1,0==a.code&&a.data&&Object.keys(a.data).length&&(t.giftBagData=a.data,t.isGiftBagReceive=t.giftBagData.bought),e.next=11;break;case 8:e.prev=8,e.t0=e["catch"](0),t.loading=!1;case 11:case"end":return e.stop()}}),e,null,[[0,8]])})))()}},watch:{visible:{handler:function(t){var e;(this.getData(),t)&&(this.giftBagData?(Q({moduleId:"2007",opt:"1",ext:null===(e=this.giftBagData)||void 0===e?void 0:e.id}),this.isGiftBagReceive&&this.$message({message:"您已领取过该礼包",type:"success",customClass:"info-message"}),sessionStorage.setItem("firstEnterDraw","gift")):(!this.giftBagData||this.giftBagData&&!Object.keys(this.giftBagData).length)&&(this.$emit("close"),this.$message({message:"活动未上线",type:"info",customClass:"info-message"})))},immediate:!0}},mounted:function(){}}),Ea=Ba,La=(a("286c"),Object(x["a"])(Ea,Pa,Oa,!1,null,null,null)),Ta=La.exports,Ra=function(){var t=this,e=t.$createElement,a=t._self._c||e;return t.dataInfo?a("div",{staticClass:"fast-link"},[t.dataInfo.imagePath?a("img",{staticClass:"bg",attrs:{src:t.dataInfo.imagePath}}):t._e(),a("div",{staticClass:"fast-link-content"},t._l(t.dataInfo.detailList,(function(e,i){return a("div",{key:i,staticClass:"item",on:{click:function(a){return t.gotoLink(e)}}},[a("ImgError",{staticClass:"icon-img",attrs:{src:e.extImgPath1}}),a("div",{staticClass:"desc"},[a("p",{staticClass:"name"},[t._v(t._s(e.extVar1))]),a("p",{staticClass:"info"},[t._v(t._s(e.extVar2))])])],1)})),0)]):t._e()},ja=[],Ua={name:"FastLink",components:{ImgError:O},props:{dataInfo:{type:Object,default:function(){}}},data:function(){return{}},mounted:function(){Q({moduleId:"2005",opt:"1",ext:""})},methods:{gotoLink:function(t){var e=document.getElementById(t.welfareId);e.classList.add("hover"),Q({moduleId:"2005",opt:"2",ext:t.welfareId}),e.scrollIntoView({behavior:"smooth",block:"center"})}}},$a=Ua,Aa=(a("9c98"),Object(x["a"])($a,Ra,ja,!1,null,"fc2a02fc",null)),Ma=Aa.exports,Na=a.p+"images/no-data.png",Ga=null,Ka={provide:function(){return{memberId:this.memberId,activeTab:this.activeTab,jumpExternal:this.jumpExternal,toLogin:this.toLogin,vipClose:this.vipClose}},data:function(){return{direction:"default",slides:[],moduleMap:{1:"LRStructures",3:"UpDownStructures",4:"Unwind",7:"TilingModule"},indexModulePathStyle:{width:"980px",height:"300px"},userInfo:null,topBanner:{data:null,state:null},rightBanner:[],allModules:[],showTop:!0,stopTimer:!1,memberId:null,guid:W.guid(),delay:3e3,externalConfig:null,returnPng:bt,nodata:Na,tabList:[],isMiniLogin:!1,loginOpen:!1,activeTab:null,vipDialogShow:!1,cdkeyDialogShow:!1,DrawPopupResultDialogShow:!1,giftBagDialogShow:!1,pondDialogShow:!1,signInDialogShow:!1,cdKey:JSON.parse(sessionStorage.getItem("cdKeyInfo")||"{}"),loading:!0,barId:W.barId(),hasSwiper:null,swipeData:{},mainPageOption:{},popupData:{visible:!1,infoData:{}},appDateInitFlag:!1,privilegeActiveTab:null,autoDraw:!1,fastLinkData:null,drawResult:{}}},components:{HeaderContainer:wt,LRStructures:$t,Unwind:Vt,TilingModule:Jt,UpDownStructures:ee,RightNav:ce,TopNav:fe,Login:ke,VipDialog:$e,CdkeyDialog:Ve,PpwDialog:Je,Swiper:ya,DrawPopup:ca,SignInDailogVue:xa,FastLink:Ma,GiftBagDialog:Ta,DrawPopupResult:fa},watch:{activeTab:function(t){t&&(this.getModuleInfo({tabId:t}),this.getBannerInfo({tabId:t}))}},created:function(){var t=this;this.getTabs(),et().then(function(){var e=Object(g["a"])(regeneratorRuntime.mark((function e(a){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return 0==a.code&&(t.isMiniLogin=!!a.data,t.isMiniLogin||sessionStorage.removeItem("firstEnterDraw"),t.checkUserInfo()),e.next=3,t.popupInit();case 3:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()),window.onscroll=function(e){if(t.hasSwiper)t.showTop=!0;else{var a=e.target.scrollingElement.scrollTop||document.body.scrollTop,i=Date.now();i-Ga>=10&&t.topBanner.data&&(Ga=i,t.showTop=a>70)}}},mounted:function(){Q({moduleId:"1000",opt:"1"})},methods:{vipClose:function(){this.vipDialogShow=!1},getTabs:function(){var t=this;sessionStorage.setItem("pageId",W.getUrlParameter("id")),tt({id:W.getUrlParameter("id")}).then((function(e){0===e.code&&(t.tabList=e.data.tabList||[],t.mainPageOption=e.data||{},(e.data.tabList||[]).length&&(t.activeTab=e.data.tabList[0].id,sessionStorage.setItem("tabId",t.activeTab),t.swiperInit(),t.getFastLinkData()))}))},changeTab:function(t){this.activeTab=t,sessionStorage.setItem("tabId",t)},checkUserInfo:function(){var t=this;if(!0!==this.stopTimer){this.isMiniLogin?_t.a.get("sso.ticket")&&J({}).then(function(){var e=Object(g["a"])(regeneratorRuntime.mark((function e(a){var i,n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(0!==a.code){e.next=20;break}if(t.userInfo=a.data,t.memberId=t.userInfo.memberId,sessionStorage.setItem("memberId",t.memberId||""),t.stopTimer=!0,!_t.a.get("undrawState")||"undrawState"!=_t.a.get("lastId")){e.next=12;break}return e.next=8,t.popupInit("true");case 8:t.pondDialogShow=!0,t.$refs.drawPopup.draw(),_t.a.remove("undrawState"),t.updateData();case 12:i=!1,_t.a.get("unreceivedId")&&"unreceivedId"==_t.a.get("lastId")?Z({memberId:t.memberId,moduleDetailId:_t.a.get("unreceivedId")}).then((function(e){0==e.code&&(_t.a.get("reportData")&&Q(JSON.parse(_t.a.get("reportData"))),_t.a.remove("unreceivedId"),_t.a.remove("reportData"),t.$message({iconClass:"mySuccess-icon",message:"已领取"}),i=!0)})):(_t.a.get("reportData")&&(n=JSON.parse(_t.a.get("reportData")),n.opt=8,Q(n)),_t.a.remove("reportData"),i=!0),_t.a.get("uncdkeyId")&&"uncdkeyId"==_t.a.get("lastId")?(t.cdkeyDialogShow=!0,_t.a.remove("uncdkeyId"),i=!0):i=!0,_t.a.get("unSignInState")&&"unSignInState"==_t.a.get("lastId")?(t.signInDialogShow=!0,_t.a.remove("unSignInState"),i=!0):i=!0,_t.a.get("unGetGiftBagState")&&"unGetGiftBagState"==_t.a.get("lastId")?(t.giftBagDialogShow=!0,t.$refs.giftBagDialog&&(t.$refs.giftBagDialog.getData(),t.$refs.giftBagDialog.getGiftBag()),_t.a.remove("unGetGiftBagState"),i=!0):i=!0,i&&t.updateData(),e.next=22;break;case 20:_t.a.remove("sso.ticket"),sessionStorage.removeItem("memberId");case 22:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()):E.getPassportID()&&(this.memberId=E.getPassportID(),sessionStorage.setItem("memberId",this.memberId||""),q({memberId:E.getPassportID()}).then(function(){var e=Object(g["a"])(regeneratorRuntime.mark((function e(a){var i;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(0!==a.code){e.next=18;break}if(t.userInfo=a.data,t.stopTimer=!0,!_t.a.get("undrawState")||"undrawState"!=_t.a.get("lastId")){e.next=10;break}return e.next=6,t.popupInit("true");case 6:t.pondDialogShow=!0,t.$refs.drawPopup.draw(),_t.a.remove("undrawState"),t.updateData();case 10:return i=!1,e.next=13,t.popupInit();case 13:_t.a.get("unreceivedId")&&"unreceivedId"==_t.a.get("lastId")?Z({memberId:t.memberId,moduleDetailId:_t.a.get("unreceivedId")}).then((function(e){_t.a.remove("unreceivedId"),_t.a.remove("reportData"),0==e.code&&(t.$message({iconClass:"mySuccess-icon",message:"已领取"}),i=!0)})):i=!0,_t.a.get("uncdkeyId")&&"uncdkeyId"==_t.a.get("lastId")?(t.cdkeyDialogShow=!0,_t.a.remove("uncdkeyId"),i=!0):i=!0,_t.a.get("unSignInState")&&"unSignInState"==_t.a.get("lastId")?(t.signInDialogShow=!0,_t.a.remove("unSignInState"),i=!0):i=!0,_t.a.get("unGetGiftBagState")&&"unGetGiftBagState"==_t.a.get("lastId")?(t.giftBagDialogShow=!0,t.$refs.giftBagDialog.getGiftBag(),_t.a.remove("unGetGiftBagState"),i=!0):i=!0,i&&t.updateData();case 18:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()));var e=setTimeout((function(){clearTimeout(e),t.checkUserInfo()}),this.delay)}},getBannerInfo:function(t){var e=this;H(Object(p["a"])({platform:1,barId:this.barId},t)).then((function(t){0===t.code&&(t.data||[]).forEach((function(t){var a;1===t.type&&(0!==(null===t||void 0===t||null===(a=t.bannerDetailList)||void 0===a?void 0:a.length)?(e.topBanner.data=t.bannerDetailList,e.topBanner.state=1):e.topBanner.state=0,e.showTop=!1);2===t.type&&(e.rightBanner=t.bannerDetailList)}))}))},getModuleInfo:function(t){var e=this;F(Object(p["a"])(Object(p["a"])({},t),{},{platform:1,memberId:this.memberId||null,guid:this.guid,barId:this.barId})).then((function(t){0===t.code&&(e.allModules=t.data||[],e.loading=!1)}))},updateData:function(){this.getModuleInfo({tabId:this.activeTab})},login:function(){this.isMiniLogin?this.loginOpen=!0:E.login()},logout:function(){var t=this;this.isMiniLogin?st({mode:1,memberId:this.memberId}).then((function(e){0==e.code&&(t.userInfo=null,t.memberId="",sessionStorage.removeItem("memberId"),window.location.reload())})):(this.userInfo=null,this.memberId="",sessionStorage.removeItem("memberId"),E.logout(),window.location.reload())},jumpExternal:function(t){this.externalConfig=t},toLogin:function(t,e,a,i){console.log(t,e,a,i),Q({moduleId:"1001",opt:4}),2==a?(t&&(_t.a.set("uncdkeyId",t),_t.a.set("lastId","uncdkeyId")),this.cdKey=i,sessionStorage.setItem("cdKeyInfo",JSON.stringify(i))):3==a?(_t.a.set("undrawState",!0),_t.a.set("lastId","undrawState")):4==a?(Q({moduleId:"2006",opt:"2"}),_t.a.set("unSignInState",!0),_t.a.set("lastId","unSignInState")):5==a?(Q({moduleId:"2007",opt:"2"}),_t.a.set("unGetGiftBagState",!0),_t.a.set("lastId","unGetGiftBagState")):(t&&(_t.a.set("unreceivedId",t),_t.a.set("lastId","unreceivedId")),e&&_t.a.set("reportData",JSON.stringify(e))),this.login()},vipShow:function(t){this.vipDialogShow=!0,this.privilegeActiveTab=t,console.log(t)},cdKeyShow:function(t){this.cdkeyDialogShow=!0,this.cdKey=t},signInShow:function(){this.signInDialogShow=!0},giftBagShow:function(){this.giftBagDialogShow=!0},pondShow:function(){this.pondDialogShow=!0},DrawPopupResultShow:function(t){this.drawData=t,this.DrawPopupResultDialogShow=!0},swiperInit:function(){var t=this;ct({platform:1,memberId:this.memberId||null,guid:this.guid,barId:this.barId,tabId:this.activeTab}).then((function(e){0==e.code&&e.data&&(t.hasSwiper=!0,t.swipeData=e.data[0])}))},popupInit:function(t){var e=this;return Object(g["a"])(regeneratorRuntime.mark((function a(){var i;return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(i={},!t){a.next=7;break}return a.next=4,ot({checkStrategy:!1,pageId:W.getUrlParameter("id"),memberId:e.memberId});case 4:i=a.sent,a.next=10;break;case 7:return a.next=9,ot({pageId:W.getUrlParameter("id"),memberId:e.memberId});case 9:i=a.sent;case 10:return 0==i.code&&(e.isMiniLogin&&!W.getUrlParameter("returnUrl")||!e.isMiniLogin&&!sessionStorage.getItem("firstEnterDraw")?2==i.data.type?(e.popupData.code=0,e.popupData.infoData=i.data,e.popupData.pond=!0,e.pondShow()):4==i.data.type?e.signInShow():5==i.data.type?e.giftBagShow():(e.popupData.code=0,e.popupData.infoData=i.data):(e.popupData.code=0,e.popupData.infoData=i.data),e.appDateInitFlag=!0,Q({moduleId:"2002",opt:"1",ext:e.popupData.infoData.id})),a.next=13,new Promise((function(t){return setTimeout(t,200)}));case 13:case"end":return a.stop()}}),a)})))()},openDrawPopup:function(t){var e=this;return Object(g["a"])(regeneratorRuntime.mark((function a(){return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(1==t&&(e.signInShow(),Q({moduleId:"2006",opt:"5"})),2==t&&(e.giftBagShow(),Q({moduleId:"2007",opt:"5"})),3==t&&(e.popupData.pond=!1,e.pondShow(),Q({moduleId:"2003",opt:"5"})),!1!==e.appDateInitFlag){a.next=5;break}return a.abrupt("return");case 5:case"end":return a.stop()}}),a)})))()},getFastLinkData:function(){var t=this;return Object(g["a"])(regeneratorRuntime.mark((function e(){var a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,ut({tabId:t.activeTab,barId:t.barId,structure:9});case 2:a=e.sent,0==a.code&&(t.fastLinkData=a.data[0]);case 4:case"end":return e.stop()}}),e)})))()}}},Va=Ka,za=(a("e5e0"),a("9bc0"),Object(x["a"])(Va,u,d,!1,null,"28ec0dde",null)),Wa=za.exports;a("8694"),a("0fae");l["default"].config.productionTip=!1,l["default"].use(E,c.a),l["default"].use(s.a),l["default"].use(n.a),l["default"].prototype.$message=c.a,new l["default"]({render:function(t){return t(Wa)}}).$mount("#app")},4305:function(t,e,a){},"44b7":function(t,e,a){},"4d42":function(t,e,a){"use strict";a("2331")},"4eba":function(t,e,a){"use strict";a("29cb")},"5ad1":function(t,e,a){"use strict";a("a346")},"5b00":function(t,e,a){},"602b":function(t,e,a){},"6be1":function(t,e,a){"use strict";a("ad0e")},"6e49":function(t,e,a){},"7bd8":function(t,e,a){},"81eb":function(t,e,a){"use strict";a("278f")},8694:function(t,e,a){},9359:function(t,e,a){"use strict";a("2aec")},9778:function(t,e,a){},9879:function(t,e,a){},"9bc0":function(t,e,a){"use strict";a("e18c")},"9c98":function(t,e,a){"use strict";a("5b00")},a346:function(t,e,a){},ad0e:function(t,e,a){},afb0:function(t,e,a){"use strict";a("6e49")},b3bc:function(t,e,a){},b4ac:function(t,e,a){"use strict";a("44b7")},c241:function(t,e,a){},d820:function(t,e,a){"use strict";a("9778")},e18c:function(t,e,a){},e29e:function(t,e,a){},e3307:function(t,e,a){},e5e0:function(t,e,a){"use strict";a("e3307")},f61f:function(t,e,a){},f641:function(t,e,a){"use strict";a("c241")},f8eb:function(t,e,a){"use strict";a("e29e")}});