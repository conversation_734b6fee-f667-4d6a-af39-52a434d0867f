package com.shunwang.marketing.interceptor;

import com.shunwang.marketing.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.text.DateFormat;
import java.text.ParseException;
import java.util.Date;

@Slf4j
@Component
public class NewSignInterceptor extends SignInterceptor {
    @Override
    protected boolean validTime(String time) {
        DateFormat format = DateUtil.getInstance("yyyyMMddHHmmss");
        try {
            Date date = DateUtil.praseDate(format, time);
            if (date == null) {
                return false;
            }
            Date now = new Date();
            long diff = DateUtil.compare(now, date, DateUtil.ONE_MINUTE);
            return diff > -10 && diff < 10;
        } catch (ParseException e) {
            return false;
        }
    }
}
