package com.shunwang.marketing.manager.response.paopao;

import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.shunwang.marketing.exception.CommonException;
import com.shunwang.marketing.manager.IResponse;
import com.shunwang.util.lang.StringUtil;
import lombok.Data;

@Data
public class SendGiftResponse implements IResponse {
    private String code;
    private String msg;
    private boolean success;
    private String data;
    private String json;

    @Override
    public void setResponse(String resp) {
        this.json = resp;
    }

    /**
     * {"code":"0","msg":"success","success":true,"data":null}
     * @return
     * @throws Exception
     */
    @Override
    public SendGiftResponse parse() throws Exception {
        try {
            if (StringUtil.isBlank(json)) {
                return null;
            }
            JsonObject jsonObject = JsonParser.parseString(json).getAsJsonObject();
            code = jsonObject.get("code").getAsString();
            msg = jsonObject.get("msg").getAsString();
            success = jsonObject.get("success").getAsBoolean();
            return this;
        } catch (Exception e) {
            throw new CommonException("泡泡商品发放解析失败,响应内容:" + json);
        }
    }
}
