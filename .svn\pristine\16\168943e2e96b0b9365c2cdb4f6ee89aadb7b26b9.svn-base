package com.shunwang.marketing.enums.marketing;

import lombok.Getter;
import lombok.Setter;

public interface BagOrderEnum {

    enum StateEnum {
        CREATE(1, "创建"),
        SUCCESS(2, "成功"),
        FAILED(3, "失败"),
        CLOSE(4, "关闭"),
        REFUNDING(5, "退款中"),
        REFUND(6, "退款完成"),
        ;
        StateEnum(int value, String name) {
            this.value = value;
            this.name = name;
        }

        private @Getter @Setter int value;
        private @Getter @Setter String name;
    }



}
