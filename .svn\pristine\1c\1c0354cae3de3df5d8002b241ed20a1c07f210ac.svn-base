package com.shunwang.marketing.service.ai;

import com.shunwang.marketing.pojo.marketing.AiModel;
import com.shunwang.marketing.pojo.marketing.AiRecord;
import lombok.Data;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.List;

@Data
public class AiSseEmitterWrapper {
    private SseEmitter sseEmitter;
    private String requestId;
    private Integer memberId;
    private AiModel model;
    private String question;
    private Integer session;
    private List<AiRecord> history;
}
