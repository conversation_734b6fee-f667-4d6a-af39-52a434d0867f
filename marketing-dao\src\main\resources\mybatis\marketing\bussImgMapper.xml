<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shunwang.marketing.dao.marketing.BussImgMapper">
    <resultMap id="BaseResultMap" type="bussImg">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="bussType" column="buss_type" jdbcType="INTEGER"/>
        <result property="bussId" column="buss_id" jdbcType="INTEGER"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="url" column="url" jdbcType="VARCHAR"/>
        <result property="sort" column="sort" jdbcType="INTEGER"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
    </resultMap>
    
    <sql id="baseColumn">
        id,
        buss_type,
        buss_id,
        name,
        url,
        sort,
        remark
    </sql>
    
    <sql id="base_where">
        <trim prefix="where" prefixOverrides="and|or">
            <if test="id != null">
                and id = #{id,jdbcType=INTEGER}
            </if>
            <if test="bussType != null">
                and buss_type = #{bussType,jdbcType=INTEGER}
            </if>
            <if test="bussId != null">
                and buss_id = #{bussId,jdbcType=INTEGER}
            </if>
            <if test="name != null and name != ''">
                and name = #{name,jdbcType=VARCHAR}
            </if>
            <if test="url != null and url != ''">
                and url = #{url,jdbcType=VARCHAR}
            </if>
            <if test="sort != null">
                and sort = #{sort,jdbcType=INTEGER}
            </if>
            <if test="remark != null and remark != ''">
                and remark = #{remark,jdbcType=VARCHAR}
            </if>
        </trim>
    </sql>
    
    <select id="find" parameterType="com.shunwang.marketing.query.BaseQuery" resultMap="BaseResultMap">
        select 
        <include refid="baseColumn" />
        from marketing_buss_img t 
        <include refid="base_where" />
        order by sort asc, id asc
    </select>
    
    <select id="findCnt" parameterType="com.shunwang.marketing.query.BaseQuery" resultType="java.lang.Integer">
        select
            count(*)
        from marketing_buss_img t
        <include refid="base_where" />
    </select>
    
    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select
        <include refid="baseColumn" />
        from marketing_buss_img
        where id = #{id,jdbcType=INTEGER}
    </select>

    <select id="findByBussTypeAndBussId" resultMap="BaseResultMap">
        select
        <include refid="baseColumn" />
        from marketing_buss_img
        where buss_type = #{bussType,jdbcType=INTEGER}
        and buss_id = #{bussId,jdbcType=INTEGER}
        order by sort asc, id asc
    </select>

    <select id="findByBussType" resultMap="BaseResultMap">
        select
        <include refid="baseColumn" />
        from marketing_buss_img
        where buss_type = #{bussType,jdbcType=INTEGER}
        order by sort asc, id asc
    </select>

    <insert id="save" parameterType="bussImg" keyProperty="id" useGeneratedKeys="true">
        insert into marketing_buss_img
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null">
                id,
            </if>
            <if test="bussType != null">
                buss_type,
            </if>
            <if test="bussId != null">
                buss_id,
            </if>
            <if test="name != null and name != ''">
                name,
            </if>
            <if test="url != null and url != ''">
                url,
            </if>
            <if test="sort != null">
                sort,
            </if>
            <if test="remark != null and remark != ''">
                remark,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="id != null">
                #{id,jdbcType=INTEGER},
            </if>
            <if test="bussType != null">
                #{bussType,jdbcType=INTEGER},
            </if>
            <if test="bussId != null">
                #{bussId,jdbcType=INTEGER},
            </if>
            <if test="name != null and name != ''">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="url != null and url != ''">
                #{url,jdbcType=VARCHAR},
            </if>
            <if test="sort != null">
                #{sort,jdbcType=INTEGER},
            </if>
            <if test="remark != null and remark != ''">
                #{remark,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="bussImg">
        update marketing_buss_img
        <set>
            <if test="bussType != null">
                buss_type = #{bussType,jdbcType=INTEGER},
            </if>
            <if test="bussId != null">
                buss_id = #{bussId,jdbcType=INTEGER},
            </if>
            <if test="name != null and name != ''">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="url != null and url != ''">
                url = #{url,jdbcType=VARCHAR},
            </if>
            <if test="sort != null">
                sort = #{sort,jdbcType=INTEGER},
            </if>
            <if test="remark != null and remark != ''">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
        </set>
        <trim prefix="where" prefixOverrides="and|or">
            id = #{id,jdbcType=INTEGER}
        </trim>
    </update>

    <delete id="delete" parameterType="integer">
        delete from marketing_buss_img where id = #{id,jdbcType=INTEGER}
    </delete>
</mapper>
