package com.shunwang.marketing.service.marketing.impl;

import com.shunwang.marketing.context.ThreadLocalContext;
import com.shunwang.marketing.context.UserContext;
import com.shunwang.marketing.dao.BaseMapper;
import com.shunwang.marketing.dao.marketing.BagOrderMapper;
import com.shunwang.marketing.dto.UserInfo;
import com.shunwang.marketing.enums.marketing.BagOrderEnum;
import com.shunwang.marketing.enums.marketing.PrizeEnum;
import com.shunwang.marketing.enums.marketing.WelfareEnum;
import com.shunwang.marketing.pojo.Pageable;
import com.shunwang.marketing.pojo.marketing.*;
import com.shunwang.marketing.pojo.personal.Member;
import com.shunwang.marketing.query.marketing.BagOrderQuery;
import com.shunwang.marketing.service.impl.BaseServiceImpl;
import com.shunwang.marketing.service.marketing.*;
import com.shunwang.marketing.util.MemberUtil;
import com.shunwang.util.date.DateUtil;
import com.shunwang.util.lang.StringUtil;
import com.shunwang.util.math.RandomUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Slf4j
@Component
public class BagOrderServiceImpl extends BaseServiceImpl<BagOrder> implements BagOrderService {
    @Autowired
    private BagOrderMapper bagOrderMapper;
    @Autowired
    private GiftBagService giftBagService;
    @Autowired
    private PrizeService prizeService;
    @Autowired
    private WelfareService welfareService;
    @Autowired
    private PrizeRecordService prizeRecordService;
    @Autowired
    private PayOrderRecordService payOrderRecordService;
    @Override
    public BaseMapper getDao() {
        return bagOrderMapper;
    }


    public BagOrder buildBagOrder(GiftBag giftBag){
        BagOrder bagOrder = new BagOrder();
        bagOrder.setBagId(giftBag.getId());
        bagOrder.setOrderNo(buildOrderNo("BUY"));
        bagOrder.setMemberId(MemberUtil.getNotNullMemberId());
        bagOrder.setCnt(1);
        bagOrder.setOrderPrice(giftBag.getPrice());
        bagOrder.setState(BagOrderEnum.StateEnum.CREATE.getValue());
        bagOrder.setCreateTime(new Date());
        this.save(bagOrder);
        return bagOrder;
    }

    public BagOrder getByOrderNo(String orderNo){
        BagOrderQuery query = new BagOrderQuery();
        query.setOrderNo(orderNo);
        List<BagOrder> bagOrders = this.find(query);
        if (bagOrders == null || bagOrders.isEmpty()){
            return null;
        }

        return bagOrders.get(0);
    }


    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void finishOrder(BagOrder order) throws Exception {

        Member member = MemberUtil.loadMember(order.getMemberId());
        UserInfo userInfo = new UserInfo();
        userInfo.setMemberId(order.getMemberId());
        userInfo.setMemberName(member.getMemberName());

        //将礼包奖品发放给用户
        GiftBag giftBag = giftBagService.get(order.getBagId());
        if (!giftBagService.checkStock(giftBag)){
            log.info("库存不足，无法给用户发放奖品，订单号：[{}], 支付单号：[{}]", order.getOrderNo(), order.getPayOrderNo());
            throw new Exception();
        }
        giftBag.setCnt(giftBag.getCnt() - 1);
        giftBagService.update(giftBag);

        List<Prize> prizes = prizeService.getByBagId(giftBag.getId());
        for (Prize prize : prizes){
            //礼包内商品有多个，需要发放多次
            for (int i = 0; i < prize.getBagPrizeCnt(); i++){
                Welfare welfare = welfareService.get(prize.getWelfareId());
                if (PrizeEnum.PrizeTypeEnum.RED_PACKET.getValue() == prize.getPrizeType().intValue()){
                    prizeService.processRedPacketPrize(welfare, "礼包", order.getBagId(), order.getOrderNo(), userInfo);
                    savePrizeRecord(prize,welfare,order,userInfo,giftBag,null,"礼包",null);
                } else {
                    givePrize(prize, welfare, order, userInfo, giftBag, null,"礼包");
                }

            }
        }

        order.setFinishTime(new Date());
        int result = this.bagOrderMapper.updatePaySuccess(order);
        //如果未更新状态成功，直接抛出异常，回滚数据
        if (result < 1){
            log.info("订单已经处理过了");
            throw new Exception("订单已经处理过了");
        }
    }

    public void givePrize(Prize prize, Welfare welfare, BagOrder order, UserInfo userInfo, GiftBag giftBag, Integer pondId,String source){

        //先减权益的库存
        int welfareResult = welfareService.decreaseCnt(welfare);
        if (welfareResult < 1){
            log.info("权益库存不足，无法给用户发放奖品，奖品id：[{}], 权益id：[{}]", prize.getId(), prize.getWelfareId());
            return;
        }

        //保存奖品记录
        String cdkey = "";
        if (welfare.getType().intValue() == WelfareEnum.TypeEnum.PC_CDKEY.getValue()){
            cdkey = welfareService.receiveCdkeyWelfare(welfare, userInfo, true);
            if (StringUtil.isBlank(cdkey)){
                log.info("权益兑换码库存不足，购买礼包失败pondId=[{}],WelfareId=[{}], userId=[{}]", giftBag.getId(), prize.getWelfareId(), UserContext.getUserId());
                welfare = welfareService.get(prize.getWelfareId());
            }
        }
        savePrizeRecord(prize, welfare, order, userInfo, giftBag, pondId, source, cdkey);
    }

    private void savePrizeRecord(Prize prize, Welfare welfare, BagOrder order, UserInfo userInfo, GiftBag giftBag, Integer pondId, String source, String cdkey) {
        PrizeRecord prizeRecord = new PrizeRecord();
        prizeRecord.setPrizeName(prize.getPrizeName());
        prizeRecord.setPrizeId(prize.getId());
        prizeRecord.setPrizeType(prize.getPrizeType());
        prizeRecord.setWelfareId(prize.getWelfareId());
        prizeRecord.setBagId(giftBag.getId());
        prizeRecord.setMemberId(userInfo.getMemberId());
        prizeRecord.setPondId(pondId);
        prizeRecord.setWelfareName(welfare.getName());
        prizeRecord.setDescription(prize.getDescription());
        if (order != null) {
            prizeRecord.setOrderNo(order.getOrderNo());
            prizeRecord.setSource(source);
        }
        prizeRecord.setChannel("" + ThreadLocalContext.getTempData(ThreadLocalContext.CHANNEL));
        prizeRecord.setSendTime(new Date());
        prizeRecord.setRemark(cdkey);
        prizeRecordService.save(prizeRecord);
    }

    @Transactional(rollbackFor = Throwable.class)
    public void buyFreeBag(GiftBag giftBag) throws Exception {
        BagOrder bagOrder = this.buildBagOrder(giftBag);
        finishOrder(bagOrder);
    }

    /**
     * 保存抽奖订单
     * @param bagId
     */
    public BagOrder saveCjOrder(Integer bagId, Integer memberId){
        BagOrder bagOrder = new BagOrder();
        bagOrder.setMemberId(memberId);
        bagOrder.setBagId(bagId);
        bagOrder.setOrderNo(buildOrderNo("CJ"));
        bagOrder.setCnt(1);
        bagOrder.setOrderPrice(BigDecimal.ZERO);
        bagOrder.setRemark("抽奖礼包");
        bagOrder.setState(BagOrderEnum.StateEnum.SUCCESS.getValue());
        Date now = new Date();
        bagOrder.setCreateTime(now);
        bagOrder.setFinishTime(now);
        this.bagOrderMapper.save(bagOrder);
        return bagOrder;
    }

    private String buildOrderNo(String prefix){
        return prefix + DateUtil.getCurrentDateStamp() + RandomUtil.getRandomStr(6);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void afterCreateOrder(BagOrder bagOrder, String disburseNo) {
        bagOrder.setPayOrderNo(disburseNo);
        bagOrder.setEditTime(new Date());
        update(bagOrder);

        payOrderRecordService.createAndSavePayOrderRecord(disburseNo, bagOrder.getMemberId());
    }

    public Integer getCntByDayLimit(Integer bagId, Integer memberId, Date startTime){
        return bagOrderMapper.getCntByDayLimit(bagId, memberId, startTime);
    }

    @Override
    public List<BagOrder> getUserBagOrderList(Pageable pageAble) {
        return bagOrderMapper.getUserBagOrderList(pageAble);
    }

    @Override
    public Integer getCntByMember(Integer memberId) {
        return bagOrderMapper.getCntByMember(memberId);
    }

    public BagOrder getTodayGiftBag(Integer memberId, Integer bagId, Date time) {
        return bagOrderMapper.getTodayGiftBag(memberId, bagId, time);
    }
}
