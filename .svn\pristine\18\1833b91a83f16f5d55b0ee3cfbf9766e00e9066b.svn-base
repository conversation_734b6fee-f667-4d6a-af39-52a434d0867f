var yf=Object.defineProperty;var mf=(e,t,n)=>t in e?yf(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var _e=(e,t,n)=>mf(e,typeof t!="symbol"?t+"":t,n);(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))r(o);new MutationObserver(o=>{for(const i of o)if(i.type==="childList")for(const s of i.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&r(s)}).observe(document,{childList:!0,subtree:!0});function n(o){const i={};return o.integrity&&(i.integrity=o.integrity),o.referrerPolicy&&(i.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?i.credentials="include":o.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(o){if(o.ep)return;o.ep=!0;const i=n(o);fetch(o.href,i)}})();/**
* @vue/shared v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function rs(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const Se={},bn=[],Lt=()=>{},vf=()=>!1,zr=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),os=e=>e.startsWith("onUpdate:"),rt=Object.assign,is=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},bf=Object.prototype.hasOwnProperty,ye=(e,t)=>bf.call(e,t),re=Array.isArray,wn=e=>Wr(e)==="[object Map]",Ql=e=>Wr(e)==="[object Set]",ie=e=>typeof e=="function",qe=e=>typeof e=="string",Ht=e=>typeof e=="symbol",De=e=>e!==null&&typeof e=="object",Jl=e=>(De(e)||ie(e))&&ie(e.then)&&ie(e.catch),Zl=Object.prototype.toString,Wr=e=>Zl.call(e),wf=e=>Wr(e).slice(8,-1),Xl=e=>Wr(e)==="[object Object]",ss=e=>qe(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,zn=rs(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),Kr=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},Sf=/-(\w)/g,Et=Kr(e=>e.replace(Sf,(t,n)=>n?n.toUpperCase():"")),xf=/\B([A-Z])/g,Jt=Kr(e=>e.replace(xf,"-$1").toLowerCase()),Vr=Kr(e=>e.charAt(0).toUpperCase()+e.slice(1)),Ao=Kr(e=>e?`on${Vr(e)}`:""),Gt=(e,t)=>!Object.is(e,t),Er=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},Yl=(e,t,n,r=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:r,value:n})},Li=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let Hs;const Gr=()=>Hs||(Hs=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function Qr(e){if(re(e)){const t={};for(let n=0;n<e.length;n++){const r=e[n],o=qe(r)?_f(r):Qr(r);if(o)for(const i in o)t[i]=o[i]}return t}else if(qe(e)||De(e))return e}const Ef=/;(?![^(]*\))/g,Af=/:([^]+)/,Rf=/\/\*[^]*?\*\//g;function _f(e){const t={};return e.replace(Rf,"").split(Ef).forEach(n=>{if(n){const r=n.split(Af);r.length>1&&(t[r[0].trim()]=r[1].trim())}}),t}function bt(e){let t="";if(qe(e))t=e;else if(re(e))for(let n=0;n<e.length;n++){const r=bt(e[n]);r&&(t+=r+" ")}else if(De(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const kf="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",Of=rs(kf);function ec(e){return!!e||e===""}const tc=e=>!!(e&&e.__v_isRef===!0),cn=e=>qe(e)?e:e==null?"":re(e)||De(e)&&(e.toString===Zl||!ie(e.toString))?tc(e)?cn(e.value):JSON.stringify(e,nc,2):String(e),nc=(e,t)=>tc(t)?nc(e,t.value):wn(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[r,o],i)=>(n[Ro(r,i)+" =>"]=o,n),{})}:Ql(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>Ro(n))}:Ht(t)?Ro(t):De(t)&&!re(t)&&!Xl(t)?String(t):t,Ro=(e,t="")=>{var n;return Ht(e)?`Symbol(${(n=e.description)!=null?n:t})`:e};/**
* @vue/reactivity v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let gt;class Cf{constructor(t=!1){this.detached=t,this._active=!0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=gt,!t&&gt&&(this.index=(gt.scopes||(gt.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].pause();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].resume();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].resume()}}run(t){if(this._active){const n=gt;try{return gt=this,t()}finally{gt=n}}}on(){gt=this}off(){gt=this.parent}stop(t){if(this._active){this._active=!1;let n,r;for(n=0,r=this.effects.length;n<r;n++)this.effects[n].stop();for(this.effects.length=0,n=0,r=this.cleanups.length;n<r;n++)this.cleanups[n]();if(this.cleanups.length=0,this.scopes){for(n=0,r=this.scopes.length;n<r;n++)this.scopes[n].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const o=this.parent.scopes.pop();o&&o!==this&&(this.parent.scopes[this.index]=o,o.index=this.index)}this.parent=void 0}}}function Tf(){return gt}let Oe;const _o=new WeakSet;class rc{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,gt&&gt.active&&gt.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,_o.has(this)&&(_o.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||ic(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,js(this),sc(this);const t=Oe,n=Rt;Oe=this,Rt=!0;try{return this.fn()}finally{ac(this),Oe=t,Rt=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)cs(t);this.deps=this.depsTail=void 0,js(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?_o.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){$i(this)&&this.run()}get dirty(){return $i(this)}}let oc=0,Wn,Kn;function ic(e,t=!1){if(e.flags|=8,t){e.next=Kn,Kn=e;return}e.next=Wn,Wn=e}function as(){oc++}function ls(){if(--oc>0)return;if(Kn){let t=Kn;for(Kn=void 0;t;){const n=t.next;t.next=void 0,t.flags&=-9,t=n}}let e;for(;Wn;){let t=Wn;for(Wn=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(r){e||(e=r)}t=n}}if(e)throw e}function sc(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function ac(e){let t,n=e.depsTail,r=n;for(;r;){const o=r.prevDep;r.version===-1?(r===n&&(n=o),cs(r),Pf(r)):t=r,r.dep.activeLink=r.prevActiveLink,r.prevActiveLink=void 0,r=o}e.deps=t,e.depsTail=n}function $i(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(lc(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function lc(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===er))return;e.globalVersion=er;const t=e.dep;if(e.flags|=2,t.version>0&&!e.isSSR&&e.deps&&!$i(e)){e.flags&=-3;return}const n=Oe,r=Rt;Oe=e,Rt=!0;try{sc(e);const o=e.fn(e._value);(t.version===0||Gt(o,e._value))&&(e._value=o,t.version++)}catch(o){throw t.version++,o}finally{Oe=n,Rt=r,ac(e),e.flags&=-3}}function cs(e,t=!1){const{dep:n,prevSub:r,nextSub:o}=e;if(r&&(r.nextSub=o,e.prevSub=void 0),o&&(o.prevSub=r,e.nextSub=void 0),n.subs===e&&(n.subs=r,!r&&n.computed)){n.computed.flags&=-5;for(let i=n.computed.deps;i;i=i.nextDep)cs(i,!0)}!t&&!--n.sc&&n.map&&n.map.delete(n.key)}function Pf(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let Rt=!0;const cc=[];function Zt(){cc.push(Rt),Rt=!1}function Xt(){const e=cc.pop();Rt=e===void 0?!0:e}function js(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=Oe;Oe=void 0;try{t()}finally{Oe=n}}}let er=0;class If{constructor(t,n){this.sub=t,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class us{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0}track(t){if(!Oe||!Rt||Oe===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==Oe)n=this.activeLink=new If(Oe,this),Oe.deps?(n.prevDep=Oe.depsTail,Oe.depsTail.nextDep=n,Oe.depsTail=n):Oe.deps=Oe.depsTail=n,uc(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const r=n.nextDep;r.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=r),n.prevDep=Oe.depsTail,n.nextDep=void 0,Oe.depsTail.nextDep=n,Oe.depsTail=n,Oe.deps===n&&(Oe.deps=r)}return n}trigger(t){this.version++,er++,this.notify(t)}notify(t){as();try{for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{ls()}}}function uc(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let r=t.deps;r;r=r.nextDep)uc(r)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const Tr=new WeakMap,on=Symbol(""),Fi=Symbol(""),tr=Symbol("");function et(e,t,n){if(Rt&&Oe){let r=Tr.get(e);r||Tr.set(e,r=new Map);let o=r.get(n);o||(r.set(n,o=new us),o.map=r,o.key=n),o.track()}}function qt(e,t,n,r,o,i){const s=Tr.get(e);if(!s){er++;return}const a=l=>{l&&l.trigger()};if(as(),t==="clear")s.forEach(a);else{const l=re(e),u=l&&ss(n);if(l&&n==="length"){const c=Number(r);s.forEach((f,d)=>{(d==="length"||d===tr||!Ht(d)&&d>=c)&&a(f)})}else switch((n!==void 0||s.has(void 0))&&a(s.get(n)),u&&a(s.get(tr)),t){case"add":l?u&&a(s.get("length")):(a(s.get(on)),wn(e)&&a(s.get(Fi)));break;case"delete":l||(a(s.get(on)),wn(e)&&a(s.get(Fi)));break;case"set":wn(e)&&a(s.get(on));break}}ls()}function Df(e,t){const n=Tr.get(e);return n&&n.get(t)}function gn(e){const t=fe(e);return t===e?t:(et(t,"iterate",tr),xt(e)?t:t.map(tt))}function Jr(e){return et(e=fe(e),"iterate",tr),e}const Nf={__proto__:null,[Symbol.iterator](){return ko(this,Symbol.iterator,tt)},concat(...e){return gn(this).concat(...e.map(t=>re(t)?gn(t):t))},entries(){return ko(this,"entries",e=>(e[1]=tt(e[1]),e))},every(e,t){return Ft(this,"every",e,t,void 0,arguments)},filter(e,t){return Ft(this,"filter",e,t,n=>n.map(tt),arguments)},find(e,t){return Ft(this,"find",e,t,tt,arguments)},findIndex(e,t){return Ft(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return Ft(this,"findLast",e,t,tt,arguments)},findLastIndex(e,t){return Ft(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return Ft(this,"forEach",e,t,void 0,arguments)},includes(...e){return Oo(this,"includes",e)},indexOf(...e){return Oo(this,"indexOf",e)},join(e){return gn(this).join(e)},lastIndexOf(...e){return Oo(this,"lastIndexOf",e)},map(e,t){return Ft(this,"map",e,t,void 0,arguments)},pop(){return Nn(this,"pop")},push(...e){return Nn(this,"push",e)},reduce(e,...t){return zs(this,"reduce",e,t)},reduceRight(e,...t){return zs(this,"reduceRight",e,t)},shift(){return Nn(this,"shift")},some(e,t){return Ft(this,"some",e,t,void 0,arguments)},splice(...e){return Nn(this,"splice",e)},toReversed(){return gn(this).toReversed()},toSorted(e){return gn(this).toSorted(e)},toSpliced(...e){return gn(this).toSpliced(...e)},unshift(...e){return Nn(this,"unshift",e)},values(){return ko(this,"values",tt)}};function ko(e,t,n){const r=Jr(e),o=r[t]();return r!==e&&!xt(e)&&(o._next=o.next,o.next=()=>{const i=o._next();return i.value&&(i.value=n(i.value)),i}),o}const Lf=Array.prototype;function Ft(e,t,n,r,o,i){const s=Jr(e),a=s!==e&&!xt(e),l=s[t];if(l!==Lf[t]){const f=l.apply(e,i);return a?tt(f):f}let u=n;s!==e&&(a?u=function(f,d){return n.call(this,tt(f),d,e)}:n.length>2&&(u=function(f,d){return n.call(this,f,d,e)}));const c=l.call(s,u,r);return a&&o?o(c):c}function zs(e,t,n,r){const o=Jr(e);let i=n;return o!==e&&(xt(e)?n.length>3&&(i=function(s,a,l){return n.call(this,s,a,l,e)}):i=function(s,a,l){return n.call(this,s,tt(a),l,e)}),o[t](i,...r)}function Oo(e,t,n){const r=fe(e);et(r,"iterate",tr);const o=r[t](...n);return(o===-1||o===!1)&&ds(n[0])?(n[0]=fe(n[0]),r[t](...n)):o}function Nn(e,t,n=[]){Zt(),as();const r=fe(e)[t].apply(e,n);return ls(),Xt(),r}const $f=rs("__proto__,__v_isRef,__isVue"),fc=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(Ht));function Ff(e){Ht(e)||(e=String(e));const t=fe(this);return et(t,"has",e),t.hasOwnProperty(e)}class pc{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,r){if(n==="__v_skip")return t.__v_skip;const o=this._isReadonly,i=this._isShallow;if(n==="__v_isReactive")return!o;if(n==="__v_isReadonly")return o;if(n==="__v_isShallow")return i;if(n==="__v_raw")return r===(o?i?Vf:yc:i?gc:hc).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(r)?t:void 0;const s=re(t);if(!o){let l;if(s&&(l=Nf[n]))return l;if(n==="hasOwnProperty")return Ff}const a=Reflect.get(t,n,Ze(t)?t:r);return(Ht(n)?fc.has(n):$f(n))||(o||et(t,"get",n),i)?a:Ze(a)?s&&ss(n)?a:a.value:De(a)?o?vc(a):Zr(a):a}}class dc extends pc{constructor(t=!1){super(!1,t)}set(t,n,r,o){let i=t[n];if(!this._isShallow){const l=un(i);if(!xt(r)&&!un(r)&&(i=fe(i),r=fe(r)),!re(t)&&Ze(i)&&!Ze(r))return l?!1:(i.value=r,!0)}const s=re(t)&&ss(n)?Number(n)<t.length:ye(t,n),a=Reflect.set(t,n,r,Ze(t)?t:o);return t===fe(o)&&(s?Gt(r,i)&&qt(t,"set",n,r):qt(t,"add",n,r)),a}deleteProperty(t,n){const r=ye(t,n);t[n];const o=Reflect.deleteProperty(t,n);return o&&r&&qt(t,"delete",n,void 0),o}has(t,n){const r=Reflect.has(t,n);return(!Ht(n)||!fc.has(n))&&et(t,"has",n),r}ownKeys(t){return et(t,"iterate",re(t)?"length":on),Reflect.ownKeys(t)}}class Mf extends pc{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const Bf=new dc,qf=new Mf,Uf=new dc(!0);const Mi=e=>e,vr=e=>Reflect.getPrototypeOf(e);function Hf(e,t,n){return function(...r){const o=this.__v_raw,i=fe(o),s=wn(i),a=e==="entries"||e===Symbol.iterator&&s,l=e==="keys"&&s,u=o[e](...r),c=n?Mi:t?Bi:tt;return!t&&et(i,"iterate",l?Fi:on),{next(){const{value:f,done:d}=u.next();return d?{value:f,done:d}:{value:a?[c(f[0]),c(f[1])]:c(f),done:d}},[Symbol.iterator](){return this}}}}function br(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function jf(e,t){const n={get(o){const i=this.__v_raw,s=fe(i),a=fe(o);e||(Gt(o,a)&&et(s,"get",o),et(s,"get",a));const{has:l}=vr(s),u=t?Mi:e?Bi:tt;if(l.call(s,o))return u(i.get(o));if(l.call(s,a))return u(i.get(a));i!==s&&i.get(o)},get size(){const o=this.__v_raw;return!e&&et(fe(o),"iterate",on),Reflect.get(o,"size",o)},has(o){const i=this.__v_raw,s=fe(i),a=fe(o);return e||(Gt(o,a)&&et(s,"has",o),et(s,"has",a)),o===a?i.has(o):i.has(o)||i.has(a)},forEach(o,i){const s=this,a=s.__v_raw,l=fe(a),u=t?Mi:e?Bi:tt;return!e&&et(l,"iterate",on),a.forEach((c,f)=>o.call(i,u(c),u(f),s))}};return rt(n,e?{add:br("add"),set:br("set"),delete:br("delete"),clear:br("clear")}:{add(o){!t&&!xt(o)&&!un(o)&&(o=fe(o));const i=fe(this);return vr(i).has.call(i,o)||(i.add(o),qt(i,"add",o,o)),this},set(o,i){!t&&!xt(i)&&!un(i)&&(i=fe(i));const s=fe(this),{has:a,get:l}=vr(s);let u=a.call(s,o);u||(o=fe(o),u=a.call(s,o));const c=l.call(s,o);return s.set(o,i),u?Gt(i,c)&&qt(s,"set",o,i):qt(s,"add",o,i),this},delete(o){const i=fe(this),{has:s,get:a}=vr(i);let l=s.call(i,o);l||(o=fe(o),l=s.call(i,o)),a&&a.call(i,o);const u=i.delete(o);return l&&qt(i,"delete",o,void 0),u},clear(){const o=fe(this),i=o.size!==0,s=o.clear();return i&&qt(o,"clear",void 0,void 0),s}}),["keys","values","entries",Symbol.iterator].forEach(o=>{n[o]=Hf(o,e,t)}),n}function fs(e,t){const n=jf(e,t);return(r,o,i)=>o==="__v_isReactive"?!e:o==="__v_isReadonly"?e:o==="__v_raw"?r:Reflect.get(ye(n,o)&&o in r?n:r,o,i)}const zf={get:fs(!1,!1)},Wf={get:fs(!1,!0)},Kf={get:fs(!0,!1)};const hc=new WeakMap,gc=new WeakMap,yc=new WeakMap,Vf=new WeakMap;function Gf(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function Qf(e){return e.__v_skip||!Object.isExtensible(e)?0:Gf(wf(e))}function Zr(e){return un(e)?e:ps(e,!1,Bf,zf,hc)}function mc(e){return ps(e,!1,Uf,Wf,gc)}function vc(e){return ps(e,!0,qf,Kf,yc)}function ps(e,t,n,r,o){if(!De(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const i=o.get(e);if(i)return i;const s=Qf(e);if(s===0)return e;const a=new Proxy(e,s===2?r:n);return o.set(e,a),a}function Sn(e){return un(e)?Sn(e.__v_raw):!!(e&&e.__v_isReactive)}function un(e){return!!(e&&e.__v_isReadonly)}function xt(e){return!!(e&&e.__v_isShallow)}function ds(e){return e?!!e.__v_raw:!1}function fe(e){const t=e&&e.__v_raw;return t?fe(t):e}function Jf(e){return!ye(e,"__v_skip")&&Object.isExtensible(e)&&Yl(e,"__v_skip",!0),e}const tt=e=>De(e)?Zr(e):e,Bi=e=>De(e)?vc(e):e;function Ze(e){return e?e.__v_isRef===!0:!1}function We(e){return wc(e,!1)}function bc(e){return wc(e,!0)}function wc(e,t){return Ze(e)?e:new Zf(e,t)}class Zf{constructor(t,n){this.dep=new us,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?t:fe(t),this._value=n?t:tt(t),this.__v_isShallow=n}get value(){return this.dep.track(),this._value}set value(t){const n=this._rawValue,r=this.__v_isShallow||xt(t)||un(t);t=r?t:fe(t),Gt(t,n)&&(this._rawValue=t,this._value=r?t:tt(t),this.dep.trigger())}}function Ke(e){return Ze(e)?e.value:e}const Xf={get:(e,t,n)=>t==="__v_raw"?e:Ke(Reflect.get(e,t,n)),set:(e,t,n,r)=>{const o=e[t];return Ze(o)&&!Ze(n)?(o.value=n,!0):Reflect.set(e,t,n,r)}};function Sc(e){return Sn(e)?e:new Proxy(e,Xf)}function Yf(e){const t=re(e)?new Array(e.length):{};for(const n in e)t[n]=tp(e,n);return t}class ep{constructor(t,n,r){this._object=t,this._key=n,this._defaultValue=r,this.__v_isRef=!0,this._value=void 0}get value(){const t=this._object[this._key];return this._value=t===void 0?this._defaultValue:t}set value(t){this._object[this._key]=t}get dep(){return Df(fe(this._object),this._key)}}function tp(e,t,n){const r=e[t];return Ze(r)?r:new ep(e,t,n)}class np{constructor(t,n,r){this.fn=t,this.setter=n,this._value=void 0,this.dep=new us(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=er-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!n,this.isSSR=r}notify(){if(this.flags|=16,!(this.flags&8)&&Oe!==this)return ic(this,!0),!0}get value(){const t=this.dep.track();return lc(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function rp(e,t,n=!1){let r,o;return ie(e)?r=e:(r=e.get,o=e.set),new np(r,o,n)}const wr={},Pr=new WeakMap;let tn;function op(e,t=!1,n=tn){if(n){let r=Pr.get(n);r||Pr.set(n,r=[]),r.push(e)}}function ip(e,t,n=Se){const{immediate:r,deep:o,once:i,scheduler:s,augmentJob:a,call:l}=n,u=_=>o?_:xt(_)||o===!1||o===0?Ut(_,1):Ut(_);let c,f,d,p,v=!1,w=!1;if(Ze(e)?(f=()=>e.value,v=xt(e)):Sn(e)?(f=()=>u(e),v=!0):re(e)?(w=!0,v=e.some(_=>Sn(_)||xt(_)),f=()=>e.map(_=>{if(Ze(_))return _.value;if(Sn(_))return u(_);if(ie(_))return l?l(_,2):_()})):ie(e)?t?f=l?()=>l(e,2):e:f=()=>{if(d){Zt();try{d()}finally{Xt()}}const _=tn;tn=c;try{return l?l(e,3,[p]):e(p)}finally{tn=_}}:f=Lt,t&&o){const _=f,L=o===!0?1/0:o;f=()=>Ut(_(),L)}const m=Tf(),b=()=>{c.stop(),m&&m.active&&is(m.effects,c)};if(i&&t){const _=t;t=(...L)=>{_(...L),b()}}let x=w?new Array(e.length).fill(wr):wr;const O=_=>{if(!(!(c.flags&1)||!c.dirty&&!_))if(t){const L=c.run();if(o||v||(w?L.some((H,M)=>Gt(H,x[M])):Gt(L,x))){d&&d();const H=tn;tn=c;try{const M=[L,x===wr?void 0:w&&x[0]===wr?[]:x,p];l?l(t,3,M):t(...M),x=L}finally{tn=H}}}else c.run()};return a&&a(O),c=new rc(f),c.scheduler=s?()=>s(O,!1):O,p=_=>op(_,!1,c),d=c.onStop=()=>{const _=Pr.get(c);if(_){if(l)l(_,4);else for(const L of _)L();Pr.delete(c)}},t?r?O(!0):x=c.run():s?s(O.bind(null,!0),!0):c.run(),b.pause=c.pause.bind(c),b.resume=c.resume.bind(c),b.stop=b,b}function Ut(e,t=1/0,n){if(t<=0||!De(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,Ze(e))Ut(e.value,t,n);else if(re(e))for(let r=0;r<e.length;r++)Ut(e[r],t,n);else if(Ql(e)||wn(e))e.forEach(r=>{Ut(r,t,n)});else if(Xl(e)){for(const r in e)Ut(e[r],t,n);for(const r of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,r)&&Ut(e[r],t,n)}return e}/**
* @vue/runtime-core v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function cr(e,t,n,r){try{return r?e(...r):e()}catch(o){Xr(o,t,n)}}function $t(e,t,n,r){if(ie(e)){const o=cr(e,t,n,r);return o&&Jl(o)&&o.catch(i=>{Xr(i,t,n)}),o}if(re(e)){const o=[];for(let i=0;i<e.length;i++)o.push($t(e[i],t,n,r));return o}}function Xr(e,t,n,r=!0){const o=t?t.vnode:null,{errorHandler:i,throwUnhandledErrorInProduction:s}=t&&t.appContext.config||Se;if(t){let a=t.parent;const l=t.proxy,u=`https://vuejs.org/error-reference/#runtime-${n}`;for(;a;){const c=a.ec;if(c){for(let f=0;f<c.length;f++)if(c[f](e,l,u)===!1)return}a=a.parent}if(i){Zt(),cr(i,null,10,[e,l,u]),Xt();return}}sp(e,n,o,r,s)}function sp(e,t,n,r=!0,o=!1){if(o)throw e;console.error(e)}const st=[];let Dt=-1;const xn=[];let Wt=null,yn=0;const xc=Promise.resolve();let Ir=null;function _n(e){const t=Ir||xc;return e?t.then(this?e.bind(this):e):t}function ap(e){let t=Dt+1,n=st.length;for(;t<n;){const r=t+n>>>1,o=st[r],i=nr(o);i<e||i===e&&o.flags&2?t=r+1:n=r}return t}function hs(e){if(!(e.flags&1)){const t=nr(e),n=st[st.length-1];!n||!(e.flags&2)&&t>=nr(n)?st.push(e):st.splice(ap(t),0,e),e.flags|=1,Ec()}}function Ec(){Ir||(Ir=xc.then(Rc))}function lp(e){re(e)?xn.push(...e):Wt&&e.id===-1?Wt.splice(yn+1,0,e):e.flags&1||(xn.push(e),e.flags|=1),Ec()}function Ws(e,t,n=Dt+1){for(;n<st.length;n++){const r=st[n];if(r&&r.flags&2){if(e&&r.id!==e.uid)continue;st.splice(n,1),n--,r.flags&4&&(r.flags&=-2),r(),r.flags&4||(r.flags&=-2)}}}function Ac(e){if(xn.length){const t=[...new Set(xn)].sort((n,r)=>nr(n)-nr(r));if(xn.length=0,Wt){Wt.push(...t);return}for(Wt=t,yn=0;yn<Wt.length;yn++){const n=Wt[yn];n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2}Wt=null,yn=0}}const nr=e=>e.id==null?e.flags&2?-1:1/0:e.id;function Rc(e){try{for(Dt=0;Dt<st.length;Dt++){const t=st[Dt];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),cr(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;Dt<st.length;Dt++){const t=st[Dt];t&&(t.flags&=-2)}Dt=-1,st.length=0,Ac(),Ir=null,(st.length||xn.length)&&Rc()}}let Ve=null,Yr=null;function Dr(e){const t=Ve;return Ve=e,Yr=e&&e.type.__scopeId||null,t}function cp(e){Yr=e}function up(){Yr=null}function gs(e,t=Ve,n){if(!t||e._n)return e;const r=(...o)=>{r._d&&ta(-1);const i=Dr(t);let s;try{s=e(...o)}finally{Dr(i),r._d&&ta(1)}return s};return r._n=!0,r._c=!0,r._d=!0,r}function eo(e,t){if(Ve===null)return e;const n=so(Ve),r=e.dirs||(e.dirs=[]);for(let o=0;o<t.length;o++){let[i,s,a,l=Se]=t[o];i&&(ie(i)&&(i={mounted:i,updated:i}),i.deep&&Ut(s),r.push({dir:i,instance:n,value:s,oldValue:void 0,arg:a,modifiers:l}))}return e}function Yt(e,t,n,r){const o=e.dirs,i=t&&t.dirs;for(let s=0;s<o.length;s++){const a=o[s];i&&(a.oldValue=i[s].value);let l=a.dir[r];l&&(Zt(),$t(l,n,8,[e.el,a,e,t]),Xt())}}const fp=Symbol("_vte"),pp=e=>e.__isTeleport;function ys(e,t){e.shapeFlag&6&&e.component?(e.transition=t,ys(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}/*! #__NO_SIDE_EFFECTS__ */function to(e,t){return ie(e)?rt({name:e.name},t,{setup:e}):e}function _c(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function dp(e){const t=Zc(),n=bc(null);if(t){const o=t.refs===Se?t.refs={}:t.refs;Object.defineProperty(o,e,{enumerable:!0,get:()=>n.value,set:i=>n.value=i})}return n}function Nr(e,t,n,r,o=!1){if(re(e)){e.forEach((v,w)=>Nr(v,t&&(re(t)?t[w]:t),n,r,o));return}if(En(r)&&!o){r.shapeFlag&512&&r.type.__asyncResolved&&r.component.subTree.component&&Nr(e,t,n,r.component.subTree);return}const i=r.shapeFlag&4?so(r.component):r.el,s=o?null:i,{i:a,r:l}=e,u=t&&t.r,c=a.refs===Se?a.refs={}:a.refs,f=a.setupState,d=fe(f),p=f===Se?()=>!1:v=>ye(d,v);if(u!=null&&u!==l&&(qe(u)?(c[u]=null,p(u)&&(f[u]=null)):Ze(u)&&(u.value=null)),ie(l))cr(l,a,12,[s,c]);else{const v=qe(l),w=Ze(l);if(v||w){const m=()=>{if(e.f){const b=v?p(l)?f[l]:c[l]:l.value;o?re(b)&&is(b,i):re(b)?b.includes(i)||b.push(i):v?(c[l]=[i],p(l)&&(f[l]=c[l])):(l.value=[i],e.k&&(c[e.k]=l.value))}else v?(c[l]=s,p(l)&&(f[l]=s)):w&&(l.value=s,e.k&&(c[e.k]=s))};s?(m.id=-1,ht(m,n)):m()}}}Gr().requestIdleCallback;Gr().cancelIdleCallback;const En=e=>!!e.type.__asyncLoader,kc=e=>e.type.__isKeepAlive;function hp(e,t){Oc(e,"a",t)}function gp(e,t){Oc(e,"da",t)}function Oc(e,t,n=Je){const r=e.__wdc||(e.__wdc=()=>{let o=n;for(;o;){if(o.isDeactivated)return;o=o.parent}return e()});if(no(t,r,n),n){let o=n.parent;for(;o&&o.parent;)kc(o.parent.vnode)&&yp(r,t,n,o),o=o.parent}}function yp(e,t,n,r){const o=no(t,e,r,!0);ro(()=>{is(r[t],o)},n)}function no(e,t,n=Je,r=!1){if(n){const o=n[e]||(n[e]=[]),i=t.__weh||(t.__weh=(...s)=>{Zt();const a=fr(n),l=$t(t,n,e,s);return a(),Xt(),l});return r?o.unshift(i):o.push(i),i}}const jt=e=>(t,n=Je)=>{(!ir||e==="sp")&&no(e,(...r)=>t(...r),n)},mp=jt("bm"),ur=jt("m"),vp=jt("bu"),bp=jt("u"),wp=jt("bum"),ro=jt("um"),Sp=jt("sp"),xp=jt("rtg"),Ep=jt("rtc");function Ap(e,t=Je){no("ec",e,t)}const Rp="components";function _p(e,t){return Op(Rp,e,!0,t)||e}const kp=Symbol.for("v-ndc");function Op(e,t,n=!0,r=!1){const o=Ve||Je;if(o){const i=o.type;{const a=gd(i,!1);if(a&&(a===t||a===Et(t)||a===Vr(Et(t))))return i}const s=Ks(o[e]||i[e],t)||Ks(o.appContext[e],t);return!s&&r?i:s}}function Ks(e,t){return e&&(e[t]||e[Et(t)]||e[Vr(Et(t))])}function Cc(e,t,n,r){let o;const i=n,s=re(e);if(s||qe(e)){const a=s&&Sn(e);let l=!1;a&&(l=!xt(e),e=Jr(e)),o=new Array(e.length);for(let u=0,c=e.length;u<c;u++)o[u]=t(l?tt(e[u]):e[u],u,void 0,i)}else if(typeof e=="number"){o=new Array(e);for(let a=0;a<e;a++)o[a]=t(a+1,a,void 0,i)}else if(De(e))if(e[Symbol.iterator])o=Array.from(e,(a,l)=>t(a,l,void 0,i));else{const a=Object.keys(e);o=new Array(a.length);for(let l=0,u=a.length;l<u;l++){const c=a[l];o[l]=t(e[c],c,l,i)}}else o=[];return o}function sn(e,t,n={},r,o){if(Ve.ce||Ve.parent&&En(Ve.parent)&&Ve.parent.ce)return t!=="default"&&(n.name=t),$e(),$r(ct,null,[Te("slot",n,r&&r())],64);let i=e[t];i&&i._c&&(i._d=!1),$e();const s=i&&Tc(i(n)),a=n.key||s&&s.key,l=$r(ct,{key:(a&&!Ht(a)?a:`_${t}`)+(!s&&r?"_fb":"")},s||(r?r():[]),s&&e._===1?64:-2);return!o&&l.scopeId&&(l.slotScopeIds=[l.scopeId+"-s"]),i&&i._c&&(i._d=!0),l}function Tc(e){return e.some(t=>or(t)?!(t.type===Qt||t.type===ct&&!Tc(t.children)):!0)?e:null}const qi=e=>e?Xc(e)?so(e):qi(e.parent):null,Vn=rt(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>qi(e.parent),$root:e=>qi(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>Ic(e),$forceUpdate:e=>e.f||(e.f=()=>{hs(e.update)}),$nextTick:e=>e.n||(e.n=_n.bind(e.proxy)),$watch:e=>Qp.bind(e)}),Co=(e,t)=>e!==Se&&!e.__isScriptSetup&&ye(e,t),Cp={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:r,data:o,props:i,accessCache:s,type:a,appContext:l}=e;let u;if(t[0]!=="$"){const p=s[t];if(p!==void 0)switch(p){case 1:return r[t];case 2:return o[t];case 4:return n[t];case 3:return i[t]}else{if(Co(r,t))return s[t]=1,r[t];if(o!==Se&&ye(o,t))return s[t]=2,o[t];if((u=e.propsOptions[0])&&ye(u,t))return s[t]=3,i[t];if(n!==Se&&ye(n,t))return s[t]=4,n[t];Ui&&(s[t]=0)}}const c=Vn[t];let f,d;if(c)return t==="$attrs"&&et(e.attrs,"get",""),c(e);if((f=a.__cssModules)&&(f=f[t]))return f;if(n!==Se&&ye(n,t))return s[t]=4,n[t];if(d=l.config.globalProperties,ye(d,t))return d[t]},set({_:e},t,n){const{data:r,setupState:o,ctx:i}=e;return Co(o,t)?(o[t]=n,!0):r!==Se&&ye(r,t)?(r[t]=n,!0):ye(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(i[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:r,appContext:o,propsOptions:i}},s){let a;return!!n[s]||e!==Se&&ye(e,s)||Co(t,s)||(a=i[0])&&ye(a,s)||ye(r,s)||ye(Vn,s)||ye(o.config.globalProperties,s)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:ye(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function Vs(e){return re(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}let Ui=!0;function Tp(e){const t=Ic(e),n=e.proxy,r=e.ctx;Ui=!1,t.beforeCreate&&Gs(t.beforeCreate,e,"bc");const{data:o,computed:i,methods:s,watch:a,provide:l,inject:u,created:c,beforeMount:f,mounted:d,beforeUpdate:p,updated:v,activated:w,deactivated:m,beforeDestroy:b,beforeUnmount:x,destroyed:O,unmounted:_,render:L,renderTracked:H,renderTriggered:M,errorCaptured:Q,serverPrefetch:j,expose:V,inheritAttrs:ee,components:J,directives:pe,filters:de}=t;if(u&&Pp(u,r,null),s)for(const te in s){const K=s[te];ie(K)&&(r[te]=K.bind(n))}if(o){const te=o.call(n,n);De(te)&&(e.data=Zr(te))}if(Ui=!0,i)for(const te in i){const K=i[te],ve=ie(K)?K.bind(n,n):ie(K.get)?K.get.bind(n,n):Lt,Fe=!ie(K)&&ie(K.set)?K.set.bind(n):Lt,xe=ut({get:ve,set:Fe});Object.defineProperty(r,te,{enumerable:!0,configurable:!0,get:()=>xe.value,set:Ee=>xe.value=Ee})}if(a)for(const te in a)Pc(a[te],r,n,te);if(l){const te=ie(l)?l.call(n):l;Reflect.ownKeys(te).forEach(K=>{Gn(K,te[K])})}c&&Gs(c,e,"c");function ue(te,K){re(K)?K.forEach(ve=>te(ve.bind(n))):K&&te(K.bind(n))}if(ue(mp,f),ue(ur,d),ue(vp,p),ue(bp,v),ue(hp,w),ue(gp,m),ue(Ap,Q),ue(Ep,H),ue(xp,M),ue(wp,x),ue(ro,_),ue(Sp,j),re(V))if(V.length){const te=e.exposed||(e.exposed={});V.forEach(K=>{Object.defineProperty(te,K,{get:()=>n[K],set:ve=>n[K]=ve})})}else e.exposed||(e.exposed={});L&&e.render===Lt&&(e.render=L),ee!=null&&(e.inheritAttrs=ee),J&&(e.components=J),pe&&(e.directives=pe),j&&_c(e)}function Pp(e,t,n=Lt){re(e)&&(e=Hi(e));for(const r in e){const o=e[r];let i;De(o)?"default"in o?i=mt(o.from||r,o.default,!0):i=mt(o.from||r):i=mt(o),Ze(i)?Object.defineProperty(t,r,{enumerable:!0,configurable:!0,get:()=>i.value,set:s=>i.value=s}):t[r]=i}}function Gs(e,t,n){$t(re(e)?e.map(r=>r.bind(t.proxy)):e.bind(t.proxy),t,n)}function Pc(e,t,n,r){let o=r.includes(".")?Kc(n,r):()=>n[r];if(qe(e)){const i=t[e];ie(i)&&Rn(o,i)}else if(ie(e))Rn(o,e.bind(n));else if(De(e))if(re(e))e.forEach(i=>Pc(i,t,n,r));else{const i=ie(e.handler)?e.handler.bind(n):t[e.handler];ie(i)&&Rn(o,i,e)}}function Ic(e){const t=e.type,{mixins:n,extends:r}=t,{mixins:o,optionsCache:i,config:{optionMergeStrategies:s}}=e.appContext,a=i.get(t);let l;return a?l=a:!o.length&&!n&&!r?l=t:(l={},o.length&&o.forEach(u=>Lr(l,u,s,!0)),Lr(l,t,s)),De(t)&&i.set(t,l),l}function Lr(e,t,n,r=!1){const{mixins:o,extends:i}=t;i&&Lr(e,i,n,!0),o&&o.forEach(s=>Lr(e,s,n,!0));for(const s in t)if(!(r&&s==="expose")){const a=Ip[s]||n&&n[s];e[s]=a?a(e[s],t[s]):t[s]}return e}const Ip={data:Qs,props:Js,emits:Js,methods:Hn,computed:Hn,beforeCreate:it,created:it,beforeMount:it,mounted:it,beforeUpdate:it,updated:it,beforeDestroy:it,beforeUnmount:it,destroyed:it,unmounted:it,activated:it,deactivated:it,errorCaptured:it,serverPrefetch:it,components:Hn,directives:Hn,watch:Np,provide:Qs,inject:Dp};function Qs(e,t){return t?e?function(){return rt(ie(e)?e.call(this,this):e,ie(t)?t.call(this,this):t)}:t:e}function Dp(e,t){return Hn(Hi(e),Hi(t))}function Hi(e){if(re(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function it(e,t){return e?[...new Set([].concat(e,t))]:t}function Hn(e,t){return e?rt(Object.create(null),e,t):t}function Js(e,t){return e?re(e)&&re(t)?[...new Set([...e,...t])]:rt(Object.create(null),Vs(e),Vs(t??{})):t}function Np(e,t){if(!e)return t;if(!t)return e;const n=rt(Object.create(null),e);for(const r in t)n[r]=it(e[r],t[r]);return n}function Dc(){return{app:null,config:{isNativeTag:vf,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let Lp=0;function $p(e,t){return function(r,o=null){ie(r)||(r=rt({},r)),o!=null&&!De(o)&&(o=null);const i=Dc(),s=new WeakSet,a=[];let l=!1;const u=i.app={_uid:Lp++,_component:r,_props:o,_container:null,_context:i,_instance:null,version:md,get config(){return i.config},set config(c){},use(c,...f){return s.has(c)||(c&&ie(c.install)?(s.add(c),c.install(u,...f)):ie(c)&&(s.add(c),c(u,...f))),u},mixin(c){return i.mixins.includes(c)||i.mixins.push(c),u},component(c,f){return f?(i.components[c]=f,u):i.components[c]},directive(c,f){return f?(i.directives[c]=f,u):i.directives[c]},mount(c,f,d){if(!l){const p=u._ceVNode||Te(r,o);return p.appContext=i,d===!0?d="svg":d===!1&&(d=void 0),e(p,c,d),l=!0,u._container=c,c.__vue_app__=u,so(p.component)}},onUnmount(c){a.push(c)},unmount(){l&&($t(a,u._instance,16),e(null,u._container),delete u._container.__vue_app__)},provide(c,f){return i.provides[c]=f,u},runWithContext(c){const f=An;An=u;try{return c()}finally{An=f}}};return u}}let An=null;function Gn(e,t){if(Je){let n=Je.provides;const r=Je.parent&&Je.parent.provides;r===n&&(n=Je.provides=Object.create(r)),n[e]=t}}function mt(e,t,n=!1){const r=Je||Ve;if(r||An){const o=An?An._context.provides:r?r.parent==null?r.vnode.appContext&&r.vnode.appContext.provides:r.parent.provides:void 0;if(o&&e in o)return o[e];if(arguments.length>1)return n&&ie(t)?t.call(r&&r.proxy):t}}const Nc={},Lc=()=>Object.create(Nc),$c=e=>Object.getPrototypeOf(e)===Nc;function Fp(e,t,n,r=!1){const o={},i=Lc();e.propsDefaults=Object.create(null),Fc(e,t,o,i);for(const s in e.propsOptions[0])s in o||(o[s]=void 0);n?e.props=r?o:mc(o):e.type.props?e.props=o:e.props=i,e.attrs=i}function Mp(e,t,n,r){const{props:o,attrs:i,vnode:{patchFlag:s}}=e,a=fe(o),[l]=e.propsOptions;let u=!1;if((r||s>0)&&!(s&16)){if(s&8){const c=e.vnode.dynamicProps;for(let f=0;f<c.length;f++){let d=c[f];if(oo(e.emitsOptions,d))continue;const p=t[d];if(l)if(ye(i,d))p!==i[d]&&(i[d]=p,u=!0);else{const v=Et(d);o[v]=ji(l,a,v,p,e,!1)}else p!==i[d]&&(i[d]=p,u=!0)}}}else{Fc(e,t,o,i)&&(u=!0);let c;for(const f in a)(!t||!ye(t,f)&&((c=Jt(f))===f||!ye(t,c)))&&(l?n&&(n[f]!==void 0||n[c]!==void 0)&&(o[f]=ji(l,a,f,void 0,e,!0)):delete o[f]);if(i!==a)for(const f in i)(!t||!ye(t,f))&&(delete i[f],u=!0)}u&&qt(e.attrs,"set","")}function Fc(e,t,n,r){const[o,i]=e.propsOptions;let s=!1,a;if(t)for(let l in t){if(zn(l))continue;const u=t[l];let c;o&&ye(o,c=Et(l))?!i||!i.includes(c)?n[c]=u:(a||(a={}))[c]=u:oo(e.emitsOptions,l)||(!(l in r)||u!==r[l])&&(r[l]=u,s=!0)}if(i){const l=fe(n),u=a||Se;for(let c=0;c<i.length;c++){const f=i[c];n[f]=ji(o,l,f,u[f],e,!ye(u,f))}}return s}function ji(e,t,n,r,o,i){const s=e[n];if(s!=null){const a=ye(s,"default");if(a&&r===void 0){const l=s.default;if(s.type!==Function&&!s.skipFactory&&ie(l)){const{propsDefaults:u}=o;if(n in u)r=u[n];else{const c=fr(o);r=u[n]=l.call(null,t),c()}}else r=l;o.ce&&o.ce._setProp(n,r)}s[0]&&(i&&!a?r=!1:s[1]&&(r===""||r===Jt(n))&&(r=!0))}return r}const Bp=new WeakMap;function Mc(e,t,n=!1){const r=n?Bp:t.propsCache,o=r.get(e);if(o)return o;const i=e.props,s={},a=[];let l=!1;if(!ie(e)){const c=f=>{l=!0;const[d,p]=Mc(f,t,!0);rt(s,d),p&&a.push(...p)};!n&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}if(!i&&!l)return De(e)&&r.set(e,bn),bn;if(re(i))for(let c=0;c<i.length;c++){const f=Et(i[c]);Zs(f)&&(s[f]=Se)}else if(i)for(const c in i){const f=Et(c);if(Zs(f)){const d=i[c],p=s[f]=re(d)||ie(d)?{type:d}:rt({},d),v=p.type;let w=!1,m=!0;if(re(v))for(let b=0;b<v.length;++b){const x=v[b],O=ie(x)&&x.name;if(O==="Boolean"){w=!0;break}else O==="String"&&(m=!1)}else w=ie(v)&&v.name==="Boolean";p[0]=w,p[1]=m,(w||ye(p,"default"))&&a.push(f)}}const u=[s,a];return De(e)&&r.set(e,u),u}function Zs(e){return e[0]!=="$"&&!zn(e)}const Bc=e=>e[0]==="_"||e==="$stable",ms=e=>re(e)?e.map(Nt):[Nt(e)],qp=(e,t,n)=>{if(t._n)return t;const r=gs((...o)=>ms(t(...o)),n);return r._c=!1,r},qc=(e,t,n)=>{const r=e._ctx;for(const o in e){if(Bc(o))continue;const i=e[o];if(ie(i))t[o]=qp(o,i,r);else if(i!=null){const s=ms(i);t[o]=()=>s}}},Uc=(e,t)=>{const n=ms(t);e.slots.default=()=>n},Hc=(e,t,n)=>{for(const r in t)(n||r!=="_")&&(e[r]=t[r])},Up=(e,t,n)=>{const r=e.slots=Lc();if(e.vnode.shapeFlag&32){const o=t._;o?(Hc(r,t,n),n&&Yl(r,"_",o,!0)):qc(t,r)}else t&&Uc(e,t)},Hp=(e,t,n)=>{const{vnode:r,slots:o}=e;let i=!0,s=Se;if(r.shapeFlag&32){const a=t._;a?n&&a===1?i=!1:Hc(o,t,n):(i=!t.$stable,qc(t,o)),s=t}else t&&(Uc(e,t),s={default:1});if(i)for(const a in o)!Bc(a)&&s[a]==null&&delete o[a]},ht=nd;function jp(e){return zp(e)}function zp(e,t){const n=Gr();n.__VUE__=!0;const{insert:r,remove:o,patchProp:i,createElement:s,createText:a,createComment:l,setText:u,setElementText:c,parentNode:f,nextSibling:d,setScopeId:p=Lt,insertStaticContent:v}=e,w=(h,g,E,D=null,y=null,S=null,T=void 0,R=null,P=!!g.dynamicChildren)=>{if(h===g)return;h&&!Ln(h,g)&&(D=N(h),Ee(h,y,S,!0),h=null),g.patchFlag===-2&&(P=!1,g.dynamicChildren=null);const{type:k,ref:q,shapeFlag:$}=g;switch(k){case io:m(h,g,E,D);break;case Qt:b(h,g,E,D);break;case Po:h==null&&x(g,E,D,T);break;case ct:J(h,g,E,D,y,S,T,R,P);break;default:$&1?L(h,g,E,D,y,S,T,R,P):$&6?pe(h,g,E,D,y,S,T,R,P):($&64||$&128)&&k.process(h,g,E,D,y,S,T,R,P,F)}q!=null&&y&&Nr(q,h&&h.ref,S,g||h,!g)},m=(h,g,E,D)=>{if(h==null)r(g.el=a(g.children),E,D);else{const y=g.el=h.el;g.children!==h.children&&u(y,g.children)}},b=(h,g,E,D)=>{h==null?r(g.el=l(g.children||""),E,D):g.el=h.el},x=(h,g,E,D)=>{[h.el,h.anchor]=v(h.children,g,E,D,h.el,h.anchor)},O=({el:h,anchor:g},E,D)=>{let y;for(;h&&h!==g;)y=d(h),r(h,E,D),h=y;r(g,E,D)},_=({el:h,anchor:g})=>{let E;for(;h&&h!==g;)E=d(h),o(h),h=E;o(g)},L=(h,g,E,D,y,S,T,R,P)=>{g.type==="svg"?T="svg":g.type==="math"&&(T="mathml"),h==null?H(g,E,D,y,S,T,R,P):j(h,g,y,S,T,R,P)},H=(h,g,E,D,y,S,T,R)=>{let P,k;const{props:q,shapeFlag:$,transition:U,dirs:W}=h;if(P=h.el=s(h.type,S,q&&q.is,q),$&8?c(P,h.children):$&16&&Q(h.children,P,null,D,y,To(h,S),T,R),W&&Yt(h,null,D,"created"),M(P,h,h.scopeId,T,D),q){for(const G in q)G!=="value"&&!zn(G)&&i(P,G,null,q[G],S,D);"value"in q&&i(P,"value",null,q.value,S),(k=q.onVnodeBeforeMount)&&Tt(k,D,h)}W&&Yt(h,null,D,"beforeMount");const Z=Wp(y,U);Z&&U.beforeEnter(P),r(P,g,E),((k=q&&q.onVnodeMounted)||Z||W)&&ht(()=>{k&&Tt(k,D,h),Z&&U.enter(P),W&&Yt(h,null,D,"mounted")},y)},M=(h,g,E,D,y)=>{if(E&&p(h,E),D)for(let S=0;S<D.length;S++)p(h,D[S]);if(y){let S=y.subTree;if(g===S||Gc(S.type)&&(S.ssContent===g||S.ssFallback===g)){const T=y.vnode;M(h,T,T.scopeId,T.slotScopeIds,y.parent)}}},Q=(h,g,E,D,y,S,T,R,P=0)=>{for(let k=P;k<h.length;k++){const q=h[k]=R?Kt(h[k]):Nt(h[k]);w(null,q,g,E,D,y,S,T,R)}},j=(h,g,E,D,y,S,T)=>{const R=g.el=h.el;let{patchFlag:P,dynamicChildren:k,dirs:q}=g;P|=h.patchFlag&16;const $=h.props||Se,U=g.props||Se;let W;if(E&&en(E,!1),(W=U.onVnodeBeforeUpdate)&&Tt(W,E,g,h),q&&Yt(g,h,E,"beforeUpdate"),E&&en(E,!0),($.innerHTML&&U.innerHTML==null||$.textContent&&U.textContent==null)&&c(R,""),k?V(h.dynamicChildren,k,R,E,D,To(g,y),S):T||K(h,g,R,null,E,D,To(g,y),S,!1),P>0){if(P&16)ee(R,$,U,E,y);else if(P&2&&$.class!==U.class&&i(R,"class",null,U.class,y),P&4&&i(R,"style",$.style,U.style,y),P&8){const Z=g.dynamicProps;for(let G=0;G<Z.length;G++){const Y=Z[G],be=$[Y],Ie=U[Y];(Ie!==be||Y==="value")&&i(R,Y,be,Ie,y,E)}}P&1&&h.children!==g.children&&c(R,g.children)}else!T&&k==null&&ee(R,$,U,E,y);((W=U.onVnodeUpdated)||q)&&ht(()=>{W&&Tt(W,E,g,h),q&&Yt(g,h,E,"updated")},D)},V=(h,g,E,D,y,S,T)=>{for(let R=0;R<g.length;R++){const P=h[R],k=g[R],q=P.el&&(P.type===ct||!Ln(P,k)||P.shapeFlag&70)?f(P.el):E;w(P,k,q,null,D,y,S,T,!0)}},ee=(h,g,E,D,y)=>{if(g!==E){if(g!==Se)for(const S in g)!zn(S)&&!(S in E)&&i(h,S,g[S],null,y,D);for(const S in E){if(zn(S))continue;const T=E[S],R=g[S];T!==R&&S!=="value"&&i(h,S,R,T,y,D)}"value"in E&&i(h,"value",g.value,E.value,y)}},J=(h,g,E,D,y,S,T,R,P)=>{const k=g.el=h?h.el:a(""),q=g.anchor=h?h.anchor:a("");let{patchFlag:$,dynamicChildren:U,slotScopeIds:W}=g;W&&(R=R?R.concat(W):W),h==null?(r(k,E,D),r(q,E,D),Q(g.children||[],E,q,y,S,T,R,P)):$>0&&$&64&&U&&h.dynamicChildren?(V(h.dynamicChildren,U,E,y,S,T,R),(g.key!=null||y&&g===y.subTree)&&jc(h,g,!0)):K(h,g,E,q,y,S,T,R,P)},pe=(h,g,E,D,y,S,T,R,P)=>{g.slotScopeIds=R,h==null?g.shapeFlag&512?y.ctx.activate(g,E,D,T,P):de(g,E,D,y,S,T,P):Ne(h,g,P)},de=(h,g,E,D,y,S,T)=>{const R=h.component=ud(h,D,y);if(kc(h)&&(R.ctx.renderer=F),fd(R,!1,T),R.asyncDep){if(y&&y.registerDep(R,ue,T),!h.el){const P=R.subTree=Te(Qt);b(null,P,g,E)}}else ue(R,h,g,E,y,S,T)},Ne=(h,g,E)=>{const D=g.component=h.component;if(ed(h,g,E))if(D.asyncDep&&!D.asyncResolved){te(D,g,E);return}else D.next=g,D.update();else g.el=h.el,D.vnode=g},ue=(h,g,E,D,y,S,T)=>{const R=()=>{if(h.isMounted){let{next:$,bu:U,u:W,parent:Z,vnode:G}=h;{const we=zc(h);if(we){$&&($.el=G.el,te(h,$,T)),we.asyncDep.then(()=>{h.isUnmounted||R()});return}}let Y=$,be;en(h,!1),$?($.el=G.el,te(h,$,T)):$=G,U&&Er(U),(be=$.props&&$.props.onVnodeBeforeUpdate)&&Tt(be,Z,$,G),en(h,!0);const Ie=Ys(h),Re=h.subTree;h.subTree=Ie,w(Re,Ie,f(Re.el),N(Re),h,y,S),$.el=Ie.el,Y===null&&td(h,Ie.el),W&&ht(W,y),(be=$.props&&$.props.onVnodeUpdated)&&ht(()=>Tt(be,Z,$,G),y)}else{let $;const{el:U,props:W}=g,{bm:Z,m:G,parent:Y,root:be,type:Ie}=h,Re=En(g);en(h,!1),Z&&Er(Z),!Re&&($=W&&W.onVnodeBeforeMount)&&Tt($,Y,g),en(h,!0);{be.ce&&be.ce._injectChildStyle(Ie);const we=h.subTree=Ys(h);w(null,we,E,D,h,y,S),g.el=we.el}if(G&&ht(G,y),!Re&&($=W&&W.onVnodeMounted)){const we=g;ht(()=>Tt($,Y,we),y)}(g.shapeFlag&256||Y&&En(Y.vnode)&&Y.vnode.shapeFlag&256)&&h.a&&ht(h.a,y),h.isMounted=!0,g=E=D=null}};h.scope.on();const P=h.effect=new rc(R);h.scope.off();const k=h.update=P.run.bind(P),q=h.job=P.runIfDirty.bind(P);q.i=h,q.id=h.uid,P.scheduler=()=>hs(q),en(h,!0),k()},te=(h,g,E)=>{g.component=h;const D=h.vnode.props;h.vnode=g,h.next=null,Mp(h,g.props,D,E),Hp(h,g.children,E),Zt(),Ws(h),Xt()},K=(h,g,E,D,y,S,T,R,P=!1)=>{const k=h&&h.children,q=h?h.shapeFlag:0,$=g.children,{patchFlag:U,shapeFlag:W}=g;if(U>0){if(U&128){Fe(k,$,E,D,y,S,T,R,P);return}else if(U&256){ve(k,$,E,D,y,S,T,R,P);return}}W&8?(q&16&&Ae(k,y,S),$!==k&&c(E,$)):q&16?W&16?Fe(k,$,E,D,y,S,T,R,P):Ae(k,y,S,!0):(q&8&&c(E,""),W&16&&Q($,E,D,y,S,T,R,P))},ve=(h,g,E,D,y,S,T,R,P)=>{h=h||bn,g=g||bn;const k=h.length,q=g.length,$=Math.min(k,q);let U;for(U=0;U<$;U++){const W=g[U]=P?Kt(g[U]):Nt(g[U]);w(h[U],W,E,null,y,S,T,R,P)}k>q?Ae(h,y,S,!0,!1,$):Q(g,E,D,y,S,T,R,P,$)},Fe=(h,g,E,D,y,S,T,R,P)=>{let k=0;const q=g.length;let $=h.length-1,U=q-1;for(;k<=$&&k<=U;){const W=h[k],Z=g[k]=P?Kt(g[k]):Nt(g[k]);if(Ln(W,Z))w(W,Z,E,null,y,S,T,R,P);else break;k++}for(;k<=$&&k<=U;){const W=h[$],Z=g[U]=P?Kt(g[U]):Nt(g[U]);if(Ln(W,Z))w(W,Z,E,null,y,S,T,R,P);else break;$--,U--}if(k>$){if(k<=U){const W=U+1,Z=W<q?g[W].el:D;for(;k<=U;)w(null,g[k]=P?Kt(g[k]):Nt(g[k]),E,Z,y,S,T,R,P),k++}}else if(k>U)for(;k<=$;)Ee(h[k],y,S,!0),k++;else{const W=k,Z=k,G=new Map;for(k=Z;k<=U;k++){const Le=g[k]=P?Kt(g[k]):Nt(g[k]);Le.key!=null&&G.set(Le.key,k)}let Y,be=0;const Ie=U-Z+1;let Re=!1,we=0;const Ue=new Array(Ie);for(k=0;k<Ie;k++)Ue[k]=0;for(k=W;k<=$;k++){const Le=h[k];if(be>=Ie){Ee(Le,y,S,!0);continue}let A;if(Le.key!=null)A=G.get(Le.key);else for(Y=Z;Y<=U;Y++)if(Ue[Y-Z]===0&&Ln(Le,g[Y])){A=Y;break}A===void 0?Ee(Le,y,S,!0):(Ue[A-Z]=k+1,A>=we?we=A:Re=!0,w(Le,g[A],E,null,y,S,T,R,P),be++)}const pt=Re?Kp(Ue):bn;for(Y=pt.length-1,k=Ie-1;k>=0;k--){const Le=Z+k,A=g[Le],C=Le+1<q?g[Le+1].el:D;Ue[k]===0?w(null,A,E,C,y,S,T,R,P):Re&&(Y<0||k!==pt[Y]?xe(A,E,C,2):Y--)}}},xe=(h,g,E,D,y=null)=>{const{el:S,type:T,transition:R,children:P,shapeFlag:k}=h;if(k&6){xe(h.component.subTree,g,E,D);return}if(k&128){h.suspense.move(g,E,D);return}if(k&64){T.move(h,g,E,F);return}if(T===ct){r(S,g,E);for(let $=0;$<P.length;$++)xe(P[$],g,E,D);r(h.anchor,g,E);return}if(T===Po){O(h,g,E);return}if(D!==2&&k&1&&R)if(D===0)R.beforeEnter(S),r(S,g,E),ht(()=>R.enter(S),y);else{const{leave:$,delayLeave:U,afterLeave:W}=R,Z=()=>r(S,g,E),G=()=>{$(S,()=>{Z(),W&&W()})};U?U(S,Z,G):G()}else r(S,g,E)},Ee=(h,g,E,D=!1,y=!1)=>{const{type:S,props:T,ref:R,children:P,dynamicChildren:k,shapeFlag:q,patchFlag:$,dirs:U,cacheIndex:W}=h;if($===-2&&(y=!1),R!=null&&Nr(R,null,E,h,!0),W!=null&&(g.renderCache[W]=void 0),q&256){g.ctx.deactivate(h);return}const Z=q&1&&U,G=!En(h);let Y;if(G&&(Y=T&&T.onVnodeBeforeUnmount)&&Tt(Y,g,h),q&6)Xe(h.component,E,D);else{if(q&128){h.suspense.unmount(E,D);return}Z&&Yt(h,null,g,"beforeUnmount"),q&64?h.type.remove(h,g,E,F,D):k&&!k.hasOnce&&(S!==ct||$>0&&$&64)?Ae(k,g,E,!1,!0):(S===ct&&$&384||!y&&q&16)&&Ae(P,g,E),D&&je(h)}(G&&(Y=T&&T.onVnodeUnmounted)||Z)&&ht(()=>{Y&&Tt(Y,g,h),Z&&Yt(h,null,g,"unmounted")},E)},je=h=>{const{type:g,el:E,anchor:D,transition:y}=h;if(g===ct){Ge(E,D);return}if(g===Po){_(h);return}const S=()=>{o(E),y&&!y.persisted&&y.afterLeave&&y.afterLeave()};if(h.shapeFlag&1&&y&&!y.persisted){const{leave:T,delayLeave:R}=y,P=()=>T(E,S);R?R(h.el,S,P):P()}else S()},Ge=(h,g)=>{let E;for(;h!==g;)E=d(h),o(h),h=E;o(g)},Xe=(h,g,E)=>{const{bum:D,scope:y,job:S,subTree:T,um:R,m:P,a:k}=h;Xs(P),Xs(k),D&&Er(D),y.stop(),S&&(S.flags|=8,Ee(T,h,g,E)),R&&ht(R,g),ht(()=>{h.isUnmounted=!0},g),g&&g.pendingBranch&&!g.isUnmounted&&h.asyncDep&&!h.asyncResolved&&h.suspenseId===g.pendingId&&(g.deps--,g.deps===0&&g.resolve())},Ae=(h,g,E,D=!1,y=!1,S=0)=>{for(let T=S;T<h.length;T++)Ee(h[T],g,E,D,y)},N=h=>{if(h.shapeFlag&6)return N(h.component.subTree);if(h.shapeFlag&128)return h.suspense.next();const g=d(h.anchor||h.el),E=g&&g[fp];return E?d(E):g};let z=!1;const B=(h,g,E)=>{h==null?g._vnode&&Ee(g._vnode,null,null,!0):w(g._vnode||null,h,g,null,null,null,E),g._vnode=h,z||(z=!0,Ws(),Ac(),z=!1)},F={p:w,um:Ee,m:xe,r:je,mt:de,mc:Q,pc:K,pbc:V,n:N,o:e};return{render:B,hydrate:void 0,createApp:$p(B)}}function To({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function en({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function Wp(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function jc(e,t,n=!1){const r=e.children,o=t.children;if(re(r)&&re(o))for(let i=0;i<r.length;i++){const s=r[i];let a=o[i];a.shapeFlag&1&&!a.dynamicChildren&&((a.patchFlag<=0||a.patchFlag===32)&&(a=o[i]=Kt(o[i]),a.el=s.el),!n&&a.patchFlag!==-2&&jc(s,a)),a.type===io&&(a.el=s.el)}}function Kp(e){const t=e.slice(),n=[0];let r,o,i,s,a;const l=e.length;for(r=0;r<l;r++){const u=e[r];if(u!==0){if(o=n[n.length-1],e[o]<u){t[r]=o,n.push(r);continue}for(i=0,s=n.length-1;i<s;)a=i+s>>1,e[n[a]]<u?i=a+1:s=a;u<e[n[i]]&&(i>0&&(t[r]=n[i-1]),n[i]=r)}}for(i=n.length,s=n[i-1];i-- >0;)n[i]=s,s=t[s];return n}function zc(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:zc(t)}function Xs(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const Vp=Symbol.for("v-scx"),Gp=()=>mt(Vp);function Rn(e,t,n){return Wc(e,t,n)}function Wc(e,t,n=Se){const{immediate:r,deep:o,flush:i,once:s}=n,a=rt({},n),l=t&&r||!t&&i!=="post";let u;if(ir){if(i==="sync"){const p=Gp();u=p.__watcherHandles||(p.__watcherHandles=[])}else if(!l){const p=()=>{};return p.stop=Lt,p.resume=Lt,p.pause=Lt,p}}const c=Je;a.call=(p,v,w)=>$t(p,c,v,w);let f=!1;i==="post"?a.scheduler=p=>{ht(p,c&&c.suspense)}:i!=="sync"&&(f=!0,a.scheduler=(p,v)=>{v?p():hs(p)}),a.augmentJob=p=>{t&&(p.flags|=4),f&&(p.flags|=2,c&&(p.id=c.uid,p.i=c))};const d=ip(e,t,a);return ir&&(u?u.push(d):l&&d()),d}function Qp(e,t,n){const r=this.proxy,o=qe(e)?e.includes(".")?Kc(r,e):()=>r[e]:e.bind(r,r);let i;ie(t)?i=t:(i=t.handler,n=t);const s=fr(this),a=Wc(o,i.bind(r),n);return s(),a}function Kc(e,t){const n=t.split(".");return()=>{let r=e;for(let o=0;o<n.length&&r;o++)r=r[n[o]];return r}}const Jp=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${Et(t)}Modifiers`]||e[`${Jt(t)}Modifiers`];function Zp(e,t,...n){if(e.isUnmounted)return;const r=e.vnode.props||Se;let o=n;const i=t.startsWith("update:"),s=i&&Jp(r,t.slice(7));s&&(s.trim&&(o=n.map(c=>qe(c)?c.trim():c)),s.number&&(o=n.map(Li)));let a,l=r[a=Ao(t)]||r[a=Ao(Et(t))];!l&&i&&(l=r[a=Ao(Jt(t))]),l&&$t(l,e,6,o);const u=r[a+"Once"];if(u){if(!e.emitted)e.emitted={};else if(e.emitted[a])return;e.emitted[a]=!0,$t(u,e,6,o)}}function Vc(e,t,n=!1){const r=t.emitsCache,o=r.get(e);if(o!==void 0)return o;const i=e.emits;let s={},a=!1;if(!ie(e)){const l=u=>{const c=Vc(u,t,!0);c&&(a=!0,rt(s,c))};!n&&t.mixins.length&&t.mixins.forEach(l),e.extends&&l(e.extends),e.mixins&&e.mixins.forEach(l)}return!i&&!a?(De(e)&&r.set(e,null),null):(re(i)?i.forEach(l=>s[l]=null):rt(s,i),De(e)&&r.set(e,s),s)}function oo(e,t){return!e||!zr(t)?!1:(t=t.slice(2).replace(/Once$/,""),ye(e,t[0].toLowerCase()+t.slice(1))||ye(e,Jt(t))||ye(e,t))}function Ys(e){const{type:t,vnode:n,proxy:r,withProxy:o,propsOptions:[i],slots:s,attrs:a,emit:l,render:u,renderCache:c,props:f,data:d,setupState:p,ctx:v,inheritAttrs:w}=e,m=Dr(e);let b,x;try{if(n.shapeFlag&4){const _=o||r,L=_;b=Nt(u.call(L,_,c,f,p,d,v)),x=a}else{const _=t;b=Nt(_.length>1?_(f,{attrs:a,slots:s,emit:l}):_(f,null)),x=t.props?a:Xp(a)}}catch(_){Qn.length=0,Xr(_,e,1),b=Te(Qt)}let O=b;if(x&&w!==!1){const _=Object.keys(x),{shapeFlag:L}=O;_.length&&L&7&&(i&&_.some(os)&&(x=Yp(x,i)),O=kn(O,x,!1,!0))}return n.dirs&&(O=kn(O,null,!1,!0),O.dirs=O.dirs?O.dirs.concat(n.dirs):n.dirs),n.transition&&ys(O,n.transition),b=O,Dr(m),b}const Xp=e=>{let t;for(const n in e)(n==="class"||n==="style"||zr(n))&&((t||(t={}))[n]=e[n]);return t},Yp=(e,t)=>{const n={};for(const r in e)(!os(r)||!(r.slice(9)in t))&&(n[r]=e[r]);return n};function ed(e,t,n){const{props:r,children:o,component:i}=e,{props:s,children:a,patchFlag:l}=t,u=i.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&l>=0){if(l&1024)return!0;if(l&16)return r?ea(r,s,u):!!s;if(l&8){const c=t.dynamicProps;for(let f=0;f<c.length;f++){const d=c[f];if(s[d]!==r[d]&&!oo(u,d))return!0}}}else return(o||a)&&(!a||!a.$stable)?!0:r===s?!1:r?s?ea(r,s,u):!0:!!s;return!1}function ea(e,t,n){const r=Object.keys(t);if(r.length!==Object.keys(e).length)return!0;for(let o=0;o<r.length;o++){const i=r[o];if(t[i]!==e[i]&&!oo(n,i))return!0}return!1}function td({vnode:e,parent:t},n){for(;t;){const r=t.subTree;if(r.suspense&&r.suspense.activeBranch===e&&(r.el=e.el),r===e)(e=t.vnode).el=n,t=t.parent;else break}}const Gc=e=>e.__isSuspense;function nd(e,t){t&&t.pendingBranch?re(e)?t.effects.push(...e):t.effects.push(e):lp(e)}const ct=Symbol.for("v-fgt"),io=Symbol.for("v-txt"),Qt=Symbol.for("v-cmt"),Po=Symbol.for("v-stc"),Qn=[];let yt=null;function $e(e=!1){Qn.push(yt=e?null:[])}function rd(){Qn.pop(),yt=Qn[Qn.length-1]||null}let rr=1;function ta(e,t=!1){rr+=e,e<0&&yt&&t&&(yt.hasOnce=!0)}function Qc(e){return e.dynamicChildren=rr>0?yt||bn:null,rd(),rr>0&&yt&&yt.push(e),e}function He(e,t,n,r,o,i){return Qc(oe(e,t,n,r,o,i,!0))}function $r(e,t,n,r,o){return Qc(Te(e,t,n,r,o,!0))}function or(e){return e?e.__v_isVNode===!0:!1}function Ln(e,t){return e.type===t.type&&e.key===t.key}const Jc=({key:e})=>e??null,Ar=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?qe(e)||Ze(e)||ie(e)?{i:Ve,r:e,k:t,f:!!n}:e:null);function oe(e,t=null,n=null,r=0,o=null,i=e===ct?0:1,s=!1,a=!1){const l={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Jc(t),ref:t&&Ar(t),scopeId:Yr,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:i,patchFlag:r,dynamicProps:o,dynamicChildren:null,appContext:null,ctx:Ve};return a?(vs(l,n),i&128&&e.normalize(l)):n&&(l.shapeFlag|=qe(n)?8:16),rr>0&&!s&&yt&&(l.patchFlag>0||i&6)&&l.patchFlag!==32&&yt.push(l),l}const Te=od;function od(e,t=null,n=null,r=0,o=null,i=!1){if((!e||e===kp)&&(e=Qt),or(e)){const a=kn(e,t,!0);return n&&vs(a,n),rr>0&&!i&&yt&&(a.shapeFlag&6?yt[yt.indexOf(e)]=a:yt.push(a)),a.patchFlag=-2,a}if(yd(e)&&(e=e.__vccOpts),t){t=id(t);let{class:a,style:l}=t;a&&!qe(a)&&(t.class=bt(a)),De(l)&&(ds(l)&&!re(l)&&(l=rt({},l)),t.style=Qr(l))}const s=qe(e)?1:Gc(e)?128:pp(e)?64:De(e)?4:ie(e)?2:0;return oe(e,t,n,r,o,s,i,!0)}function id(e){return e?ds(e)||$c(e)?rt({},e):e:null}function kn(e,t,n=!1,r=!1){const{props:o,ref:i,patchFlag:s,children:a,transition:l}=e,u=t?ad(o||{},t):o,c={__v_isVNode:!0,__v_skip:!0,type:e.type,props:u,key:u&&Jc(u),ref:t&&t.ref?n&&i?re(i)?i.concat(Ar(t)):[i,Ar(t)]:Ar(t):i,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:a,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==ct?s===-1?16:s|16:s,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:l,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&kn(e.ssContent),ssFallback:e.ssFallback&&kn(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return l&&r&&ys(c,l.clone(c)),c}function sd(e=" ",t=0){return Te(io,null,e,t)}function an(e="",t=!1){return t?($e(),$r(Qt,null,e)):Te(Qt,null,e)}function Nt(e){return e==null||typeof e=="boolean"?Te(Qt):re(e)?Te(ct,null,e.slice()):or(e)?Kt(e):Te(io,null,String(e))}function Kt(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:kn(e)}function vs(e,t){let n=0;const{shapeFlag:r}=e;if(t==null)t=null;else if(re(t))n=16;else if(typeof t=="object")if(r&65){const o=t.default;o&&(o._c&&(o._d=!1),vs(e,o()),o._c&&(o._d=!0));return}else{n=32;const o=t._;!o&&!$c(t)?t._ctx=Ve:o===3&&Ve&&(Ve.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else ie(t)?(t={default:t,_ctx:Ve},n=32):(t=String(t),r&64?(n=16,t=[sd(t)]):n=8);e.children=t,e.shapeFlag|=n}function ad(...e){const t={};for(let n=0;n<e.length;n++){const r=e[n];for(const o in r)if(o==="class")t.class!==r.class&&(t.class=bt([t.class,r.class]));else if(o==="style")t.style=Qr([t.style,r.style]);else if(zr(o)){const i=t[o],s=r[o];s&&i!==s&&!(re(i)&&i.includes(s))&&(t[o]=i?[].concat(i,s):s)}else o!==""&&(t[o]=r[o])}return t}function Tt(e,t,n,r=null){$t(e,t,7,[n,r])}const ld=Dc();let cd=0;function ud(e,t,n){const r=e.type,o=(t?t.appContext:e.appContext)||ld,i={uid:cd++,vnode:e,type:r,parent:t,appContext:o,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new Cf(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(o.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Mc(r,o),emitsOptions:Vc(r,o),emit:null,emitted:null,propsDefaults:Se,inheritAttrs:r.inheritAttrs,ctx:Se,data:Se,props:Se,attrs:Se,slots:Se,refs:Se,setupState:Se,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return i.ctx={_:i},i.root=t?t.root:i,i.emit=Zp.bind(null,i),e.ce&&e.ce(i),i}let Je=null;const Zc=()=>Je||Ve;let Fr,zi;{const e=Gr(),t=(n,r)=>{let o;return(o=e[n])||(o=e[n]=[]),o.push(r),i=>{o.length>1?o.forEach(s=>s(i)):o[0](i)}};Fr=t("__VUE_INSTANCE_SETTERS__",n=>Je=n),zi=t("__VUE_SSR_SETTERS__",n=>ir=n)}const fr=e=>{const t=Je;return Fr(e),e.scope.on(),()=>{e.scope.off(),Fr(t)}},na=()=>{Je&&Je.scope.off(),Fr(null)};function Xc(e){return e.vnode.shapeFlag&4}let ir=!1;function fd(e,t=!1,n=!1){t&&zi(t);const{props:r,children:o}=e.vnode,i=Xc(e);Fp(e,r,i,t),Up(e,o,n);const s=i?pd(e,t):void 0;return t&&zi(!1),s}function pd(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,Cp);const{setup:r}=n;if(r){Zt();const o=e.setupContext=r.length>1?hd(e):null,i=fr(e),s=cr(r,e,0,[e.props,o]),a=Jl(s);if(Xt(),i(),(a||e.sp)&&!En(e)&&_c(e),a){if(s.then(na,na),t)return s.then(l=>{ra(e,l)}).catch(l=>{Xr(l,e,0)});e.asyncDep=s}else ra(e,s)}else Yc(e)}function ra(e,t,n){ie(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:De(t)&&(e.setupState=Sc(t)),Yc(e)}function Yc(e,t,n){const r=e.type;e.render||(e.render=r.render||Lt);{const o=fr(e);Zt();try{Tp(e)}finally{Xt(),o()}}}const dd={get(e,t){return et(e,"get",""),e[t]}};function hd(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,dd),slots:e.slots,emit:e.emit,expose:t}}function so(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(Sc(Jf(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in Vn)return Vn[n](e)},has(t,n){return n in t||n in Vn}})):e.proxy}function gd(e,t=!0){return ie(e)?e.displayName||e.name:e.name||t&&e.__name}function yd(e){return ie(e)&&"__vccOpts"in e}const ut=(e,t)=>rp(e,t,ir);function eu(e,t,n){const r=arguments.length;return r===2?De(t)&&!re(t)?or(t)?Te(e,null,[t]):Te(e,t):Te(e,null,t):(r>3?n=Array.prototype.slice.call(arguments,2):r===3&&or(n)&&(n=[n]),Te(e,t,n))}const md="3.5.13";/**
* @vue/runtime-dom v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Wi;const oa=typeof window<"u"&&window.trustedTypes;if(oa)try{Wi=oa.createPolicy("vue",{createHTML:e=>e})}catch{}const tu=Wi?e=>Wi.createHTML(e):e=>e,vd="http://www.w3.org/2000/svg",bd="http://www.w3.org/1998/Math/MathML",Bt=typeof document<"u"?document:null,ia=Bt&&Bt.createElement("template"),wd={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,r)=>{const o=t==="svg"?Bt.createElementNS(vd,e):t==="mathml"?Bt.createElementNS(bd,e):n?Bt.createElement(e,{is:n}):Bt.createElement(e);return e==="select"&&r&&r.multiple!=null&&o.setAttribute("multiple",r.multiple),o},createText:e=>Bt.createTextNode(e),createComment:e=>Bt.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>Bt.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,r,o,i){const s=n?n.previousSibling:t.lastChild;if(o&&(o===i||o.nextSibling))for(;t.insertBefore(o.cloneNode(!0),n),!(o===i||!(o=o.nextSibling)););else{ia.innerHTML=tu(r==="svg"?`<svg>${e}</svg>`:r==="mathml"?`<math>${e}</math>`:e);const a=ia.content;if(r==="svg"||r==="mathml"){const l=a.firstChild;for(;l.firstChild;)a.appendChild(l.firstChild);a.removeChild(l)}t.insertBefore(a,n)}return[s?s.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},Sd=Symbol("_vtc");function xd(e,t,n){const r=e[Sd];r&&(t=(t?[t,...r]:[...r]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const Mr=Symbol("_vod"),nu=Symbol("_vsh"),bs={beforeMount(e,{value:t},{transition:n}){e[Mr]=e.style.display==="none"?"":e.style.display,n&&t?n.beforeEnter(e):$n(e,t)},mounted(e,{value:t},{transition:n}){n&&t&&n.enter(e)},updated(e,{value:t,oldValue:n},{transition:r}){!t!=!n&&(r?t?(r.beforeEnter(e),$n(e,!0),r.enter(e)):r.leave(e,()=>{$n(e,!1)}):$n(e,t))},beforeUnmount(e,{value:t}){$n(e,t)}};function $n(e,t){e.style.display=t?e[Mr]:"none",e[nu]=!t}const Ed=Symbol(""),Ad=/(^|;)\s*display\s*:/;function Rd(e,t,n){const r=e.style,o=qe(n);let i=!1;if(n&&!o){if(t)if(qe(t))for(const s of t.split(";")){const a=s.slice(0,s.indexOf(":")).trim();n[a]==null&&Rr(r,a,"")}else for(const s in t)n[s]==null&&Rr(r,s,"");for(const s in n)s==="display"&&(i=!0),Rr(r,s,n[s])}else if(o){if(t!==n){const s=r[Ed];s&&(n+=";"+s),r.cssText=n,i=Ad.test(n)}}else t&&e.removeAttribute("style");Mr in e&&(e[Mr]=i?r.display:"",e[nu]&&(r.display="none"))}const sa=/\s*!important$/;function Rr(e,t,n){if(re(n))n.forEach(r=>Rr(e,t,r));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const r=_d(e,t);sa.test(n)?e.setProperty(Jt(r),n.replace(sa,""),"important"):e[r]=n}}const aa=["Webkit","Moz","ms"],Io={};function _d(e,t){const n=Io[t];if(n)return n;let r=Et(t);if(r!=="filter"&&r in e)return Io[t]=r;r=Vr(r);for(let o=0;o<aa.length;o++){const i=aa[o]+r;if(i in e)return Io[t]=i}return t}const la="http://www.w3.org/1999/xlink";function ca(e,t,n,r,o,i=Of(t)){r&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(la,t.slice(6,t.length)):e.setAttributeNS(la,t,n):n==null||i&&!ec(n)?e.removeAttribute(t):e.setAttribute(t,i?"":Ht(n)?String(n):n)}function ua(e,t,n,r,o){if(t==="innerHTML"||t==="textContent"){n!=null&&(e[t]=t==="innerHTML"?tu(n):n);return}const i=e.tagName;if(t==="value"&&i!=="PROGRESS"&&!i.includes("-")){const a=i==="OPTION"?e.getAttribute("value")||"":e.value,l=n==null?e.type==="checkbox"?"on":"":String(n);(a!==l||!("_value"in e))&&(e.value=l),n==null&&e.removeAttribute(t),e._value=n;return}let s=!1;if(n===""||n==null){const a=typeof e[t];a==="boolean"?n=ec(n):n==null&&a==="string"?(n="",s=!0):a==="number"&&(n=0,s=!0)}try{e[t]=n}catch{}s&&e.removeAttribute(o||t)}function mn(e,t,n,r){e.addEventListener(t,n,r)}function kd(e,t,n,r){e.removeEventListener(t,n,r)}const fa=Symbol("_vei");function Od(e,t,n,r,o=null){const i=e[fa]||(e[fa]={}),s=i[t];if(r&&s)s.value=r;else{const[a,l]=Cd(t);if(r){const u=i[t]=Id(r,o);mn(e,a,u,l)}else s&&(kd(e,a,s,l),i[t]=void 0)}}const pa=/(?:Once|Passive|Capture)$/;function Cd(e){let t;if(pa.test(e)){t={};let r;for(;r=e.match(pa);)e=e.slice(0,e.length-r[0].length),t[r[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):Jt(e.slice(2)),t]}let Do=0;const Td=Promise.resolve(),Pd=()=>Do||(Td.then(()=>Do=0),Do=Date.now());function Id(e,t){const n=r=>{if(!r._vts)r._vts=Date.now();else if(r._vts<=n.attached)return;$t(Dd(r,n.value),t,5,[r])};return n.value=e,n.attached=Pd(),n}function Dd(e,t){if(re(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(r=>o=>!o._stopped&&r&&r(o))}else return t}const da=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,Nd=(e,t,n,r,o,i)=>{const s=o==="svg";t==="class"?xd(e,r,s):t==="style"?Rd(e,n,r):zr(t)?os(t)||Od(e,t,n,r,i):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):Ld(e,t,r,s))?(ua(e,t,r),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&ca(e,t,r,s,i,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!qe(r))?ua(e,Et(t),r,i,t):(t==="true-value"?e._trueValue=r:t==="false-value"&&(e._falseValue=r),ca(e,t,r,s))};function Ld(e,t,n,r){if(r)return!!(t==="innerHTML"||t==="textContent"||t in e&&da(t)&&ie(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const o=e.tagName;if(o==="IMG"||o==="VIDEO"||o==="CANVAS"||o==="SOURCE")return!1}return da(t)&&qe(n)?!1:t in e}const ha=e=>{const t=e.props["onUpdate:modelValue"]||!1;return re(t)?n=>Er(t,n):t};function $d(e){e.target.composing=!0}function ga(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const No=Symbol("_assign"),Fd={created(e,{modifiers:{lazy:t,trim:n,number:r}},o){e[No]=ha(o);const i=r||o.props&&o.props.type==="number";mn(e,t?"change":"input",s=>{if(s.target.composing)return;let a=e.value;n&&(a=a.trim()),i&&(a=Li(a)),e[No](a)}),n&&mn(e,"change",()=>{e.value=e.value.trim()}),t||(mn(e,"compositionstart",$d),mn(e,"compositionend",ga),mn(e,"change",ga))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:n,modifiers:{lazy:r,trim:o,number:i}},s){if(e[No]=ha(s),e.composing)return;const a=(i||e.type==="number")&&!/^0\d/.test(e.value)?Li(e.value):e.value,l=t??"";a!==l&&(document.activeElement===e&&e.type!=="range"&&(r&&t===n||o&&e.value.trim()===l)||(e.value=l))}},Md={esc:"escape",space:" ",up:"arrow-up",left:"arrow-left",right:"arrow-right",down:"arrow-down",delete:"backspace"},Bd=(e,t)=>{const n=e._withKeys||(e._withKeys={}),r=t.join(".");return n[r]||(n[r]=o=>{if(!("key"in o))return;const i=Jt(o.key);if(t.some(s=>s===i||Md[s]===i))return e(o)})},qd=rt({patchProp:Nd},wd);let ya;function ru(){return ya||(ya=jp(qd))}const Lo=(...e)=>{ru().render(...e)},Ud=(...e)=>{const t=ru().createApp(...e),{mount:n}=t;return t.mount=r=>{const o=jd(r);if(!o)return;const i=t._component;!ie(i)&&!i.render&&!i.template&&(i.template=o.innerHTML),o.nodeType===1&&(o.textContent="");const s=n(o,!1,Hd(o));return o instanceof Element&&(o.removeAttribute("v-cloak"),o.setAttribute("data-v-app","")),s},t};function Hd(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function jd(e){return qe(e)?document.querySelector(e):e}/*!
  * vue-router v4.5.0
  * (c) 2024 Eduardo San Martin Morote
  * @license MIT
  */const vn=typeof document<"u";function ou(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function zd(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&ou(e.default)}const ge=Object.assign;function $o(e,t){const n={};for(const r in t){const o=t[r];n[r]=_t(o)?o.map(e):e(o)}return n}const Jn=()=>{},_t=Array.isArray,iu=/#/g,Wd=/&/g,Kd=/\//g,Vd=/=/g,Gd=/\?/g,su=/\+/g,Qd=/%5B/g,Jd=/%5D/g,au=/%5E/g,Zd=/%60/g,lu=/%7B/g,Xd=/%7C/g,cu=/%7D/g,Yd=/%20/g;function ws(e){return encodeURI(""+e).replace(Xd,"|").replace(Qd,"[").replace(Jd,"]")}function eh(e){return ws(e).replace(lu,"{").replace(cu,"}").replace(au,"^")}function Ki(e){return ws(e).replace(su,"%2B").replace(Yd,"+").replace(iu,"%23").replace(Wd,"%26").replace(Zd,"`").replace(lu,"{").replace(cu,"}").replace(au,"^")}function th(e){return Ki(e).replace(Vd,"%3D")}function nh(e){return ws(e).replace(iu,"%23").replace(Gd,"%3F")}function rh(e){return e==null?"":nh(e).replace(Kd,"%2F")}function sr(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const oh=/\/$/,ih=e=>e.replace(oh,"");function Fo(e,t,n="/"){let r,o={},i="",s="";const a=t.indexOf("#");let l=t.indexOf("?");return a<l&&a>=0&&(l=-1),l>-1&&(r=t.slice(0,l),i=t.slice(l+1,a>-1?a:t.length),o=e(i)),a>-1&&(r=r||t.slice(0,a),s=t.slice(a,t.length)),r=ch(r??t,n),{fullPath:r+(i&&"?")+i+s,path:r,query:o,hash:sr(s)}}function sh(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function ma(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function ah(e,t,n){const r=t.matched.length-1,o=n.matched.length-1;return r>-1&&r===o&&On(t.matched[r],n.matched[o])&&uu(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function On(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function uu(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!lh(e[n],t[n]))return!1;return!0}function lh(e,t){return _t(e)?va(e,t):_t(t)?va(t,e):e===t}function va(e,t){return _t(t)?e.length===t.length&&e.every((n,r)=>n===t[r]):e.length===1&&e[0]===t}function ch(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),r=e.split("/"),o=r[r.length-1];(o===".."||o===".")&&r.push("");let i=n.length-1,s,a;for(s=0;s<r.length;s++)if(a=r[s],a!==".")if(a==="..")i>1&&i--;else break;return n.slice(0,i).join("/")+"/"+r.slice(s).join("/")}const zt={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var ar;(function(e){e.pop="pop",e.push="push"})(ar||(ar={}));var Zn;(function(e){e.back="back",e.forward="forward",e.unknown=""})(Zn||(Zn={}));function uh(e){if(!e)if(vn){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),ih(e)}const fh=/^[^#]+#/;function ph(e,t){return e.replace(fh,"#")+t}function dh(e,t){const n=document.documentElement.getBoundingClientRect(),r=e.getBoundingClientRect();return{behavior:t.behavior,left:r.left-n.left-(t.left||0),top:r.top-n.top-(t.top||0)}}const ao=()=>({left:window.scrollX,top:window.scrollY});function hh(e){let t;if("el"in e){const n=e.el,r=typeof n=="string"&&n.startsWith("#"),o=typeof n=="string"?r?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!o)return;t=dh(o,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function ba(e,t){return(history.state?history.state.position-t:-1)+e}const Vi=new Map;function gh(e,t){Vi.set(e,t)}function yh(e){const t=Vi.get(e);return Vi.delete(e),t}let mh=()=>location.protocol+"//"+location.host;function fu(e,t){const{pathname:n,search:r,hash:o}=t,i=e.indexOf("#");if(i>-1){let a=o.includes(e.slice(i))?e.slice(i).length:1,l=o.slice(a);return l[0]!=="/"&&(l="/"+l),ma(l,"")}return ma(n,e)+r+o}function vh(e,t,n,r){let o=[],i=[],s=null;const a=({state:d})=>{const p=fu(e,location),v=n.value,w=t.value;let m=0;if(d){if(n.value=p,t.value=d,s&&s===v){s=null;return}m=w?d.position-w.position:0}else r(p);o.forEach(b=>{b(n.value,v,{delta:m,type:ar.pop,direction:m?m>0?Zn.forward:Zn.back:Zn.unknown})})};function l(){s=n.value}function u(d){o.push(d);const p=()=>{const v=o.indexOf(d);v>-1&&o.splice(v,1)};return i.push(p),p}function c(){const{history:d}=window;d.state&&d.replaceState(ge({},d.state,{scroll:ao()}),"")}function f(){for(const d of i)d();i=[],window.removeEventListener("popstate",a),window.removeEventListener("beforeunload",c)}return window.addEventListener("popstate",a),window.addEventListener("beforeunload",c,{passive:!0}),{pauseListeners:l,listen:u,destroy:f}}function wa(e,t,n,r=!1,o=!1){return{back:e,current:t,forward:n,replaced:r,position:window.history.length,scroll:o?ao():null}}function bh(e){const{history:t,location:n}=window,r={value:fu(e,n)},o={value:t.state};o.value||i(r.value,{back:null,current:r.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function i(l,u,c){const f=e.indexOf("#"),d=f>-1?(n.host&&document.querySelector("base")?e:e.slice(f))+l:mh()+e+l;try{t[c?"replaceState":"pushState"](u,"",d),o.value=u}catch(p){console.error(p),n[c?"replace":"assign"](d)}}function s(l,u){const c=ge({},t.state,wa(o.value.back,l,o.value.forward,!0),u,{position:o.value.position});i(l,c,!0),r.value=l}function a(l,u){const c=ge({},o.value,t.state,{forward:l,scroll:ao()});i(c.current,c,!0);const f=ge({},wa(r.value,l,null),{position:c.position+1},u);i(l,f,!1),r.value=l}return{location:r,state:o,push:a,replace:s}}function wh(e){e=uh(e);const t=bh(e),n=vh(e,t.state,t.location,t.replace);function r(i,s=!0){s||n.pauseListeners(),history.go(i)}const o=ge({location:"",base:e,go:r,createHref:ph.bind(null,e)},t,n);return Object.defineProperty(o,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(o,"state",{enumerable:!0,get:()=>t.state.value}),o}function Sh(e){return e=location.host?e||location.pathname+location.search:"",e.includes("#")||(e+="#"),wh(e)}function xh(e){return typeof e=="string"||e&&typeof e=="object"}function pu(e){return typeof e=="string"||typeof e=="symbol"}const du=Symbol("");var Sa;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(Sa||(Sa={}));function Cn(e,t){return ge(new Error,{type:e,[du]:!0},t)}function Mt(e,t){return e instanceof Error&&du in e&&(t==null||!!(e.type&t))}const xa="[^/]+?",Eh={sensitive:!1,strict:!1,start:!0,end:!0},Ah=/[.+*?^${}()[\]/\\]/g;function Rh(e,t){const n=ge({},Eh,t),r=[];let o=n.start?"^":"";const i=[];for(const u of e){const c=u.length?[]:[90];n.strict&&!u.length&&(o+="/");for(let f=0;f<u.length;f++){const d=u[f];let p=40+(n.sensitive?.25:0);if(d.type===0)f||(o+="/"),o+=d.value.replace(Ah,"\\$&"),p+=40;else if(d.type===1){const{value:v,repeatable:w,optional:m,regexp:b}=d;i.push({name:v,repeatable:w,optional:m});const x=b||xa;if(x!==xa){p+=10;try{new RegExp(`(${x})`)}catch(_){throw new Error(`Invalid custom RegExp for param "${v}" (${x}): `+_.message)}}let O=w?`((?:${x})(?:/(?:${x}))*)`:`(${x})`;f||(O=m&&u.length<2?`(?:/${O})`:"/"+O),m&&(O+="?"),o+=O,p+=20,m&&(p+=-8),w&&(p+=-20),x===".*"&&(p+=-50)}c.push(p)}r.push(c)}if(n.strict&&n.end){const u=r.length-1;r[u][r[u].length-1]+=.7000000000000001}n.strict||(o+="/?"),n.end?o+="$":n.strict&&!o.endsWith("/")&&(o+="(?:/|$)");const s=new RegExp(o,n.sensitive?"":"i");function a(u){const c=u.match(s),f={};if(!c)return null;for(let d=1;d<c.length;d++){const p=c[d]||"",v=i[d-1];f[v.name]=p&&v.repeatable?p.split("/"):p}return f}function l(u){let c="",f=!1;for(const d of e){(!f||!c.endsWith("/"))&&(c+="/"),f=!1;for(const p of d)if(p.type===0)c+=p.value;else if(p.type===1){const{value:v,repeatable:w,optional:m}=p,b=v in u?u[v]:"";if(_t(b)&&!w)throw new Error(`Provided param "${v}" is an array but it is not repeatable (* or + modifiers)`);const x=_t(b)?b.join("/"):b;if(!x)if(m)d.length<2&&(c.endsWith("/")?c=c.slice(0,-1):f=!0);else throw new Error(`Missing required param "${v}"`);c+=x}}return c||"/"}return{re:s,score:r,keys:i,parse:a,stringify:l}}function _h(e,t){let n=0;for(;n<e.length&&n<t.length;){const r=t[n]-e[n];if(r)return r;n++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function hu(e,t){let n=0;const r=e.score,o=t.score;for(;n<r.length&&n<o.length;){const i=_h(r[n],o[n]);if(i)return i;n++}if(Math.abs(o.length-r.length)===1){if(Ea(r))return 1;if(Ea(o))return-1}return o.length-r.length}function Ea(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const kh={type:0,value:""},Oh=/[a-zA-Z0-9_]/;function Ch(e){if(!e)return[[]];if(e==="/")return[[kh]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(p){throw new Error(`ERR (${n})/"${u}": ${p}`)}let n=0,r=n;const o=[];let i;function s(){i&&o.push(i),i=[]}let a=0,l,u="",c="";function f(){u&&(n===0?i.push({type:0,value:u}):n===1||n===2||n===3?(i.length>1&&(l==="*"||l==="+")&&t(`A repeatable param (${u}) must be alone in its segment. eg: '/:ids+.`),i.push({type:1,value:u,regexp:c,repeatable:l==="*"||l==="+",optional:l==="*"||l==="?"})):t("Invalid state to consume buffer"),u="")}function d(){u+=l}for(;a<e.length;){if(l=e[a++],l==="\\"&&n!==2){r=n,n=4;continue}switch(n){case 0:l==="/"?(u&&f(),s()):l===":"?(f(),n=1):d();break;case 4:d(),n=r;break;case 1:l==="("?n=2:Oh.test(l)?d():(f(),n=0,l!=="*"&&l!=="?"&&l!=="+"&&a--);break;case 2:l===")"?c[c.length-1]=="\\"?c=c.slice(0,-1)+l:n=3:c+=l;break;case 3:f(),n=0,l!=="*"&&l!=="?"&&l!=="+"&&a--,c="";break;default:t("Unknown state");break}}return n===2&&t(`Unfinished custom RegExp for param "${u}"`),f(),s(),o}function Th(e,t,n){const r=Rh(Ch(e.path),n),o=ge(r,{record:e,parent:t,children:[],alias:[]});return t&&!o.record.aliasOf==!t.record.aliasOf&&t.children.push(o),o}function Ph(e,t){const n=[],r=new Map;t=ka({strict:!1,end:!0,sensitive:!1},t);function o(f){return r.get(f)}function i(f,d,p){const v=!p,w=Ra(f);w.aliasOf=p&&p.record;const m=ka(t,f),b=[w];if("alias"in f){const _=typeof f.alias=="string"?[f.alias]:f.alias;for(const L of _)b.push(Ra(ge({},w,{components:p?p.record.components:w.components,path:L,aliasOf:p?p.record:w})))}let x,O;for(const _ of b){const{path:L}=_;if(d&&L[0]!=="/"){const H=d.record.path,M=H[H.length-1]==="/"?"":"/";_.path=d.record.path+(L&&M+L)}if(x=Th(_,d,m),p?p.alias.push(x):(O=O||x,O!==x&&O.alias.push(x),v&&f.name&&!_a(x)&&s(f.name)),gu(x)&&l(x),w.children){const H=w.children;for(let M=0;M<H.length;M++)i(H[M],x,p&&p.children[M])}p=p||x}return O?()=>{s(O)}:Jn}function s(f){if(pu(f)){const d=r.get(f);d&&(r.delete(f),n.splice(n.indexOf(d),1),d.children.forEach(s),d.alias.forEach(s))}else{const d=n.indexOf(f);d>-1&&(n.splice(d,1),f.record.name&&r.delete(f.record.name),f.children.forEach(s),f.alias.forEach(s))}}function a(){return n}function l(f){const d=Nh(f,n);n.splice(d,0,f),f.record.name&&!_a(f)&&r.set(f.record.name,f)}function u(f,d){let p,v={},w,m;if("name"in f&&f.name){if(p=r.get(f.name),!p)throw Cn(1,{location:f});m=p.record.name,v=ge(Aa(d.params,p.keys.filter(O=>!O.optional).concat(p.parent?p.parent.keys.filter(O=>O.optional):[]).map(O=>O.name)),f.params&&Aa(f.params,p.keys.map(O=>O.name))),w=p.stringify(v)}else if(f.path!=null)w=f.path,p=n.find(O=>O.re.test(w)),p&&(v=p.parse(w),m=p.record.name);else{if(p=d.name?r.get(d.name):n.find(O=>O.re.test(d.path)),!p)throw Cn(1,{location:f,currentLocation:d});m=p.record.name,v=ge({},d.params,f.params),w=p.stringify(v)}const b=[];let x=p;for(;x;)b.unshift(x.record),x=x.parent;return{name:m,path:w,params:v,matched:b,meta:Dh(b)}}e.forEach(f=>i(f));function c(){n.length=0,r.clear()}return{addRoute:i,resolve:u,removeRoute:s,clearRoutes:c,getRoutes:a,getRecordMatcher:o}}function Aa(e,t){const n={};for(const r of t)r in e&&(n[r]=e[r]);return n}function Ra(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:Ih(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function Ih(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const r in e.components)t[r]=typeof n=="object"?n[r]:n;return t}function _a(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function Dh(e){return e.reduce((t,n)=>ge(t,n.meta),{})}function ka(e,t){const n={};for(const r in e)n[r]=r in t?t[r]:e[r];return n}function Nh(e,t){let n=0,r=t.length;for(;n!==r;){const i=n+r>>1;hu(e,t[i])<0?r=i:n=i+1}const o=Lh(e);return o&&(r=t.lastIndexOf(o,r-1)),r}function Lh(e){let t=e;for(;t=t.parent;)if(gu(t)&&hu(e,t)===0)return t}function gu({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function $h(e){const t={};if(e===""||e==="?")return t;const r=(e[0]==="?"?e.slice(1):e).split("&");for(let o=0;o<r.length;++o){const i=r[o].replace(su," "),s=i.indexOf("="),a=sr(s<0?i:i.slice(0,s)),l=s<0?null:sr(i.slice(s+1));if(a in t){let u=t[a];_t(u)||(u=t[a]=[u]),u.push(l)}else t[a]=l}return t}function Oa(e){let t="";for(let n in e){const r=e[n];if(n=th(n),r==null){r!==void 0&&(t+=(t.length?"&":"")+n);continue}(_t(r)?r.map(i=>i&&Ki(i)):[r&&Ki(r)]).forEach(i=>{i!==void 0&&(t+=(t.length?"&":"")+n,i!=null&&(t+="="+i))})}return t}function Fh(e){const t={};for(const n in e){const r=e[n];r!==void 0&&(t[n]=_t(r)?r.map(o=>o==null?null:""+o):r==null?r:""+r)}return t}const Mh=Symbol(""),Ca=Symbol(""),Ss=Symbol(""),yu=Symbol(""),Gi=Symbol("");function Fn(){let e=[];function t(r){return e.push(r),()=>{const o=e.indexOf(r);o>-1&&e.splice(o,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function Vt(e,t,n,r,o,i=s=>s()){const s=r&&(r.enterCallbacks[o]=r.enterCallbacks[o]||[]);return()=>new Promise((a,l)=>{const u=d=>{d===!1?l(Cn(4,{from:n,to:t})):d instanceof Error?l(d):xh(d)?l(Cn(2,{from:t,to:d})):(s&&r.enterCallbacks[o]===s&&typeof d=="function"&&s.push(d),a())},c=i(()=>e.call(r&&r.instances[o],t,n,u));let f=Promise.resolve(c);e.length<3&&(f=f.then(u)),f.catch(d=>l(d))})}function Mo(e,t,n,r,o=i=>i()){const i=[];for(const s of e)for(const a in s.components){let l=s.components[a];if(!(t!=="beforeRouteEnter"&&!s.instances[a]))if(ou(l)){const c=(l.__vccOpts||l)[t];c&&i.push(Vt(c,n,r,s,a,o))}else{let u=l();i.push(()=>u.then(c=>{if(!c)throw new Error(`Couldn't resolve component "${a}" at "${s.path}"`);const f=zd(c)?c.default:c;s.mods[a]=c,s.components[a]=f;const p=(f.__vccOpts||f)[t];return p&&Vt(p,n,r,s,a,o)()}))}}return i}function Ta(e){const t=mt(Ss),n=mt(yu),r=ut(()=>{const l=Ke(e.to);return t.resolve(l)}),o=ut(()=>{const{matched:l}=r.value,{length:u}=l,c=l[u-1],f=n.matched;if(!c||!f.length)return-1;const d=f.findIndex(On.bind(null,c));if(d>-1)return d;const p=Pa(l[u-2]);return u>1&&Pa(c)===p&&f[f.length-1].path!==p?f.findIndex(On.bind(null,l[u-2])):d}),i=ut(()=>o.value>-1&&jh(n.params,r.value.params)),s=ut(()=>o.value>-1&&o.value===n.matched.length-1&&uu(n.params,r.value.params));function a(l={}){if(Hh(l)){const u=t[Ke(e.replace)?"replace":"push"](Ke(e.to)).catch(Jn);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>u),u}return Promise.resolve()}return{route:r,href:ut(()=>r.value.href),isActive:i,isExactActive:s,navigate:a}}function Bh(e){return e.length===1?e[0]:e}const qh=to({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"}},useLink:Ta,setup(e,{slots:t}){const n=Zr(Ta(e)),{options:r}=mt(Ss),o=ut(()=>({[Ia(e.activeClass,r.linkActiveClass,"router-link-active")]:n.isActive,[Ia(e.exactActiveClass,r.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const i=t.default&&Bh(t.default(n));return e.custom?i:eu("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:o.value},i)}}}),Uh=qh;function Hh(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function jh(e,t){for(const n in t){const r=t[n],o=e[n];if(typeof r=="string"){if(r!==o)return!1}else if(!_t(o)||o.length!==r.length||r.some((i,s)=>i!==o[s]))return!1}return!0}function Pa(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const Ia=(e,t,n)=>e??t??n,zh=to({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const r=mt(Gi),o=ut(()=>e.route||r.value),i=mt(Ca,0),s=ut(()=>{let u=Ke(i);const{matched:c}=o.value;let f;for(;(f=c[u])&&!f.components;)u++;return u}),a=ut(()=>o.value.matched[s.value]);Gn(Ca,ut(()=>s.value+1)),Gn(Mh,a),Gn(Gi,o);const l=We();return Rn(()=>[l.value,a.value,e.name],([u,c,f],[d,p,v])=>{c&&(c.instances[f]=u,p&&p!==c&&u&&u===d&&(c.leaveGuards.size||(c.leaveGuards=p.leaveGuards),c.updateGuards.size||(c.updateGuards=p.updateGuards))),u&&c&&(!p||!On(c,p)||!d)&&(c.enterCallbacks[f]||[]).forEach(w=>w(u))},{flush:"post"}),()=>{const u=o.value,c=e.name,f=a.value,d=f&&f.components[c];if(!d)return Da(n.default,{Component:d,route:u});const p=f.props[c],v=p?p===!0?u.params:typeof p=="function"?p(u):p:null,m=eu(d,ge({},v,t,{onVnodeUnmounted:b=>{b.component.isUnmounted&&(f.instances[c]=null)},ref:l}));return Da(n.default,{Component:m,route:u})||m}}});function Da(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const mu=zh;function Wh(e){const t=Ph(e.routes,e),n=e.parseQuery||$h,r=e.stringifyQuery||Oa,o=e.history,i=Fn(),s=Fn(),a=Fn(),l=bc(zt);let u=zt;vn&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const c=$o.bind(null,N=>""+N),f=$o.bind(null,rh),d=$o.bind(null,sr);function p(N,z){let B,F;return pu(N)?(B=t.getRecordMatcher(N),F=z):F=N,t.addRoute(F,B)}function v(N){const z=t.getRecordMatcher(N);z&&t.removeRoute(z)}function w(){return t.getRoutes().map(N=>N.record)}function m(N){return!!t.getRecordMatcher(N)}function b(N,z){if(z=ge({},z||l.value),typeof N=="string"){const E=Fo(n,N,z.path),D=t.resolve({path:E.path},z),y=o.createHref(E.fullPath);return ge(E,D,{params:d(D.params),hash:sr(E.hash),redirectedFrom:void 0,href:y})}let B;if(N.path!=null)B=ge({},N,{path:Fo(n,N.path,z.path).path});else{const E=ge({},N.params);for(const D in E)E[D]==null&&delete E[D];B=ge({},N,{params:f(E)}),z.params=f(z.params)}const F=t.resolve(B,z),X=N.hash||"";F.params=c(d(F.params));const h=sh(r,ge({},N,{hash:eh(X),path:F.path})),g=o.createHref(h);return ge({fullPath:h,hash:X,query:r===Oa?Fh(N.query):N.query||{}},F,{redirectedFrom:void 0,href:g})}function x(N){return typeof N=="string"?Fo(n,N,l.value.path):ge({},N)}function O(N,z){if(u!==N)return Cn(8,{from:z,to:N})}function _(N){return M(N)}function L(N){return _(ge(x(N),{replace:!0}))}function H(N){const z=N.matched[N.matched.length-1];if(z&&z.redirect){const{redirect:B}=z;let F=typeof B=="function"?B(N):B;return typeof F=="string"&&(F=F.includes("?")||F.includes("#")?F=x(F):{path:F},F.params={}),ge({query:N.query,hash:N.hash,params:F.path!=null?{}:N.params},F)}}function M(N,z){const B=u=b(N),F=l.value,X=N.state,h=N.force,g=N.replace===!0,E=H(B);if(E)return M(ge(x(E),{state:typeof E=="object"?ge({},X,E.state):X,force:h,replace:g}),z||B);const D=B;D.redirectedFrom=z;let y;return!h&&ah(r,F,B)&&(y=Cn(16,{to:D,from:F}),xe(F,F,!0,!1)),(y?Promise.resolve(y):V(D,F)).catch(S=>Mt(S)?Mt(S,2)?S:Fe(S):K(S,D,F)).then(S=>{if(S){if(Mt(S,2))return M(ge({replace:g},x(S.to),{state:typeof S.to=="object"?ge({},X,S.to.state):X,force:h}),z||D)}else S=J(D,F,!0,g,X);return ee(D,F,S),S})}function Q(N,z){const B=O(N,z);return B?Promise.reject(B):Promise.resolve()}function j(N){const z=Ge.values().next().value;return z&&typeof z.runWithContext=="function"?z.runWithContext(N):N()}function V(N,z){let B;const[F,X,h]=Kh(N,z);B=Mo(F.reverse(),"beforeRouteLeave",N,z);for(const E of F)E.leaveGuards.forEach(D=>{B.push(Vt(D,N,z))});const g=Q.bind(null,N,z);return B.push(g),Ae(B).then(()=>{B=[];for(const E of i.list())B.push(Vt(E,N,z));return B.push(g),Ae(B)}).then(()=>{B=Mo(X,"beforeRouteUpdate",N,z);for(const E of X)E.updateGuards.forEach(D=>{B.push(Vt(D,N,z))});return B.push(g),Ae(B)}).then(()=>{B=[];for(const E of h)if(E.beforeEnter)if(_t(E.beforeEnter))for(const D of E.beforeEnter)B.push(Vt(D,N,z));else B.push(Vt(E.beforeEnter,N,z));return B.push(g),Ae(B)}).then(()=>(N.matched.forEach(E=>E.enterCallbacks={}),B=Mo(h,"beforeRouteEnter",N,z,j),B.push(g),Ae(B))).then(()=>{B=[];for(const E of s.list())B.push(Vt(E,N,z));return B.push(g),Ae(B)}).catch(E=>Mt(E,8)?E:Promise.reject(E))}function ee(N,z,B){a.list().forEach(F=>j(()=>F(N,z,B)))}function J(N,z,B,F,X){const h=O(N,z);if(h)return h;const g=z===zt,E=vn?history.state:{};B&&(F||g?o.replace(N.fullPath,ge({scroll:g&&E&&E.scroll},X)):o.push(N.fullPath,X)),l.value=N,xe(N,z,B,g),Fe()}let pe;function de(){pe||(pe=o.listen((N,z,B)=>{if(!Xe.listening)return;const F=b(N),X=H(F);if(X){M(ge(X,{replace:!0,force:!0}),F).catch(Jn);return}u=F;const h=l.value;vn&&gh(ba(h.fullPath,B.delta),ao()),V(F,h).catch(g=>Mt(g,12)?g:Mt(g,2)?(M(ge(x(g.to),{force:!0}),F).then(E=>{Mt(E,20)&&!B.delta&&B.type===ar.pop&&o.go(-1,!1)}).catch(Jn),Promise.reject()):(B.delta&&o.go(-B.delta,!1),K(g,F,h))).then(g=>{g=g||J(F,h,!1),g&&(B.delta&&!Mt(g,8)?o.go(-B.delta,!1):B.type===ar.pop&&Mt(g,20)&&o.go(-1,!1)),ee(F,h,g)}).catch(Jn)}))}let Ne=Fn(),ue=Fn(),te;function K(N,z,B){Fe(N);const F=ue.list();return F.length?F.forEach(X=>X(N,z,B)):console.error(N),Promise.reject(N)}function ve(){return te&&l.value!==zt?Promise.resolve():new Promise((N,z)=>{Ne.add([N,z])})}function Fe(N){return te||(te=!N,de(),Ne.list().forEach(([z,B])=>N?B(N):z()),Ne.reset()),N}function xe(N,z,B,F){const{scrollBehavior:X}=e;if(!vn||!X)return Promise.resolve();const h=!B&&yh(ba(N.fullPath,0))||(F||!B)&&history.state&&history.state.scroll||null;return _n().then(()=>X(N,z,h)).then(g=>g&&hh(g)).catch(g=>K(g,N,z))}const Ee=N=>o.go(N);let je;const Ge=new Set,Xe={currentRoute:l,listening:!0,addRoute:p,removeRoute:v,clearRoutes:t.clearRoutes,hasRoute:m,getRoutes:w,resolve:b,options:e,push:_,replace:L,go:Ee,back:()=>Ee(-1),forward:()=>Ee(1),beforeEach:i.add,beforeResolve:s.add,afterEach:a.add,onError:ue.add,isReady:ve,install(N){const z=this;N.component("RouterLink",Uh),N.component("RouterView",mu),N.config.globalProperties.$router=z,Object.defineProperty(N.config.globalProperties,"$route",{enumerable:!0,get:()=>Ke(l)}),vn&&!je&&l.value===zt&&(je=!0,_(o.location).catch(X=>{}));const B={};for(const X in zt)Object.defineProperty(B,X,{get:()=>l.value[X],enumerable:!0});N.provide(Ss,z),N.provide(yu,mc(B)),N.provide(Gi,l);const F=N.unmount;Ge.add(N),N.unmount=function(){Ge.delete(N),Ge.size<1&&(u=zt,pe&&pe(),pe=null,l.value=zt,je=!1,te=!1),F()}}};function Ae(N){return N.reduce((z,B)=>z.then(()=>j(B)),Promise.resolve())}return Xe}function Kh(e,t){const n=[],r=[],o=[],i=Math.max(t.matched.length,e.matched.length);for(let s=0;s<i;s++){const a=t.matched[s];a&&(e.matched.find(u=>On(u,a))?r.push(a):n.push(a));const l=e.matched[s];l&&(t.matched.find(u=>On(u,l))||o.push(l))}return[n,r,o]}const Vh={class:"wrapper"},Gh={__name:"App",setup(e){return(t,n)=>($e(),He("div",null,[n[0]||(n[0]=oe("header",null,null,-1)),oe("div",Vh,[Te(Ke(mu))]),n[1]||(n[1]=oe("footer",null,null,-1))]))}},Qh={SUCCESS_CODE:0},vu="https://m.kedou.com";var Na=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function Jh(e){if(e.__esModule)return e;var t=e.default;if(typeof t=="function"){var n=function r(){return this instanceof r?Reflect.construct(t,arguments,this.constructor):t.apply(this,arguments)};n.prototype=t.prototype}else n={};return Object.defineProperty(n,"__esModule",{value:!0}),Object.keys(e).forEach(function(r){var o=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(n,r,o.get?o:{enumerable:!0,get:function(){return e[r]}})}),n}var Bo,La;function Tn(){return La||(La=1,Bo=TypeError),Bo}const Zh={},Xh=Object.freeze(Object.defineProperty({__proto__:null,default:Zh},Symbol.toStringTag,{value:"Module"})),Yh=Jh(Xh);var qo,$a;function lo(){if($a)return qo;$a=1;var e=typeof Map=="function"&&Map.prototype,t=Object.getOwnPropertyDescriptor&&e?Object.getOwnPropertyDescriptor(Map.prototype,"size"):null,n=e&&t&&typeof t.get=="function"?t.get:null,r=e&&Map.prototype.forEach,o=typeof Set=="function"&&Set.prototype,i=Object.getOwnPropertyDescriptor&&o?Object.getOwnPropertyDescriptor(Set.prototype,"size"):null,s=o&&i&&typeof i.get=="function"?i.get:null,a=o&&Set.prototype.forEach,l=typeof WeakMap=="function"&&WeakMap.prototype,u=l?WeakMap.prototype.has:null,c=typeof WeakSet=="function"&&WeakSet.prototype,f=c?WeakSet.prototype.has:null,d=typeof WeakRef=="function"&&WeakRef.prototype,p=d?WeakRef.prototype.deref:null,v=Boolean.prototype.valueOf,w=Object.prototype.toString,m=Function.prototype.toString,b=String.prototype.match,x=String.prototype.slice,O=String.prototype.replace,_=String.prototype.toUpperCase,L=String.prototype.toLowerCase,H=RegExp.prototype.test,M=Array.prototype.concat,Q=Array.prototype.join,j=Array.prototype.slice,V=Math.floor,ee=typeof BigInt=="function"?BigInt.prototype.valueOf:null,J=Object.getOwnPropertySymbols,pe=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?Symbol.prototype.toString:null,de=typeof Symbol=="function"&&typeof Symbol.iterator=="object",Ne=typeof Symbol=="function"&&Symbol.toStringTag&&(typeof Symbol.toStringTag===de||!0)?Symbol.toStringTag:null,ue=Object.prototype.propertyIsEnumerable,te=(typeof Reflect=="function"?Reflect.getPrototypeOf:Object.getPrototypeOf)||([].__proto__===Array.prototype?function(A){return A.__proto__}:null);function K(A,C){if(A===1/0||A===-1/0||A!==A||A&&A>-1e3&&A<1e3||H.call(/e/,C))return C;var ne=/[0-9](?=(?:[0-9]{3})+(?![0-9]))/g;if(typeof A=="number"){var he=A<0?-V(-A):V(A);if(he!==A){var le=String(he),ae=x.call(C,le.length+1);return O.call(le,ne,"$&_")+"."+O.call(O.call(ae,/([0-9]{3})/g,"$&_"),/_$/,"")}}return O.call(C,ne,"$&_")}var ve=Yh,Fe=ve.custom,xe=E(Fe)?Fe:null,Ee={__proto__:null,double:'"',single:"'"},je={__proto__:null,double:/(["\\])/g,single:/(['\\])/g};qo=function A(C,ne,he,le){var ae=ne||{};if(S(ae,"quoteStyle")&&!S(Ee,ae.quoteStyle))throw new TypeError('option "quoteStyle" must be "single" or "double"');if(S(ae,"maxStringLength")&&(typeof ae.maxStringLength=="number"?ae.maxStringLength<0&&ae.maxStringLength!==1/0:ae.maxStringLength!==null))throw new TypeError('option "maxStringLength", if provided, must be a positive integer, Infinity, or `null`');var Ot=S(ae,"customInspect")?ae.customInspect:!0;if(typeof Ot!="boolean"&&Ot!=="symbol")throw new TypeError("option \"customInspect\", if provided, must be `true`, `false`, or `'symbol'`");if(S(ae,"indent")&&ae.indent!==null&&ae.indent!=="	"&&!(parseInt(ae.indent,10)===ae.indent&&ae.indent>0))throw new TypeError('option "indent" must be "\\t", an integer > 0, or `null`');if(S(ae,"numericSeparator")&&typeof ae.numericSeparator!="boolean")throw new TypeError('option "numericSeparator", if provided, must be `true` or `false`');var Ct=ae.numericSeparator;if(typeof C>"u")return"undefined";if(C===null)return"null";if(typeof C=="boolean")return C?"true":"false";if(typeof C=="string")return G(C,ae);if(typeof C=="number"){if(C===0)return 1/0/C>0?"0":"-0";var Qe=String(C);return Ct?K(C,Qe):Qe}if(typeof C=="bigint"){var lt=String(C)+"n";return Ct?K(C,lt):lt}var Me=typeof ae.depth>"u"?5:ae.depth;if(typeof he>"u"&&(he=0),he>=Me&&Me>0&&typeof C=="object")return N(C)?"[Array]":"[Object]";var ke=Ue(ae,he);if(typeof le>"u")le=[];else if(P(le,C)>=0)return"[Circular]";function Ce(hn,mr,gf){if(mr&&(le=j.call(le),le.push(mr)),gf){var Us={depth:ae.depth};return S(ae,"quoteStyle")&&(Us.quoteStyle=ae.quoteStyle),A(hn,Us,he+1,le)}return A(hn,ae,he+1,le)}if(typeof C=="function"&&!B(C)){var Ye=R(C),ze=Le(C,Ce);return"[Function"+(Ye?": "+Ye:" (anonymous)")+"]"+(ze.length>0?" { "+Q.call(ze,", ")+" }":"")}if(E(C)){var Be=de?O.call(String(C),/^(Symbol\(.*\))_[^)]*$/,"$1"):pe.call(C);return typeof C=="object"&&!de?be(Be):Be}if(Z(C)){for(var ot="<"+L.call(String(C.nodeName)),dt=C.attributes||[],At=0;At<dt.length;At++)ot+=" "+dt[At].name+"="+Ge(Xe(dt[At].value),"double",ae);return ot+=">",C.childNodes&&C.childNodes.length&&(ot+="..."),ot+="</"+L.call(String(C.nodeName))+">",ot}if(N(C)){if(C.length===0)return"[]";var bo=Le(C,Ce);return ke&&!we(bo)?"["+pt(bo,ke)+"]":"[ "+Q.call(bo,", ")+" ]"}if(F(C)){var wo=Le(C,Ce);return!("cause"in Error.prototype)&&"cause"in C&&!ue.call(C,"cause")?"{ ["+String(C)+"] "+Q.call(M.call("[cause]: "+Ce(C.cause),wo),", ")+" }":wo.length===0?"["+String(C)+"]":"{ ["+String(C)+"] "+Q.call(wo,", ")+" }"}if(typeof C=="object"&&Ot){if(xe&&typeof C[xe]=="function"&&ve)return ve(C,{depth:Me-he});if(Ot!=="symbol"&&typeof C.inspect=="function")return C.inspect()}if(k(C)){var Fs=[];return r&&r.call(C,function(hn,mr){Fs.push(Ce(mr,C,!0)+" => "+Ce(hn,C))}),Re("Map",n.call(C),Fs,ke)}if(U(C)){var Ms=[];return a&&a.call(C,function(hn){Ms.push(Ce(hn,C))}),Re("Set",s.call(C),Ms,ke)}if(q(C))return Ie("WeakMap");if(W(C))return Ie("WeakSet");if($(C))return Ie("WeakRef");if(h(C))return be(Ce(Number(C)));if(D(C))return be(Ce(ee.call(C)));if(g(C))return be(v.call(C));if(X(C))return be(Ce(String(C)));if(typeof window<"u"&&C===window)return"{ [object Window] }";if(typeof globalThis<"u"&&C===globalThis||typeof Na<"u"&&C===Na)return"{ [object globalThis] }";if(!z(C)&&!B(C)){var So=Le(C,Ce),Bs=te?te(C)===Object.prototype:C instanceof Object||C.constructor===Object,xo=C instanceof Object?"":"null prototype",qs=!Bs&&Ne&&Object(C)===C&&Ne in C?x.call(T(C),8,-1):xo?"Object":"",hf=Bs||typeof C.constructor!="function"?"":C.constructor.name?C.constructor.name+" ":"",Eo=hf+(qs||xo?"["+Q.call(M.call([],qs||[],xo||[]),": ")+"] ":"");return So.length===0?Eo+"{}":ke?Eo+"{"+pt(So,ke)+"}":Eo+"{ "+Q.call(So,", ")+" }"}return String(C)};function Ge(A,C,ne){var he=ne.quoteStyle||C,le=Ee[he];return le+A+le}function Xe(A){return O.call(String(A),/"/g,"&quot;")}function Ae(A){return!Ne||!(typeof A=="object"&&(Ne in A||typeof A[Ne]<"u"))}function N(A){return T(A)==="[object Array]"&&Ae(A)}function z(A){return T(A)==="[object Date]"&&Ae(A)}function B(A){return T(A)==="[object RegExp]"&&Ae(A)}function F(A){return T(A)==="[object Error]"&&Ae(A)}function X(A){return T(A)==="[object String]"&&Ae(A)}function h(A){return T(A)==="[object Number]"&&Ae(A)}function g(A){return T(A)==="[object Boolean]"&&Ae(A)}function E(A){if(de)return A&&typeof A=="object"&&A instanceof Symbol;if(typeof A=="symbol")return!0;if(!A||typeof A!="object"||!pe)return!1;try{return pe.call(A),!0}catch{}return!1}function D(A){if(!A||typeof A!="object"||!ee)return!1;try{return ee.call(A),!0}catch{}return!1}var y=Object.prototype.hasOwnProperty||function(A){return A in this};function S(A,C){return y.call(A,C)}function T(A){return w.call(A)}function R(A){if(A.name)return A.name;var C=b.call(m.call(A),/^function\s*([\w$]+)/);return C?C[1]:null}function P(A,C){if(A.indexOf)return A.indexOf(C);for(var ne=0,he=A.length;ne<he;ne++)if(A[ne]===C)return ne;return-1}function k(A){if(!n||!A||typeof A!="object")return!1;try{n.call(A);try{s.call(A)}catch{return!0}return A instanceof Map}catch{}return!1}function q(A){if(!u||!A||typeof A!="object")return!1;try{u.call(A,u);try{f.call(A,f)}catch{return!0}return A instanceof WeakMap}catch{}return!1}function $(A){if(!p||!A||typeof A!="object")return!1;try{return p.call(A),!0}catch{}return!1}function U(A){if(!s||!A||typeof A!="object")return!1;try{s.call(A);try{n.call(A)}catch{return!0}return A instanceof Set}catch{}return!1}function W(A){if(!f||!A||typeof A!="object")return!1;try{f.call(A,f);try{u.call(A,u)}catch{return!0}return A instanceof WeakSet}catch{}return!1}function Z(A){return!A||typeof A!="object"?!1:typeof HTMLElement<"u"&&A instanceof HTMLElement?!0:typeof A.nodeName=="string"&&typeof A.getAttribute=="function"}function G(A,C){if(A.length>C.maxStringLength){var ne=A.length-C.maxStringLength,he="... "+ne+" more character"+(ne>1?"s":"");return G(x.call(A,0,C.maxStringLength),C)+he}var le=je[C.quoteStyle||"single"];le.lastIndex=0;var ae=O.call(O.call(A,le,"\\$1"),/[\x00-\x1f]/g,Y);return Ge(ae,"single",C)}function Y(A){var C=A.charCodeAt(0),ne={8:"b",9:"t",10:"n",12:"f",13:"r"}[C];return ne?"\\"+ne:"\\x"+(C<16?"0":"")+_.call(C.toString(16))}function be(A){return"Object("+A+")"}function Ie(A){return A+" { ? }"}function Re(A,C,ne,he){var le=he?pt(ne,he):Q.call(ne,", ");return A+" ("+C+") {"+le+"}"}function we(A){for(var C=0;C<A.length;C++)if(P(A[C],`
`)>=0)return!1;return!0}function Ue(A,C){var ne;if(A.indent==="	")ne="	";else if(typeof A.indent=="number"&&A.indent>0)ne=Q.call(Array(A.indent+1)," ");else return null;return{base:ne,prev:Q.call(Array(C+1),ne)}}function pt(A,C){if(A.length===0)return"";var ne=`
`+C.prev+C.base;return ne+Q.call(A,","+ne)+`
`+C.prev}function Le(A,C){var ne=N(A),he=[];if(ne){he.length=A.length;for(var le=0;le<A.length;le++)he[le]=S(A,le)?C(A[le],A):""}var ae=typeof J=="function"?J(A):[],Ot;if(de){Ot={};for(var Ct=0;Ct<ae.length;Ct++)Ot["$"+ae[Ct]]=ae[Ct]}for(var Qe in A)S(A,Qe)&&(ne&&String(Number(Qe))===Qe&&Qe<A.length||de&&Ot["$"+Qe]instanceof Symbol||(H.call(/[^\w$]/,Qe)?he.push(C(Qe,A)+": "+C(A[Qe],A)):he.push(Qe+": "+C(A[Qe],A))));if(typeof J=="function")for(var lt=0;lt<ae.length;lt++)ue.call(A,ae[lt])&&he.push("["+C(ae[lt])+"]: "+C(A[ae[lt]],A));return he}return qo}var Uo,Fa;function eg(){if(Fa)return Uo;Fa=1;var e=lo(),t=Tn(),n=function(a,l,u){for(var c=a,f;(f=c.next)!=null;c=f)if(f.key===l)return c.next=f.next,u||(f.next=a.next,a.next=f),f},r=function(a,l){if(a){var u=n(a,l);return u&&u.value}},o=function(a,l,u){var c=n(a,l);c?c.value=u:a.next={key:l,next:a.next,value:u}},i=function(a,l){return a?!!n(a,l):!1},s=function(a,l){if(a)return n(a,l,!0)};return Uo=function(){var l,u={assert:function(c){if(!u.has(c))throw new t("Side channel does not contain "+e(c))},delete:function(c){var f=l&&l.next,d=s(l,c);return d&&f&&f===d&&(l=void 0),!!d},get:function(c){return r(l,c)},has:function(c){return i(l,c)},set:function(c,f){l||(l={next:void 0}),o(l,c,f)}};return u},Uo}var Ho,Ma;function bu(){return Ma||(Ma=1,Ho=Object),Ho}var jo,Ba;function tg(){return Ba||(Ba=1,jo=Error),jo}var zo,qa;function ng(){return qa||(qa=1,zo=EvalError),zo}var Wo,Ua;function rg(){return Ua||(Ua=1,Wo=RangeError),Wo}var Ko,Ha;function og(){return Ha||(Ha=1,Ko=ReferenceError),Ko}var Vo,ja;function ig(){return ja||(ja=1,Vo=SyntaxError),Vo}var Go,za;function sg(){return za||(za=1,Go=URIError),Go}var Qo,Wa;function ag(){return Wa||(Wa=1,Qo=Math.abs),Qo}var Jo,Ka;function lg(){return Ka||(Ka=1,Jo=Math.floor),Jo}var Zo,Va;function cg(){return Va||(Va=1,Zo=Math.max),Zo}var Xo,Ga;function ug(){return Ga||(Ga=1,Xo=Math.min),Xo}var Yo,Qa;function fg(){return Qa||(Qa=1,Yo=Math.pow),Yo}var ei,Ja;function pg(){return Ja||(Ja=1,ei=Math.round),ei}var ti,Za;function dg(){return Za||(Za=1,ti=Number.isNaN||function(t){return t!==t}),ti}var ni,Xa;function hg(){if(Xa)return ni;Xa=1;var e=dg();return ni=function(n){return e(n)||n===0?n:n<0?-1:1},ni}var ri,Ya;function gg(){return Ya||(Ya=1,ri=Object.getOwnPropertyDescriptor),ri}var oi,el;function wu(){if(el)return oi;el=1;var e=gg();if(e)try{e([],"length")}catch{e=null}return oi=e,oi}var ii,tl;function yg(){if(tl)return ii;tl=1;var e=Object.defineProperty||!1;if(e)try{e({},"a",{value:1})}catch{e=!1}return ii=e,ii}var si,nl;function mg(){return nl||(nl=1,si=function(){if(typeof Symbol!="function"||typeof Object.getOwnPropertySymbols!="function")return!1;if(typeof Symbol.iterator=="symbol")return!0;var t={},n=Symbol("test"),r=Object(n);if(typeof n=="string"||Object.prototype.toString.call(n)!=="[object Symbol]"||Object.prototype.toString.call(r)!=="[object Symbol]")return!1;var o=42;t[n]=o;for(var i in t)return!1;if(typeof Object.keys=="function"&&Object.keys(t).length!==0||typeof Object.getOwnPropertyNames=="function"&&Object.getOwnPropertyNames(t).length!==0)return!1;var s=Object.getOwnPropertySymbols(t);if(s.length!==1||s[0]!==n||!Object.prototype.propertyIsEnumerable.call(t,n))return!1;if(typeof Object.getOwnPropertyDescriptor=="function"){var a=Object.getOwnPropertyDescriptor(t,n);if(a.value!==o||a.enumerable!==!0)return!1}return!0}),si}var ai,rl;function vg(){if(rl)return ai;rl=1;var e=typeof Symbol<"u"&&Symbol,t=mg();return ai=function(){return typeof e!="function"||typeof Symbol!="function"||typeof e("foo")!="symbol"||typeof Symbol("bar")!="symbol"?!1:t()},ai}var li,ol;function Su(){return ol||(ol=1,li=typeof Reflect<"u"&&Reflect.getPrototypeOf||null),li}var ci,il;function xu(){if(il)return ci;il=1;var e=bu();return ci=e.getPrototypeOf||null,ci}var ui,sl;function bg(){if(sl)return ui;sl=1;var e="Function.prototype.bind called on incompatible ",t=Object.prototype.toString,n=Math.max,r="[object Function]",o=function(l,u){for(var c=[],f=0;f<l.length;f+=1)c[f]=l[f];for(var d=0;d<u.length;d+=1)c[d+l.length]=u[d];return c},i=function(l,u){for(var c=[],f=u,d=0;f<l.length;f+=1,d+=1)c[d]=l[f];return c},s=function(a,l){for(var u="",c=0;c<a.length;c+=1)u+=a[c],c+1<a.length&&(u+=l);return u};return ui=function(l){var u=this;if(typeof u!="function"||t.apply(u)!==r)throw new TypeError(e+u);for(var c=i(arguments,1),f,d=function(){if(this instanceof f){var b=u.apply(this,o(c,arguments));return Object(b)===b?b:this}return u.apply(l,o(c,arguments))},p=n(0,u.length-c.length),v=[],w=0;w<p;w++)v[w]="$"+w;if(f=Function("binder","return function ("+s(v,",")+"){ return binder.apply(this,arguments); }")(d),u.prototype){var m=function(){};m.prototype=u.prototype,f.prototype=new m,m.prototype=null}return f},ui}var fi,al;function co(){if(al)return fi;al=1;var e=bg();return fi=Function.prototype.bind||e,fi}var pi,ll;function xs(){return ll||(ll=1,pi=Function.prototype.call),pi}var di,cl;function Eu(){return cl||(cl=1,di=Function.prototype.apply),di}var hi,ul;function wg(){return ul||(ul=1,hi=typeof Reflect<"u"&&Reflect&&Reflect.apply),hi}var gi,fl;function Sg(){if(fl)return gi;fl=1;var e=co(),t=Eu(),n=xs(),r=wg();return gi=r||e.call(n,t),gi}var yi,pl;function Au(){if(pl)return yi;pl=1;var e=co(),t=Tn(),n=xs(),r=Sg();return yi=function(i){if(i.length<1||typeof i[0]!="function")throw new t("a function is required");return r(e,n,i)},yi}var mi,dl;function xg(){if(dl)return mi;dl=1;var e=Au(),t=wu(),n;try{n=[].__proto__===Array.prototype}catch(s){if(!s||typeof s!="object"||!("code"in s)||s.code!=="ERR_PROTO_ACCESS")throw s}var r=!!n&&t&&t(Object.prototype,"__proto__"),o=Object,i=o.getPrototypeOf;return mi=r&&typeof r.get=="function"?e([r.get]):typeof i=="function"?function(a){return i(a==null?a:o(a))}:!1,mi}var vi,hl;function Eg(){if(hl)return vi;hl=1;var e=Su(),t=xu(),n=xg();return vi=e?function(o){return e(o)}:t?function(o){if(!o||typeof o!="object"&&typeof o!="function")throw new TypeError("getProto: not an object");return t(o)}:n?function(o){return n(o)}:null,vi}var bi,gl;function Ag(){if(gl)return bi;gl=1;var e=Function.prototype.call,t=Object.prototype.hasOwnProperty,n=co();return bi=n.call(e,t),bi}var wi,yl;function Es(){if(yl)return wi;yl=1;var e,t=bu(),n=tg(),r=ng(),o=rg(),i=og(),s=ig(),a=Tn(),l=sg(),u=ag(),c=lg(),f=cg(),d=ug(),p=fg(),v=pg(),w=hg(),m=Function,b=function(B){try{return m('"use strict"; return ('+B+").constructor;")()}catch{}},x=wu(),O=yg(),_=function(){throw new a},L=x?function(){try{return arguments.callee,_}catch{try{return x(arguments,"callee").get}catch{return _}}}():_,H=vg()(),M=Eg(),Q=xu(),j=Su(),V=Eu(),ee=xs(),J={},pe=typeof Uint8Array>"u"||!M?e:M(Uint8Array),de={__proto__:null,"%AggregateError%":typeof AggregateError>"u"?e:AggregateError,"%Array%":Array,"%ArrayBuffer%":typeof ArrayBuffer>"u"?e:ArrayBuffer,"%ArrayIteratorPrototype%":H&&M?M([][Symbol.iterator]()):e,"%AsyncFromSyncIteratorPrototype%":e,"%AsyncFunction%":J,"%AsyncGenerator%":J,"%AsyncGeneratorFunction%":J,"%AsyncIteratorPrototype%":J,"%Atomics%":typeof Atomics>"u"?e:Atomics,"%BigInt%":typeof BigInt>"u"?e:BigInt,"%BigInt64Array%":typeof BigInt64Array>"u"?e:BigInt64Array,"%BigUint64Array%":typeof BigUint64Array>"u"?e:BigUint64Array,"%Boolean%":Boolean,"%DataView%":typeof DataView>"u"?e:DataView,"%Date%":Date,"%decodeURI%":decodeURI,"%decodeURIComponent%":decodeURIComponent,"%encodeURI%":encodeURI,"%encodeURIComponent%":encodeURIComponent,"%Error%":n,"%eval%":eval,"%EvalError%":r,"%Float16Array%":typeof Float16Array>"u"?e:Float16Array,"%Float32Array%":typeof Float32Array>"u"?e:Float32Array,"%Float64Array%":typeof Float64Array>"u"?e:Float64Array,"%FinalizationRegistry%":typeof FinalizationRegistry>"u"?e:FinalizationRegistry,"%Function%":m,"%GeneratorFunction%":J,"%Int8Array%":typeof Int8Array>"u"?e:Int8Array,"%Int16Array%":typeof Int16Array>"u"?e:Int16Array,"%Int32Array%":typeof Int32Array>"u"?e:Int32Array,"%isFinite%":isFinite,"%isNaN%":isNaN,"%IteratorPrototype%":H&&M?M(M([][Symbol.iterator]())):e,"%JSON%":typeof JSON=="object"?JSON:e,"%Map%":typeof Map>"u"?e:Map,"%MapIteratorPrototype%":typeof Map>"u"||!H||!M?e:M(new Map()[Symbol.iterator]()),"%Math%":Math,"%Number%":Number,"%Object%":t,"%Object.getOwnPropertyDescriptor%":x,"%parseFloat%":parseFloat,"%parseInt%":parseInt,"%Promise%":typeof Promise>"u"?e:Promise,"%Proxy%":typeof Proxy>"u"?e:Proxy,"%RangeError%":o,"%ReferenceError%":i,"%Reflect%":typeof Reflect>"u"?e:Reflect,"%RegExp%":RegExp,"%Set%":typeof Set>"u"?e:Set,"%SetIteratorPrototype%":typeof Set>"u"||!H||!M?e:M(new Set()[Symbol.iterator]()),"%SharedArrayBuffer%":typeof SharedArrayBuffer>"u"?e:SharedArrayBuffer,"%String%":String,"%StringIteratorPrototype%":H&&M?M(""[Symbol.iterator]()):e,"%Symbol%":H?Symbol:e,"%SyntaxError%":s,"%ThrowTypeError%":L,"%TypedArray%":pe,"%TypeError%":a,"%Uint8Array%":typeof Uint8Array>"u"?e:Uint8Array,"%Uint8ClampedArray%":typeof Uint8ClampedArray>"u"?e:Uint8ClampedArray,"%Uint16Array%":typeof Uint16Array>"u"?e:Uint16Array,"%Uint32Array%":typeof Uint32Array>"u"?e:Uint32Array,"%URIError%":l,"%WeakMap%":typeof WeakMap>"u"?e:WeakMap,"%WeakRef%":typeof WeakRef>"u"?e:WeakRef,"%WeakSet%":typeof WeakSet>"u"?e:WeakSet,"%Function.prototype.call%":ee,"%Function.prototype.apply%":V,"%Object.defineProperty%":O,"%Object.getPrototypeOf%":Q,"%Math.abs%":u,"%Math.floor%":c,"%Math.max%":f,"%Math.min%":d,"%Math.pow%":p,"%Math.round%":v,"%Math.sign%":w,"%Reflect.getPrototypeOf%":j};if(M)try{null.error}catch(B){var Ne=M(M(B));de["%Error.prototype%"]=Ne}var ue=function B(F){var X;if(F==="%AsyncFunction%")X=b("async function () {}");else if(F==="%GeneratorFunction%")X=b("function* () {}");else if(F==="%AsyncGeneratorFunction%")X=b("async function* () {}");else if(F==="%AsyncGenerator%"){var h=B("%AsyncGeneratorFunction%");h&&(X=h.prototype)}else if(F==="%AsyncIteratorPrototype%"){var g=B("%AsyncGenerator%");g&&M&&(X=M(g.prototype))}return de[F]=X,X},te={__proto__:null,"%ArrayBufferPrototype%":["ArrayBuffer","prototype"],"%ArrayPrototype%":["Array","prototype"],"%ArrayProto_entries%":["Array","prototype","entries"],"%ArrayProto_forEach%":["Array","prototype","forEach"],"%ArrayProto_keys%":["Array","prototype","keys"],"%ArrayProto_values%":["Array","prototype","values"],"%AsyncFunctionPrototype%":["AsyncFunction","prototype"],"%AsyncGenerator%":["AsyncGeneratorFunction","prototype"],"%AsyncGeneratorPrototype%":["AsyncGeneratorFunction","prototype","prototype"],"%BooleanPrototype%":["Boolean","prototype"],"%DataViewPrototype%":["DataView","prototype"],"%DatePrototype%":["Date","prototype"],"%ErrorPrototype%":["Error","prototype"],"%EvalErrorPrototype%":["EvalError","prototype"],"%Float32ArrayPrototype%":["Float32Array","prototype"],"%Float64ArrayPrototype%":["Float64Array","prototype"],"%FunctionPrototype%":["Function","prototype"],"%Generator%":["GeneratorFunction","prototype"],"%GeneratorPrototype%":["GeneratorFunction","prototype","prototype"],"%Int8ArrayPrototype%":["Int8Array","prototype"],"%Int16ArrayPrototype%":["Int16Array","prototype"],"%Int32ArrayPrototype%":["Int32Array","prototype"],"%JSONParse%":["JSON","parse"],"%JSONStringify%":["JSON","stringify"],"%MapPrototype%":["Map","prototype"],"%NumberPrototype%":["Number","prototype"],"%ObjectPrototype%":["Object","prototype"],"%ObjProto_toString%":["Object","prototype","toString"],"%ObjProto_valueOf%":["Object","prototype","valueOf"],"%PromisePrototype%":["Promise","prototype"],"%PromiseProto_then%":["Promise","prototype","then"],"%Promise_all%":["Promise","all"],"%Promise_reject%":["Promise","reject"],"%Promise_resolve%":["Promise","resolve"],"%RangeErrorPrototype%":["RangeError","prototype"],"%ReferenceErrorPrototype%":["ReferenceError","prototype"],"%RegExpPrototype%":["RegExp","prototype"],"%SetPrototype%":["Set","prototype"],"%SharedArrayBufferPrototype%":["SharedArrayBuffer","prototype"],"%StringPrototype%":["String","prototype"],"%SymbolPrototype%":["Symbol","prototype"],"%SyntaxErrorPrototype%":["SyntaxError","prototype"],"%TypedArrayPrototype%":["TypedArray","prototype"],"%TypeErrorPrototype%":["TypeError","prototype"],"%Uint8ArrayPrototype%":["Uint8Array","prototype"],"%Uint8ClampedArrayPrototype%":["Uint8ClampedArray","prototype"],"%Uint16ArrayPrototype%":["Uint16Array","prototype"],"%Uint32ArrayPrototype%":["Uint32Array","prototype"],"%URIErrorPrototype%":["URIError","prototype"],"%WeakMapPrototype%":["WeakMap","prototype"],"%WeakSetPrototype%":["WeakSet","prototype"]},K=co(),ve=Ag(),Fe=K.call(ee,Array.prototype.concat),xe=K.call(V,Array.prototype.splice),Ee=K.call(ee,String.prototype.replace),je=K.call(ee,String.prototype.slice),Ge=K.call(ee,RegExp.prototype.exec),Xe=/[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g,Ae=/\\(\\)?/g,N=function(F){var X=je(F,0,1),h=je(F,-1);if(X==="%"&&h!=="%")throw new s("invalid intrinsic syntax, expected closing `%`");if(h==="%"&&X!=="%")throw new s("invalid intrinsic syntax, expected opening `%`");var g=[];return Ee(F,Xe,function(E,D,y,S){g[g.length]=y?Ee(S,Ae,"$1"):D||E}),g},z=function(F,X){var h=F,g;if(ve(te,h)&&(g=te[h],h="%"+g[0]+"%"),ve(de,h)){var E=de[h];if(E===J&&(E=ue(h)),typeof E>"u"&&!X)throw new a("intrinsic "+F+" exists, but is not available. Please file an issue!");return{alias:g,name:h,value:E}}throw new s("intrinsic "+F+" does not exist!")};return wi=function(F,X){if(typeof F!="string"||F.length===0)throw new a("intrinsic name must be a non-empty string");if(arguments.length>1&&typeof X!="boolean")throw new a('"allowMissing" argument must be a boolean');if(Ge(/^%?[^%]*%?$/,F)===null)throw new s("`%` may not be present anywhere but at the beginning and end of the intrinsic name");var h=N(F),g=h.length>0?h[0]:"",E=z("%"+g+"%",X),D=E.name,y=E.value,S=!1,T=E.alias;T&&(g=T[0],xe(h,Fe([0,1],T)));for(var R=1,P=!0;R<h.length;R+=1){var k=h[R],q=je(k,0,1),$=je(k,-1);if((q==='"'||q==="'"||q==="`"||$==='"'||$==="'"||$==="`")&&q!==$)throw new s("property names with quotes must have matching quotes");if((k==="constructor"||!P)&&(S=!0),g+="."+k,D="%"+g+"%",ve(de,D))y=de[D];else if(y!=null){if(!(k in y)){if(!X)throw new a("base intrinsic for "+F+" exists, but the property is not available.");return}if(x&&R+1>=h.length){var U=x(y,k);P=!!U,P&&"get"in U&&!("originalValue"in U.get)?y=U.get:y=y[k]}else P=ve(y,k),y=y[k];P&&!S&&(de[D]=y)}}return y},wi}var Si,ml;function Ru(){if(ml)return Si;ml=1;var e=Es(),t=Au(),n=t([e("%String.prototype.indexOf%")]);return Si=function(o,i){var s=e(o,!!i);return typeof s=="function"&&n(o,".prototype.")>-1?t([s]):s},Si}var xi,vl;function _u(){if(vl)return xi;vl=1;var e=Es(),t=Ru(),n=lo(),r=Tn(),o=e("%Map%",!0),i=t("Map.prototype.get",!0),s=t("Map.prototype.set",!0),a=t("Map.prototype.has",!0),l=t("Map.prototype.delete",!0),u=t("Map.prototype.size",!0);return xi=!!o&&function(){var f,d={assert:function(p){if(!d.has(p))throw new r("Side channel does not contain "+n(p))},delete:function(p){if(f){var v=l(f,p);return u(f)===0&&(f=void 0),v}return!1},get:function(p){if(f)return i(f,p)},has:function(p){return f?a(f,p):!1},set:function(p,v){f||(f=new o),s(f,p,v)}};return d},xi}var Ei,bl;function Rg(){if(bl)return Ei;bl=1;var e=Es(),t=Ru(),n=lo(),r=_u(),o=Tn(),i=e("%WeakMap%",!0),s=t("WeakMap.prototype.get",!0),a=t("WeakMap.prototype.set",!0),l=t("WeakMap.prototype.has",!0),u=t("WeakMap.prototype.delete",!0);return Ei=i?function(){var f,d,p={assert:function(v){if(!p.has(v))throw new o("Side channel does not contain "+n(v))},delete:function(v){if(i&&v&&(typeof v=="object"||typeof v=="function")){if(f)return u(f,v)}else if(r&&d)return d.delete(v);return!1},get:function(v){return i&&v&&(typeof v=="object"||typeof v=="function")&&f?s(f,v):d&&d.get(v)},has:function(v){return i&&v&&(typeof v=="object"||typeof v=="function")&&f?l(f,v):!!d&&d.has(v)},set:function(v,w){i&&v&&(typeof v=="object"||typeof v=="function")?(f||(f=new i),a(f,v,w)):r&&(d||(d=r()),d.set(v,w))}};return p}:r,Ei}var Ai,wl;function _g(){if(wl)return Ai;wl=1;var e=Tn(),t=lo(),n=eg(),r=_u(),o=Rg(),i=o||r||n;return Ai=function(){var a,l={assert:function(u){if(!l.has(u))throw new e("Side channel does not contain "+t(u))},delete:function(u){return!!a&&a.delete(u)},get:function(u){return a&&a.get(u)},has:function(u){return!!a&&a.has(u)},set:function(u,c){a||(a=i()),a.set(u,c)}};return l},Ai}var Ri,Sl;function As(){if(Sl)return Ri;Sl=1;var e=String.prototype.replace,t=/%20/g,n={RFC1738:"RFC1738",RFC3986:"RFC3986"};return Ri={default:n.RFC3986,formatters:{RFC1738:function(r){return e.call(r,t,"+")},RFC3986:function(r){return String(r)}},RFC1738:n.RFC1738,RFC3986:n.RFC3986},Ri}var _i,xl;function ku(){if(xl)return _i;xl=1;var e=As(),t=Object.prototype.hasOwnProperty,n=Array.isArray,r=function(){for(var m=[],b=0;b<256;++b)m.push("%"+((b<16?"0":"")+b.toString(16)).toUpperCase());return m}(),o=function(b){for(;b.length>1;){var x=b.pop(),O=x.obj[x.prop];if(n(O)){for(var _=[],L=0;L<O.length;++L)typeof O[L]<"u"&&_.push(O[L]);x.obj[x.prop]=_}}},i=function(b,x){for(var O=x&&x.plainObjects?{__proto__:null}:{},_=0;_<b.length;++_)typeof b[_]<"u"&&(O[_]=b[_]);return O},s=function m(b,x,O){if(!x)return b;if(typeof x!="object"&&typeof x!="function"){if(n(b))b.push(x);else if(b&&typeof b=="object")(O&&(O.plainObjects||O.allowPrototypes)||!t.call(Object.prototype,x))&&(b[x]=!0);else return[b,x];return b}if(!b||typeof b!="object")return[b].concat(x);var _=b;return n(b)&&!n(x)&&(_=i(b,O)),n(b)&&n(x)?(x.forEach(function(L,H){if(t.call(b,H)){var M=b[H];M&&typeof M=="object"&&L&&typeof L=="object"?b[H]=m(M,L,O):b.push(L)}else b[H]=L}),b):Object.keys(x).reduce(function(L,H){var M=x[H];return t.call(L,H)?L[H]=m(L[H],M,O):L[H]=M,L},_)},a=function(b,x){return Object.keys(x).reduce(function(O,_){return O[_]=x[_],O},b)},l=function(m,b,x){var O=m.replace(/\+/g," ");if(x==="iso-8859-1")return O.replace(/%[0-9a-f]{2}/gi,unescape);try{return decodeURIComponent(O)}catch{return O}},u=1024,c=function(b,x,O,_,L){if(b.length===0)return b;var H=b;if(typeof b=="symbol"?H=Symbol.prototype.toString.call(b):typeof b!="string"&&(H=String(b)),O==="iso-8859-1")return escape(H).replace(/%u[0-9a-f]{4}/gi,function(pe){return"%26%23"+parseInt(pe.slice(2),16)+"%3B"});for(var M="",Q=0;Q<H.length;Q+=u){for(var j=H.length>=u?H.slice(Q,Q+u):H,V=[],ee=0;ee<j.length;++ee){var J=j.charCodeAt(ee);if(J===45||J===46||J===95||J===126||J>=48&&J<=57||J>=65&&J<=90||J>=97&&J<=122||L===e.RFC1738&&(J===40||J===41)){V[V.length]=j.charAt(ee);continue}if(J<128){V[V.length]=r[J];continue}if(J<2048){V[V.length]=r[192|J>>6]+r[128|J&63];continue}if(J<55296||J>=57344){V[V.length]=r[224|J>>12]+r[128|J>>6&63]+r[128|J&63];continue}ee+=1,J=65536+((J&1023)<<10|j.charCodeAt(ee)&1023),V[V.length]=r[240|J>>18]+r[128|J>>12&63]+r[128|J>>6&63]+r[128|J&63]}M+=V.join("")}return M},f=function(b){for(var x=[{obj:{o:b},prop:"o"}],O=[],_=0;_<x.length;++_)for(var L=x[_],H=L.obj[L.prop],M=Object.keys(H),Q=0;Q<M.length;++Q){var j=M[Q],V=H[j];typeof V=="object"&&V!==null&&O.indexOf(V)===-1&&(x.push({obj:H,prop:j}),O.push(V))}return o(x),b},d=function(b){return Object.prototype.toString.call(b)==="[object RegExp]"},p=function(b){return!b||typeof b!="object"?!1:!!(b.constructor&&b.constructor.isBuffer&&b.constructor.isBuffer(b))},v=function(b,x){return[].concat(b,x)},w=function(b,x){if(n(b)){for(var O=[],_=0;_<b.length;_+=1)O.push(x(b[_]));return O}return x(b)};return _i={arrayToObject:i,assign:a,combine:v,compact:f,decode:l,encode:c,isBuffer:p,isRegExp:d,maybeMap:w,merge:s},_i}var ki,El;function kg(){if(El)return ki;El=1;var e=_g(),t=ku(),n=As(),r=Object.prototype.hasOwnProperty,o={brackets:function(m){return m+"[]"},comma:"comma",indices:function(m,b){return m+"["+b+"]"},repeat:function(m){return m}},i=Array.isArray,s=Array.prototype.push,a=function(w,m){s.apply(w,i(m)?m:[m])},l=Date.prototype.toISOString,u=n.default,c={addQueryPrefix:!1,allowDots:!1,allowEmptyArrays:!1,arrayFormat:"indices",charset:"utf-8",charsetSentinel:!1,commaRoundTrip:!1,delimiter:"&",encode:!0,encodeDotInKeys:!1,encoder:t.encode,encodeValuesOnly:!1,filter:void 0,format:u,formatter:n.formatters[u],indices:!1,serializeDate:function(m){return l.call(m)},skipNulls:!1,strictNullHandling:!1},f=function(m){return typeof m=="string"||typeof m=="number"||typeof m=="boolean"||typeof m=="symbol"||typeof m=="bigint"},d={},p=function w(m,b,x,O,_,L,H,M,Q,j,V,ee,J,pe,de,Ne,ue,te){for(var K=m,ve=te,Fe=0,xe=!1;(ve=ve.get(d))!==void 0&&!xe;){var Ee=ve.get(m);if(Fe+=1,typeof Ee<"u"){if(Ee===Fe)throw new RangeError("Cyclic object value");xe=!0}typeof ve.get(d)>"u"&&(Fe=0)}if(typeof j=="function"?K=j(b,K):K instanceof Date?K=J(K):x==="comma"&&i(K)&&(K=t.maybeMap(K,function(D){return D instanceof Date?J(D):D})),K===null){if(L)return Q&&!Ne?Q(b,c.encoder,ue,"key",pe):b;K=""}if(f(K)||t.isBuffer(K)){if(Q){var je=Ne?b:Q(b,c.encoder,ue,"key",pe);return[de(je)+"="+de(Q(K,c.encoder,ue,"value",pe))]}return[de(b)+"="+de(String(K))]}var Ge=[];if(typeof K>"u")return Ge;var Xe;if(x==="comma"&&i(K))Ne&&Q&&(K=t.maybeMap(K,Q)),Xe=[{value:K.length>0?K.join(",")||null:void 0}];else if(i(j))Xe=j;else{var Ae=Object.keys(K);Xe=V?Ae.sort(V):Ae}var N=M?String(b).replace(/\./g,"%2E"):String(b),z=O&&i(K)&&K.length===1?N+"[]":N;if(_&&i(K)&&K.length===0)return z+"[]";for(var B=0;B<Xe.length;++B){var F=Xe[B],X=typeof F=="object"&&F&&typeof F.value<"u"?F.value:K[F];if(!(H&&X===null)){var h=ee&&M?String(F).replace(/\./g,"%2E"):String(F),g=i(K)?typeof x=="function"?x(z,h):z:z+(ee?"."+h:"["+h+"]");te.set(m,Fe);var E=e();E.set(d,te),a(Ge,w(X,g,x,O,_,L,H,M,x==="comma"&&Ne&&i(K)?null:Q,j,V,ee,J,pe,de,Ne,ue,E))}}return Ge},v=function(m){if(!m)return c;if(typeof m.allowEmptyArrays<"u"&&typeof m.allowEmptyArrays!="boolean")throw new TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(typeof m.encodeDotInKeys<"u"&&typeof m.encodeDotInKeys!="boolean")throw new TypeError("`encodeDotInKeys` option can only be `true` or `false`, when provided");if(m.encoder!==null&&typeof m.encoder<"u"&&typeof m.encoder!="function")throw new TypeError("Encoder has to be a function.");var b=m.charset||c.charset;if(typeof m.charset<"u"&&m.charset!=="utf-8"&&m.charset!=="iso-8859-1")throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var x=n.default;if(typeof m.format<"u"){if(!r.call(n.formatters,m.format))throw new TypeError("Unknown format option provided.");x=m.format}var O=n.formatters[x],_=c.filter;(typeof m.filter=="function"||i(m.filter))&&(_=m.filter);var L;if(m.arrayFormat in o?L=m.arrayFormat:"indices"in m?L=m.indices?"indices":"repeat":L=c.arrayFormat,"commaRoundTrip"in m&&typeof m.commaRoundTrip!="boolean")throw new TypeError("`commaRoundTrip` must be a boolean, or absent");var H=typeof m.allowDots>"u"?m.encodeDotInKeys===!0?!0:c.allowDots:!!m.allowDots;return{addQueryPrefix:typeof m.addQueryPrefix=="boolean"?m.addQueryPrefix:c.addQueryPrefix,allowDots:H,allowEmptyArrays:typeof m.allowEmptyArrays=="boolean"?!!m.allowEmptyArrays:c.allowEmptyArrays,arrayFormat:L,charset:b,charsetSentinel:typeof m.charsetSentinel=="boolean"?m.charsetSentinel:c.charsetSentinel,commaRoundTrip:!!m.commaRoundTrip,delimiter:typeof m.delimiter>"u"?c.delimiter:m.delimiter,encode:typeof m.encode=="boolean"?m.encode:c.encode,encodeDotInKeys:typeof m.encodeDotInKeys=="boolean"?m.encodeDotInKeys:c.encodeDotInKeys,encoder:typeof m.encoder=="function"?m.encoder:c.encoder,encodeValuesOnly:typeof m.encodeValuesOnly=="boolean"?m.encodeValuesOnly:c.encodeValuesOnly,filter:_,format:x,formatter:O,serializeDate:typeof m.serializeDate=="function"?m.serializeDate:c.serializeDate,skipNulls:typeof m.skipNulls=="boolean"?m.skipNulls:c.skipNulls,sort:typeof m.sort=="function"?m.sort:null,strictNullHandling:typeof m.strictNullHandling=="boolean"?m.strictNullHandling:c.strictNullHandling}};return ki=function(w,m){var b=w,x=v(m),O,_;typeof x.filter=="function"?(_=x.filter,b=_("",b)):i(x.filter)&&(_=x.filter,O=_);var L=[];if(typeof b!="object"||b===null)return"";var H=o[x.arrayFormat],M=H==="comma"&&x.commaRoundTrip;O||(O=Object.keys(b)),x.sort&&O.sort(x.sort);for(var Q=e(),j=0;j<O.length;++j){var V=O[j],ee=b[V];x.skipNulls&&ee===null||a(L,p(ee,V,H,M,x.allowEmptyArrays,x.strictNullHandling,x.skipNulls,x.encodeDotInKeys,x.encode?x.encoder:null,x.filter,x.sort,x.allowDots,x.serializeDate,x.format,x.formatter,x.encodeValuesOnly,x.charset,Q))}var J=L.join(x.delimiter),pe=x.addQueryPrefix===!0?"?":"";return x.charsetSentinel&&(x.charset==="iso-8859-1"?pe+="utf8=%26%2310003%3B&":pe+="utf8=%E2%9C%93&"),J.length>0?pe+J:""},ki}var Oi,Al;function Og(){if(Al)return Oi;Al=1;var e=ku(),t=Object.prototype.hasOwnProperty,n=Array.isArray,r={allowDots:!1,allowEmptyArrays:!1,allowPrototypes:!1,allowSparse:!1,arrayLimit:20,charset:"utf-8",charsetSentinel:!1,comma:!1,decodeDotInKeys:!1,decoder:e.decode,delimiter:"&",depth:5,duplicates:"combine",ignoreQueryPrefix:!1,interpretNumericEntities:!1,parameterLimit:1e3,parseArrays:!0,plainObjects:!1,strictDepth:!1,strictNullHandling:!1,throwOnLimitExceeded:!1},o=function(d){return d.replace(/&#(\d+);/g,function(p,v){return String.fromCharCode(parseInt(v,10))})},i=function(d,p,v){if(d&&typeof d=="string"&&p.comma&&d.indexOf(",")>-1)return d.split(",");if(p.throwOnLimitExceeded&&v>=p.arrayLimit)throw new RangeError("Array limit exceeded. Only "+p.arrayLimit+" element"+(p.arrayLimit===1?"":"s")+" allowed in an array.");return d},s="utf8=%26%2310003%3B",a="utf8=%E2%9C%93",l=function(p,v){var w={__proto__:null},m=v.ignoreQueryPrefix?p.replace(/^\?/,""):p;m=m.replace(/%5B/gi,"[").replace(/%5D/gi,"]");var b=v.parameterLimit===1/0?void 0:v.parameterLimit,x=m.split(v.delimiter,v.throwOnLimitExceeded?b+1:b);if(v.throwOnLimitExceeded&&x.length>b)throw new RangeError("Parameter limit exceeded. Only "+b+" parameter"+(b===1?"":"s")+" allowed.");var O=-1,_,L=v.charset;if(v.charsetSentinel)for(_=0;_<x.length;++_)x[_].indexOf("utf8=")===0&&(x[_]===a?L="utf-8":x[_]===s&&(L="iso-8859-1"),O=_,_=x.length);for(_=0;_<x.length;++_)if(_!==O){var H=x[_],M=H.indexOf("]="),Q=M===-1?H.indexOf("="):M+1,j,V;Q===-1?(j=v.decoder(H,r.decoder,L,"key"),V=v.strictNullHandling?null:""):(j=v.decoder(H.slice(0,Q),r.decoder,L,"key"),V=e.maybeMap(i(H.slice(Q+1),v,n(w[j])?w[j].length:0),function(J){return v.decoder(J,r.decoder,L,"value")})),V&&v.interpretNumericEntities&&L==="iso-8859-1"&&(V=o(String(V))),H.indexOf("[]=")>-1&&(V=n(V)?[V]:V);var ee=t.call(w,j);ee&&v.duplicates==="combine"?w[j]=e.combine(w[j],V):(!ee||v.duplicates==="last")&&(w[j]=V)}return w},u=function(d,p,v,w){var m=0;if(d.length>0&&d[d.length-1]==="[]"){var b=d.slice(0,-1).join("");m=Array.isArray(p)&&p[b]?p[b].length:0}for(var x=w?p:i(p,v,m),O=d.length-1;O>=0;--O){var _,L=d[O];if(L==="[]"&&v.parseArrays)_=v.allowEmptyArrays&&(x===""||v.strictNullHandling&&x===null)?[]:e.combine([],x);else{_=v.plainObjects?{__proto__:null}:{};var H=L.charAt(0)==="["&&L.charAt(L.length-1)==="]"?L.slice(1,-1):L,M=v.decodeDotInKeys?H.replace(/%2E/g,"."):H,Q=parseInt(M,10);!v.parseArrays&&M===""?_={0:x}:!isNaN(Q)&&L!==M&&String(Q)===M&&Q>=0&&v.parseArrays&&Q<=v.arrayLimit?(_=[],_[Q]=x):M!=="__proto__"&&(_[M]=x)}x=_}return x},c=function(p,v,w,m){if(p){var b=w.allowDots?p.replace(/\.([^.[]+)/g,"[$1]"):p,x=/(\[[^[\]]*])/,O=/(\[[^[\]]*])/g,_=w.depth>0&&x.exec(b),L=_?b.slice(0,_.index):b,H=[];if(L){if(!w.plainObjects&&t.call(Object.prototype,L)&&!w.allowPrototypes)return;H.push(L)}for(var M=0;w.depth>0&&(_=O.exec(b))!==null&&M<w.depth;){if(M+=1,!w.plainObjects&&t.call(Object.prototype,_[1].slice(1,-1))&&!w.allowPrototypes)return;H.push(_[1])}if(_){if(w.strictDepth===!0)throw new RangeError("Input depth exceeded depth option of "+w.depth+" and strictDepth is true");H.push("["+b.slice(_.index)+"]")}return u(H,v,w,m)}},f=function(p){if(!p)return r;if(typeof p.allowEmptyArrays<"u"&&typeof p.allowEmptyArrays!="boolean")throw new TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(typeof p.decodeDotInKeys<"u"&&typeof p.decodeDotInKeys!="boolean")throw new TypeError("`decodeDotInKeys` option can only be `true` or `false`, when provided");if(p.decoder!==null&&typeof p.decoder<"u"&&typeof p.decoder!="function")throw new TypeError("Decoder has to be a function.");if(typeof p.charset<"u"&&p.charset!=="utf-8"&&p.charset!=="iso-8859-1")throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");if(typeof p.throwOnLimitExceeded<"u"&&typeof p.throwOnLimitExceeded!="boolean")throw new TypeError("`throwOnLimitExceeded` option must be a boolean");var v=typeof p.charset>"u"?r.charset:p.charset,w=typeof p.duplicates>"u"?r.duplicates:p.duplicates;if(w!=="combine"&&w!=="first"&&w!=="last")throw new TypeError("The duplicates option must be either combine, first, or last");var m=typeof p.allowDots>"u"?p.decodeDotInKeys===!0?!0:r.allowDots:!!p.allowDots;return{allowDots:m,allowEmptyArrays:typeof p.allowEmptyArrays=="boolean"?!!p.allowEmptyArrays:r.allowEmptyArrays,allowPrototypes:typeof p.allowPrototypes=="boolean"?p.allowPrototypes:r.allowPrototypes,allowSparse:typeof p.allowSparse=="boolean"?p.allowSparse:r.allowSparse,arrayLimit:typeof p.arrayLimit=="number"?p.arrayLimit:r.arrayLimit,charset:v,charsetSentinel:typeof p.charsetSentinel=="boolean"?p.charsetSentinel:r.charsetSentinel,comma:typeof p.comma=="boolean"?p.comma:r.comma,decodeDotInKeys:typeof p.decodeDotInKeys=="boolean"?p.decodeDotInKeys:r.decodeDotInKeys,decoder:typeof p.decoder=="function"?p.decoder:r.decoder,delimiter:typeof p.delimiter=="string"||e.isRegExp(p.delimiter)?p.delimiter:r.delimiter,depth:typeof p.depth=="number"||p.depth===!1?+p.depth:r.depth,duplicates:w,ignoreQueryPrefix:p.ignoreQueryPrefix===!0,interpretNumericEntities:typeof p.interpretNumericEntities=="boolean"?p.interpretNumericEntities:r.interpretNumericEntities,parameterLimit:typeof p.parameterLimit=="number"?p.parameterLimit:r.parameterLimit,parseArrays:p.parseArrays!==!1,plainObjects:typeof p.plainObjects=="boolean"?p.plainObjects:r.plainObjects,strictDepth:typeof p.strictDepth=="boolean"?!!p.strictDepth:r.strictDepth,strictNullHandling:typeof p.strictNullHandling=="boolean"?p.strictNullHandling:r.strictNullHandling,throwOnLimitExceeded:typeof p.throwOnLimitExceeded=="boolean"?p.throwOnLimitExceeded:!1}};return Oi=function(d,p){var v=f(p);if(d===""||d===null||typeof d>"u")return v.plainObjects?{__proto__:null}:{};for(var w=typeof d=="string"?l(d,v):d,m=v.plainObjects?{__proto__:null}:{},b=Object.keys(w),x=0;x<b.length;++x){var O=b[x],_=c(O,w[O],v,typeof d=="string");m=e.merge(m,_,v)}return v.allowSparse===!0?m:e.compact(m)},Oi}var Ci,Rl;function Cg(){if(Rl)return Ci;Rl=1;var e=kg(),t=Og(),n=As();return Ci={formats:n,parse:t,stringify:e},Ci}var Tg=Cg();function Ou(e,t){return function(){return e.apply(t,arguments)}}const{toString:Pg}=Object.prototype,{getPrototypeOf:Rs}=Object,uo=(e=>t=>{const n=Pg.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),kt=e=>(e=e.toLowerCase(),t=>uo(t)===e),fo=e=>t=>typeof t===e,{isArray:Pn}=Array,lr=fo("undefined");function Ig(e){return e!==null&&!lr(e)&&e.constructor!==null&&!lr(e.constructor)&&vt(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const Cu=kt("ArrayBuffer");function Dg(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&Cu(e.buffer),t}const Ng=fo("string"),vt=fo("function"),Tu=fo("number"),po=e=>e!==null&&typeof e=="object",Lg=e=>e===!0||e===!1,_r=e=>{if(uo(e)!=="object")return!1;const t=Rs(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)},$g=kt("Date"),Fg=kt("File"),Mg=kt("Blob"),Bg=kt("FileList"),qg=e=>po(e)&&vt(e.pipe),Ug=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||vt(e.append)&&((t=uo(e))==="formdata"||t==="object"&&vt(e.toString)&&e.toString()==="[object FormData]"))},Hg=kt("URLSearchParams"),[jg,zg,Wg,Kg]=["ReadableStream","Request","Response","Headers"].map(kt),Vg=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function pr(e,t,{allOwnKeys:n=!1}={}){if(e===null||typeof e>"u")return;let r,o;if(typeof e!="object"&&(e=[e]),Pn(e))for(r=0,o=e.length;r<o;r++)t.call(null,e[r],r,e);else{const i=n?Object.getOwnPropertyNames(e):Object.keys(e),s=i.length;let a;for(r=0;r<s;r++)a=i[r],t.call(null,e[a],a,e)}}function Pu(e,t){t=t.toLowerCase();const n=Object.keys(e);let r=n.length,o;for(;r-- >0;)if(o=n[r],t===o.toLowerCase())return o;return null}const rn=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,Iu=e=>!lr(e)&&e!==rn;function Qi(){const{caseless:e}=Iu(this)&&this||{},t={},n=(r,o)=>{const i=e&&Pu(t,o)||o;_r(t[i])&&_r(r)?t[i]=Qi(t[i],r):_r(r)?t[i]=Qi({},r):Pn(r)?t[i]=r.slice():t[i]=r};for(let r=0,o=arguments.length;r<o;r++)arguments[r]&&pr(arguments[r],n);return t}const Gg=(e,t,n,{allOwnKeys:r}={})=>(pr(t,(o,i)=>{n&&vt(o)?e[i]=Ou(o,n):e[i]=o},{allOwnKeys:r}),e),Qg=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),Jg=(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},Zg=(e,t,n,r)=>{let o,i,s;const a={};if(t=t||{},e==null)return t;do{for(o=Object.getOwnPropertyNames(e),i=o.length;i-- >0;)s=o[i],(!r||r(s,e,t))&&!a[s]&&(t[s]=e[s],a[s]=!0);e=n!==!1&&Rs(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},Xg=(e,t,n)=>{e=String(e),(n===void 0||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return r!==-1&&r===n},Yg=e=>{if(!e)return null;if(Pn(e))return e;let t=e.length;if(!Tu(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},ey=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&Rs(Uint8Array)),ty=(e,t)=>{const r=(e&&e[Symbol.iterator]).call(e);let o;for(;(o=r.next())&&!o.done;){const i=o.value;t.call(e,i[0],i[1])}},ny=(e,t)=>{let n;const r=[];for(;(n=e.exec(t))!==null;)r.push(n);return r},ry=kt("HTMLFormElement"),oy=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(n,r,o){return r.toUpperCase()+o}),_l=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),iy=kt("RegExp"),Du=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};pr(n,(o,i)=>{let s;(s=t(o,i,e))!==!1&&(r[i]=s||o)}),Object.defineProperties(e,r)},sy=e=>{Du(e,(t,n)=>{if(vt(e)&&["arguments","caller","callee"].indexOf(n)!==-1)return!1;const r=e[n];if(vt(r)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")})}})},ay=(e,t)=>{const n={},r=o=>{o.forEach(i=>{n[i]=!0})};return Pn(e)?r(e):r(String(e).split(t)),n},ly=()=>{},cy=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function uy(e){return!!(e&&vt(e.append)&&e[Symbol.toStringTag]==="FormData"&&e[Symbol.iterator])}const fy=e=>{const t=new Array(10),n=(r,o)=>{if(po(r)){if(t.indexOf(r)>=0)return;if(!("toJSON"in r)){t[o]=r;const i=Pn(r)?[]:{};return pr(r,(s,a)=>{const l=n(s,o+1);!lr(l)&&(i[a]=l)}),t[o]=void 0,i}}return r};return n(e,0)},py=kt("AsyncFunction"),dy=e=>e&&(po(e)||vt(e))&&vt(e.then)&&vt(e.catch),Nu=((e,t)=>e?setImmediate:t?((n,r)=>(rn.addEventListener("message",({source:o,data:i})=>{o===rn&&i===n&&r.length&&r.shift()()},!1),o=>{r.push(o),rn.postMessage(n,"*")}))(`axios@${Math.random()}`,[]):n=>setTimeout(n))(typeof setImmediate=="function",vt(rn.postMessage)),hy=typeof queueMicrotask<"u"?queueMicrotask.bind(rn):typeof process<"u"&&process.nextTick||Nu,I={isArray:Pn,isArrayBuffer:Cu,isBuffer:Ig,isFormData:Ug,isArrayBufferView:Dg,isString:Ng,isNumber:Tu,isBoolean:Lg,isObject:po,isPlainObject:_r,isReadableStream:jg,isRequest:zg,isResponse:Wg,isHeaders:Kg,isUndefined:lr,isDate:$g,isFile:Fg,isBlob:Mg,isRegExp:iy,isFunction:vt,isStream:qg,isURLSearchParams:Hg,isTypedArray:ey,isFileList:Bg,forEach:pr,merge:Qi,extend:Gg,trim:Vg,stripBOM:Qg,inherits:Jg,toFlatObject:Zg,kindOf:uo,kindOfTest:kt,endsWith:Xg,toArray:Yg,forEachEntry:ty,matchAll:ny,isHTMLForm:ry,hasOwnProperty:_l,hasOwnProp:_l,reduceDescriptors:Du,freezeMethods:sy,toObjectSet:ay,toCamelCase:oy,noop:ly,toFiniteNumber:cy,findKey:Pu,global:rn,isContextDefined:Iu,isSpecCompliantForm:uy,toJSONObject:fy,isAsyncFn:py,isThenable:dy,setImmediate:Nu,asap:hy};function se(e,t,n,r,o){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),o&&(this.response=o,this.status=o.status?o.status:null)}I.inherits(se,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:I.toJSONObject(this.config),code:this.code,status:this.status}}});const Lu=se.prototype,$u={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{$u[e]={value:e}});Object.defineProperties(se,$u);Object.defineProperty(Lu,"isAxiosError",{value:!0});se.from=(e,t,n,r,o,i)=>{const s=Object.create(Lu);return I.toFlatObject(e,s,function(l){return l!==Error.prototype},a=>a!=="isAxiosError"),se.call(s,e.message,t,n,r,o),s.cause=e,s.name=e.name,i&&Object.assign(s,i),s};const gy=null;function Ji(e){return I.isPlainObject(e)||I.isArray(e)}function Fu(e){return I.endsWith(e,"[]")?e.slice(0,-2):e}function kl(e,t,n){return e?e.concat(t).map(function(o,i){return o=Fu(o),!n&&i?"["+o+"]":o}).join(n?".":""):t}function yy(e){return I.isArray(e)&&!e.some(Ji)}const my=I.toFlatObject(I,{},null,function(t){return/^is[A-Z]/.test(t)});function ho(e,t,n){if(!I.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,n=I.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(w,m){return!I.isUndefined(m[w])});const r=n.metaTokens,o=n.visitor||c,i=n.dots,s=n.indexes,l=(n.Blob||typeof Blob<"u"&&Blob)&&I.isSpecCompliantForm(t);if(!I.isFunction(o))throw new TypeError("visitor must be a function");function u(v){if(v===null)return"";if(I.isDate(v))return v.toISOString();if(!l&&I.isBlob(v))throw new se("Blob is not supported. Use a Buffer instead.");return I.isArrayBuffer(v)||I.isTypedArray(v)?l&&typeof Blob=="function"?new Blob([v]):Buffer.from(v):v}function c(v,w,m){let b=v;if(v&&!m&&typeof v=="object"){if(I.endsWith(w,"{}"))w=r?w:w.slice(0,-2),v=JSON.stringify(v);else if(I.isArray(v)&&yy(v)||(I.isFileList(v)||I.endsWith(w,"[]"))&&(b=I.toArray(v)))return w=Fu(w),b.forEach(function(O,_){!(I.isUndefined(O)||O===null)&&t.append(s===!0?kl([w],_,i):s===null?w:w+"[]",u(O))}),!1}return Ji(v)?!0:(t.append(kl(m,w,i),u(v)),!1)}const f=[],d=Object.assign(my,{defaultVisitor:c,convertValue:u,isVisitable:Ji});function p(v,w){if(!I.isUndefined(v)){if(f.indexOf(v)!==-1)throw Error("Circular reference detected in "+w.join("."));f.push(v),I.forEach(v,function(b,x){(!(I.isUndefined(b)||b===null)&&o.call(t,b,I.isString(x)?x.trim():x,w,d))===!0&&p(b,w?w.concat(x):[x])}),f.pop()}}if(!I.isObject(e))throw new TypeError("data must be an object");return p(e),t}function Ol(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(r){return t[r]})}function _s(e,t){this._pairs=[],e&&ho(e,this,t)}const Mu=_s.prototype;Mu.append=function(t,n){this._pairs.push([t,n])};Mu.toString=function(t){const n=t?function(r){return t.call(this,r,Ol)}:Ol;return this._pairs.map(function(o){return n(o[0])+"="+n(o[1])},"").join("&")};function vy(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Bu(e,t,n){if(!t)return e;const r=n&&n.encode||vy;I.isFunction(n)&&(n={serialize:n});const o=n&&n.serialize;let i;if(o?i=o(t,n):i=I.isURLSearchParams(t)?t.toString():new _s(t,n).toString(r),i){const s=e.indexOf("#");s!==-1&&(e=e.slice(0,s)),e+=(e.indexOf("?")===-1?"?":"&")+i}return e}class Cl{constructor(){this.handlers=[]}use(t,n,r){return this.handlers.push({fulfilled:t,rejected:n,synchronous:r?r.synchronous:!1,runWhen:r?r.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){I.forEach(this.handlers,function(r){r!==null&&t(r)})}}const qu={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},by=typeof URLSearchParams<"u"?URLSearchParams:_s,wy=typeof FormData<"u"?FormData:null,Sy=typeof Blob<"u"?Blob:null,xy={isBrowser:!0,classes:{URLSearchParams:by,FormData:wy,Blob:Sy},protocols:["http","https","file","blob","url","data"]},ks=typeof window<"u"&&typeof document<"u",Zi=typeof navigator=="object"&&navigator||void 0,Ey=ks&&(!Zi||["ReactNative","NativeScript","NS"].indexOf(Zi.product)<0),Ay=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",Ry=ks&&window.location.href||"http://localhost",_y=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:ks,hasStandardBrowserEnv:Ey,hasStandardBrowserWebWorkerEnv:Ay,navigator:Zi,origin:Ry},Symbol.toStringTag,{value:"Module"})),nt={..._y,...xy};function ky(e,t){return ho(e,new nt.classes.URLSearchParams,Object.assign({visitor:function(n,r,o,i){return nt.isNode&&I.isBuffer(n)?(this.append(r,n.toString("base64")),!1):i.defaultVisitor.apply(this,arguments)}},t))}function Oy(e){return I.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function Cy(e){const t={},n=Object.keys(e);let r;const o=n.length;let i;for(r=0;r<o;r++)i=n[r],t[i]=e[i];return t}function Uu(e){function t(n,r,o,i){let s=n[i++];if(s==="__proto__")return!0;const a=Number.isFinite(+s),l=i>=n.length;return s=!s&&I.isArray(o)?o.length:s,l?(I.hasOwnProp(o,s)?o[s]=[o[s],r]:o[s]=r,!a):((!o[s]||!I.isObject(o[s]))&&(o[s]=[]),t(n,r,o[s],i)&&I.isArray(o[s])&&(o[s]=Cy(o[s])),!a)}if(I.isFormData(e)&&I.isFunction(e.entries)){const n={};return I.forEachEntry(e,(r,o)=>{t(Oy(r),o,n,0)}),n}return null}function Ty(e,t,n){if(I.isString(e))try{return(t||JSON.parse)(e),I.trim(e)}catch(r){if(r.name!=="SyntaxError")throw r}return(n||JSON.stringify)(e)}const dr={transitional:qu,adapter:["xhr","http","fetch"],transformRequest:[function(t,n){const r=n.getContentType()||"",o=r.indexOf("application/json")>-1,i=I.isObject(t);if(i&&I.isHTMLForm(t)&&(t=new FormData(t)),I.isFormData(t))return o?JSON.stringify(Uu(t)):t;if(I.isArrayBuffer(t)||I.isBuffer(t)||I.isStream(t)||I.isFile(t)||I.isBlob(t)||I.isReadableStream(t))return t;if(I.isArrayBufferView(t))return t.buffer;if(I.isURLSearchParams(t))return n.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let a;if(i){if(r.indexOf("application/x-www-form-urlencoded")>-1)return ky(t,this.formSerializer).toString();if((a=I.isFileList(t))||r.indexOf("multipart/form-data")>-1){const l=this.env&&this.env.FormData;return ho(a?{"files[]":t}:t,l&&new l,this.formSerializer)}}return i||o?(n.setContentType("application/json",!1),Ty(t)):t}],transformResponse:[function(t){const n=this.transitional||dr.transitional,r=n&&n.forcedJSONParsing,o=this.responseType==="json";if(I.isResponse(t)||I.isReadableStream(t))return t;if(t&&I.isString(t)&&(r&&!this.responseType||o)){const s=!(n&&n.silentJSONParsing)&&o;try{return JSON.parse(t)}catch(a){if(s)throw a.name==="SyntaxError"?se.from(a,se.ERR_BAD_RESPONSE,this,null,this.response):a}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:nt.classes.FormData,Blob:nt.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};I.forEach(["delete","get","head","post","put","patch"],e=>{dr.headers[e]={}});const Py=I.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Iy=e=>{const t={};let n,r,o;return e&&e.split(`
`).forEach(function(s){o=s.indexOf(":"),n=s.substring(0,o).trim().toLowerCase(),r=s.substring(o+1).trim(),!(!n||t[n]&&Py[n])&&(n==="set-cookie"?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)}),t},Tl=Symbol("internals");function Mn(e){return e&&String(e).trim().toLowerCase()}function kr(e){return e===!1||e==null?e:I.isArray(e)?e.map(kr):String(e)}function Dy(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}const Ny=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function Ti(e,t,n,r,o){if(I.isFunction(r))return r.call(this,t,n);if(o&&(t=n),!!I.isString(t)){if(I.isString(r))return t.indexOf(r)!==-1;if(I.isRegExp(r))return r.test(t)}}function Ly(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,n,r)=>n.toUpperCase()+r)}function $y(e,t){const n=I.toCamelCase(" "+t);["get","set","has"].forEach(r=>{Object.defineProperty(e,r+n,{value:function(o,i,s){return this[r].call(this,t,o,i,s)},configurable:!0})})}let ft=class{constructor(t){t&&this.set(t)}set(t,n,r){const o=this;function i(a,l,u){const c=Mn(l);if(!c)throw new Error("header name must be a non-empty string");const f=I.findKey(o,c);(!f||o[f]===void 0||u===!0||u===void 0&&o[f]!==!1)&&(o[f||l]=kr(a))}const s=(a,l)=>I.forEach(a,(u,c)=>i(u,c,l));if(I.isPlainObject(t)||t instanceof this.constructor)s(t,n);else if(I.isString(t)&&(t=t.trim())&&!Ny(t))s(Iy(t),n);else if(I.isHeaders(t))for(const[a,l]of t.entries())i(l,a,r);else t!=null&&i(n,t,r);return this}get(t,n){if(t=Mn(t),t){const r=I.findKey(this,t);if(r){const o=this[r];if(!n)return o;if(n===!0)return Dy(o);if(I.isFunction(n))return n.call(this,o,r);if(I.isRegExp(n))return n.exec(o);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,n){if(t=Mn(t),t){const r=I.findKey(this,t);return!!(r&&this[r]!==void 0&&(!n||Ti(this,this[r],r,n)))}return!1}delete(t,n){const r=this;let o=!1;function i(s){if(s=Mn(s),s){const a=I.findKey(r,s);a&&(!n||Ti(r,r[a],a,n))&&(delete r[a],o=!0)}}return I.isArray(t)?t.forEach(i):i(t),o}clear(t){const n=Object.keys(this);let r=n.length,o=!1;for(;r--;){const i=n[r];(!t||Ti(this,this[i],i,t,!0))&&(delete this[i],o=!0)}return o}normalize(t){const n=this,r={};return I.forEach(this,(o,i)=>{const s=I.findKey(r,i);if(s){n[s]=kr(o),delete n[i];return}const a=t?Ly(i):String(i).trim();a!==i&&delete n[i],n[a]=kr(o),r[a]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const n=Object.create(null);return I.forEach(this,(r,o)=>{r!=null&&r!==!1&&(n[o]=t&&I.isArray(r)?r.join(", "):r)}),n}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,n])=>t+": "+n).join(`
`)}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...n){const r=new this(t);return n.forEach(o=>r.set(o)),r}static accessor(t){const r=(this[Tl]=this[Tl]={accessors:{}}).accessors,o=this.prototype;function i(s){const a=Mn(s);r[a]||($y(o,s),r[a]=!0)}return I.isArray(t)?t.forEach(i):i(t),this}};ft.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);I.reduceDescriptors(ft.prototype,({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(r){this[n]=r}}});I.freezeMethods(ft);function Pi(e,t){const n=this||dr,r=t||n,o=ft.from(r.headers);let i=r.data;return I.forEach(e,function(a){i=a.call(n,i,o.normalize(),t?t.status:void 0)}),o.normalize(),i}function Hu(e){return!!(e&&e.__CANCEL__)}function In(e,t,n){se.call(this,e??"canceled",se.ERR_CANCELED,t,n),this.name="CanceledError"}I.inherits(In,se,{__CANCEL__:!0});function ju(e,t,n){const r=n.config.validateStatus;!n.status||!r||r(n.status)?e(n):t(new se("Request failed with status code "+n.status,[se.ERR_BAD_REQUEST,se.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n))}function Fy(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function My(e,t){e=e||10;const n=new Array(e),r=new Array(e);let o=0,i=0,s;return t=t!==void 0?t:1e3,function(l){const u=Date.now(),c=r[i];s||(s=u),n[o]=l,r[o]=u;let f=i,d=0;for(;f!==o;)d+=n[f++],f=f%e;if(o=(o+1)%e,o===i&&(i=(i+1)%e),u-s<t)return;const p=c&&u-c;return p?Math.round(d*1e3/p):void 0}}function By(e,t){let n=0,r=1e3/t,o,i;const s=(u,c=Date.now())=>{n=c,o=null,i&&(clearTimeout(i),i=null),e.apply(null,u)};return[(...u)=>{const c=Date.now(),f=c-n;f>=r?s(u,c):(o=u,i||(i=setTimeout(()=>{i=null,s(o)},r-f)))},()=>o&&s(o)]}const Br=(e,t,n=3)=>{let r=0;const o=My(50,250);return By(i=>{const s=i.loaded,a=i.lengthComputable?i.total:void 0,l=s-r,u=o(l),c=s<=a;r=s;const f={loaded:s,total:a,progress:a?s/a:void 0,bytes:l,rate:u||void 0,estimated:u&&a&&c?(a-s)/u:void 0,event:i,lengthComputable:a!=null,[t?"download":"upload"]:!0};e(f)},n)},Pl=(e,t)=>{const n=e!=null;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},Il=e=>(...t)=>I.asap(()=>e(...t)),qy=nt.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,nt.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(nt.origin),nt.navigator&&/(msie|trident)/i.test(nt.navigator.userAgent)):()=>!0,Uy=nt.hasStandardBrowserEnv?{write(e,t,n,r,o,i){const s=[e+"="+encodeURIComponent(t)];I.isNumber(n)&&s.push("expires="+new Date(n).toGMTString()),I.isString(r)&&s.push("path="+r),I.isString(o)&&s.push("domain="+o),i===!0&&s.push("secure"),document.cookie=s.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function Hy(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function jy(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function zu(e,t,n){let r=!Hy(t);return e&&r||n==!1?jy(e,t):t}const Dl=e=>e instanceof ft?{...e}:e;function fn(e,t){t=t||{};const n={};function r(u,c,f,d){return I.isPlainObject(u)&&I.isPlainObject(c)?I.merge.call({caseless:d},u,c):I.isPlainObject(c)?I.merge({},c):I.isArray(c)?c.slice():c}function o(u,c,f,d){if(I.isUndefined(c)){if(!I.isUndefined(u))return r(void 0,u,f,d)}else return r(u,c,f,d)}function i(u,c){if(!I.isUndefined(c))return r(void 0,c)}function s(u,c){if(I.isUndefined(c)){if(!I.isUndefined(u))return r(void 0,u)}else return r(void 0,c)}function a(u,c,f){if(f in t)return r(u,c);if(f in e)return r(void 0,u)}const l={url:i,method:i,data:i,baseURL:s,transformRequest:s,transformResponse:s,paramsSerializer:s,timeout:s,timeoutMessage:s,withCredentials:s,withXSRFToken:s,adapter:s,responseType:s,xsrfCookieName:s,xsrfHeaderName:s,onUploadProgress:s,onDownloadProgress:s,decompress:s,maxContentLength:s,maxBodyLength:s,beforeRedirect:s,transport:s,httpAgent:s,httpsAgent:s,cancelToken:s,socketPath:s,responseEncoding:s,validateStatus:a,headers:(u,c,f)=>o(Dl(u),Dl(c),f,!0)};return I.forEach(Object.keys(Object.assign({},e,t)),function(c){const f=l[c]||o,d=f(e[c],t[c],c);I.isUndefined(d)&&f!==a||(n[c]=d)}),n}const Wu=e=>{const t=fn({},e);let{data:n,withXSRFToken:r,xsrfHeaderName:o,xsrfCookieName:i,headers:s,auth:a}=t;t.headers=s=ft.from(s),t.url=Bu(zu(t.baseURL,t.url),e.params,e.paramsSerializer),a&&s.set("Authorization","Basic "+btoa((a.username||"")+":"+(a.password?unescape(encodeURIComponent(a.password)):"")));let l;if(I.isFormData(n)){if(nt.hasStandardBrowserEnv||nt.hasStandardBrowserWebWorkerEnv)s.setContentType(void 0);else if((l=s.getContentType())!==!1){const[u,...c]=l?l.split(";").map(f=>f.trim()).filter(Boolean):[];s.setContentType([u||"multipart/form-data",...c].join("; "))}}if(nt.hasStandardBrowserEnv&&(r&&I.isFunction(r)&&(r=r(t)),r||r!==!1&&qy(t.url))){const u=o&&i&&Uy.read(i);u&&s.set(o,u)}return t},zy=typeof XMLHttpRequest<"u",Wy=zy&&function(e){return new Promise(function(n,r){const o=Wu(e);let i=o.data;const s=ft.from(o.headers).normalize();let{responseType:a,onUploadProgress:l,onDownloadProgress:u}=o,c,f,d,p,v;function w(){p&&p(),v&&v(),o.cancelToken&&o.cancelToken.unsubscribe(c),o.signal&&o.signal.removeEventListener("abort",c)}let m=new XMLHttpRequest;m.open(o.method.toUpperCase(),o.url,!0),m.timeout=o.timeout;function b(){if(!m)return;const O=ft.from("getAllResponseHeaders"in m&&m.getAllResponseHeaders()),L={data:!a||a==="text"||a==="json"?m.responseText:m.response,status:m.status,statusText:m.statusText,headers:O,config:e,request:m};ju(function(M){n(M),w()},function(M){r(M),w()},L),m=null}"onloadend"in m?m.onloadend=b:m.onreadystatechange=function(){!m||m.readyState!==4||m.status===0&&!(m.responseURL&&m.responseURL.indexOf("file:")===0)||setTimeout(b)},m.onabort=function(){m&&(r(new se("Request aborted",se.ECONNABORTED,e,m)),m=null)},m.onerror=function(){r(new se("Network Error",se.ERR_NETWORK,e,m)),m=null},m.ontimeout=function(){let _=o.timeout?"timeout of "+o.timeout+"ms exceeded":"timeout exceeded";const L=o.transitional||qu;o.timeoutErrorMessage&&(_=o.timeoutErrorMessage),r(new se(_,L.clarifyTimeoutError?se.ETIMEDOUT:se.ECONNABORTED,e,m)),m=null},i===void 0&&s.setContentType(null),"setRequestHeader"in m&&I.forEach(s.toJSON(),function(_,L){m.setRequestHeader(L,_)}),I.isUndefined(o.withCredentials)||(m.withCredentials=!!o.withCredentials),a&&a!=="json"&&(m.responseType=o.responseType),u&&([d,v]=Br(u,!0),m.addEventListener("progress",d)),l&&m.upload&&([f,p]=Br(l),m.upload.addEventListener("progress",f),m.upload.addEventListener("loadend",p)),(o.cancelToken||o.signal)&&(c=O=>{m&&(r(!O||O.type?new In(null,e,m):O),m.abort(),m=null)},o.cancelToken&&o.cancelToken.subscribe(c),o.signal&&(o.signal.aborted?c():o.signal.addEventListener("abort",c)));const x=Fy(o.url);if(x&&nt.protocols.indexOf(x)===-1){r(new se("Unsupported protocol "+x+":",se.ERR_BAD_REQUEST,e));return}m.send(i||null)})},Ky=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let r=new AbortController,o;const i=function(u){if(!o){o=!0,a();const c=u instanceof Error?u:this.reason;r.abort(c instanceof se?c:new In(c instanceof Error?c.message:c))}};let s=t&&setTimeout(()=>{s=null,i(new se(`timeout ${t} of ms exceeded`,se.ETIMEDOUT))},t);const a=()=>{e&&(s&&clearTimeout(s),s=null,e.forEach(u=>{u.unsubscribe?u.unsubscribe(i):u.removeEventListener("abort",i)}),e=null)};e.forEach(u=>u.addEventListener("abort",i));const{signal:l}=r;return l.unsubscribe=()=>I.asap(a),l}},Vy=function*(e,t){let n=e.byteLength;if(n<t){yield e;return}let r=0,o;for(;r<n;)o=r+t,yield e.slice(r,o),r=o},Gy=async function*(e,t){for await(const n of Qy(e))yield*Vy(n,t)},Qy=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:n,value:r}=await t.read();if(n)break;yield r}}finally{await t.cancel()}},Nl=(e,t,n,r)=>{const o=Gy(e,t);let i=0,s,a=l=>{s||(s=!0,r&&r(l))};return new ReadableStream({async pull(l){try{const{done:u,value:c}=await o.next();if(u){a(),l.close();return}let f=c.byteLength;if(n){let d=i+=f;n(d)}l.enqueue(new Uint8Array(c))}catch(u){throw a(u),u}},cancel(l){return a(l),o.return()}},{highWaterMark:2})},go=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",Ku=go&&typeof ReadableStream=="function",Jy=go&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),Vu=(e,...t)=>{try{return!!e(...t)}catch{return!1}},Zy=Ku&&Vu(()=>{let e=!1;const t=new Request(nt.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),Ll=64*1024,Xi=Ku&&Vu(()=>I.isReadableStream(new Response("").body)),qr={stream:Xi&&(e=>e.body)};go&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!qr[t]&&(qr[t]=I.isFunction(e[t])?n=>n[t]():(n,r)=>{throw new se(`Response type '${t}' is not supported`,se.ERR_NOT_SUPPORT,r)})})})(new Response);const Xy=async e=>{if(e==null)return 0;if(I.isBlob(e))return e.size;if(I.isSpecCompliantForm(e))return(await new Request(nt.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(I.isArrayBufferView(e)||I.isArrayBuffer(e))return e.byteLength;if(I.isURLSearchParams(e)&&(e=e+""),I.isString(e))return(await Jy(e)).byteLength},Yy=async(e,t)=>{const n=I.toFiniteNumber(e.getContentLength());return n??Xy(t)},em=go&&(async e=>{let{url:t,method:n,data:r,signal:o,cancelToken:i,timeout:s,onDownloadProgress:a,onUploadProgress:l,responseType:u,headers:c,withCredentials:f="same-origin",fetchOptions:d}=Wu(e);u=u?(u+"").toLowerCase():"text";let p=Ky([o,i&&i.toAbortSignal()],s),v;const w=p&&p.unsubscribe&&(()=>{p.unsubscribe()});let m;try{if(l&&Zy&&n!=="get"&&n!=="head"&&(m=await Yy(c,r))!==0){let L=new Request(t,{method:"POST",body:r,duplex:"half"}),H;if(I.isFormData(r)&&(H=L.headers.get("content-type"))&&c.setContentType(H),L.body){const[M,Q]=Pl(m,Br(Il(l)));r=Nl(L.body,Ll,M,Q)}}I.isString(f)||(f=f?"include":"omit");const b="credentials"in Request.prototype;v=new Request(t,{...d,signal:p,method:n.toUpperCase(),headers:c.normalize().toJSON(),body:r,duplex:"half",credentials:b?f:void 0});let x=await fetch(v);const O=Xi&&(u==="stream"||u==="response");if(Xi&&(a||O&&w)){const L={};["status","statusText","headers"].forEach(j=>{L[j]=x[j]});const H=I.toFiniteNumber(x.headers.get("content-length")),[M,Q]=a&&Pl(H,Br(Il(a),!0))||[];x=new Response(Nl(x.body,Ll,M,()=>{Q&&Q(),w&&w()}),L)}u=u||"text";let _=await qr[I.findKey(qr,u)||"text"](x,e);return!O&&w&&w(),await new Promise((L,H)=>{ju(L,H,{data:_,headers:ft.from(x.headers),status:x.status,statusText:x.statusText,config:e,request:v})})}catch(b){throw w&&w(),b&&b.name==="TypeError"&&/fetch/i.test(b.message)?Object.assign(new se("Network Error",se.ERR_NETWORK,e,v),{cause:b.cause||b}):se.from(b,b&&b.code,e,v)}}),Yi={http:gy,xhr:Wy,fetch:em};I.forEach(Yi,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const $l=e=>`- ${e}`,tm=e=>I.isFunction(e)||e===null||e===!1,Gu={getAdapter:e=>{e=I.isArray(e)?e:[e];const{length:t}=e;let n,r;const o={};for(let i=0;i<t;i++){n=e[i];let s;if(r=n,!tm(n)&&(r=Yi[(s=String(n)).toLowerCase()],r===void 0))throw new se(`Unknown adapter '${s}'`);if(r)break;o[s||"#"+i]=r}if(!r){const i=Object.entries(o).map(([a,l])=>`adapter ${a} `+(l===!1?"is not supported by the environment":"is not available in the build"));let s=t?i.length>1?`since :
`+i.map($l).join(`
`):" "+$l(i[0]):"as no adapter specified";throw new se("There is no suitable adapter to dispatch the request "+s,"ERR_NOT_SUPPORT")}return r},adapters:Yi};function Ii(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new In(null,e)}function Fl(e){return Ii(e),e.headers=ft.from(e.headers),e.data=Pi.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),Gu.getAdapter(e.adapter||dr.adapter)(e).then(function(r){return Ii(e),r.data=Pi.call(e,e.transformResponse,r),r.headers=ft.from(r.headers),r},function(r){return Hu(r)||(Ii(e),r&&r.response&&(r.response.data=Pi.call(e,e.transformResponse,r.response),r.response.headers=ft.from(r.response.headers))),Promise.reject(r)})}const Qu="1.8.2",yo={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{yo[e]=function(r){return typeof r===e||"a"+(t<1?"n ":" ")+e}});const Ml={};yo.transitional=function(t,n,r){function o(i,s){return"[Axios v"+Qu+"] Transitional option '"+i+"'"+s+(r?". "+r:"")}return(i,s,a)=>{if(t===!1)throw new se(o(s," has been removed"+(n?" in "+n:"")),se.ERR_DEPRECATED);return n&&!Ml[s]&&(Ml[s]=!0,console.warn(o(s," has been deprecated since v"+n+" and will be removed in the near future"))),t?t(i,s,a):!0}};yo.spelling=function(t){return(n,r)=>(console.warn(`${r} is likely a misspelling of ${t}`),!0)};function nm(e,t,n){if(typeof e!="object")throw new se("options must be an object",se.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let o=r.length;for(;o-- >0;){const i=r[o],s=t[i];if(s){const a=e[i],l=a===void 0||s(a,i,e);if(l!==!0)throw new se("option "+i+" must be "+l,se.ERR_BAD_OPTION_VALUE);continue}if(n!==!0)throw new se("Unknown option "+i,se.ERR_BAD_OPTION)}}const Or={assertOptions:nm,validators:yo},Pt=Or.validators;let ln=class{constructor(t){this.defaults=t,this.interceptors={request:new Cl,response:new Cl}}async request(t,n){try{return await this._request(t,n)}catch(r){if(r instanceof Error){let o={};Error.captureStackTrace?Error.captureStackTrace(o):o=new Error;const i=o.stack?o.stack.replace(/^.+\n/,""):"";try{r.stack?i&&!String(r.stack).endsWith(i.replace(/^.+\n.+\n/,""))&&(r.stack+=`
`+i):r.stack=i}catch{}}throw r}}_request(t,n){typeof t=="string"?(n=n||{},n.url=t):n=t||{},n=fn(this.defaults,n);const{transitional:r,paramsSerializer:o,headers:i}=n;r!==void 0&&Or.assertOptions(r,{silentJSONParsing:Pt.transitional(Pt.boolean),forcedJSONParsing:Pt.transitional(Pt.boolean),clarifyTimeoutError:Pt.transitional(Pt.boolean)},!1),o!=null&&(I.isFunction(o)?n.paramsSerializer={serialize:o}:Or.assertOptions(o,{encode:Pt.function,serialize:Pt.function},!0)),n.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?n.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:n.allowAbsoluteUrls=!0),Or.assertOptions(n,{baseUrl:Pt.spelling("baseURL"),withXsrfToken:Pt.spelling("withXSRFToken")},!0),n.method=(n.method||this.defaults.method||"get").toLowerCase();let s=i&&I.merge(i.common,i[n.method]);i&&I.forEach(["delete","get","head","post","put","patch","common"],v=>{delete i[v]}),n.headers=ft.concat(s,i);const a=[];let l=!0;this.interceptors.request.forEach(function(w){typeof w.runWhen=="function"&&w.runWhen(n)===!1||(l=l&&w.synchronous,a.unshift(w.fulfilled,w.rejected))});const u=[];this.interceptors.response.forEach(function(w){u.push(w.fulfilled,w.rejected)});let c,f=0,d;if(!l){const v=[Fl.bind(this),void 0];for(v.unshift.apply(v,a),v.push.apply(v,u),d=v.length,c=Promise.resolve(n);f<d;)c=c.then(v[f++],v[f++]);return c}d=a.length;let p=n;for(f=0;f<d;){const v=a[f++],w=a[f++];try{p=v(p)}catch(m){w.call(this,m);break}}try{c=Fl.call(this,p)}catch(v){return Promise.reject(v)}for(f=0,d=u.length;f<d;)c=c.then(u[f++],u[f++]);return c}getUri(t){t=fn(this.defaults,t);const n=zu(t.baseURL,t.url,t.allowAbsoluteUrls);return Bu(n,t.params,t.paramsSerializer)}};I.forEach(["delete","get","head","options"],function(t){ln.prototype[t]=function(n,r){return this.request(fn(r||{},{method:t,url:n,data:(r||{}).data}))}});I.forEach(["post","put","patch"],function(t){function n(r){return function(i,s,a){return this.request(fn(a||{},{method:t,headers:r?{"Content-Type":"multipart/form-data"}:{},url:i,data:s}))}}ln.prototype[t]=n(),ln.prototype[t+"Form"]=n(!0)});let rm=class Ju{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let n;this.promise=new Promise(function(i){n=i});const r=this;this.promise.then(o=>{if(!r._listeners)return;let i=r._listeners.length;for(;i-- >0;)r._listeners[i](o);r._listeners=null}),this.promise.then=o=>{let i;const s=new Promise(a=>{r.subscribe(a),i=a}).then(o);return s.cancel=function(){r.unsubscribe(i)},s},t(function(i,s,a){r.reason||(r.reason=new In(i,s,a),n(r.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const n=this._listeners.indexOf(t);n!==-1&&this._listeners.splice(n,1)}toAbortSignal(){const t=new AbortController,n=r=>{t.abort(r)};return this.subscribe(n),t.signal.unsubscribe=()=>this.unsubscribe(n),t.signal}static source(){let t;return{token:new Ju(function(o){t=o}),cancel:t}}};function om(e){return function(n){return e.apply(null,n)}}function im(e){return I.isObject(e)&&e.isAxiosError===!0}const es={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(es).forEach(([e,t])=>{es[t]=e});function Zu(e){const t=new ln(e),n=Ou(ln.prototype.request,t);return I.extend(n,ln.prototype,t,{allOwnKeys:!0}),I.extend(n,t,null,{allOwnKeys:!0}),n.create=function(o){return Zu(fn(e,o))},n}const Pe=Zu(dr);Pe.Axios=ln;Pe.CanceledError=In;Pe.CancelToken=rm;Pe.isCancel=Hu;Pe.VERSION=Qu;Pe.toFormData=ho;Pe.AxiosError=se;Pe.Cancel=Pe.CanceledError;Pe.all=function(t){return Promise.all(t)};Pe.spread=om;Pe.isAxiosError=im;Pe.mergeConfig=fn;Pe.AxiosHeaders=ft;Pe.formToJSON=e=>Uu(I.isHTMLForm(e)?new FormData(e):e);Pe.getAdapter=Gu.getAdapter;Pe.HttpStatusCode=es;Pe.default=Pe;const{Axios:ib,AxiosError:sb,CanceledError:ab,isCancel:lb,CancelToken:cb,VERSION:ub,all:fb,Cancel:pb,isAxiosError:db,spread:hb,toFormData:gb,AxiosHeaders:yb,HttpStatusCode:mb,formToJSON:vb,getAdapter:bb,mergeConfig:wb}=Pe;Pe.defaults.headers.post["Content-Type"]="application/x-www-form-urlencoded";Pe.defaults.baseURL=vu;Pe.interceptors.request.use(e=>{var r,o,i;const{method:t="get",data:n}=e;switch(e.headers.tockenid=ef(),t.toLocaleUpperCase()){case"POST":case"PUT":if((r=e==null?void 0:e.headers)!=null&&r.isBody)e.data=JSON.stringify(n);else if(((o=e==null?void 0:e.headers)==null?void 0:o["content-type"])==="multipart/form-data"){const s=new FormData;Object.keys(n).map(a=>{s.append(a,n[a])}),e.data=s}break;case"PATCH":case"DElETE":if(((i=e==null?void 0:e.headers)==null?void 0:i["content-type"])==="multipart/form-data"){const s=new FormData;Object.keys(n).map(a=>{s.append(a,n[a])}),e.data=s}break}return e},e=>Promise.reject(e));Pe.interceptors.response.use(e=>{var t;return e.data.code===Qh.SUCCESS_CODE||((t=e==null?void 0:e.config)==null?void 0:t.responseType)==="blob",Promise.resolve(e.data)},e=>Promise.reject(e));function sm(e){return Pe.request(e)}const am=async e=>new Promise((t,n)=>{const{callback:r,...o}=e||{};sm(e).then(i=>{r&&r(i),t(i)}).catch(i=>{n(i)})}),Bn="",lm={PostAuthorize:Bn+"/ai/authorize",PostChatBasic:Bn+"/ai/basic",PostChatHistory:Bn+"/ai/historyRecord",PostChatSend:Bn+"/ai/chat",PostReportData:Bn+"/bigData/report"},cm={},Di={post:lm,get:cm},hr={};Object.keys(Di).forEach(e=>{Object.keys(Di[e]).forEach((t,n)=>{hr[t]=async(r={})=>{const{data:o,urlData:i="",...s}=r;let a={url:Di[e][t]+i,method:e.toUpperCase(),...s||{}};return e.toUpperCase()==="GET"?a.params=o||{}:a.data=o||{},await am(a)}})});const Xu="tockenId",Yu="pageInfo";function Sr(){return Tg.parse(window.location.search.split("?")[1])}function um(e){return sessionStorage.setItem(Xu,e)}function ef(){return sessionStorage.getItem(Xu)}function fm(e){return sessionStorage.setItem(Yu,e)}function Bl(){return JSON.parse(sessionStorage.getItem(Yu)||"{}")}const ql=async function(e){const t=["moduleId","evt","id","pondId","userId","note","channel","privilegeID","platform","strategy","barId","landingPageId","ruleTaskId","taskStatus","surveyId","consumeTime"],n={linkurl:window.location.href,...e};let r=Object.keys(n).filter(s=>!t.includes(s)),o="";if(r!=null&&r.length){let s={};r.forEach(a=>{s[a]=n[a]}),o=JSON.stringify(s)}const i={projectId:"privilege",businessId:"mini",body:[e.moduleId,e.evt,e.id||"",e.pondId||"",Bl().userId,"",Bl().channel||"default",e.privilegeID||"",e.platform||"",e.strategy||"",e.barId||"",e.landingPageId||"",e.ruleTaskId||"",e.taskStatus||"",e.surveyId||"",e.consumeTime||"",o||""].join("	")};hr.PostReportData({data:{...i}})};var jn={exports:{}};/** @license
 * eventsource.js
 * Available under MIT License (MIT)
 * https://github.com/Yaffle/EventSource/
 */var pm=jn.exports,Ul;function dm(){return Ul||(Ul=1,function(e,t){(function(n){var r=n.setTimeout,o=n.clearTimeout,i=n.XMLHttpRequest,s=n.XDomainRequest,a=n.ActiveXObject,l=n.EventSource,u=n.document,c=n.Promise,f=n.fetch,d=n.Response,p=n.TextDecoder,v=n.TextEncoder,w=n.AbortController;if(typeof window<"u"&&typeof u<"u"&&!("readyState"in u)&&u.body==null&&(u.readyState="loading",window.addEventListener("load",function(y){u.readyState="complete"},!1)),i==null&&a!=null&&(i=function(){return new a("Microsoft.XMLHTTP")}),Object.create==null&&(Object.create=function(y){function S(){}return S.prototype=y,new S}),Date.now||(Date.now=function(){return new Date().getTime()}),w==null){var m=f;f=function(y,S){var T=S.signal;return m(y,{headers:S.headers,credentials:S.credentials,cache:S.cache}).then(function(R){var P=R.body.getReader();return T._reader=P,T._aborted&&T._reader.cancel(),{status:R.status,statusText:R.statusText,headers:R.headers,body:{getReader:function(){return P}}}})},w=function(){this.signal={_reader:null,_aborted:!1},this.abort=function(){this.signal._reader!=null&&this.signal._reader.cancel(),this.signal._aborted=!0}}}function b(){this.bitsNeeded=0,this.codePoint=0}b.prototype.decode=function(y){function S(W,Z,G){if(G===1)return W>=128>>Z&&W<<Z<=2047;if(G===2)return W>=2048>>Z&&W<<Z<=55295||W>=57344>>Z&&W<<Z<=65535;if(G===3)return W>=65536>>Z&&W<<Z<=1114111;throw new Error}function T(W,Z){if(W===6*1)return Z>>6>15?3:Z>31?2:1;if(W===6*2)return Z>15?3:2;if(W===6*3)return 3;throw new Error}for(var R=65533,P="",k=this.bitsNeeded,q=this.codePoint,$=0;$<y.length;$+=1){var U=y[$];k!==0&&(U<128||U>191||!S(q<<6|U&63,k-6,T(k,q)))&&(k=0,q=R,P+=String.fromCharCode(q)),k===0?(U>=0&&U<=127?(k=0,q=U):U>=192&&U<=223?(k=6*1,q=U&31):U>=224&&U<=239?(k=6*2,q=U&15):U>=240&&U<=247?(k=6*3,q=U&7):(k=0,q=R),k!==0&&!S(q,k,T(k,q))&&(k=0,q=R)):(k-=6,q=q<<6|U&63),k===0&&(q<=65535?P+=String.fromCharCode(q):(P+=String.fromCharCode(55296+(q-65535-1>>10)),P+=String.fromCharCode(56320+(q-65535-1&1023))))}return this.bitsNeeded=k,this.codePoint=q,P};var x=function(){try{return new p().decode(new v().encode("test"),{stream:!0})==="test"}catch(y){console.debug("TextDecoder does not support streaming option. Using polyfill instead: "+y)}return!1};(p==null||v==null||!x())&&(p=b);var O=function(){};function _(y){this.withCredentials=!1,this.readyState=0,this.status=0,this.statusText="",this.responseText="",this.onprogress=O,this.onload=O,this.onerror=O,this.onreadystatechange=O,this._contentType="",this._xhr=y,this._sendTimeout=0,this._abort=O}_.prototype.open=function(y,S){this._abort(!0);var T=this,R=this._xhr,P=1,k=0;this._abort=function(G){T._sendTimeout!==0&&(o(T._sendTimeout),T._sendTimeout=0),(P===1||P===2||P===3)&&(P=4,R.onload=O,R.onerror=O,R.onabort=O,R.onprogress=O,R.onreadystatechange=O,R.abort(),k!==0&&(o(k),k=0),G||(T.readyState=4,T.onabort(null),T.onreadystatechange())),P=0};var q=function(){if(P===1){var G=0,Y="",be=void 0;if("contentType"in R)G=200,Y="OK",be=R.contentType;else try{G=R.status,Y=R.statusText,be=R.getResponseHeader("Content-Type")}catch{G=0,Y="",be=void 0}G!==0&&(P=2,T.readyState=2,T.status=G,T.statusText=Y,T._contentType=be,T.onreadystatechange())}},$=function(){if(q(),P===2||P===3){P=3;var G="";try{G=R.responseText}catch{}T.readyState=3,T.responseText=G,T.onprogress()}},U=function(G,Y){if((Y==null||Y.preventDefault==null)&&(Y={preventDefault:O}),$(),P===1||P===2||P===3){if(P=4,k!==0&&(o(k),k=0),T.readyState=4,G==="load")T.onload(Y);else if(G==="error")T.onerror(Y);else if(G==="abort")T.onabort(Y);else throw new TypeError;T.onreadystatechange()}},W=function(G){R!=null&&(R.readyState===4?(!("onload"in R)||!("onerror"in R)||!("onabort"in R))&&U(R.responseText===""?"error":"load",G):R.readyState===3?"onprogress"in R||$():R.readyState===2&&q())},Z=function(){k=r(function(){Z()},500),R.readyState===3&&$()};"onload"in R&&(R.onload=function(G){U("load",G)}),"onerror"in R&&(R.onerror=function(G){U("error",G)}),"onabort"in R&&(R.onabort=function(G){U("abort",G)}),"onprogress"in R&&(R.onprogress=$),"onreadystatechange"in R&&(R.onreadystatechange=function(G){W(G)}),("contentType"in R||!("ontimeout"in i.prototype))&&(S+=(S.indexOf("?")===-1?"?":"&")+"padding=true"),R.open(y,S,!0),"readyState"in R&&(k=r(function(){Z()},0))},_.prototype.abort=function(){this._abort(!1)},_.prototype.getResponseHeader=function(y){return this._contentType},_.prototype.setRequestHeader=function(y,S){var T=this._xhr;"setRequestHeader"in T&&T.setRequestHeader(y,S)},_.prototype.getAllResponseHeaders=function(){return this._xhr.getAllResponseHeaders!=null&&this._xhr.getAllResponseHeaders()||""},_.prototype.send=function(){if((!("ontimeout"in i.prototype)||!("sendAsBinary"in i.prototype)&&!("mozAnon"in i.prototype))&&u!=null&&u.readyState!=null&&u.readyState!=="complete"){var y=this;y._sendTimeout=r(function(){y._sendTimeout=0,y.send()},4);return}var S=this._xhr;"withCredentials"in S&&(S.withCredentials=this.withCredentials);try{S.send(void 0)}catch(T){throw T}};function L(y){return y.replace(/[A-Z]/g,function(S){return String.fromCharCode(S.charCodeAt(0)+32)})}function H(y){for(var S=Object.create(null),T=y.split(`\r
`),R=0;R<T.length;R+=1){var P=T[R],k=P.split(": "),q=k.shift(),$=k.join(": ");S[L(q)]=$}this._map=S}H.prototype.get=function(y){return this._map[L(y)]},i!=null&&i.HEADERS_RECEIVED==null&&(i.HEADERS_RECEIVED=2);function M(){}M.prototype.open=function(y,S,T,R,P,k,q){y.open("GET",P);var $=0;y.onprogress=function(){var W=y.responseText,Z=W.slice($);$+=Z.length,T(Z)},y.onerror=function(W){W.preventDefault(),R(new Error("NetworkError"))},y.onload=function(){R(null)},y.onabort=function(){R(null)},y.onreadystatechange=function(){if(y.readyState===i.HEADERS_RECEIVED){var W=y.status,Z=y.statusText,G=y.getResponseHeader("Content-Type"),Y=y.getAllResponseHeaders();S(W,Z,G,new H(Y))}},y.withCredentials=k;for(var U in q)Object.prototype.hasOwnProperty.call(q,U)&&y.setRequestHeader(U,q[U]);return y.send(),y};function Q(y){this._headers=y}Q.prototype.get=function(y){return this._headers.get(y)};function j(){}j.prototype.open=function(y,S,T,R,P,k,q){var $=null,U=new w,W=U.signal,Z=new p;return f(P,{headers:q,credentials:k?"include":"same-origin",signal:W,cache:"no-store"}).then(function(G){return $=G.body.getReader(),S(G.status,G.statusText,G.headers.get("Content-Type"),new Q(G.headers)),new c(function(Y,be){var Ie=function(){$.read().then(function(Re){if(Re.done)Y(void 0);else{var we=Z.decode(Re.value,{stream:!0});T(we),Ie()}}).catch(function(Re){be(Re)})};Ie()})}).catch(function(G){if(G.name!=="AbortError")return G}).then(function(G){R(G)}),{abort:function(){$!=null&&$.cancel(),U.abort()}}};function V(){this._listeners=Object.create(null)}function ee(y){r(function(){throw y},0)}V.prototype.dispatchEvent=function(y){y.target=this;var S=this._listeners[y.type];if(S!=null)for(var T=S.length,R=0;R<T;R+=1){var P=S[R];try{typeof P.handleEvent=="function"?P.handleEvent(y):P.call(this,y)}catch(k){ee(k)}}},V.prototype.addEventListener=function(y,S){y=String(y);var T=this._listeners,R=T[y];R==null&&(R=[],T[y]=R);for(var P=!1,k=0;k<R.length;k+=1)R[k]===S&&(P=!0);P||R.push(S)},V.prototype.removeEventListener=function(y,S){y=String(y);var T=this._listeners,R=T[y];if(R!=null){for(var P=[],k=0;k<R.length;k+=1)R[k]!==S&&P.push(R[k]);P.length===0?delete T[y]:T[y]=P}};function J(y){this.type=y,this.target=void 0}function pe(y,S){J.call(this,y),this.data=S.data,this.lastEventId=S.lastEventId}pe.prototype=Object.create(J.prototype);function de(y,S){J.call(this,y),this.status=S.status,this.statusText=S.statusText,this.headers=S.headers}de.prototype=Object.create(J.prototype);function Ne(y,S){J.call(this,y),this.error=S.error}Ne.prototype=Object.create(J.prototype);var ue=-1,te=0,K=1,ve=2,Fe=-1,xe=0,Ee=1,je=2,Ge=3,Xe=/^text\/event\-stream(;.*)?$/i,Ae=1e3,N=18e6,z=function(y,S){var T=y==null?S:parseInt(y,10);return T!==T&&(T=S),B(T)},B=function(y){return Math.min(Math.max(y,Ae),N)},F=function(y,S,T){try{typeof S=="function"&&S.call(y,T)}catch(R){ee(R)}};function X(y,S){V.call(this),S=S||{},this.onopen=void 0,this.onmessage=void 0,this.onerror=void 0,this.url=void 0,this.readyState=void 0,this.withCredentials=void 0,this.headers=void 0,this._close=void 0,E(this,y,S)}function h(){return i!=null&&"withCredentials"in i.prototype||s==null?new i:new s}var g=f!=null&&d!=null&&"body"in d.prototype;function E(y,S,T){S=String(S);var R=!!T.withCredentials,P=T.lastEventIdQueryParameterName||"lastEventId",k=B(1e3),q=z(T.heartbeatTimeout,45e3),$="",U=k,W=!1,Z=0,G=T.headers||{},Y=T.Transport,be=g&&Y==null?void 0:new _(Y!=null?new Y:h()),Ie=Y!=null&&typeof Y!="string"?new Y:be==null?new j:new M,Re=void 0,we=0,Ue=ue,pt="",Le="",A="",C="",ne=xe,he=0,le=0,ae=function(Me,ke,Ce,Ye){if(Ue===te)if(Me===200&&Ce!=null&&Xe.test(Ce)){Ue=K,W=Date.now(),U=k,y.readyState=K;var ze=new de("open",{status:Me,statusText:ke,headers:Ye});y.dispatchEvent(ze),F(y,y.onopen,ze)}else{var Be="";Me!==200?(ke&&(ke=ke.replace(/\s+/g," ")),Be="EventSource's response has a status "+Me+" "+ke+" that is not 200. Aborting the connection."):Be="EventSource's response has a Content-Type specifying an unsupported type: "+(Ce==null?"-":Ce.replace(/\s+/g," "))+". Aborting the connection.",Qe();var ze=new de("error",{status:Me,statusText:ke,headers:Ye});y.dispatchEvent(ze),F(y,y.onerror,ze),console.error(Be)}},Ot=function(Me){if(Ue===K){for(var ke=-1,Ce=0;Ce<Me.length;Ce+=1){var Ye=Me.charCodeAt(Ce);(Ye===10||Ye===13)&&(ke=Ce)}var ze=(ke!==-1?C:"")+Me.slice(0,ke+1);C=(ke===-1?C:"")+Me.slice(ke+1),Me!==""&&(W=Date.now(),Z+=Me.length);for(var Be=0;Be<ze.length;Be+=1){var Ye=ze.charCodeAt(Be);if(ne===Fe&&Ye===10)ne=xe;else if(ne===Fe&&(ne=xe),Ye===13||Ye===10){if(ne!==xe){ne===Ee&&(le=Be+1);var ot=ze.slice(he,le-1),dt=ze.slice(le+(le<Be&&ze.charCodeAt(le)===32?1:0),Be);ot==="data"?(pt+=`
`,pt+=dt):ot==="id"?Le=dt:ot==="event"?A=dt:ot==="retry"?(k=z(dt,k),U=k):ot==="heartbeatTimeout"&&(q=z(dt,q),we!==0&&(o(we),we=r(function(){lt()},q)))}if(ne===xe){if(pt!==""){$=Le,A===""&&(A="message");var At=new pe(A,{data:pt.slice(1),lastEventId:Le});if(y.dispatchEvent(At),A==="open"?F(y,y.onopen,At):A==="message"?F(y,y.onmessage,At):A==="error"&&F(y,y.onerror,At),Ue===ve)return}pt="",A=""}ne=Ye===13?Fe:xe}else ne===xe&&(he=Be,ne=Ee),ne===Ee?Ye===58&&(le=Be+1,ne=je):ne===je&&(ne=Ge)}}},Ct=function(Me){if(Ue===K||Ue===te){Ue=ue,we!==0&&(o(we),we=0),we=r(function(){lt()},U),U=B(Math.min(k*16,U*2)),y.readyState=te;var ke=new Ne("error",{error:Me});y.dispatchEvent(ke),F(y,y.onerror,ke),Me!=null&&console.error(Me)}},Qe=function(){Ue=ve,Re!=null&&(Re.abort(),Re=void 0),we!==0&&(o(we),we=0),y.readyState=ve},lt=function(){if(we=0,Ue!==ue){if(!W&&Re!=null)Ct(new Error("No activity within "+q+" milliseconds. "+(Ue===te?"No response received.":Z+" chars received.")+" Reconnecting.")),Re!=null&&(Re.abort(),Re=void 0);else{var Me=Math.max((W||Date.now())+q-Date.now(),1);W=!1,we=r(function(){lt()},Me)}return}W=!1,Z=0,we=r(function(){lt()},q),Ue=te,pt="",A="",Le=$,C="",he=0,le=0,ne=xe;var ke=S;if(S.slice(0,5)!=="data:"&&S.slice(0,5)!=="blob:"&&$!==""){var Ce=S.indexOf("?");ke=Ce===-1?S:S.slice(0,Ce+1)+S.slice(Ce+1).replace(/(?:^|&)([^=&]*)(?:=[^&]*)?/g,function(dt,At){return At===P?"":dt}),ke+=(S.indexOf("?")===-1?"?":"&")+P+"="+encodeURIComponent($)}var Ye=y.withCredentials,ze={};ze.Accept="text/event-stream";var Be=y.headers;if(Be!=null)for(var ot in Be)Object.prototype.hasOwnProperty.call(Be,ot)&&(ze[ot]=Be[ot]);try{Re=Ie.open(be,ae,Ot,Ct,ke,Ye,ze)}catch(dt){throw Qe(),dt}};y.url=S,y.readyState=te,y.withCredentials=R,y.headers=G,y._close=Qe,lt()}X.prototype=Object.create(V.prototype),X.prototype.CONNECTING=te,X.prototype.OPEN=K,X.prototype.CLOSED=ve,X.prototype.close=function(){this._close()},X.CONNECTING=te,X.OPEN=K,X.CLOSED=ve,X.prototype.withCredentials=void 0;var D=l;i!=null&&(l==null||!("withCredentials"in l.prototype))&&(D=X),function(y){{var S=y(t);S!==void 0&&(e.exports=S)}}(function(y){y.EventSourcePolyfill=X,y.NativeEventSource=l,y.EventSource=D})})(typeof globalThis>"u"?typeof window<"u"?window:typeof self<"u"?self:pm:globalThis)}(jn,jn.exports)),jn.exports}var hm=dm();const gm="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAIKADAAQAAAABAAAAIAAAAACshmLzAAAEsklEQVRYCb1Xa2xUVRCeObvb3dYK2gpqTVSIpv4hhvBHItLEEJUaGmm9aLE09U+bvmkkxKCBSsRGQ6z2AbQxkZS2GpYWU6NE1ATf/jFEjSFKYrQxBaWAhpbW7t4zfnO7W/tulW4nOffMPWdmvrlnzpkzl2me5DjNqW7QzTLM64V4HdQyhGwqE18H/gLaT8Rylsl8wEP9J8Lh2pH5mOa5hHKebMxIClC1MJWQ0NK55GPzl0X4KFmq6+6s+HU2nRkdcJyjPhs6v5uIn4WBpJgRi/5bYvqUSc4AYMAac5WFMvD1d+N9NeZ0dUxMHnOyj/++tH+mFZnWgdytTXewkQ4Yuz9m6CKRNFsKNB8/UvpHbGzazik8eJvYaD50dwjRzTGhHwzb7HBbde9kpSkOOAXNdwnbz/9VluaR5ODOd1tLrk5Wnu3dcVqW2lBkLxwvh5yPmfqEfBu72sq+G683wQGNdyDAX0DpTnzBX2KpsLu9sme8wn/l8wobNpCYbti8Xkj6fSxrxq9EPFZUXNwSCATofQ+caIitbLpWcHW2q63qI2KbBfYyTsxNVnzdRUVvhuIfMubAxeGRZzB4L5pl5i3H2qs+iwtdaw8nTiMET6ltfOCaK9FBhGaUvBA4ha/fbsWcwVAK2qGuI5WlsfkF7fK2NdbBoJ6qYWzKTA2FtwJ29Kgp+IWg69u1oKjjjJlhsw+veopCCMUenWLHqU2yofRz4NNwZp/D0r+kE4kirEINbL+KNmCGI8uNDaZn4yUNTXD229EnlCJRtxMALlqqDQayDTLYBg9R6MvxxyNRXvS8tf13pPSP1T4bWuVHl+mBGTqdKNApdq2vVIy7zZL/IBygUQes/DhFMEEDXZ1lP8P0C2peT0G6MmLovPaLTerAoIIa6+WAxcb3VmDAQzWcuujoANRNiBwgK7Az71ksB5ALHgbWAWHegWNovZyPm0ovjEUi3g2glShqHjS4oT4ZReVVOfmvxQuIhDmiBQtWfK0HIHzC8JDBCvAVDHCS31+RMOSYYUvuE4qF9md6cuBDEw6XDyD+h3QeYSjX6lf5RNBjRfU3kIjehsj7cqy1tSTi3YZCkXqMaRl9oxu0L6tAIsjv+vfC7jK0QdwGY4mIuttrzmEvvKKgKBzKcgubNiu/kLS5oPkR1Jllnk2mF7s7qn5T3lsBZXh4eS1igHoQvMjhxwsaHlB+IcgpbFqLAqQLtnyI/vdm6KJexx6NORAOb3GjAZOPUS0YluCMnswtaMwZFfv/z9yChketyHuwkIKGCsi/cfw/gu7GCaRluWV7EoMr0FDD0RsoEncdb6vAv8H8aVNxS0rSUKQO261KtbyKWOy6cPv2CZfeFAdUOLeg/lZmv3q9Wt9BCl7v+qId7xyu+UUHZiLn6YZl4nI5vrpcq2CVQ+y/Ipfzp/tNm9YBVdJSzQ2l7YSR5/Ea1DGQwNjX2KjfgDuL/8U+XKPJ+DVLJSOZQFoPGa2s46EdQSj3X+rt33PqVG1UDUymGR2IC+ZtPbCSfLYa2EUYWxIfn71HYmNpiYxQfc/blX2zyc7pQFxZE5SE3IeETBbieR8Ub8GcnmklhIh79V7R1K7Z1Utwo3OzPv8BC3KiVbf/CYIAAAAASUVORK5CYII=";function Os(){return{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null}}let dn=Os();function tf(e){dn=e}const Xn={exec:()=>null};function me(e,t=""){let n=typeof e=="string"?e:e.source;const r={replace:(o,i)=>{let s=typeof i=="string"?i:i.source;return s=s.replace(at.caret,"$1"),n=n.replace(o,s),r},getRegex:()=>new RegExp(n,t)};return r}const at={codeRemoveIndent:/^(?: {1,4}| {0,3}\t)/gm,outputLinkReplace:/\\([\[\]])/g,indentCodeCompensation:/^(\s+)(?:```)/,beginningSpace:/^\s+/,endingHash:/#$/,startingSpaceChar:/^ /,endingSpaceChar:/ $/,nonSpaceChar:/[^ ]/,newLineCharGlobal:/\n/g,tabCharGlobal:/\t/g,multipleSpaceGlobal:/\s+/g,blankLine:/^[ \t]*$/,doubleBlankLine:/\n[ \t]*\n[ \t]*$/,blockquoteStart:/^ {0,3}>/,blockquoteSetextReplace:/\n {0,3}((?:=+|-+) *)(?=\n|$)/g,blockquoteSetextReplace2:/^ {0,3}>[ \t]?/gm,listReplaceTabs:/^\t+/,listReplaceNesting:/^ {1,4}(?=( {4})*[^ ])/g,listIsTask:/^\[[ xX]\] /,listReplaceTask:/^\[[ xX]\] +/,anyLine:/\n.*\n/,hrefBrackets:/^<(.*)>$/,tableDelimiter:/[:|]/,tableAlignChars:/^\||\| *$/g,tableRowBlankLine:/\n[ \t]*$/,tableAlignRight:/^ *-+: *$/,tableAlignCenter:/^ *:-+: *$/,tableAlignLeft:/^ *:-+ *$/,startATag:/^<a /i,endATag:/^<\/a>/i,startPreScriptTag:/^<(pre|code|kbd|script)(\s|>)/i,endPreScriptTag:/^<\/(pre|code|kbd|script)(\s|>)/i,startAngleBracket:/^</,endAngleBracket:/>$/,pedanticHrefTitle:/^([^'"]*[^\s])\s+(['"])(.*)\2/,unicodeAlphaNumeric:/[\p{L}\p{N}]/u,escapeTest:/[&<>"']/,escapeReplace:/[&<>"']/g,escapeTestNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,escapeReplaceNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/g,unescapeTest:/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/ig,caret:/(^|[^\[])\^/g,percentDecode:/%25/g,findPipe:/\|/g,splitPipe:/ \|/,slashPipe:/\\\|/g,carriageReturn:/\r\n|\r/g,spaceLine:/^ +$/gm,notSpaceStart:/^\S*/,endingNewline:/\n$/,listItemRegex:e=>new RegExp(`^( {0,3}${e})((?:[	 ][^\\n]*)?(?:\\n|$))`),nextBulletRegex:e=>new RegExp(`^ {0,${Math.min(3,e-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ 	][^\\n]*)?(?:\\n|$))`),hrRegex:e=>new RegExp(`^ {0,${Math.min(3,e-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),fencesBeginRegex:e=>new RegExp(`^ {0,${Math.min(3,e-1)}}(?:\`\`\`|~~~)`),headingBeginRegex:e=>new RegExp(`^ {0,${Math.min(3,e-1)}}#`),htmlBeginRegex:e=>new RegExp(`^ {0,${Math.min(3,e-1)}}<(?:[a-z].*>|!--)`,"i")},ym=/^(?:[ \t]*(?:\n|$))+/,mm=/^((?: {4}| {0,3}\t)[^\n]+(?:\n(?:[ \t]*(?:\n|$))*)?)+/,vm=/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,gr=/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,bm=/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,Cs=/(?:[*+-]|\d{1,9}[.)])/,nf=/^(?!bull |blockCode|fences|blockquote|heading|html|table)((?:.|\n(?!\s*?\n|bull |blockCode|fences|blockquote|heading|html|table))+?)\n {0,3}(=+|-+) *(?:\n+|$)/,rf=me(nf).replace(/bull/g,Cs).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).replace(/\|table/g,"").getRegex(),wm=me(nf).replace(/bull/g,Cs).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).replace(/table/g,/ {0,3}\|?(?:[:\- ]*\|)+[\:\- ]*\n/).getRegex(),Ts=/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,Sm=/^[^\n]+/,Ps=/(?!\s*\])(?:\\.|[^\[\]\\])+/,xm=me(/^ {0,3}\[(label)\]: *(?:\n[ \t]*)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n[ \t]*)?| *\n[ \t]*)(title))? *(?:\n+|$)/).replace("label",Ps).replace("title",/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/).getRegex(),Em=me(/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/).replace(/bull/g,Cs).getRegex(),mo="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",Is=/<!--(?:-?>|[\s\S]*?(?:-->|$))/,Am=me("^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$))","i").replace("comment",Is).replace("tag",mo).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),of=me(Ts).replace("hr",gr).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",mo).getRegex(),Rm=me(/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/).replace("paragraph",of).getRegex(),Ds={blockquote:Rm,code:mm,def:xm,fences:vm,heading:bm,hr:gr,html:Am,lheading:rf,list:Em,newline:ym,paragraph:of,table:Xn,text:Sm},Hl=me("^ *([^\\n ].*)\\n {0,3}((?:\\| *)?:?-+:? *(?:\\| *:?-+:? *)*(?:\\| *)?)(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)").replace("hr",gr).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("blockquote"," {0,3}>").replace("code","(?: {4}| {0,3}	)[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",mo).getRegex(),_m={...Ds,lheading:wm,table:Hl,paragraph:me(Ts).replace("hr",gr).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("table",Hl).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",mo).getRegex()},km={...Ds,html:me(`^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:"[^"]*"|'[^']*'|\\s[^'"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))`).replace("comment",Is).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:Xn,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:me(Ts).replace("hr",gr).replace("heading",` *#{1,6} *[^
]`).replace("lheading",rf).replace("|table","").replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").replace("|tag","").getRegex()},Om=/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,Cm=/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,sf=/^( {2,}|\\)\n(?!\s*$)/,Tm=/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,vo=/[\p{P}\p{S}]/u,Ns=/[\s\p{P}\p{S}]/u,af=/[^\s\p{P}\p{S}]/u,Pm=me(/^((?![*_])punctSpace)/,"u").replace(/punctSpace/g,Ns).getRegex(),lf=/(?!~)[\p{P}\p{S}]/u,Im=/(?!~)[\s\p{P}\p{S}]/u,Dm=/(?:[^\s\p{P}\p{S}]|~)/u,Nm=/\[[^[\]]*?\]\((?:\\.|[^\\\(\)]|\((?:\\.|[^\\\(\)])*\))*\)|`[^`]*?`|<[^<>]*?>/g,cf=/^(?:\*+(?:((?!\*)punct)|[^\s*]))|^_+(?:((?!_)punct)|([^\s_]))/,Lm=me(cf,"u").replace(/punct/g,vo).getRegex(),$m=me(cf,"u").replace(/punct/g,lf).getRegex(),uf="^[^_*]*?__[^_*]*?\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\*)punct(\\*+)(?=[\\s]|$)|notPunctSpace(\\*+)(?!\\*)(?=punctSpace|$)|(?!\\*)punctSpace(\\*+)(?=notPunctSpace)|[\\s](\\*+)(?!\\*)(?=punct)|(?!\\*)punct(\\*+)(?!\\*)(?=punct)|notPunctSpace(\\*+)(?=notPunctSpace)",Fm=me(uf,"gu").replace(/notPunctSpace/g,af).replace(/punctSpace/g,Ns).replace(/punct/g,vo).getRegex(),Mm=me(uf,"gu").replace(/notPunctSpace/g,Dm).replace(/punctSpace/g,Im).replace(/punct/g,lf).getRegex(),Bm=me("^[^_*]*?\\*\\*[^_*]*?_[^_*]*?(?=\\*\\*)|[^_]+(?=[^_])|(?!_)punct(_+)(?=[\\s]|$)|notPunctSpace(_+)(?!_)(?=punctSpace|$)|(?!_)punctSpace(_+)(?=notPunctSpace)|[\\s](_+)(?!_)(?=punct)|(?!_)punct(_+)(?!_)(?=punct)","gu").replace(/notPunctSpace/g,af).replace(/punctSpace/g,Ns).replace(/punct/g,vo).getRegex(),qm=me(/\\(punct)/,"gu").replace(/punct/g,vo).getRegex(),Um=me(/^<(scheme:[^\s\x00-\x1f<>]*|email)>/).replace("scheme",/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace("email",/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex(),Hm=me(Is).replace("(?:-->|$)","-->").getRegex(),jm=me("^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>").replace("comment",Hm).replace("attribute",/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/).getRegex(),Ur=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,zm=me(/^!?\[(label)\]\(\s*(href)(?:\s+(title))?\s*\)/).replace("label",Ur).replace("href",/<(?:\\.|[^\n<>\\])+>|[^\s\x00-\x1f]*/).replace("title",/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/).getRegex(),ff=me(/^!?\[(label)\]\[(ref)\]/).replace("label",Ur).replace("ref",Ps).getRegex(),pf=me(/^!?\[(ref)\](?:\[\])?/).replace("ref",Ps).getRegex(),Wm=me("reflink|nolink(?!\\()","g").replace("reflink",ff).replace("nolink",pf).getRegex(),Ls={_backpedal:Xn,anyPunctuation:qm,autolink:Um,blockSkip:Nm,br:sf,code:Cm,del:Xn,emStrongLDelim:Lm,emStrongRDelimAst:Fm,emStrongRDelimUnd:Bm,escape:Om,link:zm,nolink:pf,punctuation:Pm,reflink:ff,reflinkSearch:Wm,tag:jm,text:Tm,url:Xn},Km={...Ls,link:me(/^!?\[(label)\]\((.*?)\)/).replace("label",Ur).getRegex(),reflink:me(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",Ur).getRegex()},ts={...Ls,emStrongRDelimAst:Mm,emStrongLDelim:$m,url:me(/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,"i").replace("email",/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])((?:\\.|[^\\])*?(?:\\.|[^\s~\\]))\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/},Vm={...ts,br:me(sf).replace("{2,}","*").getRegex(),text:me(ts.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()},xr={normal:Ds,gfm:_m,pedantic:km},qn={normal:Ls,gfm:ts,breaks:Vm,pedantic:Km},Gm={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},jl=e=>Gm[e];function It(e,t){if(t){if(at.escapeTest.test(e))return e.replace(at.escapeReplace,jl)}else if(at.escapeTestNoEncode.test(e))return e.replace(at.escapeReplaceNoEncode,jl);return e}function zl(e){try{e=encodeURI(e).replace(at.percentDecode,"%")}catch{return null}return e}function Wl(e,t){var i;const n=e.replace(at.findPipe,(s,a,l)=>{let u=!1,c=a;for(;--c>=0&&l[c]==="\\";)u=!u;return u?"|":" |"}),r=n.split(at.splitPipe);let o=0;if(r[0].trim()||r.shift(),r.length>0&&!((i=r.at(-1))!=null&&i.trim())&&r.pop(),t)if(r.length>t)r.splice(t);else for(;r.length<t;)r.push("");for(;o<r.length;o++)r[o]=r[o].trim().replace(at.slashPipe,"|");return r}function Un(e,t,n){const r=e.length;if(r===0)return"";let o=0;for(;o<r&&e.charAt(r-o-1)===t;)o++;return e.slice(0,r-o)}function Qm(e,t){if(e.indexOf(t[1])===-1)return-1;let n=0;for(let r=0;r<e.length;r++)if(e[r]==="\\")r++;else if(e[r]===t[0])n++;else if(e[r]===t[1]&&(n--,n<0))return r;return-1}function Kl(e,t,n,r,o){const i=t.href,s=t.title||null,a=e[1].replace(o.other.outputLinkReplace,"$1");if(e[0].charAt(0)!=="!"){r.state.inLink=!0;const l={type:"link",raw:n,href:i,title:s,text:a,tokens:r.inlineTokens(a)};return r.state.inLink=!1,l}return{type:"image",raw:n,href:i,title:s,text:a}}function Jm(e,t,n){const r=e.match(n.other.indentCodeCompensation);if(r===null)return t;const o=r[1];return t.split(`
`).map(i=>{const s=i.match(n.other.beginningSpace);if(s===null)return i;const[a]=s;return a.length>=o.length?i.slice(o.length):i}).join(`
`)}class Hr{constructor(t){_e(this,"options");_e(this,"rules");_e(this,"lexer");this.options=t||dn}space(t){const n=this.rules.block.newline.exec(t);if(n&&n[0].length>0)return{type:"space",raw:n[0]}}code(t){const n=this.rules.block.code.exec(t);if(n){const r=n[0].replace(this.rules.other.codeRemoveIndent,"");return{type:"code",raw:n[0],codeBlockStyle:"indented",text:this.options.pedantic?r:Un(r,`
`)}}}fences(t){const n=this.rules.block.fences.exec(t);if(n){const r=n[0],o=Jm(r,n[3]||"",this.rules);return{type:"code",raw:r,lang:n[2]?n[2].trim().replace(this.rules.inline.anyPunctuation,"$1"):n[2],text:o}}}heading(t){const n=this.rules.block.heading.exec(t);if(n){let r=n[2].trim();if(this.rules.other.endingHash.test(r)){const o=Un(r,"#");(this.options.pedantic||!o||this.rules.other.endingSpaceChar.test(o))&&(r=o.trim())}return{type:"heading",raw:n[0],depth:n[1].length,text:r,tokens:this.lexer.inline(r)}}}hr(t){const n=this.rules.block.hr.exec(t);if(n)return{type:"hr",raw:Un(n[0],`
`)}}blockquote(t){const n=this.rules.block.blockquote.exec(t);if(n){let r=Un(n[0],`
`).split(`
`),o="",i="";const s=[];for(;r.length>0;){let a=!1;const l=[];let u;for(u=0;u<r.length;u++)if(this.rules.other.blockquoteStart.test(r[u]))l.push(r[u]),a=!0;else if(!a)l.push(r[u]);else break;r=r.slice(u);const c=l.join(`
`),f=c.replace(this.rules.other.blockquoteSetextReplace,`
    $1`).replace(this.rules.other.blockquoteSetextReplace2,"");o=o?`${o}
${c}`:c,i=i?`${i}
${f}`:f;const d=this.lexer.state.top;if(this.lexer.state.top=!0,this.lexer.blockTokens(f,s,!0),this.lexer.state.top=d,r.length===0)break;const p=s.at(-1);if((p==null?void 0:p.type)==="code")break;if((p==null?void 0:p.type)==="blockquote"){const v=p,w=v.raw+`
`+r.join(`
`),m=this.blockquote(w);s[s.length-1]=m,o=o.substring(0,o.length-v.raw.length)+m.raw,i=i.substring(0,i.length-v.text.length)+m.text;break}else if((p==null?void 0:p.type)==="list"){const v=p,w=v.raw+`
`+r.join(`
`),m=this.list(w);s[s.length-1]=m,o=o.substring(0,o.length-p.raw.length)+m.raw,i=i.substring(0,i.length-v.raw.length)+m.raw,r=w.substring(s.at(-1).raw.length).split(`
`);continue}}return{type:"blockquote",raw:o,tokens:s,text:i}}}list(t){let n=this.rules.block.list.exec(t);if(n){let r=n[1].trim();const o=r.length>1,i={type:"list",raw:"",ordered:o,start:o?+r.slice(0,-1):"",loose:!1,items:[]};r=o?`\\d{1,9}\\${r.slice(-1)}`:`\\${r}`,this.options.pedantic&&(r=o?r:"[*+-]");const s=this.rules.other.listItemRegex(r);let a=!1;for(;t;){let u=!1,c="",f="";if(!(n=s.exec(t))||this.rules.block.hr.test(t))break;c=n[0],t=t.substring(c.length);let d=n[2].split(`
`,1)[0].replace(this.rules.other.listReplaceTabs,x=>" ".repeat(3*x.length)),p=t.split(`
`,1)[0],v=!d.trim(),w=0;if(this.options.pedantic?(w=2,f=d.trimStart()):v?w=n[1].length+1:(w=n[2].search(this.rules.other.nonSpaceChar),w=w>4?1:w,f=d.slice(w),w+=n[1].length),v&&this.rules.other.blankLine.test(p)&&(c+=p+`
`,t=t.substring(p.length+1),u=!0),!u){const x=this.rules.other.nextBulletRegex(w),O=this.rules.other.hrRegex(w),_=this.rules.other.fencesBeginRegex(w),L=this.rules.other.headingBeginRegex(w),H=this.rules.other.htmlBeginRegex(w);for(;t;){const M=t.split(`
`,1)[0];let Q;if(p=M,this.options.pedantic?(p=p.replace(this.rules.other.listReplaceNesting,"  "),Q=p):Q=p.replace(this.rules.other.tabCharGlobal,"    "),_.test(p)||L.test(p)||H.test(p)||x.test(p)||O.test(p))break;if(Q.search(this.rules.other.nonSpaceChar)>=w||!p.trim())f+=`
`+Q.slice(w);else{if(v||d.replace(this.rules.other.tabCharGlobal,"    ").search(this.rules.other.nonSpaceChar)>=4||_.test(d)||L.test(d)||O.test(d))break;f+=`
`+p}!v&&!p.trim()&&(v=!0),c+=M+`
`,t=t.substring(M.length+1),d=Q.slice(w)}}i.loose||(a?i.loose=!0:this.rules.other.doubleBlankLine.test(c)&&(a=!0));let m=null,b;this.options.gfm&&(m=this.rules.other.listIsTask.exec(f),m&&(b=m[0]!=="[ ] ",f=f.replace(this.rules.other.listReplaceTask,""))),i.items.push({type:"list_item",raw:c,task:!!m,checked:b,loose:!1,text:f,tokens:[]}),i.raw+=c}const l=i.items.at(-1);if(l)l.raw=l.raw.trimEnd(),l.text=l.text.trimEnd();else return;i.raw=i.raw.trimEnd();for(let u=0;u<i.items.length;u++)if(this.lexer.state.top=!1,i.items[u].tokens=this.lexer.blockTokens(i.items[u].text,[]),!i.loose){const c=i.items[u].tokens.filter(d=>d.type==="space"),f=c.length>0&&c.some(d=>this.rules.other.anyLine.test(d.raw));i.loose=f}if(i.loose)for(let u=0;u<i.items.length;u++)i.items[u].loose=!0;return i}}html(t){const n=this.rules.block.html.exec(t);if(n)return{type:"html",block:!0,raw:n[0],pre:n[1]==="pre"||n[1]==="script"||n[1]==="style",text:n[0]}}def(t){const n=this.rules.block.def.exec(t);if(n){const r=n[1].toLowerCase().replace(this.rules.other.multipleSpaceGlobal," "),o=n[2]?n[2].replace(this.rules.other.hrefBrackets,"$1").replace(this.rules.inline.anyPunctuation,"$1"):"",i=n[3]?n[3].substring(1,n[3].length-1).replace(this.rules.inline.anyPunctuation,"$1"):n[3];return{type:"def",tag:r,raw:n[0],href:o,title:i}}}table(t){var a;const n=this.rules.block.table.exec(t);if(!n||!this.rules.other.tableDelimiter.test(n[2]))return;const r=Wl(n[1]),o=n[2].replace(this.rules.other.tableAlignChars,"").split("|"),i=(a=n[3])!=null&&a.trim()?n[3].replace(this.rules.other.tableRowBlankLine,"").split(`
`):[],s={type:"table",raw:n[0],header:[],align:[],rows:[]};if(r.length===o.length){for(const l of o)this.rules.other.tableAlignRight.test(l)?s.align.push("right"):this.rules.other.tableAlignCenter.test(l)?s.align.push("center"):this.rules.other.tableAlignLeft.test(l)?s.align.push("left"):s.align.push(null);for(let l=0;l<r.length;l++)s.header.push({text:r[l],tokens:this.lexer.inline(r[l]),header:!0,align:s.align[l]});for(const l of i)s.rows.push(Wl(l,s.header.length).map((u,c)=>({text:u,tokens:this.lexer.inline(u),header:!1,align:s.align[c]})));return s}}lheading(t){const n=this.rules.block.lheading.exec(t);if(n)return{type:"heading",raw:n[0],depth:n[2].charAt(0)==="="?1:2,text:n[1],tokens:this.lexer.inline(n[1])}}paragraph(t){const n=this.rules.block.paragraph.exec(t);if(n){const r=n[1].charAt(n[1].length-1)===`
`?n[1].slice(0,-1):n[1];return{type:"paragraph",raw:n[0],text:r,tokens:this.lexer.inline(r)}}}text(t){const n=this.rules.block.text.exec(t);if(n)return{type:"text",raw:n[0],text:n[0],tokens:this.lexer.inline(n[0])}}escape(t){const n=this.rules.inline.escape.exec(t);if(n)return{type:"escape",raw:n[0],text:n[1]}}tag(t){const n=this.rules.inline.tag.exec(t);if(n)return!this.lexer.state.inLink&&this.rules.other.startATag.test(n[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&this.rules.other.endATag.test(n[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&this.rules.other.startPreScriptTag.test(n[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&this.rules.other.endPreScriptTag.test(n[0])&&(this.lexer.state.inRawBlock=!1),{type:"html",raw:n[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,block:!1,text:n[0]}}link(t){const n=this.rules.inline.link.exec(t);if(n){const r=n[2].trim();if(!this.options.pedantic&&this.rules.other.startAngleBracket.test(r)){if(!this.rules.other.endAngleBracket.test(r))return;const s=Un(r.slice(0,-1),"\\");if((r.length-s.length)%2===0)return}else{const s=Qm(n[2],"()");if(s>-1){const l=(n[0].indexOf("!")===0?5:4)+n[1].length+s;n[2]=n[2].substring(0,s),n[0]=n[0].substring(0,l).trim(),n[3]=""}}let o=n[2],i="";if(this.options.pedantic){const s=this.rules.other.pedanticHrefTitle.exec(o);s&&(o=s[1],i=s[3])}else i=n[3]?n[3].slice(1,-1):"";return o=o.trim(),this.rules.other.startAngleBracket.test(o)&&(this.options.pedantic&&!this.rules.other.endAngleBracket.test(r)?o=o.slice(1):o=o.slice(1,-1)),Kl(n,{href:o&&o.replace(this.rules.inline.anyPunctuation,"$1"),title:i&&i.replace(this.rules.inline.anyPunctuation,"$1")},n[0],this.lexer,this.rules)}}reflink(t,n){let r;if((r=this.rules.inline.reflink.exec(t))||(r=this.rules.inline.nolink.exec(t))){const o=(r[2]||r[1]).replace(this.rules.other.multipleSpaceGlobal," "),i=n[o.toLowerCase()];if(!i){const s=r[0].charAt(0);return{type:"text",raw:s,text:s}}return Kl(r,i,r[0],this.lexer,this.rules)}}emStrong(t,n,r=""){let o=this.rules.inline.emStrongLDelim.exec(t);if(!o||o[3]&&r.match(this.rules.other.unicodeAlphaNumeric))return;if(!(o[1]||o[2]||"")||!r||this.rules.inline.punctuation.exec(r)){const s=[...o[0]].length-1;let a,l,u=s,c=0;const f=o[0][0]==="*"?this.rules.inline.emStrongRDelimAst:this.rules.inline.emStrongRDelimUnd;for(f.lastIndex=0,n=n.slice(-1*t.length+s);(o=f.exec(n))!=null;){if(a=o[1]||o[2]||o[3]||o[4]||o[5]||o[6],!a)continue;if(l=[...a].length,o[3]||o[4]){u+=l;continue}else if((o[5]||o[6])&&s%3&&!((s+l)%3)){c+=l;continue}if(u-=l,u>0)continue;l=Math.min(l,l+u+c);const d=[...o[0]][0].length,p=t.slice(0,s+o.index+d+l);if(Math.min(s,l)%2){const w=p.slice(1,-1);return{type:"em",raw:p,text:w,tokens:this.lexer.inlineTokens(w)}}const v=p.slice(2,-2);return{type:"strong",raw:p,text:v,tokens:this.lexer.inlineTokens(v)}}}}codespan(t){const n=this.rules.inline.code.exec(t);if(n){let r=n[2].replace(this.rules.other.newLineCharGlobal," ");const o=this.rules.other.nonSpaceChar.test(r),i=this.rules.other.startingSpaceChar.test(r)&&this.rules.other.endingSpaceChar.test(r);return o&&i&&(r=r.substring(1,r.length-1)),{type:"codespan",raw:n[0],text:r}}}br(t){const n=this.rules.inline.br.exec(t);if(n)return{type:"br",raw:n[0]}}del(t){const n=this.rules.inline.del.exec(t);if(n)return{type:"del",raw:n[0],text:n[2],tokens:this.lexer.inlineTokens(n[2])}}autolink(t){const n=this.rules.inline.autolink.exec(t);if(n){let r,o;return n[2]==="@"?(r=n[1],o="mailto:"+r):(r=n[1],o=r),{type:"link",raw:n[0],text:r,href:o,tokens:[{type:"text",raw:r,text:r}]}}}url(t){var r;let n;if(n=this.rules.inline.url.exec(t)){let o,i;if(n[2]==="@")o=n[0],i="mailto:"+o;else{let s;do s=n[0],n[0]=((r=this.rules.inline._backpedal.exec(n[0]))==null?void 0:r[0])??"";while(s!==n[0]);o=n[0],n[1]==="www."?i="http://"+n[0]:i=n[0]}return{type:"link",raw:n[0],text:o,href:i,tokens:[{type:"text",raw:o,text:o}]}}}inlineText(t){const n=this.rules.inline.text.exec(t);if(n){const r=this.lexer.state.inRawBlock;return{type:"text",raw:n[0],text:n[0],escaped:r}}}}class wt{constructor(t){_e(this,"tokens");_e(this,"options");_e(this,"state");_e(this,"tokenizer");_e(this,"inlineQueue");this.tokens=[],this.tokens.links=Object.create(null),this.options=t||dn,this.options.tokenizer=this.options.tokenizer||new Hr,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};const n={other:at,block:xr.normal,inline:qn.normal};this.options.pedantic?(n.block=xr.pedantic,n.inline=qn.pedantic):this.options.gfm&&(n.block=xr.gfm,this.options.breaks?n.inline=qn.breaks:n.inline=qn.gfm),this.tokenizer.rules=n}static get rules(){return{block:xr,inline:qn}}static lex(t,n){return new wt(n).lex(t)}static lexInline(t,n){return new wt(n).inlineTokens(t)}lex(t){t=t.replace(at.carriageReturn,`
`),this.blockTokens(t,this.tokens);for(let n=0;n<this.inlineQueue.length;n++){const r=this.inlineQueue[n];this.inlineTokens(r.src,r.tokens)}return this.inlineQueue=[],this.tokens}blockTokens(t,n=[],r=!1){var o,i,s;for(this.options.pedantic&&(t=t.replace(at.tabCharGlobal,"    ").replace(at.spaceLine,""));t;){let a;if((i=(o=this.options.extensions)==null?void 0:o.block)!=null&&i.some(u=>(a=u.call({lexer:this},t,n))?(t=t.substring(a.raw.length),n.push(a),!0):!1))continue;if(a=this.tokenizer.space(t)){t=t.substring(a.raw.length);const u=n.at(-1);a.raw.length===1&&u!==void 0?u.raw+=`
`:n.push(a);continue}if(a=this.tokenizer.code(t)){t=t.substring(a.raw.length);const u=n.at(-1);(u==null?void 0:u.type)==="paragraph"||(u==null?void 0:u.type)==="text"?(u.raw+=`
`+a.raw,u.text+=`
`+a.text,this.inlineQueue.at(-1).src=u.text):n.push(a);continue}if(a=this.tokenizer.fences(t)){t=t.substring(a.raw.length),n.push(a);continue}if(a=this.tokenizer.heading(t)){t=t.substring(a.raw.length),n.push(a);continue}if(a=this.tokenizer.hr(t)){t=t.substring(a.raw.length),n.push(a);continue}if(a=this.tokenizer.blockquote(t)){t=t.substring(a.raw.length),n.push(a);continue}if(a=this.tokenizer.list(t)){t=t.substring(a.raw.length),n.push(a);continue}if(a=this.tokenizer.html(t)){t=t.substring(a.raw.length),n.push(a);continue}if(a=this.tokenizer.def(t)){t=t.substring(a.raw.length);const u=n.at(-1);(u==null?void 0:u.type)==="paragraph"||(u==null?void 0:u.type)==="text"?(u.raw+=`
`+a.raw,u.text+=`
`+a.raw,this.inlineQueue.at(-1).src=u.text):this.tokens.links[a.tag]||(this.tokens.links[a.tag]={href:a.href,title:a.title});continue}if(a=this.tokenizer.table(t)){t=t.substring(a.raw.length),n.push(a);continue}if(a=this.tokenizer.lheading(t)){t=t.substring(a.raw.length),n.push(a);continue}let l=t;if((s=this.options.extensions)!=null&&s.startBlock){let u=1/0;const c=t.slice(1);let f;this.options.extensions.startBlock.forEach(d=>{f=d.call({lexer:this},c),typeof f=="number"&&f>=0&&(u=Math.min(u,f))}),u<1/0&&u>=0&&(l=t.substring(0,u+1))}if(this.state.top&&(a=this.tokenizer.paragraph(l))){const u=n.at(-1);r&&(u==null?void 0:u.type)==="paragraph"?(u.raw+=`
`+a.raw,u.text+=`
`+a.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=u.text):n.push(a),r=l.length!==t.length,t=t.substring(a.raw.length);continue}if(a=this.tokenizer.text(t)){t=t.substring(a.raw.length);const u=n.at(-1);(u==null?void 0:u.type)==="text"?(u.raw+=`
`+a.raw,u.text+=`
`+a.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=u.text):n.push(a);continue}if(t){const u="Infinite loop on byte: "+t.charCodeAt(0);if(this.options.silent){console.error(u);break}else throw new Error(u)}}return this.state.top=!0,n}inline(t,n=[]){return this.inlineQueue.push({src:t,tokens:n}),n}inlineTokens(t,n=[]){var a,l,u;let r=t,o=null;if(this.tokens.links){const c=Object.keys(this.tokens.links);if(c.length>0)for(;(o=this.tokenizer.rules.inline.reflinkSearch.exec(r))!=null;)c.includes(o[0].slice(o[0].lastIndexOf("[")+1,-1))&&(r=r.slice(0,o.index)+"["+"a".repeat(o[0].length-2)+"]"+r.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;(o=this.tokenizer.rules.inline.blockSkip.exec(r))!=null;)r=r.slice(0,o.index)+"["+"a".repeat(o[0].length-2)+"]"+r.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);for(;(o=this.tokenizer.rules.inline.anyPunctuation.exec(r))!=null;)r=r.slice(0,o.index)+"++"+r.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);let i=!1,s="";for(;t;){i||(s=""),i=!1;let c;if((l=(a=this.options.extensions)==null?void 0:a.inline)!=null&&l.some(d=>(c=d.call({lexer:this},t,n))?(t=t.substring(c.raw.length),n.push(c),!0):!1))continue;if(c=this.tokenizer.escape(t)){t=t.substring(c.raw.length),n.push(c);continue}if(c=this.tokenizer.tag(t)){t=t.substring(c.raw.length),n.push(c);continue}if(c=this.tokenizer.link(t)){t=t.substring(c.raw.length),n.push(c);continue}if(c=this.tokenizer.reflink(t,this.tokens.links)){t=t.substring(c.raw.length);const d=n.at(-1);c.type==="text"&&(d==null?void 0:d.type)==="text"?(d.raw+=c.raw,d.text+=c.text):n.push(c);continue}if(c=this.tokenizer.emStrong(t,r,s)){t=t.substring(c.raw.length),n.push(c);continue}if(c=this.tokenizer.codespan(t)){t=t.substring(c.raw.length),n.push(c);continue}if(c=this.tokenizer.br(t)){t=t.substring(c.raw.length),n.push(c);continue}if(c=this.tokenizer.del(t)){t=t.substring(c.raw.length),n.push(c);continue}if(c=this.tokenizer.autolink(t)){t=t.substring(c.raw.length),n.push(c);continue}if(!this.state.inLink&&(c=this.tokenizer.url(t))){t=t.substring(c.raw.length),n.push(c);continue}let f=t;if((u=this.options.extensions)!=null&&u.startInline){let d=1/0;const p=t.slice(1);let v;this.options.extensions.startInline.forEach(w=>{v=w.call({lexer:this},p),typeof v=="number"&&v>=0&&(d=Math.min(d,v))}),d<1/0&&d>=0&&(f=t.substring(0,d+1))}if(c=this.tokenizer.inlineText(f)){t=t.substring(c.raw.length),c.raw.slice(-1)!=="_"&&(s=c.raw.slice(-1)),i=!0;const d=n.at(-1);(d==null?void 0:d.type)==="text"?(d.raw+=c.raw,d.text+=c.text):n.push(c);continue}if(t){const d="Infinite loop on byte: "+t.charCodeAt(0);if(this.options.silent){console.error(d);break}else throw new Error(d)}}return n}}class jr{constructor(t){_e(this,"options");_e(this,"parser");this.options=t||dn}space(t){return""}code({text:t,lang:n,escaped:r}){var s;const o=(s=(n||"").match(at.notSpaceStart))==null?void 0:s[0],i=t.replace(at.endingNewline,"")+`
`;return o?'<pre><code class="language-'+It(o)+'">'+(r?i:It(i,!0))+`</code></pre>
`:"<pre><code>"+(r?i:It(i,!0))+`</code></pre>
`}blockquote({tokens:t}){return`<blockquote>
${this.parser.parse(t)}</blockquote>
`}html({text:t}){return t}heading({tokens:t,depth:n}){return`<h${n}>${this.parser.parseInline(t)}</h${n}>
`}hr(t){return`<hr>
`}list(t){const n=t.ordered,r=t.start;let o="";for(let a=0;a<t.items.length;a++){const l=t.items[a];o+=this.listitem(l)}const i=n?"ol":"ul",s=n&&r!==1?' start="'+r+'"':"";return"<"+i+s+`>
`+o+"</"+i+`>
`}listitem(t){var r;let n="";if(t.task){const o=this.checkbox({checked:!!t.checked});t.loose?((r=t.tokens[0])==null?void 0:r.type)==="paragraph"?(t.tokens[0].text=o+" "+t.tokens[0].text,t.tokens[0].tokens&&t.tokens[0].tokens.length>0&&t.tokens[0].tokens[0].type==="text"&&(t.tokens[0].tokens[0].text=o+" "+It(t.tokens[0].tokens[0].text),t.tokens[0].tokens[0].escaped=!0)):t.tokens.unshift({type:"text",raw:o+" ",text:o+" ",escaped:!0}):n+=o+" "}return n+=this.parser.parse(t.tokens,!!t.loose),`<li>${n}</li>
`}checkbox({checked:t}){return"<input "+(t?'checked="" ':"")+'disabled="" type="checkbox">'}paragraph({tokens:t}){return`<p>${this.parser.parseInline(t)}</p>
`}table(t){let n="",r="";for(let i=0;i<t.header.length;i++)r+=this.tablecell(t.header[i]);n+=this.tablerow({text:r});let o="";for(let i=0;i<t.rows.length;i++){const s=t.rows[i];r="";for(let a=0;a<s.length;a++)r+=this.tablecell(s[a]);o+=this.tablerow({text:r})}return o&&(o=`<tbody>${o}</tbody>`),`<table>
<thead>
`+n+`</thead>
`+o+`</table>
`}tablerow({text:t}){return`<tr>
${t}</tr>
`}tablecell(t){const n=this.parser.parseInline(t.tokens),r=t.header?"th":"td";return(t.align?`<${r} align="${t.align}">`:`<${r}>`)+n+`</${r}>
`}strong({tokens:t}){return`<strong>${this.parser.parseInline(t)}</strong>`}em({tokens:t}){return`<em>${this.parser.parseInline(t)}</em>`}codespan({text:t}){return`<code>${It(t,!0)}</code>`}br(t){return"<br>"}del({tokens:t}){return`<del>${this.parser.parseInline(t)}</del>`}link({href:t,title:n,tokens:r}){const o=this.parser.parseInline(r),i=zl(t);if(i===null)return o;t=i;let s='<a href="'+t+'"';return n&&(s+=' title="'+It(n)+'"'),s+=">"+o+"</a>",s}image({href:t,title:n,text:r}){const o=zl(t);if(o===null)return It(r);t=o;let i=`<img src="${t}" alt="${r}"`;return n&&(i+=` title="${It(n)}"`),i+=">",i}text(t){return"tokens"in t&&t.tokens?this.parser.parseInline(t.tokens):"escaped"in t&&t.escaped?t.text:It(t.text)}}class $s{strong({text:t}){return t}em({text:t}){return t}codespan({text:t}){return t}del({text:t}){return t}html({text:t}){return t}text({text:t}){return t}link({text:t}){return""+t}image({text:t}){return""+t}br(){return""}}class St{constructor(t){_e(this,"options");_e(this,"renderer");_e(this,"textRenderer");this.options=t||dn,this.options.renderer=this.options.renderer||new jr,this.renderer=this.options.renderer,this.renderer.options=this.options,this.renderer.parser=this,this.textRenderer=new $s}static parse(t,n){return new St(n).parse(t)}static parseInline(t,n){return new St(n).parseInline(t)}parse(t,n=!0){var o,i;let r="";for(let s=0;s<t.length;s++){const a=t[s];if((i=(o=this.options.extensions)==null?void 0:o.renderers)!=null&&i[a.type]){const u=a,c=this.options.extensions.renderers[u.type].call({parser:this},u);if(c!==!1||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(u.type)){r+=c||"";continue}}const l=a;switch(l.type){case"space":{r+=this.renderer.space(l);continue}case"hr":{r+=this.renderer.hr(l);continue}case"heading":{r+=this.renderer.heading(l);continue}case"code":{r+=this.renderer.code(l);continue}case"table":{r+=this.renderer.table(l);continue}case"blockquote":{r+=this.renderer.blockquote(l);continue}case"list":{r+=this.renderer.list(l);continue}case"html":{r+=this.renderer.html(l);continue}case"paragraph":{r+=this.renderer.paragraph(l);continue}case"text":{let u=l,c=this.renderer.text(u);for(;s+1<t.length&&t[s+1].type==="text";)u=t[++s],c+=`
`+this.renderer.text(u);n?r+=this.renderer.paragraph({type:"paragraph",raw:c,text:c,tokens:[{type:"text",raw:c,text:c,escaped:!0}]}):r+=c;continue}default:{const u='Token with "'+l.type+'" type was not found.';if(this.options.silent)return console.error(u),"";throw new Error(u)}}}return r}parseInline(t,n=this.renderer){var o,i;let r="";for(let s=0;s<t.length;s++){const a=t[s];if((i=(o=this.options.extensions)==null?void 0:o.renderers)!=null&&i[a.type]){const u=this.options.extensions.renderers[a.type].call({parser:this},a);if(u!==!1||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(a.type)){r+=u||"";continue}}const l=a;switch(l.type){case"escape":{r+=n.text(l);break}case"html":{r+=n.html(l);break}case"link":{r+=n.link(l);break}case"image":{r+=n.image(l);break}case"strong":{r+=n.strong(l);break}case"em":{r+=n.em(l);break}case"codespan":{r+=n.codespan(l);break}case"br":{r+=n.br(l);break}case"del":{r+=n.del(l);break}case"text":{r+=n.text(l);break}default:{const u='Token with "'+l.type+'" type was not found.';if(this.options.silent)return console.error(u),"";throw new Error(u)}}}return r}}class Yn{constructor(t){_e(this,"options");_e(this,"block");this.options=t||dn}preprocess(t){return t}postprocess(t){return t}processAllTokens(t){return t}provideLexer(){return this.block?wt.lex:wt.lexInline}provideParser(){return this.block?St.parse:St.parseInline}}_e(Yn,"passThroughHooks",new Set(["preprocess","postprocess","processAllTokens"]));class Zm{constructor(...t){_e(this,"defaults",Os());_e(this,"options",this.setOptions);_e(this,"parse",this.parseMarkdown(!0));_e(this,"parseInline",this.parseMarkdown(!1));_e(this,"Parser",St);_e(this,"Renderer",jr);_e(this,"TextRenderer",$s);_e(this,"Lexer",wt);_e(this,"Tokenizer",Hr);_e(this,"Hooks",Yn);this.use(...t)}walkTokens(t,n){var o,i;let r=[];for(const s of t)switch(r=r.concat(n.call(this,s)),s.type){case"table":{const a=s;for(const l of a.header)r=r.concat(this.walkTokens(l.tokens,n));for(const l of a.rows)for(const u of l)r=r.concat(this.walkTokens(u.tokens,n));break}case"list":{const a=s;r=r.concat(this.walkTokens(a.items,n));break}default:{const a=s;(i=(o=this.defaults.extensions)==null?void 0:o.childTokens)!=null&&i[a.type]?this.defaults.extensions.childTokens[a.type].forEach(l=>{const u=a[l].flat(1/0);r=r.concat(this.walkTokens(u,n))}):a.tokens&&(r=r.concat(this.walkTokens(a.tokens,n)))}}return r}use(...t){const n=this.defaults.extensions||{renderers:{},childTokens:{}};return t.forEach(r=>{const o={...r};if(o.async=this.defaults.async||o.async||!1,r.extensions&&(r.extensions.forEach(i=>{if(!i.name)throw new Error("extension name required");if("renderer"in i){const s=n.renderers[i.name];s?n.renderers[i.name]=function(...a){let l=i.renderer.apply(this,a);return l===!1&&(l=s.apply(this,a)),l}:n.renderers[i.name]=i.renderer}if("tokenizer"in i){if(!i.level||i.level!=="block"&&i.level!=="inline")throw new Error("extension level must be 'block' or 'inline'");const s=n[i.level];s?s.unshift(i.tokenizer):n[i.level]=[i.tokenizer],i.start&&(i.level==="block"?n.startBlock?n.startBlock.push(i.start):n.startBlock=[i.start]:i.level==="inline"&&(n.startInline?n.startInline.push(i.start):n.startInline=[i.start]))}"childTokens"in i&&i.childTokens&&(n.childTokens[i.name]=i.childTokens)}),o.extensions=n),r.renderer){const i=this.defaults.renderer||new jr(this.defaults);for(const s in r.renderer){if(!(s in i))throw new Error(`renderer '${s}' does not exist`);if(["options","parser"].includes(s))continue;const a=s,l=r.renderer[a],u=i[a];i[a]=(...c)=>{let f=l.apply(i,c);return f===!1&&(f=u.apply(i,c)),f||""}}o.renderer=i}if(r.tokenizer){const i=this.defaults.tokenizer||new Hr(this.defaults);for(const s in r.tokenizer){if(!(s in i))throw new Error(`tokenizer '${s}' does not exist`);if(["options","rules","lexer"].includes(s))continue;const a=s,l=r.tokenizer[a],u=i[a];i[a]=(...c)=>{let f=l.apply(i,c);return f===!1&&(f=u.apply(i,c)),f}}o.tokenizer=i}if(r.hooks){const i=this.defaults.hooks||new Yn;for(const s in r.hooks){if(!(s in i))throw new Error(`hook '${s}' does not exist`);if(["options","block"].includes(s))continue;const a=s,l=r.hooks[a],u=i[a];Yn.passThroughHooks.has(s)?i[a]=c=>{if(this.defaults.async)return Promise.resolve(l.call(i,c)).then(d=>u.call(i,d));const f=l.call(i,c);return u.call(i,f)}:i[a]=(...c)=>{let f=l.apply(i,c);return f===!1&&(f=u.apply(i,c)),f}}o.hooks=i}if(r.walkTokens){const i=this.defaults.walkTokens,s=r.walkTokens;o.walkTokens=function(a){let l=[];return l.push(s.call(this,a)),i&&(l=l.concat(i.call(this,a))),l}}this.defaults={...this.defaults,...o}}),this}setOptions(t){return this.defaults={...this.defaults,...t},this}lexer(t,n){return wt.lex(t,n??this.defaults)}parser(t,n){return St.parse(t,n??this.defaults)}parseMarkdown(t){return(r,o)=>{const i={...o},s={...this.defaults,...i},a=this.onError(!!s.silent,!!s.async);if(this.defaults.async===!0&&i.async===!1)return a(new Error("marked(): The async option was set to true by an extension. Remove async: false from the parse options object to return a Promise."));if(typeof r>"u"||r===null)return a(new Error("marked(): input parameter is undefined or null"));if(typeof r!="string")return a(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(r)+", string expected"));s.hooks&&(s.hooks.options=s,s.hooks.block=t);const l=s.hooks?s.hooks.provideLexer():t?wt.lex:wt.lexInline,u=s.hooks?s.hooks.provideParser():t?St.parse:St.parseInline;if(s.async)return Promise.resolve(s.hooks?s.hooks.preprocess(r):r).then(c=>l(c,s)).then(c=>s.hooks?s.hooks.processAllTokens(c):c).then(c=>s.walkTokens?Promise.all(this.walkTokens(c,s.walkTokens)).then(()=>c):c).then(c=>u(c,s)).then(c=>s.hooks?s.hooks.postprocess(c):c).catch(a);try{s.hooks&&(r=s.hooks.preprocess(r));let c=l(r,s);s.hooks&&(c=s.hooks.processAllTokens(c)),s.walkTokens&&this.walkTokens(c,s.walkTokens);let f=u(c,s);return s.hooks&&(f=s.hooks.postprocess(f)),f}catch(c){return a(c)}}}onError(t,n){return r=>{if(r.message+=`
Please report this to https://github.com/markedjs/marked.`,t){const o="<p>An error occurred:</p><pre>"+It(r.message+"",!0)+"</pre>";return n?Promise.resolve(o):o}if(n)return Promise.reject(r);throw r}}}const pn=new Zm;function ce(e,t){return pn.parse(e,t)}ce.options=ce.setOptions=function(e){return pn.setOptions(e),ce.defaults=pn.defaults,tf(ce.defaults),ce};ce.getDefaults=Os;ce.defaults=dn;ce.use=function(...e){return pn.use(...e),ce.defaults=pn.defaults,tf(ce.defaults),ce};ce.walkTokens=function(e,t){return pn.walkTokens(e,t)};ce.parseInline=pn.parseInline;ce.Parser=St;ce.parser=St.parse;ce.Renderer=jr;ce.TextRenderer=$s;ce.Lexer=wt;ce.lexer=wt.lex;ce.Tokenizer=Hr;ce.Hooks=Yn;ce.parse=ce;ce.options;ce.setOptions;ce.use;ce.walkTokens;ce.parseInline;St.parse;wt.lex;function Xm(e){class t extends ce.Renderer{link(o,i,s){return ce.Renderer.prototype.link.call(this,o,i,s).replace("<a",'<a target="_blank" rel="noopener noreferrer"')}}const n=new t;return e?ce==null?void 0:ce(e,{renderer:n}):""}const Dn=(e,t)=>{const n=e.__vccOpts||e;for(const[r,o]of t)n[r]=o;return n},Ym={class:"home-question-box"},ev={class:"question-content"},tv={class:"box"},nv=["onClick"],rv={__name:"QuestionBox",setup(e){const{baseInfo:t,sendMessage:n}=mt("chatProvide"),r=ut(()=>{var i;return(i=t.value)==null?void 0:i.questions}),o=i=>{n(i)};return(i,s)=>($e(),He("div",Ym,[s[1]||(s[1]=oe("div",{class:"question-title"},[oe("h2",null,"Hi~我是AI助手"),oe("p",null,"你身边的智能助手，已集成DeepSeek大模型，可以为你答疑解惑，快来点击以下任一功能体验吧～")],-1)),oe("div",ev,[s[0]||(s[0]=oe("h3",null,"你可以这样问",-1)),oe("div",tv,[($e(!0),He(ct,null,Cc(r.value,a=>($e(),He("div",{class:"box-item",key:a,onClick:l=>o(a)},cn(a),9,nv))),128))])])]))}},ov=Dn(rv,[["__scopeId","data-v-7a6f63ef"]]),iv={class:"sw-comment flex-col"},sv={key:0,class:"sw-hd"},av=["src"],lv={key:0,class:"author"},cv=["innerHTML"],uv={className:"sw-ft"},Vl={__name:"index",props:["author","avatar","content","dateTime","reverse","pop","customClass","contentMaxWidth"],setup(e){const t=e,n=ut(()=>t.reverse?"right":"left");return(r,o)=>($e(),He("div",iv,[e.dateTime?($e(),He("div",{key:0,class:bt(["time",n.value])},cn(e.dateTime),3)):an("",!0),oe("div",{class:bt(["sw-comment-inner flex items-start",[{"flex-row-reverse":e.reverse},e.customClass]])},[e.avatar?($e(),He("div",sv,[typeof e.avatar=="string"?($e(),He("img",{key:0,src:e.avatar,alt:""},null,8,av)):sn(r.$slots,"avatar",{key:1})])):an("",!0),oe("div",{class:bt(["sw-bd",[{"flex-1":!e.pop},`max-w-[${e.contentMaxWidth}px] text-${n.value}`]])},[oe("div",{class:bt(["flex",{"flex-row-reverse":!!e.reverse}])},[e.author?($e(),He("div",lv,cn(e.author),1)):an("",!0)],2),oe("div",{class:bt(["content",[{pop:e.pop,"p-[12px]":e.content},`pop-${n.value}`]])},[oe("div",{innerHTML:e.content},null,8,cv),sn(r.$slots,"loadComponents")],2)],2),oe("div",uv,[sn(r.$slots,"footer")])],2)]))}},fv={className:"home-chat-box",ref:"scroll-box"},pv={class:"chat-comment-box"},dv={key:0,className:"chat-message"},hv={className:"chat-message"},gv={__name:"ChatBox",setup(e){const{chatList:t,backState:n,chatScrollTop:r,setChatList:o}=mt("chatProvide"),i=dp("scroll-box"),s=l=>{},a=()=>{console.log("获取消息记录"),hr.PostChatHistory({}).then(l=>{var u;o(((u=l==null?void 0:l.data)==null?void 0:u.list)||[]),_n(()=>{var c;i.value.scrollTop=((c=i.value)==null?void 0:c.scrollHeight)||1e4})})};return Rn(r,async l=>{_n(()=>{var u,c;i.value.scrollTop=(u=i.value)!=null&&u.scrollHeight?((c=i.value)==null?void 0:c.scrollHeight)+1e4:1e4})}),ur(()=>{a()}),(l,u)=>{const c=_p("infinite-loading");return $e(),He("div",fv,[Te(c,{direction:"top",onInfinite:s}),Te(ov),oe("div",pv,[($e(!0),He(ct,null,Cc(Ke(t),f=>($e(),He("div",{key:f.id},[f.content?($e(),He("div",dv,[Te(Vl,{reverse:f.type===2,pop:!0,data:f,content:Ke(Xm)(f.content),customClass:"chat-comment"},null,8,["reverse","data","content"])])):an("",!0)]))),128)),eo(oe("div",hv,[Te(Vl,{pop:!0,customClass:"chat-comment chat-load",content:""},{loadComponents:gs(()=>u[0]||(u[0]=[oe("img",{class:"load",src:gm},null,-1)])),_:1})],512),[[bs,Ke(n)==="loading"]])])],512)}}},yv="data:image/png;base64,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",mv={class:"home-input-box"},vv={class:"input-box"},bv=["placeholder"],wv={__name:"InputBox",setup(e){const{placeholderMsg:t,sendState:n,checkAuthorize:r,sendMessage:o}=mt("chatProvide"),i=We(""),s=l=>{o(i.value),i.value=""},a=l=>{o(i.value),i.value=""};return(l,u)=>($e(),He("div",mv,[oe("div",vv,[eo(oe("input",{type:"text","onUpdate:modelValue":u[0]||(u[0]=c=>i.value=c),onKeyup:u[1]||(u[1]=Bd(c=>Ke(r)(a),["enter"])),placeholder:Ke(t)},null,40,bv),[[Fd,i.value]]),oe("img",{class:bt({disabled:Ke(n)==="loading"||!i.value}),src:yv,onClick:u[2]||(u[2]=c=>Ke(r)(s))},null,2)]),u[3]||(u[3]=oe("p",{class:"footer"},"结果由DeepSeek大模型生成",-1))]))}},Sv=Dn(wv,[["__scopeId","data-v-d8b271ca"]]),xv={__name:"index",props:["maskClass","maskClick","position","customClass"],setup(e){return(t,n)=>($e(),He("div",null,[oe("div",{class:bt(["sw-modal-mask",e.maskClass]),onClick:n[0]||(n[0]=r=>({maskClick:e.maskClick}))},null,2),oe("div",{class:bt(["sw-modal-content",[e.position,e.customClass]])},[sn(t.$slots,"content",{},void 0,!0)],2)]))}},Ev=Dn(xv,[["__scopeId","data-v-1f4197d9"]]),Av={class:"auth-dialog"},Rv=["href"],_v=["href"],kv=["href"],Ov={class:"btn"},Cv={__name:"AuthDialog",setup(e){const{baseInfo:t,changeAuthorize:n,changeAuthShow:r}=mt("chatProvide"),o=()=>{hr.PostAuthorize({}).then(i=>{n()})};return(i,s)=>($e(),$r(Ev,{position:"center"},{content:gs(()=>{var a,l,u;return[oe("div",Av,[s[1]||(s[1]=oe("h3",null,"所有内容均由AI生成仅供参考",-1)),s[2]||(s[2]=oe("p",null,"请阅读并知悉",-1)),oe("a",{href:(a=Ke(t))==null?void 0:a.userAgreement},"《顺网用户协议》",8,Rv),oe("a",{href:(l=Ke(t))==null?void 0:l.privacy},"《隐私政策》",8,_v),oe("a",{href:(u=Ke(t))==null?void 0:u.aiAgreement},"《AI条款》",8,kv),oe("div",Ov,[oe("button",{onClick:s[0]||(s[0]=c=>Ke(r)(!1))},"暂不授权"),oe("button",{onClick:o},"确定授权")])])]}),_:1}))}},Tv=Dn(Cv,[["__scopeId","data-v-b1e0036b"]]),Pv={class:"home-container"},Iv={class:"home-content"},Dv={__name:"HomeView",setup(e){const{proxy:t}=Zc(),n=We([]),r=We({}),o=We(0),i=We("idle"),s=We("idle"),a=We({}),l=We(!1),u=We(!1),c=We("和小顺说点什么");let f=We();const d=j=>{i.value=j},p=j=>{s.value=j},v=j=>{o.value+=1},w=()=>{l.value=!0,u.value=!1},m=j=>{u.value=j},b=j=>l.value?(u.value=!1,j==null||j(),!0):(u.value=!0,!1),x=j=>{n.value=j},O=()=>{n.value=[]},_=j=>{const V=n.value.findIndex(ee=>ee.messageId===j.messageId);if(!~V)n.value.push(j);else{const ee=n.value[V];n.value[V]={...j,content:ee.content+j.content}}v()},L=j=>{const V=j.messageId?"messageId":"content",ee=n.value.findIndex(J=>J[V]===j[V]);~ee&&n.value.splice(ee,1)},H=j=>{f=new hm.EventSourcePolyfill(`${vu}/ai/chat?question=${j}`,{headers:{tockenid:ef()}}),f.retry=200,f.onmessage=V=>{const ee=JSON.parse(V.data);ee!=null&&ee.finish&&(d("idle"),c.value="你可以继续问"),p("idle"),_(ee)},f.onopen=V=>{},f.onerror=V=>{f.close(),d("idle"),p("idle")}},M=j=>{if(!j)return!1;if(/^\s+$/.test(j))return t.$message("请输入想问的问题"),!1;if(i.value==="loading")return t.$message("操作频繁，请稍后再试..."),!1;console.log("发送消息事件",j),d("loading"),p("loading"),_({type:2,content:j,messageId:`message${n.value.length+1}`}),ql({moduleId:"ai-send",evt:"click"}),H(j)},Q=()=>{hr.PostChatBasic({}).then(j=>{var V;a.value=(j==null?void 0:j.data)||{},l.value=((V=j==null?void 0:j.data)==null?void 0:V.authorize)||!1,b()})};return ur(()=>{Object.keys(Sr()).length&&Sr().tockenId&&um(Sr().tockenId),fm(JSON.stringify(Sr())),Q(),ql({moduleId:"ai-page",evt:"pv"})}),Gn("chatProvide",{baseInfo:a,authorize:l,chatList:n,chatInfo:r,sendState:i,backState:s,chatScrollTop:o,placeholderMsg:c,sendMessage:M,changeSendState:d,changeBackState:p,setChatList:x,removeChatList:O,incrementChatList:_,decrementChatList:L,connectEventSource:H,changeAuthorize:w,changeAuthShow:m,checkAuthorize:b}),(j,V)=>($e(),He("main",null,[eo(Te(Tv,null,null,512),[[bs,u.value]]),oe("div",Pv,[oe("div",Iv,[Te(gv),Te(Sv)])])]))}},Nv=Dn(Dv,[["__scopeId","data-v-50721720"]]),Lv=Wh({history:Sh("/"),routes:[{path:"/",name:"home",component:Nv}]});function $v(e,t=null){if(!e)return!1;const n=e.getBoundingClientRect(),r=t?t.getBoundingClientRect():{top:0,left:0,bottom:window.innerHeight,right:window.innerWidth};return n.bottom>=r.top&&n.top<=r.bottom&&n.right>=r.left&&n.left<=r.right}async function Fv(e){return e?(await _n(),e.value instanceof HTMLElement?e.value:e.value?document.querySelector(e.value):null):null}function Mv(e){let t=`0px 0px ${e.distance}px 0px`;e.top&&(t=`${e.distance}px 0px 0px 0px`);const n=new IntersectionObserver(r=>{r[0].isIntersecting&&(e.firstload&&e.emit(),e.firstload=!0)},{root:e.parentEl,rootMargin:t});return e.infiniteLoading.value&&n.observe(e.infiniteLoading.value),n}async function Gl(e,t){if(await _n(),!e.top)return;const n=e.parentEl||document.documentElement;n.scrollTop=n.scrollHeight-t}const df=(e,t)=>{const n=e.__vccOpts||e;for(const[r,o]of t)n[r]=o;return n},Bv={},qv=e=>(cp("data-v-d3e37633"),e=e(),up(),e),Uv={class:"container"},Hv=qv(()=>oe("div",{class:"spinner"},null,-1)),jv=[Hv];function zv(e,t){return $e(),He("div",Uv,jv)}const Wv=df(Bv,[["render",zv],["__scopeId","data-v-d3e37633"]]),Kv={class:"state-error"},Vv=to({__name:"InfiniteLoading",props:{top:{type:Boolean,default:!1},target:{},distance:{default:0},identifier:{},firstload:{type:Boolean,default:!0},slots:{}},emits:["infinite"],setup(e,{emit:t}){const n=e;let r=null,o=0;const i=We(null),s=We(""),{top:a,firstload:l,distance:u}=n,{identifier:c,target:f}=Yf(n),d={infiniteLoading:i,top:a,firstload:l,distance:u,parentEl:null,emit(){o=(d.parentEl||document.documentElement).scrollHeight,p.loading(),t("infinite",p)}},p={loading(){s.value="loading"},async loaded(){s.value="loaded",await Gl(d,o),$v(i.value,d.parentEl)&&d.emit()},async complete(){s.value="complete",await Gl(d,o),r==null||r.disconnect()},error(){s.value="error"}};function v(){r==null||r.disconnect(),r=Mv(d)}return Rn(c,v),ur(async()=>{d.parentEl=await Fv(f),v()}),ro(()=>r==null?void 0:r.disconnect()),(w,m)=>($e(),He("div",{ref_key:"infiniteLoading",ref:i,class:"v3-infinite-loading"},[eo(oe("div",null,[sn(w.$slots,"spinner",{},()=>[Te(Wv)],!0)],512),[[bs,s.value=="loading"]]),s.value=="complete"?sn(w.$slots,"complete",{key:0},()=>{var b;return[oe("span",null,cn(((b=w.slots)==null?void 0:b.complete)||"No more results!"),1)]},!0):an("",!0),s.value=="error"?sn(w.$slots,"error",{key:1,retry:d.emit},()=>{var b;return[oe("span",Kv,[oe("span",null,cn(((b=w.slots)==null?void 0:b.error)||"Oops something went wrong!"),1),oe("button",{class:"retry",onClick:m[0]||(m[0]=(...x)=>d.emit&&d.emit(...x))},"retry")])]},!0):an("",!0)],512))}}),Gv=df(Vv,[["__scopeId","data-v-4bdee133"]]),Qv={class:"message-content"},Jv=to({__name:"message",props:{message:{type:String,required:!0},duration:{type:Number,default:2e3},background:{type:String,default:"#333"},textColor:{type:String,default:"#fff"},position:{type:String,default:"center"}},setup(e){const t=e,n=We(!1),r=We(!1),o=We(!1),i=We(window.innerWidth<=768),s=ut(()=>`message-${t.position}`);ur(()=>{n.value=!0,setTimeout(()=>{r.value=!0},10);const u=setTimeout(()=>{o.value=!0,setTimeout(()=>{n.value=!1},300)},t.duration);ro(()=>{clearTimeout(u)})});const a=()=>{o.value=!0,setTimeout(()=>{n.value=!1},300)},l=()=>{i.value&&a()};return(u,c)=>n.value?($e(),He("div",{key:0,class:bt(["message",[i.value?"message-mobile":"message-pc",s.value,{"message-fade-in":r.value,"message-fade-out":o.value}]]),style:Qr({backgroundColor:e.background,color:e.textColor}),onClick:l},[oe("div",Qv,[oe("span",null,cn(e.message),1)])],6)):an("",!0)}}),ns=Dn(Jv,[["__scopeId","data-v-85527d75"]]),Cr=document.createElement("div");document.body.appendChild(Cr);let nn=[],Ni=null;const Zv=5,Xv=window.innerWidth<=768;function Yv(){return(e,t={})=>{nn.push({content:e,options:t}),eb()}}function eb(){var e;if(Xv){if(Ni!==null)return;const{content:t="",options:n}=nn.shift(),r=Te(ns,{message:t,duration:n.duration||2e3,background:n.background||"#333",textColor:n.textColor||"#fff",position:n.position||"center"});Lo(r,Cr),Ni=setTimeout(()=>{Lo(null,Cr),Ni=null,nn=[]},((e=r.props)==null?void 0:e.duration)||2e3)}else{nn.length>Zv&&nn.shift();const{content:t,options:n}=nn[nn.length-1],r=Te(ns,{message:t,duration:n.duration||2e3,background:n.background||"#333",textColor:n.textColor||"#fff",position:n.position||"center"});Lo(r,Cr)}}const yr=Ud(Gh),tb=Yv();Array.prototype.at||(Array.prototype.at=function(e){return this[e]});yr.use(Lv);yr.component("infinite-loading",Gv);yr.component("v-message",ns);yr.config.globalProperties.$message=tb;yr.mount("#app");
