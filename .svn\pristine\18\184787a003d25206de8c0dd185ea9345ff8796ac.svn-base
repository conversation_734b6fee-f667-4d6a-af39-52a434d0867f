package com.shunwang.marketing.service.marketing;

import com.shunwang.marketing.pojo.Pageable;
import com.shunwang.marketing.pojo.marketing.PrizeRecord;
import com.shunwang.marketing.service.BaseService;

import java.util.Date;
import java.util.List;

public interface PrizeRecordService extends BaseService<PrizeRecord> {

    /**
     * 用户中奖列表
     * @return
     */
    List<PrizeRecord> getUserPrizeList(Pageable pageAble);

    /**
     * 根据支付订单查询订单是否已经抽过奖
     * @param orderNo
     * @return
     */
    int getByOrderNo(String orderNo);

    /**
     * 获取用户购买的某个福袋奖品
     * @param memberId
     * @param bagId
     * @return
     */
    List<PrizeRecord> getByBagIdAndUser(Integer memberId, Integer bagId);

    /**
     * 是否已经购买过某个礼包
     * @param bagId
     * @return
     */
    boolean isPurchased(Integer memberId, Integer bagId);

    /**
     * 获取奖池最近10条中奖记录
     * @param pondId
     * @return
     */
    List<PrizeRecord> lastPrizeRecord(Integer pondId);

    int getDayOrderCnt(Integer memberId);

    List<PrizeRecord> getUserAllPrizeList(Integer memberId);

    List<PrizeRecord> getUserPrize(Integer memberId,Integer id);

    /**
     * 用户省钱卡内包含的cdkey奖品记录
     * @param memberId
     * @param orderNo
     * @return
     */
    List<PrizeRecord> getMscCardCdkeyPrize(Integer memberId, Integer welfareId, String orderNo);

    /**
     * 用户从某个时间到现在抽中固定奖品的次数
     * @param prizeId
     * @param memberId
     * @param startTime
     * @return
     */
    Integer getCntByDayLimit(Integer prizeId, Integer memberId, Date startTime, String channel, String source);

    Integer getCntByMember(Integer memberId);

    /**
     * 获取用户此奖品的最后一条中奖记录
     * @param memberId
     * @param prizeId
     * @return
     */
    PrizeRecord myLastPrize(Integer memberId, Integer prizeId, Integer welfareId);

    Integer getUserPondPrizeCntToday(Integer memberId, Integer pondId, Date time);

    /**
     * 获取用户在某个奖池抽奖的次数
     * @param memberId
     * @param pondId
     * @return
     */
    Integer getUserPondPrizeCnt(Integer memberId, Integer pondId);
}
