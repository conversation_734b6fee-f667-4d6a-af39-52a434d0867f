package com.shunwang.marketing.service.marketing.impl;

import com.shunwang.marketing.dao.marketing.SurveyMapper;
import com.shunwang.marketing.dao.BaseMapper;
import com.shunwang.marketing.enums.marketing.SurveyEnum;
import com.shunwang.marketing.exception.CommonException;
import com.shunwang.marketing.pojo.marketing.Image;
import com.shunwang.marketing.pojo.marketing.SurveyVo;
import com.shunwang.marketing.service.marketing.ImageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import com.shunwang.marketing.service.marketing.SurveyService;
import com.shunwang.marketing.pojo.marketing.Survey;
import com.shunwang.marketing.service.impl.BaseServiceImpl;
import org.springframework.stereotype.Component;
@Component
@Slf4j
public class SurveyServiceImpl extends BaseServiceImpl<Survey> implements SurveyService {
    @Autowired
    private SurveyMapper surveyMapper;
    @Autowired
    ImageService imageService;
    @Override
    public BaseMapper getDao() {
        return surveyMapper;
    }


    @Override
    public Survey getIgnoreState(Integer id) {
        return surveyMapper.getIgnoreState(id);
    }

    @Override
    public Survey getAndCheckState(Integer id) {
        Survey survey = get(id);
        if (survey == null) {
            throw new CommonException(-10, "问卷不存在");
        }
        if (SurveyEnum.StateEnum.CLOSE.getValue().equals(survey.getState())) {
            log.error("问卷已下线:{}", id);
            throw new CommonException(-11, "问卷已下线");
        }
        return survey;
    }

    @Override
    public void populate(SurveyVo survey) {
        if (survey.getRewardType() != null
                && !SurveyEnum.RewardTypeEnum.NONE.getValue().equals(survey.getRewardType())
                &&survey.getRewardImgId()!=null) {
            Image image = imageService.get(survey.getRewardImgId());
            if (image!=null){
                survey.setRewardImg(image.getImagePath());
            }
        }
    }
}
