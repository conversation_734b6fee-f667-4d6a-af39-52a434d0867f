package com.shunwang.marketing.constant;

/**
 * 接口常量
 */
public abstract class InterfacesConstant {


    public static final int INTERFACE_KEY_HEAD_IMAGE_MODIFY = 1108;
    /** 电竞酒店手机号绑定 **/
    public static final int INTERFACE_KEY_HOTEL_BIND = 1113;

    //获取微信手机号
    public static final int INTERFACE_KEY_WEIXIN_GET_USER_PHONE_NUMBER = 1218;

    public static final int INTERFACE_KEY_SEND_WX_TEMPLATE_MSG = 1298;

    //微信AccessToken查询
    public static final int INTERFACE_KEY_INTERFACE_ACCESS_TOKEN_QUERY = 1299;
    public static final int INTERFACE_KEY_GET_TICKET_WHITOUT_LOGIN = 1301;
    public static final int INTERFACE_KEY_VEFIFY_LOGIN_STATUS = 1302;
    public static final int INTERFACE_KEY_LOGIN_AUTHORIZE = 1303;

    //tencent game
    public static final int TENCENT_GAME_TOKEN_TO_UID = 1310;
    public static final int TENCENT_GAME_GIVE_GAME_GIFT = 1311;

    public static final int PAOPAO_SEND_GIFT = 1321;
    public static final int ICLOUD_PC_GRANT_GOOD = 1322;

    //02、蝌蚪SSO单点登录接口
    public static final int INTERFACE_KEY_SSO_TICKET_CHECK = 5001;
    //获取微信授权地址
    public static final int INTERFACE_KEY_SSO_GET_OAUTH = 5003;

    public static final int PAY_List_QUERY=7002;

    //导入微信用户
    public static final int CREATE_BY_WX_UNIONID=7003;

    //导入微信用户
    public static final int CREATE_BY_WX_UNIONID_WELFARE=7008;

    //62.统一下单接口  create_unifiedorder
    public static final int INTERFACE_KEY_SWPAY_UNIFIED_ORDER = 7004;
    public static final int INTERFACE_KEY_SWPAY_REFUND = 7005;
    //06 关闭订单
    public static final int INTERFACE_KEY_CLOSE_ORDER = 7009;
    public static final int INTERFACE_KEY_USERAPI = 10000;
    public static final int INTERFACE_KEY_WEIXIN_DECRYPT = 10002;
    //腾讯39号接口下单
    public static final int INTERFACE_KEY_TENCENT_MOBILE_PAY = 10003;


}
