package com.shunwang.marketing.manager.request.game;

import com.shunwang.marketing.manager.HttpMethod;
import com.shunwang.marketing.manager.response.game.QueryCouponResponse;
import com.shunwang.marketing.pojo.config.Interfaces;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * 优惠券查询
 *
 */
@Data
@NoArgsConstructor
public class QueryCouponRequest extends BaseGameRequest<QueryCouponResponse>{
    private String orderNo;
    @Override
    public Map<String, String> buildParams() {
        return null;
    }

    @Override
    public Class<QueryCouponResponse> getResponseClass() {
        return QueryCouponResponse.class;
    }

    @Override
    public Integer getInterfaceKey() {
        return null;
    }

    @Override
    public void doInterfaceSetting(Interfaces setting) {

    }

    public QueryCouponRequest(String orderNo) {
        this.orderNo = orderNo;
    }

    @Override
    public Map<String,String> buildRequestParams() {
        Map<String,String> map=new HashMap<>();
        map.put("orderNo",getData());
        map.put("sign",getSign());
        return map;
    }

    @Override
    public String buildToSignStr() {
        return orderNo;
    }

    @Override
    public String getUrl() {
        return "https://newbag.swjoy.com/front/api/userCoupon/appId/state";
    }

    @Override
    public HttpMethod getHttpMethod() {
        return HttpMethod.POST;
    }

}
