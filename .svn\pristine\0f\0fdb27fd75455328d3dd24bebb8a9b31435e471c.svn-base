package com.shunwang.marketing.enums.marketing;

import lombok.Getter;
import lombok.Setter;

public interface PrizeEnum {

     enum PrizeTypeEnum {
         DIRECT(1,"直领"),
         CDKEY(2,"兑换码"),
         ESHOTEL_NOVEL(3,"电竞酒店直领"),
         VIRTUAL_QUALIFICATION(4,"虚拟资格"),
         RED_PACKET(5,"红包"),
         SCORE(8,"网费通"),

         GIFT_BAG(100, "礼包")
;
        PrizeTypeEnum(int value, String name) {
            this.value = value;
            this.name = name;
        }

        private @Getter @Setter int value;
        private @Getter @Setter String name;
    }



}
