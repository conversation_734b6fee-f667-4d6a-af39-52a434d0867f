package com.shunwang.marketing.result;

import com.shunwang.marketing.enums.GenericEnum;
import com.shunwang.marketing.pojo.BaseAggregate;

import java.math.BigDecimal;

/**
 * Created by bao.jin on 2020/2/19.
 */
public class ApiResult {
    private Integer code;
    private String msg;
    private Integer count;
    private BigDecimal totalAmount;
    private BigDecimal payoutAmount;
    private Object data;

    public ApiResult() {

    }

    public static ApiResult newSuccessResult() {
        ApiResult result = new ApiResult();
        result.code = GenericEnum.SUCCESS.getCode();
        return result;
    }

    public static ApiResult newSuccessResult(Object data) {
        ApiResult result = new ApiResult();
        result.code = GenericEnum.SUCCESS.getCode();
        result.setData(data);
        return result;
    }

    public static ApiResult newSuccessResult(Object data, Integer count) {
        ApiResult result = new ApiResult();
        result.code = GenericEnum.SUCCESS.getCode();
        result.setData(data);
        result.setCount(count);
        return result;
    }

    public static ApiResult newSuccessResult(Object data, Integer count, BigDecimal totalAmount) {
        ApiResult result = new ApiResult();
        result.code = GenericEnum.SUCCESS.getCode();
        result.setData(data);
        result.setCount(count);
        result.setTotalAmount(totalAmount);
        return result;
    }

    public static ApiResult newSuccessResult(Object data, BaseAggregate aggregate) {
        ApiResult result = new ApiResult();
        result.code = GenericEnum.SUCCESS.getCode();
        result.setData(data);
        result.setCount(aggregate.getCnt());
        return result;
    }

    public static ApiResult newErrorResult(String msg) {
        return newErrorResult(GenericEnum.FAIL.getCode(), msg);
    }

    public static ApiResult newErrorResult(int code, String msg) {
        return newErrorResult(code, msg, null);
    }

    public static ApiResult newErrorResult(int code, String msg, Object data) {
        ApiResult result = new ApiResult();
        result.code = code;
        result.setMsg(msg);
        result.setData(data);
        return result;
    }

    public Boolean isSuccess() {
        if (code.equals(GenericEnum.SUCCESS.getCode())) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }

    public BigDecimal getPayoutAmount() {
        return payoutAmount;
    }

    public void setPayoutAmount(BigDecimal payoutAmount) {
        this.payoutAmount = payoutAmount;
    }
}
