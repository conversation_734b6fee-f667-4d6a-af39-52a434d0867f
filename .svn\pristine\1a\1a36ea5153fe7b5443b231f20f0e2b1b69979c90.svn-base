package com.shunwang.marketing.query.marketing;

import com.shunwang.marketing.query.BaseQuery;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
@Data
@NoArgsConstructor
public class PromotionPopupWindowQuery extends BaseQuery {

	private Integer id;
	private String name;
	private Integer platform;
	private String channel;
	private Integer pages;
	private String pageId;
	private Integer type;
	private Integer kind;
	private String strategy;
	private Integer extensionId;
	private Integer sort;
	private Integer state;
	private String remark;
	private String userAdd;
	private String userEdit;
	private Date timeAdd;
	private Date timeEdit;

	/**
	 * 小程序
	 */
	public PromotionPopupWindowQuery(Integer platform, String channel, Integer pages, Integer kind) {
		this.platform = platform;
		this.channel = channel;
		this.pages = pages;
		this.kind = kind;
	}
}
