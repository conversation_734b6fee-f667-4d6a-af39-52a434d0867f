package com.shunwang.marketing.pojo.marketing;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.shunwang.marketing.constant.UploadContstant;
import com.shunwang.marketing.context.DomainContext;
import com.shunwang.marketing.enums.marketing.WelfareEnum;
import com.shunwang.marketing.pojo.BaseObject;
import com.shunwang.util.lang.StringUtil;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

import static com.shunwang.marketing.constant.UploadContstant.UPLOAD_DIRECTORY;

@Data
public class Welfare extends BaseObject {
	private static final long serialVersionUID = 1L;
	private Integer id;
	/**
	 * 名称
	 */
	private String name;
	/**
	 * 描述
	 */
	private String description;
	@JsonIgnore
	private Integer property;
	/**
	 * 类型 1:直领 2:兑换码 3:电竞酒店直领 4:虚拟资格 5:满减红包 6:端内自动发 7:活动页领取
	 */
	private Integer type;
	/**
	 * 说明
	 */
	private String explain;
	/**
	 * 内容
	 */
	@JsonIgnore
	private String contents;
	/**
	 * 价值
	 */
	private BigDecimal worth;
	private String img;
	private String url;
	/**
	 * 小程序端富文本详情
	 */
	private String details;
	private String remark;
	private String gameId;
	private String gameName;
	@JsonIgnore
	private Integer sort;
	@JsonIgnore
	private Date startTime;
	@JsonIgnore
	private Date endTime;
	@JsonIgnore
	private Integer cnt;
	@JsonIgnore
	private String userAdd;
	@JsonIgnore
	private Date timeAdd;
	@JsonIgnore
	private String userEdit;
	@JsonIgnore
	private Date timeEdit;
	private Integer businessLine;
	@JsonIgnore
	private Integer businessType;
	private Integer faceValue;
	private Integer limiting;
	private Integer needUnlocked;
	private Integer receiveState;
	/**
	 * 有效期，默认14天
	 */
	@JsonIgnore
	private Integer effectiveDays=14;
	/**
	 * web端富文本详情
	 */
	private String webDetail;

	private String duration;
	private Integer goodsId;
	/**
	 * 网费通数量
	 */
	private Integer scoreCnt;
	private Integer openType;
	private String backUrl;
	/**
	 * 第三方发放物id
	 */
	@JsonIgnore
	private String flowId;


	public Integer getReceiveState() {
		if (receiveState == null){
			return WelfareEnum.ReceiveStateEnum.NO_RECEIVE.getValue();
		}
		return receiveState;
	}

	public String getImg() {
		if (StringUtil.isBlank(img)) {
			return "";
		}
		return DomainContext.getBackStaticServer() + UPLOAD_DIRECTORY + UploadContstant.PATH_WELFARE_IMG + img;
	}

	@JsonIgnore
	public boolean isEffective() {
		Date now = new Date();
		return now.before(endTime) && now.after(startTime);
	}

}
