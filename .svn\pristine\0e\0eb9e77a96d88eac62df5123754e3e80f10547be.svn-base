<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shunwang.marketing.dao.marketing.RedPacketRecordMapper">
    <resultMap id="BaseResultMap" type="redPacketRecord">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="savingCardId" column="saving_card_id" jdbcType="INTEGER"/>
        <result property="welfareId" column="welfare_id" jdbcType="INTEGER"/>
        <result property="code" column="code" jdbcType="VARCHAR"/>
        <result property="memberId" column="member_id" jdbcType="INTEGER"/>
        <result property="moduleDetailId" column="module_detail_id" jdbcType="INTEGER"/>
        <result property="source" column="source" jdbcType="VARCHAR"/>
        <result property="state" column="state" jdbcType="INTEGER"/>
        <result property="sendTime" column="send_time" jdbcType="TIMESTAMP"/>
        <result property="expireTime" column="expire_time" jdbcType="TIMESTAMP"/>
        <result property="orderNo" column="order_no" jdbcType="VARCHAR"/>
        <result property="mscOrderNo" column="msc_order_no" jdbcType="VARCHAR"/>
        <result property="bagOrderNo" column="bag_order_no" jdbcType="VARCHAR"/>
        <result property="useTime" column="use_time" jdbcType="TIMESTAMP"/>
        <result property="bagId" column="bag_id" jdbcType="INTEGER"/>
        <result property="needUnlocked" column="need_unlocked" jdbcType="INTEGER"/>

        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="description" column="description" jdbcType="VARCHAR"/>
        <result property="explain" column="explain" jdbcType="VARCHAR"/>
        <result property="contents" column="contents" jdbcType="VARCHAR"/>
        <result property="details" column="details" jdbcType="LONGVARCHAR"/>
        <result property="img" column="img" jdbcType="VARCHAR"/>
        <result property="url" column="url" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="faceValue" column="face_value" jdbcType="INTEGER"/>
        <result property="limiting" column="limiting" jdbcType="INTEGER"/>
        <result property="gameId" column="game_id" jdbcType="VARCHAR"/>
        <result property="businessLine" column="business_line" jdbcType="INTEGER"/>
        <result property="webDetail" column="web_detail" jdbcType="VARCHAR"/>
        <result property="openType" column="open_type" jdbcType="INTEGER"/>
        <result property="backUrl" column="back_url" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="baseColumn">
        id,
        saving_card_id,
        welfare_id,
        code,
        member_id,
        module_detail_id,
        `source`,
        `state`,
        send_time,
        order_no,
        msc_order_no,
        bag_id,
        need_unlocked,
        use_time,
        expire_time
    </sql>
    <sql id="recordColumn">
        t.id,
        t.saving_card_id,
        t.welfare_id,
        t.code,
        t.member_id,
        t.source,
        t.state,
        t.send_time,
        t.order_no,
        t.msc_order_no,
        t.use_time,
        t.expire_time,
        t.bag_id,
        t.module_detail_id,
        t.need_unlocked,
        w.name,
        w.description,
        w.explain,
        w.contents,
        w.details,
        w.img,
        w.url,
        w.remark,
        w.face_value,
        w.limiting,
        w.business_line,
        w.game_id,
        w.web_detail,
        w.open_type,
        w.back_url
    </sql>
    <sql id="base_where" >
        <trim prefix="where" prefixOverrides="and|or">
            <if test="id != null ">
     	        and id = #{id,jdbcType=INTEGER}
            </if>
            <if test="savingCardId != null ">
     	        and saving_card_id = #{savingCardId,jdbcType=INTEGER}
            </if>
            <if test="welfareId != null ">
     	        and welfare_id = #{welfareId,jdbcType=INTEGER}
            </if>
            <if test="moduleDetailId != null ">
                and module_detail_id = #{moduleDetailId,jdbcType=INTEGER}
            </if>
            <if test="mscOrderNo != null ">
     	        and msc_order_no = #{mscOrderNo,jdbcType=VARCHAR}
            </if>
            <if test="memberId != null ">
     	        and member_id = #{memberId,jdbcType=INTEGER}
            </if>
            <if test="source != null ">
     	        and `source` = #{source,jdbcType=VARCHAR}
            </if>
            <if test="state != null ">
     	        and `state` = #{state,jdbcType=INTEGER}
            </if>
            <if test="bagId != null ">
                and bag_id = #{bagId,jdbcType=INTEGER}
            </if>
        </trim>
    </sql>
	<select id="find" parameterType="com.shunwang.marketing.query.BaseQuery" resultMap="BaseResultMap" >
		select
        <include refid="baseColumn" />
		from marketing_red_packet_record t
		<include refid="base_where" />
	</select>
    <select id="findOne" parameterType="com.shunwang.marketing.query.BaseQuery" resultMap="BaseResultMap" >
        select
        <include refid="baseColumn" />
        from marketing_red_packet_record t
        <include refid="base_where" />
        limit 1
    </select>
    <select id="findCnt" parameterType="com.shunwang.marketing.query.BaseQuery" resultType="java.lang.Integer" >
        select
            count(*)
        from marketing_red_packet_record t
		<include refid="base_where" />
    </select>
    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.Integer">
		select
        <include refid="baseColumn" />
		from marketing_red_packet_record
		where id = #{id,jdbcType=INTEGER}
    </select>
	<insert id="save" parameterType="redPacketRecord" useGeneratedKeys="true" keyProperty="id">
		insert into marketing_red_packet_record
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="savingCardId != null">
                saving_card_id,
            </if>
            <if test="welfareId != null">
                welfare_id,
            </if>
            <if test="code != null">
                code,
            </if>
            <if test="memberId != null">
                member_id,
            </if>
            <if test="source != null">
                `source`,
            </if>
            <if test="state != null">
                `state`,
            </if>
            <if test="orderNo != null">
                order_no,
            </if>
            <if test="mscOrderNo != null">
                msc_order_no,
            </if>
            <if test="bagOrderNo != null">
                bag_order_no,
            </if>
            <if test="bagId != null">
                bag_id,
            </if>
            <if test="needUnlocked != null">
                need_unlocked,
            </if>
            <if test="sendTime != null">
                send_time,
            </if>
            <if test="useTime != null">
                use_time,
            </if>
            <if test="expireTime != null">
                expire_time,
            </if>
            <if test="moduleDetailId != null">
                module_detail_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="savingCardId != null">
                #{savingCardId,jdbcType=INTEGER},
            </if>
            <if test="welfareId != null">
                #{welfareId,jdbcType=INTEGER},
            </if>
            <if test="code != null">
                #{code,jdbcType=VARCHAR},
            </if>
            <if test="memberId != null">
                #{memberId,jdbcType=INTEGER},
            </if>
            <if test="source != null">
                #{source,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                #{state,jdbcType=INTEGER},
            </if>
            <if test="orderNo != null">
                #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="mscOrderNo != null">
                #{mscOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="bagOrderNo != null">
                #{bagOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="bagId != null">
                #{bagId,jdbcType=INTEGER},
            </if>
            <if test="needUnlocked != null">
                #{needUnlocked,jdbcType=INTEGER},
            </if>
            <if test="sendTime != null">
                #{sendTime,jdbcType=TIMESTAMP},
            </if>
            <if test="useTime != null">
                #{useTime,jdbcType=TIMESTAMP},
            </if>
            <if test="expireTime != null">
                #{expireTime,jdbcType=TIMESTAMP},
            </if>
            <if test="moduleDetailId != null">
                #{moduleDetailId,jdbcType=INTEGER},
            </if>
        </trim>
	</insert>
	<update id="update" parameterType="redPacketRecord" >
		update marketing_red_packet_record
        <set>
            <if test="id != null">
     	        id = #{id,jdbcType=INTEGER},
            </if>
            <if test="savingCardId != null">
     	        saving_card_id = #{savingCardId,jdbcType=INTEGER},
            </if>
            <if test="welfareId != null">
     	        welfare_id = #{welfareId,jdbcType=INTEGER},
            </if>
            <if test="code != null">
     	        code = #{code,jdbcType=VARCHAR},
            </if>
            <if test="memberId != null">
     	        member_id = #{memberId,jdbcType=INTEGER},
            </if>
            <if test="source != null">
     	        `source` = #{source,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
     	        `state` = #{state,jdbcType=INTEGER},
            </if>
            <if test="orderNo != null">
                order_no = #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="mscOrderNo != null">
                msc_order_no = #{mscOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="bagOrderNo != null">
                bag_order_no = #{bagOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="sendTime != null">
     	        send_time = #{sendTime,jdbcType=TIMESTAMP},
            </if>
            <if test="useTime != null">
     	        use_time = #{useTime,jdbcType=TIMESTAMP},
            </if>
            <if test="expireTime != null">
                expire_time = #{expireTime,jdbcType=TIMESTAMP},
            </if>
        </set>
		<trim prefix="where" prefixOverrides="and|or">
            id = #{id,jdbcType=INTEGER}
        </trim>
	</update>
    <select id="getUserRedPacketList" resultMap="BaseResultMap">
        select
        <include refid="recordColumn" />
        from marketing_red_packet_record t
        left join marketing_welfare w on t.welfare_id = w.id
        where t.member_id = #{memberId,jdbcType=INTEGER}
        and t.`state` != 0
        and t.`state` != 3
        and t.bag_id is null
        order by t.id desc
        <if test="open">
            LIMIT #{first:INTEGER}, #{size:INTEGER}
        </if>
    </select>

    <select id="getUserAllRedPacketList" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select
        <include refid="recordColumn" />
        from marketing_red_packet_record t
        left join marketing_welfare w on t.welfare_id = w.id
        where t.member_id = #{memberId,jdbcType=INTEGER}
        and t.`state` = 1
        and NOW() >= w.start_time and NOW() &lt; w.end_time
        order by t.id desc
    </select>

    <select id="getUserRedPacket" resultMap="BaseResultMap">
        select
        <include refid="recordColumn" />
        from marketing_red_packet_record t
        left join marketing_welfare w on t.welfare_id = w.id
        where t.member_id = #{memberId,jdbcType=INTEGER}
        and t.`state` = 1
        and NOW() >= w.start_time and NOW() &lt; w.end_time and t.id=#{redPacketId,jdbcType=INTEGER}
    </select>

    <select id="getByMscOrderNoAndState" resultMap="BaseResultMap">
        select
        <include refid="baseColumn" />
        from marketing_red_packet_record t
        where t.msc_order_no = #{mscOrderNo,jdbcType=VARCHAR}
        and t.`state` = #{state,jdbcType=INTEGER}
        order by t.id desc
    </select>
    <select id="getCntByMember" resultType="java.lang.Integer">
        select count(*)
        from marketing_red_packet_record t
        where t.member_id = #{memberId,jdbcType=INTEGER}
          and t.`state` != 0
        and t.`state` != 3
        and t.bag_id is null
    </select>

    <update id="unLock">
        update marketing_red_packet_record
        set state=0
        where msc_order_no = #{mscOrderNo,jdbcType=VARCHAR}
          and state = 3
    </update>
</mapper>
