package com.shunwang.marketing.filter;

import com.google.gson.Gson;
import com.shunwang.baseStone.sso.exception.SSOException;
import com.shunwang.baseStone.sso.filter.LoginValidateFilter;
import com.shunwang.baseStone.sso.pojo.SSOUser;
import com.shunwang.marketing.cacheService.CacheKeyConstant;
import com.shunwang.marketing.cacheService.service.CacheService;
import com.shunwang.marketing.constant.SysconfigConstant;
import com.shunwang.marketing.context.DomainContext;
import com.shunwang.marketing.context.SessionContext;
import com.shunwang.marketing.context.UserContext;
import com.shunwang.marketing.dto.UserInfo;
import com.shunwang.marketing.manager.request.sso.TicketCheckRequest;
import com.shunwang.marketing.manager.response.sso.TicketCheckResponse;
import com.shunwang.marketing.manager.service.SsoServiceClient;
import com.shunwang.util.json.GsonUtil;
import com.shunwang.util.lang.StringUtil;
import com.shunwang.util.net.CookieUtil;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class UserLoginCheckFilter extends LoginValidateFilter {
    private static final Logger LOGGER = LoggerFactory.getLogger(UserLoginCheckFilter.class);

    private static final List<String> NO_NEED_FILTER_LIST = new ArrayList<String>();

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain chain) throws IOException,
            ServletException {
        if (isNeedLogin(servletRequest)) {
            HttpServletRequest request = (HttpServletRequest) servletRequest;
            HttpServletResponse response = (HttpServletResponse) servletResponse;
            String tockenId = request.getParameter("tockenId");
            String tockenid = request.getHeader("tockenid");
            String ticketId = request.getParameter("ticketId");
            //web登录，tockenId和ticketId都不为空，说明是sso的登录回调，调用sso接口去查询用户信息
            if (StringUtil.isNotBlank(tockenId) && StringUtil.isNotBlank(ticketId) && this.needCheckedTicket(request, response, ticketId)) {
                //未登录走sso登录
                if (!this.isTicketExist(request,ticketId)){
                    TicketCheckRequest ssoRequest = new TicketCheckRequest();
                    ssoRequest.setTicketId(ticketId);
                    ssoRequest.setTokenId(ticketId);
                    try {
                        TicketCheckResponse ssoResponse = SsoServiceClient.execute(ssoRequest);
                        if (response != null) {
                            UserInfo user = new UserInfo();
                            user.setMemberId(ssoResponse.getUserId());
                            user.setMemberName(ssoResponse.getUserName());
                            user.setNickName(ssoResponse.getNickName());
                            user.setOpenId(ssoResponse.getOpenId());
                            user.setHeadImg(ssoResponse.getHeadImg());
                            user.setAppId(SessionContext.get(UserContext.USER_APPID));
                            UserContext.setUser(user);
                            CacheService.getRedisOperation().set(CacheKeyConstant.USER_INFO_CACHE_PREFIX + ssoResponse.getUserId(),
                                    GsonUtil.toJson(user), 24 * 60 * 60);
                        }
                        this.saveTicket(request, response, ticketId);
                    } catch (SSOException var11) {
                        var11.printStackTrace();
                        this.handleException(request, response, var11);
                        return;
                    }
                    chain.doFilter(servletRequest, servletResponse);
                    return;
                }else{
                    //已登录
                    //用户信息存在放行
                    if (UserContext.getUser() != null) {
                        refushUser(response);
                        chain.doFilter(servletRequest, servletResponse);
                        return;
                    }else{
                        //用户信息不存在，去掉ticket和token参数回到原始页面并删除cookie
                        CookieUtil.setCookie(response, S_Tick_Name, "", "/", null, 0, false);
                        redirect(request, response);
                        return;
                    }
                }
            }
            //小程序登录
            UserInfo userInfo = CacheService.getRedisOperation().get(CacheKeyConstant.USER_INFO_CACHE_PREFIX + tockenid, UserInfo.class);
            if (StringUtil.isBlank(tockenid) || userInfo == null) {
                PrintWriter pw = response.getWriter();
                pw.print("loginFirst");
                pw.flush();
                pw.close();
                LOGGER.info("用户登录失败，重定向到[{}]", "/login");
                return;
            }
            UserContext.setUser(userInfo);
        }
        chain.doFilter(servletRequest, servletResponse);
    }

    private static void redirect(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String queryStr = request.getQueryString();
        if (queryStr.indexOf("ticketId") > 0) {
            if (queryStr.indexOf("&", queryStr.indexOf("ticketId")) > 0) {
                queryStr =
                        queryStr.substring(0, queryStr.indexOf("ticketId")) + queryStr.substring(queryStr.indexOf("&", queryStr.indexOf("ticketId")) + 1);
            } else {
                queryStr = queryStr.substring(0, queryStr.indexOf("ticketId"));
            }
        }
        String appServer = DomainContext.getAppServer();
        if (appServer.endsWith("/")) appServer = appServer.substring(0, appServer.length() - 1);
        response.sendRedirect(appServer + request.getRequestURI() + (StringUtil.isNotBlank(queryStr) ? ("?" + queryStr) : ""));
    }

    private boolean isNeedLogin(ServletRequest servletRequest) {
        HttpServletRequest request = (HttpServletRequest) servletRequest;
        String uri = request.getRequestURI();
        String tockenId = request.getParameter("tockenId");
        String ticketId = request.getParameter("ticketId");
        if (StringUtil.isNotBlank(tockenId) && StringUtil.isNotBlank(ticketId)) {
            return true;
        }

        return !NO_NEED_FILTER_LIST.contains(uri);
    }

    @Override
    protected void handleException(ServletRequest servletRequest,
                                   ServletResponse servletResponse, SSOException validateException)
            throws IOException {
        writeLog((HttpServletRequest) servletRequest, validateException);
        goReffer((HttpServletRequest) servletRequest, (HttpServletResponse) servletResponse, null);
    }

    @Override
    protected void handleNoTicket(ServletRequest servletRequest,
                                  ServletResponse servletResponse) {
        try {
            goReffer((HttpServletRequest) servletRequest, (HttpServletResponse) servletResponse, null);
        } catch (IOException e) {
            LOGGER.error("UserLoginCheckFilter handleNoTicket ERROR", e);
        }
    }

    @Override
    protected boolean isLogin(ServletRequest servletRequest) {
        return UserContext.getUserId() != null;
    }

    @Override
    protected void refushUser(HttpServletResponse resp) {
        UserContext.setUser(UserContext.getUser());
    }

    //调用saveUser(UserInfo userInfo)保存登录信息
    @Override
    protected void saveUser(HttpServletRequest servletRequest,
                            HttpServletResponse resp, SSOUser user) {
    }

    /**
     * 哪里来 返回哪里去
     *
     * @throws IOException
     * <AUTHOR> create at 2011-9-8 上午11:34:10
     */
    protected void goReffer(HttpServletRequest servletRequest, HttpServletResponse servletResponse, String paramsStr) throws IOException {
        // 判断是否为ajax请求，如果是ajax请求，直接返回
        String ajax = servletRequest.getHeader("X-Requested-With");
        String appServer = DomainContext.getAppServer();
        if (StringUtils.isNotBlank(ajax)) {
            LOGGER.warn("AJAX请求不允许重定向，返回响应码[302]");
            servletResponse.setHeader("X-timeout", "1");
            servletResponse.setStatus(302);
            servletResponse.getWriter().write(appServer + SysconfigConstant.FRONT_LOGIN_PAGE + "?msg=loginFirst");
            servletResponse.getWriter().close();
            return;
        }
        // 从哪里过来的，就要回到哪里去
        String reffer = servletRequest.getRequestURI();
        if (!isNeedRedirect(reffer)) {
            servletResponse.sendRedirect(appServer + SysconfigConstant.FRONT_LOGIN_PAGE + "?msg=loginFirst");
            LOGGER.info("用户登录失败，重定向到[{}]", reffer);
            return;
        }
        String queryStr = servletRequest.getQueryString();
        if (null != UserContext.getUserId()) { //防止死循环
            if (queryStr.indexOf("ticketId") > 0) {
                if (queryStr.indexOf("&", queryStr.indexOf("ticketId")) > 0) {
                    queryStr =
                            queryStr.substring(0, queryStr.indexOf("ticketId")) + queryStr.substring(queryStr.indexOf("&", queryStr.indexOf("ticketId")) + 1);
                } else {
                    queryStr = queryStr.substring(0, queryStr.indexOf("ticketId"));
                }
            }
        }
        reffer = StringUtils.isNotBlank(queryStr) ? (reffer + "?" + queryStr) : reffer;
        Map<String, String[]> param = servletRequest.getParameterMap();
        if ("POST".equals(servletRequest.getMethod())) {
            SessionContext.put("reffer_param", new Gson().toJson(param));
        }
        SessionContext.put("reffer", appServer + reffer);
        StringBuilder stringBuilder = new StringBuilder(appServer + SysconfigConstant.FRONT_LOGIN_PAGE
                + "?reffer_cache=true&time=1");
        if (!"/".equals(reffer)) {
            stringBuilder.append("&msg=loginFirst");
        }
        servletResponse.sendRedirect(stringBuilder.toString());
        LOGGER.info("用户登录失败，重定向到[{}]", reffer);
    }


    @SuppressWarnings("unchecked")
    protected String getParamStr(HttpServletRequest servletRequest) {
        Map<String, String[]> paramMap = servletRequest.getParameterMap();
        StringBuilder builder = new StringBuilder();
        for (Map.Entry<String, String[]> entry : paramMap.entrySet()) {
            for (String val : entry.getValue()) {
                if (val != null) {
                    try {
                        val = URLEncoder.encode(val, "UTF-8");
                    } catch (UnsupportedEncodingException e) {
                        LOGGER.error("UserLoginCheckFilter getParamStr ERROR", e);
                    }
                    builder.append(entry.getKey()).append("=").append(val).append("&");
                }
            }
        }
        return builder.toString();
    }

    /**
     * 记录日志
     *
     * @param servletRequest
     * @param e
     * <AUTHOR> create at 2011-9-8 下午01:56:24
     */
    protected void writeLog(HttpServletRequest servletRequest, Exception e) {
        String paramsStr = getParamStr(servletRequest);
        String logInfo = "{登录检查结果：false,用户IP：" + servletRequest.getRemoteAddr() + ",访问地址：" +
                servletRequest.getRequestURI() + ",参数列表：{" +
                paramsStr + "}}";
        LOGGER.info(logInfo, e);
    }

    /**
     * 是否需要重定向到原来的URL
     */
    public static Boolean isNeedRedirect(String url) {
        return !NO_NEED_FILTER_LIST.contains(url);
    }

    static {
        NO_NEED_FILTER_LIST.add("/login");
        NO_NEED_FILTER_LIST.add("/logout");
        NO_NEED_FILTER_LIST.add("/h5/welfare/indexData");
        NO_NEED_FILTER_LIST.add("/h5/welfare/index");
        NO_NEED_FILTER_LIST.add("/h5/welfare/receive");
        NO_NEED_FILTER_LIST.add("/h5/welfare/indexPage");
        NO_NEED_FILTER_LIST.add("/reachPage");
        NO_NEED_FILTER_LIST.add("/h5/prize/sysConfig");
        NO_NEED_FILTER_LIST.add("/h5/prize/jumpToMiniApp");
        NO_NEED_FILTER_LIST.add("/h5/prize/prizeRecord");
        NO_NEED_FILTER_LIST.add("/h5/prize/checkOrder");
        NO_NEED_FILTER_LIST.add("/weixin/userCodeLogin");
        NO_NEED_FILTER_LIST.add("/weixin/codeLogin");
        NO_NEED_FILTER_LIST.add("/weixin/getUserInfo");
        NO_NEED_FILTER_LIST.add("/weixin/miniAppSchemeUrl");
        NO_NEED_FILTER_LIST.add("/api/pay/callback");
        NO_NEED_FILTER_LIST.add("/bigData/report");
        NO_NEED_FILTER_LIST.add("/bigData/tencentReport");
        NO_NEED_FILTER_LIST.add("/printErrorMsg");
        NO_NEED_FILTER_LIST.add("/survey/goPage");
        NO_NEED_FILTER_LIST.add("/survey/endPage/goPage");
        NO_NEED_FILTER_LIST.add("/landingPage/pageDetail");
        NO_NEED_FILTER_LIST.add("/extension/netBar");
        NO_NEED_FILTER_LIST.add("/redisDel");
        NO_NEED_FILTER_LIST.add("/weixin/callback");
        //pc端接口
        NO_NEED_FILTER_LIST.add("/redPacketRecord/module");
        NO_NEED_FILTER_LIST.add("/member/get");
        NO_NEED_FILTER_LIST.add("/barPrivilege/toPage");
        NO_NEED_FILTER_LIST.add("/h5/welfare/getByGameId");
        NO_NEED_FILTER_LIST.add("/ai/goPage");
        NO_NEED_FILTER_LIST.add("/gameGuess/goPage");
    }

}
