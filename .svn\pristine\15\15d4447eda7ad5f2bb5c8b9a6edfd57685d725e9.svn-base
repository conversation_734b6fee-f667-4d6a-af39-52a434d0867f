package com.shunwang.marketing.context;

import com.shunwang.marketing.cacheService.RedisOperation;
import com.shunwang.util.json.GsonUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

@Component
public class SessionContext {

    private final static ConcurrentHashMap<String, String> sessionMap = new ConcurrentHashMap<>();
    private final static ThreadLocal<String> sessions = new ThreadLocal<String>();

    private static RedisOperation redisOperation;


    public static String getCacheId(String session) {
        String sb = "session[" +
                session +
                "]";
        return sb;
    }

    protected static ConcurrentHashMap<String, String> getMap() {
        String sessionId = sessions.get();
        if (sessionId == null) {
            return sessionMap;
        }
        ConcurrentHashMap<String, String> map = redisOperation.get(getCacheId(sessionId), ConcurrentHashMap.class);
        if (map == null) {
            map = new ConcurrentHashMap<>();
        }
        return map;
    }

    /**
     * 放置值
     *
     * @param key
     * @param value
     */
    public static void put(String key, Object value) {
        Objects.requireNonNull(value);
        ConcurrentHashMap<String, String> map = getMap();
        if (value instanceof Number) {
            map.put(key, String.valueOf(value));
        } else if (value instanceof String) {
            map.put(key, (String) value);
        } else {
            map.put(key, GsonUtil.toJson(value));
        }
        saveMap(map);
    }


    protected static void saveMap(ConcurrentHashMap<String, String> map) {
        String session = sessions.get();
        if (session != null) {
            redisOperation.set(getCacheId(session), map, 30, TimeUnit.MINUTES);
        }
    }

    /**
     * 取得值
     */
    public static String get(String key) {
        return getMap().get(key);
    }


    /**
     * 取得值
     */
    public static <T> T get(String key, Class<T> clazz) {
        return GsonUtil.fromJson(get(key), clazz);
    }


    /**
     * 删除值
     */
    public static String remove(String key) {
        ConcurrentHashMap<String, String> map = getMap();
        String ret = map.remove(key);
        saveMap(map);
        return ret;
    }

    public static void setSessionId(String session) {
        sessions.set(session);
    }

    @Autowired
    public void setRedisOperation(RedisOperation redisOperation) {
        SessionContext.redisOperation = redisOperation;
    }
}
