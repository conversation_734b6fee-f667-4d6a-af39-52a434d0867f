package com.shunwang.marketing.pojo.marketing;

import com.shunwang.marketing.pojo.BaseObject;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Date;
@Data
@Accessors(chain = true)
public class SignRecord extends BaseObject {
	private static final long serialVersionUID = 1L;
	private Integer id;
	private Integer memberId;
	private Integer day;
	private String channel;
	private Integer signConfigId;
	private Date signTime;

	private Integer signInId;
}
