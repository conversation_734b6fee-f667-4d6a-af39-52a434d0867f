package com.shunwang.marketing.pojo.sms;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.shunwang.marketing.pojo.BaseObject;
import lombok.Data;

import java.util.Date;

@Data
public class FunctionPage extends BaseObject {

    private Integer id;
    private String title;
    private String bgImg;
    private String btnImg;
    private String btnName;
    private String recBtnImg;
    private String recBtnName;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private String startTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private String endTime;
    private Integer isDel;
    private String userAdd;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date timeAdd;
    private String userEdit;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date timeEdit;
}
