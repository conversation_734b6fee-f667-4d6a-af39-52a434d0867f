package com.shunwang.marketing.service.impl;

import com.shunwang.marketing.dao.BaseMapper;
import com.shunwang.marketing.pojo.BaseObject;
import com.shunwang.marketing.query.BaseQuery;
import com.shunwang.marketing.service.BaseService;

import java.io.Serializable;
import java.util.List;

public abstract class BaseServiceImpl<Pojo extends BaseObject> implements BaseService<Pojo> {

    @Override
    public Pojo save(Pojo pojo) {
        int count = getDao().save(pojo);
        if(count > 0)
            return pojo;
        return null;
    }
    @Override
    public int update(Pojo pojo) {
        int count = getDao().update(pojo);
        return count;
    }
    @Override
    public Pojo get(Serializable id) {
        return getDao().get(id);
    }
    @Override
    public List<Pojo> find(BaseQuery query) {
        return getDao().find(query);
    }
    @Override
    public int findCnt(BaseQuery query) {
        return getDao().findCnt(query);
    }
    @Override
    public List<Pojo> findAll() {
        return getDao().findAll();
    }
    public abstract BaseMapper<Pojo> getDao();

    @Override
    public Pojo findOne(BaseQuery query) {
        return getDao().findOne(query);
    }
}
