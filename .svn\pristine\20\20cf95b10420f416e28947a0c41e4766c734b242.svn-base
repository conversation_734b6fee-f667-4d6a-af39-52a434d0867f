package com.shunwang.marketing.context;

import com.shunwang.marketing.dto.UserInfo;
import com.shunwang.util.lang.StringUtil;
import org.springframework.stereotype.Component;

@Component
public class UserContext {

    public static final String USER_ID = "User_Id_Key";
    public static final String USER_NAME = "User_Name_Key";
    public static final String USER_INFO = "User_Info";

    public static final String USER_APPID = "User_Appid";

    public static Integer getUserId() {
        if (SessionContext.get(USER_ID) == null) {
            return null;
        }
        return Integer.valueOf(SessionContext.get(USER_ID));
    }

    public static void setMobile(String mobile){
        UserInfo userInfo = SessionContext.get(USER_INFO, UserInfo.class);
        if (userInfo == null || StringUtil.isBlank(mobile)){
            return;
        }
        userInfo.setMobile(mobile);
        SessionContext.put(USER_INFO, userInfo);
    }

    public static String getUserName() {
        String userName = SessionContext.get(USER_NAME);
        if (null == userName) {
            return "";
        }
        return userName;
    }

    public static UserInfo getUserInfo() {
        return SessionContext.get(USER_INFO, UserInfo.class);
    }

    protected static void setUserId(Integer userId) {
        SessionContext.put(USER_ID, userId.toString());
    }

    protected static void setUserName(String userName) {
        SessionContext.put(USER_NAME, userName);
    }

    protected static void setUserInfo(UserInfo userInfo) {
        SessionContext.put(USER_INFO, userInfo);
    }

    public static UserInfo getUser() {
        return getUserInfo();
    }

    public static void setUser(UserInfo userInfo) {
        if (userInfo == null){
            return;
        }
        setUserId(userInfo.getMemberId());
        setUserName(userInfo.getMemberName());
        setUserInfo(userInfo);
    }

    public static void removeUser() {
        SessionContext.remove(USER_ID);
        SessionContext.remove(USER_NAME);
        SessionContext.remove(USER_INFO);
    }

}
