package com.shunwang.marketing.controller;

import com.shunwang.marketing.cacheService.CacheKeyConstant;
import com.shunwang.marketing.cacheService.RedisOperation;
import com.shunwang.marketing.context.DomainContext;
import com.shunwang.marketing.enums.marketing.StoreOrderEnum;
import com.shunwang.marketing.enums.marketing.WelfareEnum;
import com.shunwang.marketing.pojo.marketing.*;
import com.shunwang.marketing.result.Result;
import com.shunwang.marketing.result.ResultGenerator;
import com.shunwang.marketing.service.marketing.*;
import com.shunwang.marketing.util.MemberUtil;
import com.shunwang.util.lang.StringUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 网费通相关接口
 */
@RestController
@Slf4j
@RequestMapping("/score")
public class ScoreController {
    @Autowired
    ScoreService scoreService;
    @Autowired
    ScoreRecordService scoreRecordService;
    @Autowired
    StoreOrderService storeOrderService;
    @Autowired
    ModuleService moduleService;
    @Autowired
    RedisOperation redisOperation;
    @Autowired
    ModuleDetailsService moduleDetailsService;
    @Autowired
    ImageService imageService;
    @Autowired
    WelfareService welfareService;
    @Autowired
    RedPacketRecordService redPacketRecordService;
    @Autowired
    PrizeRecordService prizeRecordService;

    /**
     * 用户网费通信息
     *
     * @return
     */
    @RequestMapping("/member/info")
    public Result<Score> memberScoreInfo() {
        Integer memberId = MemberUtil.getNotNullMemberId();
        Score score = scoreService.getByMemberId(memberId);
        return ResultGenerator.newSuccessResult(score);
    }

    /**
     * 网费通收支明细
     *
     * @return
     */
    @RequestMapping("/account/detail")
    public Result accountDetail(ScoreRecord pageable) {
        Integer memberId = MemberUtil.getNotNullMemberId();
        pageable.setMemberId(memberId);
        Map<String, Object> result = scoreRecordService.getPageMap(pageable);
        return ResultGenerator.newSuccessResult(result);
    }

    /**
     * 兑换订单记录
     *
     * @return
     */
    @RequestMapping("/order/record")
    public Result orderRecord(StoreOrder pageable) {
        Integer memberId = MemberUtil.getNotNullMemberId();
        pageable.setMemberId(memberId);
        Map<String, Object> result = storeOrderService.getPageMap(pageable);
        return ResultGenerator.newSuccessResult(result);
    }

    /**
     * 兑换商品
     * @Param moduleDetailId 模块详情ID
     * @Param channel 渠道
     * @return type:1免费 2付费  body：仅在付费商品时有，用于拉起支付 orderNo:订单号，用于查询cdkey类型权益领取的cdkey
     */
    @RequestMapping("/product/exChange")
    public Result exChangeProduct(@RequestParam("moduleDetailId") Integer moduleDetailId, @RequestParam("channel") String channel) {
        if (StringUtil.isBlank(channel)) {
            return ResultGenerator.newSystemErrorResult("参数异常");
        }
        if (!redisOperation.setIfAbsent(CacheKeyConstant.STORE_PRODUCT_EXCHANGE + MemberUtil.getNotNullMemberId() + "_" + moduleDetailId, "0", 3, TimeUnit.SECONDS)) {
            return ResultGenerator.newSystemErrorResult("操作频繁");
        }
        return ResultGenerator.newSuccessResult(scoreService.exChange(moduleDetailId));
    }

    /**
     * 关闭订单
     *
     * @param orderId 订单ID
     * @return
     */
    @RequestMapping("/order/close")
    public Result closeOrder(@RequestParam("orderId") Integer orderId) {
        if (!redisOperation.setIfAbsent(CacheKeyConstant.STORE_CLOSE_ORDER + orderId, "0", 3, TimeUnit.SECONDS)) {
            return ResultGenerator.newSystemErrorResult("操作频繁");
        }
        storeOrderService.closeOrder(orderId);
        return ResultGenerator.newEmptySuccessResult();
    }

    /**
     * 查询网费通订单状态
     * @param orderNo 订单号
     * @return
     */
    @RequestMapping("/order/query")
    @ResponseBody
    public Result queryOrder(String orderNo) {
        if (StringUtil.isBlank(orderNo)) {
            return ResultGenerator.newSystemErrorResult("参数错误");
        }
        StoreOrder order = storeOrderService.getByOrderNo(orderNo);
        if (order == null) {
            return ResultGenerator.newSystemErrorResult("订单不存在");
        }

        if (!StoreOrderEnum.StateEnum.SUCCESS.getValue().equals(order.getState())) {
            return ResultGenerator.newSystemErrorResult("支付失败");
        }

        return ResultGenerator.newSuccessResult("支付成功");
    }

    /**
     * 查询订单奖励
     *
     * @param orderNo 订单号
     * @return
     */
    @RequestMapping("/order/prize")
    @ResponseBody
    public Result queryOrderPrize(String orderNo) {
        if (StringUtil.isBlank(orderNo)) {
            return ResultGenerator.newSystemErrorResult("参数错误");
        }
        StoreOrder order = storeOrderService.getByOrderNo(orderNo);
        if (order == null) {
            return ResultGenerator.newSystemErrorResult("订单不存在");
        }
        if (!StoreOrderEnum.StateEnum.SUCCESS.getValue().equals(order.getState())) {
            return ResultGenerator.newSystemErrorResult("订单未成功");
        }

        ModuleDetails moduleDetails = moduleDetailsService.get(order.getModuleDetailId());
        if (moduleDetails == null) {
            return ResultGenerator.newSystemErrorResult("详情不存在");
        }
        String saveId = redisOperation.get(CacheKeyConstant.STORE_ORDER_PRIZE + orderNo);
        if (StringUtil.isBlank(saveId)){
            return ResultGenerator.newEmptySystemErrorResult();
        }
        Welfare welfare = welfareService.get(moduleDetails.getWelfareId());
        if (WelfareEnum.TypeEnum.RED_PACKET.getValue().equals(welfare.getType())){
            return ResultGenerator.newSuccessResult(redPacketRecordService.getUserRedPacket(MemberUtil.getNotNullMemberId(),Integer.parseInt(saveId)));
        }else{
            return ResultGenerator.newSuccessResult(prizeRecordService.getUserPrize(MemberUtil.getNotNullMemberId(),Integer.parseInt(saveId)));
        }
    }

    /**
     * 商品信息
     *
     * @param moduleDetailId 模块详情ID
     * @return
     */
    @RequestMapping("/store/info")
    @ResponseBody
    public Result store(@RequestParam("moduleDetailId") Integer moduleDetailId) {
        ModuleDetails details = moduleDetailsService.get(moduleDetailId);
//        if (!ModuleDetailsEnum.TypeEnum.WELFARE.getValue().equals(details.getType())){
//            return ResultGenerator.newSystemErrorResult("非权益商品");
//        }
        StoreInfo storeInfo = new StoreInfo();
        BeanUtils.copyProperties(details,storeInfo);
        storeInfo.setMonthCnt(storeOrderService.monthCnt(moduleDetailId));
        imageService.populate(details.getProductImgId(),storeInfo::setProductImgPath);
        Welfare welfare = welfareService.get(details.getWelfareId());
        storeInfo.setType(welfare.getType());
        storeInfo.setWelfareName(welfare.getName());
        storeInfo.setDescription(welfare.getDescription());
        return ResultGenerator.newSuccessResult(storeInfo);

    }

    @Data
    static class StoreInfo {
        /**
         * 库存
         */
        private Integer cnt = 0;
        /**
         * 积分价格
         */
        private Integer scorePrice = 0;
        /**
         * 人民币价格
         */
        private BigDecimal rmbPrice = BigDecimal.ZERO;
        /**
         * 天数范围
         */
        private Integer dayLimit;
        /**
         * 兑换次数限制
         */
        private Integer cntLimit;
        /**
         * 标题
         */
        private String title;
        /**
         * 价值
         */
        private BigDecimal worth;
        /**
         * 标签，逗号分隔
         */
        private String tag;
        /**
         * 网费通商品图
         */
        private String productImgPath;
        /**
         * 月兑换
         */
        private Integer monthCnt;
        /**
         * 类型 1:直领 2:兑换码 3:电竞酒店直领 4:虚拟资格 5:满减红包 6:端内自动发 7:活动页领取
         */
        private Integer type;
        /**
         * 权益名
         */
        private String welfareName;
        /**
         * 描述
         */
        private String description;
        /**
         * 商品详情
         */
        private String productDetail;

        public String getProductImgPath() {
            if (StringUtil.isBlank(productImgPath)) {
                return "";
            }
            return DomainContext.getBackStaticUploadServer() + productImgPath;
        }
    }



}
