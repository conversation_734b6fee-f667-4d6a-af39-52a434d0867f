package com.shunwang.marketing.enums.personal;

import lombok.Getter;
import lombok.Setter;

public interface MemberEnum {

    int BINDER_LIMIT_EMAIL = 5;

     enum MemberTypeEnum {
         PERSONAL(1, "个人"),
         COMPANY(2, "企业"),
         MERCHANT(3, "商户"),
         ;
         MemberTypeEnum(int value, String name) {
            this.value = value;
            this.name = name;
        }

        private @Getter @Setter int value;
        private @Getter @Setter String name;
    }

    enum MemberSpecialTypeEnum {
        NON_MERCHANT(0, "非商户"),
        MERCHANT(1, "商户"),
        ;
        MemberSpecialTypeEnum(int value, String name) {
            this.value = value;
            this.name = name;
        }

        private @Getter @Setter int value;
        private @Getter @Setter String name;
    }

     enum MemberStateEnum {
         NATURAL(0, "启用"),
         DISABLED(1, "禁用"),
         TEMP(2, "临时"),
         CANCEL(3, "注销"),
         DELETE(4, "已删除"),
         ;
         MemberStateEnum(int value, String name) {
            this.value = value;
            this.name = name;
        }

        private @Getter @Setter Integer value;
        private @Getter @Setter String name;
    }

    enum BindStateEnum {
        MOBILE(1, "手机绑定"),
        EMAIL(2, "邮箱绑定"),
//        TEMP1(4, "暂未使用"),
        QUESTION(8, "密保问题"),
        DYNAMIC(16, "动态密钥"),
//        TEMP2(32, "暂未使用"),
        SAFE_NOTICE(64, "安全提醒"),
        MOBILE_LOGIN(128, "手机登录"),
        EMAIL_LOGIN(256, "邮箱登录"),
        ;
        BindStateEnum(int value, String name) {
            this.value = value;
            this.name = name;
        }

        private @Getter @Setter int value;
        private @Getter @Setter String name;
    }

    enum CertStateEnum {
        NO_COMMIT(0, "未申请"),
        PENDING_CHECK(1, "待审核"),
        PASS(2, "审核通过"),
        REFUSED(3, "已拒绝"),
        REVOKED(4, "已撤销"),
        UNBIND(5, "已解绑"),
        ;
        CertStateEnum(int value, String name) {
            this.value = value;
            this.name = name;
        }

        private @Getter @Setter int value;
        private @Getter @Setter String name;
    }

    enum WeakPwdStateEnum {
        WEAK(1, "弱口令"),
        STRONG(2, "正常"),
        ;
        WeakPwdStateEnum(int value, String name) {
            this.value = value;
            this.name = name;
        }

        private @Getter @Setter int value;
        private @Getter @Setter String name;
    }

    enum CancelNotifyType {
        TYPE_COMMIT_CANCEL(1, "提交注销通知"),
        TYPE_REVOKE_CANCEL(2, "撤销注销通知"),
        TYPE_REAL_CANCEL(3, "真实注销通知"),
        ;

        CancelNotifyType(Integer type, String name) {
            this.type = type;
            this.name = name;
        }
        private final int type;
        private final String name;
        public int getType() {
            return type;
        }
        public String getName() {
            return name;
        }
    }

    enum MobileChangeNotifyType {
        TYPE_CHANGE(1, "变更通知"),
        TYPE_UNBIND(2, "解绑通知"),
        ;

        MobileChangeNotifyType(Integer type, String name) {
            this.type = type;
            this.name = name;
        }
        private final int type;
        private final String name;
        public int getType() {
            return type;
        }
        public String getName() {
            return name;
        }
    }

}
