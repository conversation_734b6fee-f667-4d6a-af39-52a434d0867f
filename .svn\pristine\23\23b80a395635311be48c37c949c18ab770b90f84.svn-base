package com.shunwang.marketing.util;
import com.shunwang.util.encrypt.Md5Encrypt;
import com.shunwang.util.lang.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

public class SignTool {

    private final static Logger logger = LoggerFactory.getLogger(SignTool.class);
    /**
     * 参数分隔符
     */
    public final static String PARAM_SPLIT = "|";

    public static final String ENCODE = "UTF-8";


    /**
     * 获取签名原串:按字典排序参数值+key
     *
     * @param param        需要签名的参数
     * @param ignoredParam 需要忽略的参数名称
     * @param md5Key
     * @return 签名
     */
    public static String buildSignStringSortedMap(Map<String, String> param, String ignoredParam, String md5Key) {
        Map<String, String> sortedParamMap = new TreeMap<>();
        for (Map.Entry<String, String> entry : param.entrySet()) {
            if (entry.getKey().equals(ignoredParam)) {
                continue;
            }
            if (StringUtil.isBlank(entry.getValue())){
                entry.setValue( "" );
            }
            sortedParamMap.put(entry.getKey(), entry.getValue());
        }
        List<String> allList = new LinkedList<>(sortedParamMap.values());
        String source = String.join(PARAM_SPLIT, allList);
        logger.debug("签名原串[{}]", (source + PARAM_SPLIT + "md5key"));
        return source + PARAM_SPLIT + md5Key;
    }

    /**
     * 获取签名原串:按字典排序参数值+key
     *
     * @param param        需要签名的参数
     * @param ignoredParam 需要忽略的参数名称
     * @param md5Key
     * @return 签名
     */
    public static String buildSignStringSorted(Map<String, Collection<String>> param, String ignoredParam,
                                               String md5Key) {
        Map<String, String> sortedParamMap = new TreeMap<>();
        for (Map.Entry<String, Collection<String>> entry : param.entrySet()) {
            if (entry.getKey().equals(ignoredParam)) {
                continue;
            }
            sortedParamMap.put(entry.getKey(), String.join(PARAM_SPLIT, entry.getValue()));
        }
        List<String> allList = new LinkedList<>(sortedParamMap.values());
        String source = String.join(PARAM_SPLIT, allList);
        logger.debug("签名原串[{}]", (source + PARAM_SPLIT + "md5key"));
        return source + PARAM_SPLIT + md5Key;
    }

    public static String buildSignStringSorted1(Map<String, String[]> param, String ignoredParam,
                                               String md5Key) {
        Map<String, String> sortedParamMap = new TreeMap<>();
        for (Map.Entry<String, String[]> entry : param.entrySet()) {
            if (entry.getKey().equals(ignoredParam)) {
                continue;
            }
            sortedParamMap.put(entry.getKey(), String.join(PARAM_SPLIT, entry.getValue()));
        }
        List<String> allList = new LinkedList<>(sortedParamMap.values());
        String source = String.join(PARAM_SPLIT, allList);
        logger.debug("签名原串[{}]", (source + PARAM_SPLIT + "md5key"));
        return source + PARAM_SPLIT + md5Key;
    }


    public static void main(String[] args) {
        Map<String, String> params = new HashMap<>();
        params.put("unionId", "oZjbMwr8g2izLbI5Ckfz0Q4a1bXs");
        params.put("key", "24567");
        params.put("serviceId", "ai");
        params.put("time", "20231122141356");
        String ss = "24567|ai|20231122141356|oZjbMwr8g2izLbI5Ckfz0Q4a1bXs|7392192402769834";
        String s = buildSignStringSortedMap(params, "sign", "7392192402769834");
        System.out.println(s);
        System.out.println(Md5Encrypt.encrypt(s, "UTF-8").toLowerCase());

//        System.out.println(String.join(PARAM_SPLIT, "123"));
    }

    /**
     * 升级新版本
     *
     * @return
     */
    public static boolean isUpdateVersion(String version) {
        if ("1.0".equals(version)) {
            return true;
        }
        return false;
    }

}
