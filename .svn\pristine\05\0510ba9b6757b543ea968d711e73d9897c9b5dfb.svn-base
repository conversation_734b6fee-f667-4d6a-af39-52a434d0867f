package com.shunwang.marketing.service.privilege;

import com.shunwang.marketing.pojo.marketing.vo.BarPrivilegeVO;
import com.shunwang.marketing.pojo.marketing.vo.BarStatisticsVo;

import java.util.List;

/**
 * 创建日期：2024/9/27 13:48
 * 创建作者：胡严峻 (<EMAIL>)
 * 描　　述：
 *
 * @author: 胡严峻 (<EMAIL>)
 * @date: 2024/9/27 13:48
 * @version: 1.0.1
 */

public interface PrivilegeService {

    BarPrivilegeVO queryPrivilegeContent(Integer barId, List<Integer> resourceIdList);
    BarPrivilegeVO queryPrivilegeContent(Integer barId);
    BarStatisticsVo queryBarStatistics(Integer barId);
    List<String> imageResources();

}
