package com.shunwang.marketing.enums.marketing;

import lombok.Getter;
import lombok.Setter;

public interface CdkeyRecordEnum {

     enum StateEnum {
         NOT_USED(1, "未发放"),
         USED(2, "已发放"),
;
        StateEnum(int value, String name) {
            this.value = value;
            this.name = name;
        }

        private @Getter @Setter int value;
        private @Getter @Setter String name;
    }



}
