package com.shunwang.marketing.enums;

import lombok.Getter;

public interface ResourcesEnum {

    @Getter
    enum TypeName {
        //通用配置
        COMMON_URL_DOMAIN_WHITE_LIST_FOR_XSS("commonConfig", "urlDomainsWhiteList", "地址域名白名单"),
        COMMON_PARAMS_WHITE_LIST_FOR_XSS("commonConfig", "paramsWhiteListForXss", "xss传参白名单"),
        COMMON_VALUE_DANGER_LIST_FOR_XSS("commonConfig", "valueDangerListForXss", "参数值危险关键词黑名单"),
        COMMON_DANGER_CHAR_LIST_FOR_XSS("commonConfig", "dangerCharListForXss", "危险字符黑名单"),
        COMMON_DANGER_CHAR_LIMIT_FOR_XSS("commonConfig", "dangerCharLimitForXss", "危险字符黑名单"),
        COMMON_DANGER_CHAR_IS_OPEN("commonConfig", "dangerCharIsOpen", "危险字符开关"),
        COMMON_USER_NAME_PROTECTED_PREFIXS("commonConfig", "userNameProtectedPrefix", "用户名保留前缀"),
        URL_DOMAINS_WHITE_LIST("commonConfig","urlDomainsWhiteList","域白名单"),
        PPW_DETAIL_SWITCH("commonConfig","ppwDetailSwitch","推广弹窗策略详情日志开关"),

        COMMON_XSS_IS_OPEN("commonConfig", "xssIsOpen", "xss开关"),
        //大数据统一采集配置
        DATA_REPORT_SWITCH("dataReportConfig", "dataReportSwitch", "大数据统一采集开关"),

        //接口超时时间
        TIME_OUT_IMITATE("timeOutImitate", "timeOut", "超时时间"),

        WEIXIN_JSAPI_TICKET_URL("wxJsapiTicketUrlConfig", "wxJsapiTicketUrl", "获取微信jsapi的ticket调用地址"),
        //极验开关
        GEETEST_SWITCH("commonConfig", "geetestSwitch", "极验开关"),
        //网吧配置
        IP_BLACKLIST("barConfig", "ip_blackList", "ip黑名单"),
        BAR_ID_BLACKLIST("barConfig", "bar_id_blackList", "barId黑名单"),
        WX_PHONE_NUMBER_SWITCH("commonConfig", "userPhoneNumber", "微信授权获取手机号开关"),
        MINI_APP_LOGIN_SWITCH("barConfig", "miniAppLoginSwitch", "小程序登录开关"),
        POND_AD_SWITCH("barConfig", "pondAdSwitch", "小程序奖池广告开关"),
        //腾讯接口监控配置
        TENCENT_INTERFACE_MONITOR_LIMIT_CNT("tencentInterfaceMonitorConfig", "tencentInterfaceMonitorLimitCnt", "腾讯接口监控失败次数阈值"),
        TENCENT_INTERFACE_MONITOR_LIMIT_HOUR("tencentInterfaceMonitorConfig", "tencentInterfaceMonitorLimitHour", "腾讯接口监控失败统计时间范围（小时）"),
        AI_USER_AGREEMENT("aiConfig", "userAgreement", "顺网用户协议"),
        AI_PRIVACY("aiConfig", "privacy", "隐私协议"),
        AI_AI_AGREEMENT("aiConfig", "aiAgreement", "ai条款"),
        EMAIL_RECEIVER("aiConfig", "emailReceiver", "邮件接受者"),
        SMS_RECEIVER("aiConfig", "smsReceiver", "短信接收者"),
        AI_PROMPT("aiConfig", "aiPrompt", "ai提示词"),
        AI_FALLBACK("aiConfig", "aiFallback", "ai消息失败返回消息"),
        ;
        String type;
        String name;
        String nameCn;
        TypeName(String type,String name, String nameCn) {
            this.type = type;
            this.name = name;
            this.nameCn = nameCn;
        }
    }

    @Getter
    enum StateEnum {
        DISABLE(0, "关闭"),
        ENABLE(1, "可用");

        StateEnum(int value, String name) {
            this.value = value;
            this.name = name;
        }

        private int value;
        private String name;

    }

}
