package com.shunwang.marketing.service.marketing.impl;

import com.shunwang.marketing.query.BaseQuery;
import com.shunwang.marketing.query.marketing.ContentInfoQuery;
import com.shunwang.marketing.service.marketing.ContentInfoService;
import com.shunwang.marketing.dao.BaseMapper;
import org.springframework.beans.factory.annotation.Autowired;
import com.shunwang.marketing.pojo.marketing.ContentInfo;
import com.shunwang.marketing.dao.marketing.ContentInfoMapper;
import com.shunwang.marketing.service.impl.BaseServiceImpl;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class ContentInfoServiceImpl extends BaseServiceImpl<ContentInfo> implements ContentInfoService {
    @Autowired
    private ContentInfoMapper contentInfoMapper;
    @Override
    public BaseMapper<ContentInfo> getDao() {
        return contentInfoMapper;
    }


    /**
     * 根据配置ID查找内容信息列表。
     *
     * @param configId 配置的ID，用于查询相关的内容信息。
     * @return 返回一个内容信息列表，这些信息与指定的配置ID相关联。
     */
    public List<ContentInfo> findByConfigId(Integer configId) {
        ContentInfoQuery query = new ContentInfoQuery();
        query.setConfigId(configId);
        return find(query);
    }
}
