package com.shunwang.marketing.query.marketing;

import com.shunwang.marketing.query.BaseQuery;
import lombok.Data;

@Data
public class ResourcePrivilegeContentQuery extends BaseQuery {

	private Integer id;
	private Integer level;
	private Integer privilegeType;
	private Integer privilegeRelateId;
	private Integer resourcePrivilegeId;
	private String hoverImg; // hover图
	private String description; // 描述
	private Integer type; // 类型 1 图标 2卡牌
	private Integer openType; // 打开方式
	private String openUrl; // 打开链接
	private String detail; // 具体介绍
	private String showImg; // 展示图片
	private String args; // 启动参数
	private java.util.Date startTime; // 开始时间
	private java.util.Date endTime; // 结束时间
	private Integer buss; // 业务归属 1云海菜单 2云菜单
	private String remark; // 备注


}
