package com.shunwang.marketing.cacheService.service;


import com.shunwang.marketing.cacheService.RedisOperation;
import com.shunwang.marketing.cacheService.lock.CounterLock;
import com.shunwang.marketing.cacheService.lock.TimedLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalTime;

@Component
public class CacheService {
    public final static LocalTime dayEnd = LocalTime.of(23, 59, 59);


    static RedisOperation redisOperation;

    public static RedisOperation getRedisOperation() {
        return redisOperation;
    }

    @Autowired
    public void setRedisOperation(RedisOperation redisOperation) {
        CacheService.redisOperation = redisOperation;
    }

    public static TimedLock newTimedLock(String key, Duration duration) {
        TimedLock timedLock = new TimedLock(key, duration);
        timedLock.setRedisOperation(getRedisOperation());
        return timedLock;
    }

    public static CounterLock newCounterLock(String key, int maxCount) {
        return newCounterLock(key, maxCount, tillDayEnd());
    }

    public static CounterLock newCounterLock(String key, int maxCount, Duration duration) {
        CounterLock counterLock = new CounterLock(key, maxCount, duration);
        counterLock.setRedisOperation(getRedisOperation());
        return counterLock;
    }

    public static Duration tillDayEnd() {
        return Duration.between(LocalTime.now(), dayEnd);
    }


}
