package com.shunwang.marketing.controller;

import com.shunwang.marketing.dto.UserInfo;
import com.shunwang.marketing.enums.marketing.BussMemberRelationEnum;
import com.shunwang.marketing.enums.marketing.SubMsgEnum;
import com.shunwang.marketing.enums.marketing.SubRecordEnum;
import com.shunwang.marketing.exception.CommonException;
import com.shunwang.marketing.pojo.marketing.SubMsg;
import com.shunwang.marketing.pojo.marketing.SubRecord;
import com.shunwang.marketing.query.marketing.SubMsgQuery;
import com.shunwang.marketing.query.marketing.SubRecordQuery;
import com.shunwang.marketing.result.Result;
import com.shunwang.marketing.result.ResultGenerator;
import com.shunwang.marketing.service.bigData.BigDataService;
import com.shunwang.marketing.service.marketing.BussMemberRelationService;
import com.shunwang.marketing.service.marketing.MemberTagService;
import com.shunwang.marketing.service.marketing.SubMsgService;
import com.shunwang.marketing.service.marketing.SubRecordService;
import com.shunwang.marketing.util.MemberUtil;
import com.shunwang.util.lang.StringUtil;
import lombok.Data;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.StringJoiner;
import java.util.stream.Collectors;

/**
 * 订阅消息接口
 */
@RestController
@RequestMapping("subRecord")
public class SubRecordController {
    @Autowired
    SubRecordService subRecordService;
    @Autowired
    SubMsgService subMsgService;
    @Autowired
    BigDataService bigDataService;
    @Value("${bigDataReport.bussId.mini}")
    private String businessIdMini;
    @Autowired
    MemberTagService memberTagService;
    @Autowired
    BussMemberRelationService bussMemberRelationService;

    /**
     * 用户订阅状态
     *
     * @param param
     * @return 需要展示的模板ID列表
     */
    @RequestMapping("/subState")
    public Result<SubRecordVo> subState(@RequestBody SubRecordDto param) {
        check(param);
        UserInfo userInfo = MemberUtil.getNotNullUserInfo();
        SubRecordVo res = new SubRecordVo();

        List<SubMsg> subMsgList = subMsgService.findByPage(param.forPage);

        List<String> templateIdList = subMsgList.stream().filter((subMsg) -> {
            boolean isImportUser = bussMemberRelationService.findOne(null, BussMemberRelationEnum.BussTypeEnum.SUB_MSG.getValue(), subMsg.getId()) != null;
            //无定向
            if (StringUtil.isBlank(subMsg.getTags()) && !isImportUser) {
                return needShowSub(subMsg.getTemplateId(), userInfo);
            }else{
                if (StringUtil.isNotBlank(subMsg.getTags()) && memberTagService.isMatching(subMsg.getTags(), userInfo.getMemberId())) {
                    return needShowSub(subMsg.getTemplateId(), userInfo);
                }
                if (isImportUser && bussMemberRelationService.findOne(MemberUtil.getNotNullMemberId(), BussMemberRelationEnum.BussTypeEnum.SUB_MSG.getValue(), subMsg.getId()) != null) {
                    return needShowSub(subMsg.getTemplateId(), userInfo);
                }
                //有定向但不满足定向直接过滤
                return false;
            }
        }).map(SubMsg::getTemplateId).collect(Collectors.toList());


        res.setTemplateIdList(templateIdList);
        res.setShowTips(needShowTips(param, templateIdList));
        if (res.showTips) {
            bigDataService.bigDataReport(buildReportBody(), businessIdMini);
        }
        return ResultGenerator.newSuccessResult(res);
    }

    @NotNull
    private boolean needShowTips(SubRecordDto param, List<String> templateIdList) {
        if (CollectionUtils.isEmpty(templateIdList)) {
            return false;
        } else {
            //如果需要弹窗的所有模板 用户都设置了不再询问，则不展示图片
            return param.userSetting == null || CollectionUtils.isEmpty(param.userSetting.itemSettings) ||
                    !templateIdList.stream().allMatch((template) -> param.userSetting.itemSettings.containsKey(template));
        }
    }

    private void check(SubRecordDto param) {
        if (StringUtil.isBlank(param.getForPage())){
            throw new CommonException("缺少必要参数");
        }
    }

    private String buildReportBody() {
        StringJoiner joiner = new StringJoiner("\t");
        joiner.add("submsg-show").add("pv").add("").add("").add(MemberUtil.getNotNullUserInfo().getOpenId()).add("");
        return joiner.toString();
    }

    @NotNull
    private boolean needShowSub(String templateId, UserInfo userInfo) {
        SubRecordQuery query = new SubRecordQuery();
        query.setOpenId(userInfo.getOpenId());
        query.setTemplateId(templateId);
        SubRecord record = subRecordService.findOne(query);
        return record == null || record.getTimes() <= 0;
    }

    @Data
    public static class SubRecordDto {
        /**
         * 位置 1网费查询 2点击抽奖
         */
        private String forPage;

        /**
         * 用户设置了不再询问的模板ID，如果需要展示的模板ID都是设置了不再询问的模板ID或者已经存在发送次数，那么返回的showTips为false
         */
        private SubSetting userSetting;

        @Data
        static class SubSetting {
            private boolean mainSwitch;
            private Map<String, String> itemSettings;
        }
    }

    @Data
    public static class SubRecordVo {
        /**
         * 需要请求展示的模板ID
         */
        private List<String> templateIdList;
        /**
         * 是否展示引导图片
         */
        private boolean showTips;
    }

}
