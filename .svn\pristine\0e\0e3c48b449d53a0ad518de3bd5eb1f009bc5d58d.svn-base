package com.shunwang.marketing.service.weixin.impl;

import com.shunwang.marketing.enums.marketing.SubMsgEnum;
import com.shunwang.marketing.enums.marketing.SubRecordEnum;
import com.shunwang.marketing.manager.response.weixin.WxMessage;
import com.shunwang.marketing.manager.service.UserApiServiceClient;
import com.shunwang.marketing.pojo.marketing.NewSubRecord;
import com.shunwang.marketing.pojo.marketing.SubMsg;
import com.shunwang.marketing.service.bigData.BigDataService;
import com.shunwang.marketing.service.marketing.MemberTagService;
import com.shunwang.marketing.service.marketing.NewSubRecordService;
import com.shunwang.marketing.service.marketing.SubMsgService;
import com.shunwang.marketing.service.weixin.WxService;
import com.shunwang.userapi.client.model.common.WxOpenIdUnionIdQueryResponse;
import com.shunwang.userapi.client.model.user.UserQueryByOutSiteRequest;
import com.shunwang.userapi.client.model.user.UserQueryResponse;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.StringJoiner;
import java.util.stream.Collectors;

@Component
@Slf4j
public class WxServiceImpl implements WxService {
    @Autowired
    NewSubRecordService newSubRecordService;
    @Autowired
    BigDataService bigDataService;
    @Value("${bigDataReport.bussId.mini}")
    private String businessIdMini;
    @Value("${weixin.jscode2session.appId}")
    private String appId;
    @Autowired
    UserApiServiceClient userApiServiceClient;
    @Autowired
    MemberTagService memberTagService;
    @Autowired
    SubMsgService subMsgService;

    @Override
    public void process(WxMessage wxMessage) {
        if (WxMessage.EVENT_SUB_MSG_POPUP.equals(wxMessage.getEvent())) { //订阅消息事件
            processMsgPopupEvent(wxMessage);
        } else if (WxMessage.EVENT_MSG_CHANGE.equals(wxMessage.getEvent())) {
            //改变事件，事件只会接收到用户拒绝接收消息的信息，只有用户选择不再询问后才能触发此事件,不处理
        } else if (WxMessage.EVENT_MSG_SENT.equals(wxMessage.getEvent())) { //发送通知成功事件，上报大数据
            processMsgSentEvent(wxMessage);
        }
    }

    private void processMsgSentEvent(WxMessage wxMessage) {
        if (!CollectionUtils.isEmpty(wxMessage.getList())) {
            wxMessage.getList().forEach((subMsgState -> {
                String templateId = subMsgState.getTemplateId();
                bigDataService.bigDataReport(buildSendBody(templateId, wxMessage.getFromUserName()), businessIdMini);
            }));
        }
    }

    private String buildSendBody(String templateId, String openId) {
        StringJoiner joiner = new StringJoiner("\t");
        joiner.add("submsg-send").add("click").add(templateId).add("").add(openId).add("");
        return joiner.toString();
    }

    private void processMsgPopupEvent(WxMessage wxMessage) {
        if (!CollectionUtils.isEmpty(wxMessage.getList())) {
            wxMessage.getList().forEach((subMsgState -> {
                String templateId = subMsgState.getTemplateId();
                String status = subMsgState.getSubscribeStatusString();
                //只保存用户接受的记录
                if (WxMessage.SubMsgState.SUB_STATUS_ACCEPT.equals(status)) {
                    saveRecord(wxMessage, templateId);
                }
                bigDataService.bigDataReport(buildPopupBody(status, templateId, wxMessage.getFromUserName()), businessIdMini);
            }));
        }
    }

    private void saveRecord(WxMessage wxMessage, String templateId) {
        NewSubRecord subRecord = new NewSubRecord();
        subRecord.setOpenId(wxMessage.getFromUserName());
        subRecord.setTemplateId(templateId);
        subRecord.setSubTime(new Date());
        subRecord.setSubType(SubMsgEnum.SubTypeEnum.ONCE.getValue()); //目前只有这个
        subRecord.setState(SubRecordEnum.StateEnum.ACCEPT.getValue());
        try {
            //关联memberId,匹配需要发送的订阅消息
            WxOpenIdUnionIdQueryRequest wxOpenIdUnionIdQueryRequest = new WxOpenIdUnionIdQueryRequest();
            wxOpenIdUnionIdQueryRequest.setWechatAppId(appId);
            wxOpenIdUnionIdQueryRequest.setOpenId(wxMessage.getFromUserName());
            WxOpenIdUnionIdQueryResponse wxOpenIdUnionIdQueryResponse = userApiServiceClient.getOpenIdUnionId(wxOpenIdUnionIdQueryRequest);

            UserQueryByOutSiteRequest userQueryByOutSiteRequest = new UserQueryByOutSiteRequest();
            userQueryByOutSiteRequest.setOutMemberId(wxOpenIdUnionIdQueryResponse.getUnionId());
            UserQueryResponse member = userApiServiceClient.getUserByOutSite(userQueryByOutSiteRequest);
            subRecord.setMemberId(member.getMemberId());
        } catch (Exception e) {
            log.error("获取微信用户关联信息失败", e);
        }


        //所有订阅消息，根据排序进行匹配，发送优先匹配到的消息
        List<SubMsg> subMsgList = subMsgService.findByTemplateId(templateId);
        subMsgList.sort(Comparator.comparingInt(SubMsg::getSort));
        if (subRecord.getMemberId() != null) {
            List<SubMsg> result = subMsgList.stream().filter((subMsg -> memberTagService.isMatching(subMsg.getTags(), subRecord.getMemberId()))).limit(1).collect(Collectors.toList());
            subRecord.setSubMsgId(CollectionUtils.isEmpty(result) ? subMsgList.get(subMsgList.size() - 1).getId() : result.get(0).getId());
        } else {
            subRecord.setSubMsgId(subMsgList.get(subMsgList.size() - 1).getId());
            subRecord.setRemark("关联信息失败");
        }
        newSubRecordService.save(subRecord);
    }

    private String buildPopupBody(String status, String templateId, String openId) {
        StringJoiner joiner = new StringJoiner("\t");
        joiner.add(WxMessage.SubMsgState.SUB_STATUS_ACCEPT.equals(status) ? "submsg-agree" : "submsg-reject").add("click").add(templateId).add("").add(openId).add("");
        return joiner.toString();
    }

    @Data
    public class WxOpenIdUnionIdQueryRequest extends com.shunwang.userapi.client.model.common.WxOpenIdUnionIdQueryRequest {
        private String openId;
    }
}
