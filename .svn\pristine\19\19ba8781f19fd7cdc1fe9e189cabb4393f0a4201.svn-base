package com.shunwang.marketing.enums.marketing;

import lombok.Getter;

public interface AiModelEnum {

    enum PlatformEnum {
        HUOSHAN(1, "火山"),
        AI_DEPARTMENT(2, "AI部门");

        PlatformEnum(int value, String name) {
            this.value = value;
            this.name = name;
        }

        private @Getter Integer value;
        private @Getter String name;
    }

    enum StateEnum {
        ONLINE(1, "上线"),
        OFFLINE(2, "下线");

        StateEnum(int value, String name) {
            this.value = value;
            this.name = name;
        }

        private @Getter Integer value;
        private @Getter String name;
    }

} 
