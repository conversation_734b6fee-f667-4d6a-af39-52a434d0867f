package com.shunwang.marketing.service.marketing.impl;

import com.shunwang.marketing.dao.BaseMapper;
import com.shunwang.marketing.dao.marketing.ResourcePrivilegeMapper;
import com.shunwang.marketing.enums.marketing.ResourcePrivilegeEnum;
import com.shunwang.marketing.pojo.marketing.ResourcePrivilege;
import com.shunwang.marketing.query.marketing.ResourcePrivilegeQuery;
import com.shunwang.marketing.service.impl.BaseServiceImpl;
import com.shunwang.marketing.service.marketing.ResourcePrivilegeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class ResourcePrivilegeServiceImpl extends BaseServiceImpl<ResourcePrivilege> implements ResourcePrivilegeService {
    @Autowired
    private ResourcePrivilegeMapper resourcePrivilegeMapper;


    @Override
    public BaseMapper<ResourcePrivilege> getDao() {
        return resourcePrivilegeMapper;
    }

    @Override
    @Cacheable(value = "cache_10_minutes", unless = "#result==null or #result.size()==0", key = "'m_online_resource_privileges'")
    public List<ResourcePrivilege> findAllOnline() {
        ResourcePrivilegeQuery resourcePrivilegeQuery = new ResourcePrivilegeQuery();
        resourcePrivilegeQuery.setOnlineState(ResourcePrivilegeEnum.OnlineStateEnum.ONLINE.getValue());
        return resourcePrivilegeMapper.find(resourcePrivilegeQuery);
    }
}
