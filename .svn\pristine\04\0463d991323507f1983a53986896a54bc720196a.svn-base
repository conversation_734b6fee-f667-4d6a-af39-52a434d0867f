<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shunwang.marketing.dao.marketing.PondStyleMapper">
    <resultMap id="BaseResultMap" type="pondStyle">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="channel" column="channel" jdbcType="VARCHAR"/>
        <result property="state" column="state" jdbcType="INTEGER"/>
        <result property="bgImg" column="bg_img" jdbcType="VARCHAR"/>
        <result property="bgImgMsg" column="bg_img_msg" jdbcType="VARCHAR"/>
        <result property="titleImg" column="title_img" jdbcType="VARCHAR"/>
        <result property="adImg" column="ad_img" jdbcType="VARCHAR"/>
        <result property="borderImg" column="border_img" jdbcType="VARCHAR"/>
        <result property="btnImg" column="btn_img" jdbcType="VARCHAR"/>
        <result property="btnImgMsg" column="btn_img_msg" jdbcType="VARCHAR"/>
        <result property="pageAdImg" column="page_ad_img" jdbcType="VARCHAR"/>
        <result property="pageUrl" column="page_url" jdbcType="VARCHAR"/>
        <result property="adUrl" column="ad_url" jdbcType="VARCHAR"/>
        <result property="adSwitch" column="ad_switch" jdbcType="INTEGER"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="rule" column="rule" jdbcType="VARCHAR"/>
        <result property="userAdd" column="user_add" jdbcType="VARCHAR"/>
        <result property="timeAdd" column="time_add" jdbcType="TIMESTAMP"/>
        <result property="userEdit" column="user_edit" jdbcType="VARCHAR"/>
        <result property="timeEdit" column="time_edit" jdbcType="TIMESTAMP"/>

        <result property="shareTitle" column="share_title" jdbcType="VARCHAR"/>
        <result property="shareImg" column="share_img" jdbcType="VARCHAR"/>
        <result property="bgColor" column="bg_color" jdbcType="VARCHAR"/>
        <result property="undercountImg" column="undercount_img" jdbcType="VARCHAR"/>
        <result property="welfareImg" column="welfare_img" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="baseColumn">
        id,
        `name`,
        channel,
        `state`,
        bg_img,
        bg_img_msg,
        title_img,
        ad_img,
        border_img,
        btn_img,
        btn_img_msg,
        page_ad_img,
        page_url,
        ad_url,
        ad_switch,
        share_title,
        share_img,
        undercount_img,
        welfare_img,
        bg_color,
        remark,
        `rule`,
        user_add,
        time_add,
        user_edit,
        time_edit
    </sql>
    <sql id="base_where" >
        <trim prefix="where" prefixOverrides="and|or">
            <if test="id != null ">
     	        and id = #{id,jdbcType=INTEGER}
            </if>
            <if test="name != null and name != ''">
     	        and `name` = #{name,jdbcType=VARCHAR}
            </if>
            <if test="channel != null ">
     	        and channel = #{channel,jdbcType=VARCHAR}
            </if>
            <if test="state != null ">
     	        and `state` = #{state,jdbcType=INTEGER}
            </if>
            <if test="bgImg != null ">
     	        and bg_img = #{bgImg,jdbcType=VARCHAR}
            </if>
            <if test="titleImg != null ">
     	        and title_img = #{titleImg,jdbcType=VARCHAR}
            </if>
            <if test="adImg != null ">
     	        and ad_img = #{adImg,jdbcType=VARCHAR}
            </if>
            <if test="adUrl != null ">
     	        and ad_url = #{adUrl,jdbcType=VARCHAR}
            </if>
            <if test="adSwitch != null ">
     	        and ad_switch = #{adSwitch,jdbcType=INTEGER}
            </if>
            <if test="remark != null ">
     	        and remark = #{remark,jdbcType=VARCHAR}
            </if>
            <if test="rule != null ">
     	        and `rule` = #{rule,jdbcType=VARCHAR}
            </if>
        </trim>
    </sql>
	<select id="find" parameterType="com.shunwang.marketing.query.BaseQuery" resultMap="BaseResultMap" >
		select 
        <include refid="baseColumn" />
		from marketing_pond_style t 
		<include refid="base_where" />
	</select>
    <select id="findCnt" parameterType="com.shunwang.marketing.query.BaseQuery" resultType="java.lang.Integer" >
        select
            count(*)
        from marketing_pond_style t
		<include refid="base_where" />
    </select>
    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.Integer">
		select
        <include refid="baseColumn" />
		from marketing_pond_style
		where id = #{id,jdbcType=INTEGER}
    </select>

</mapper>
