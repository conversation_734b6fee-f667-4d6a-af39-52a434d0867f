package com.shunwang.marketing.service.marketing;

import com.shunwang.marketing.service.ai.AiSseEmitterWrapper;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;

public interface AiService {
    void chat(AiSseEmitterWrapper sseEmitter) throws IOException;

    String getFailMsg();

    SseEmitter createSseEmitter(Integer memberId, String sessionId);
}
