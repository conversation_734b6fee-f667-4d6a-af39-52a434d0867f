package com.shunwang.marketing.pojo.marketing;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.shunwang.marketing.pojo.BaseObject;
import com.shunwang.util.lang.StringUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@Accessors(chain = true)
public class PrizeRecord extends BaseObject {
	private static final long serialVersionUID = 1L;
	private Integer id;
	private Integer memberId;
	private Integer prizeId;
	private String prizeName;
	private Integer prizeType;
	private Integer welfareId;
	private String remark;
	private Integer bagId;
	private Integer pondId;
	@JsonIgnore
	private String payOrderNo;
	@JsonIgnore
	private String orderNo;
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	private Date sendTime;
	private String channel;

	private String details;
	private String description;
	private String welfareName;
	private String url;
	private String memberName;
	private Integer isExpired;
	private String source;
	private String webDetail;
	private Integer businessLine;
	private Integer gameId;
	/**
	 * 打开方式，1：启动菜单详情页，2：浏览器，3：菜单内部浏览器 4:小程序h5页面 5:小程序内部页 6:外部小程序 7:外部小程序短链
	 */
	private Integer openType;
	/**
	 * 回跳地址
	 */
	private String backUrl;

	/**
	 * 落地页ID
	 */
	public String getLandingPageId() {
		if (StringUtil.isNotBlank(url) && url.contains("https://m.kedou.com/landingPage/goPage")) {
			return url.replaceFirst("https://m.kedou.com/landingPage/goPage\\?id=", "");
		}
		return "";
	}

}
