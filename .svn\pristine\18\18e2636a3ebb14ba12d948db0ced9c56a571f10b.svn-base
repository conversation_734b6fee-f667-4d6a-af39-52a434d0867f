<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shunwang.marketing.dao.marketing.BannerDetailsMapper">
    <resultMap id="BaseResultMap" type="bannerDetails">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="bannerId" column="banner_id" jdbcType="INTEGER"/>
        <result property="openType" column="open_type" jdbcType="INTEGER"/>
        <result property="gameId" column="game_id" jdbcType="VARCHAR"/>
        <result property="imgId" column="img_id" jdbcType="INTEGER"/>
        <result property="linkUrl" column="link_url" jdbcType="VARCHAR"/>
        <result property="state" column="state" jdbcType="INTEGER"/>
        <result property="sort" column="sort" jdbcType="INTEGER"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
    </resultMap>
    <resultMap id="BannerDetailVoMap" type="com.shunwang.marketing.pojo.marketing.vo.BannerDetailVo" extends="BaseResultMap">
        <result property="imagePath" column="image_path" jdbcType="VARCHAR"></result>
    </resultMap>
    <sql id="baseColumn">
        id,
        name,
        banner_id,
        open_type,
        game_id,
        img_id,
        link_url,
        state,
        sort,
        remark
    </sql>
    <sql id="base_where" >
        <trim prefix="where" prefixOverrides="and|or">
            <if test="id != null ">
     	        and id = #{id,jdbcType=INTEGER}
            </if>
            <if test="name != null ">
     	        and name = #{name,jdbcType=VARCHAR}
            </if>
            <if test="bannerId != null ">
     	        and banner_id = #{bannerId,jdbcType=INTEGER}
            </if>
            <if test="imgId != null ">
     	        and img_id = #{imgId,jdbcType=INTEGER}
            </if>
            <if test="linkUrl != null ">
     	        and link_url = #{linkUrl,jdbcType=VARCHAR}
            </if>
            <if test="state != null ">
     	        and state = #{state,jdbcType=INTEGER}
            </if>
            <if test="sort != null ">
     	        and sort = #{sort,jdbcType=INTEGER}
            </if>
            <if test="remark != null ">
     	        and remark = #{remark,jdbcType=VARCHAR}
            </if>
        </trim>
    </sql>
	<select id="find" parameterType="com.shunwang.marketing.query.BaseQuery" resultMap="BaseResultMap" >
		select 
        <include refid="baseColumn" />
		from marketing_banner_details t 
		<include refid="base_where" />
	</select>
    <select id="findCnt" parameterType="com.shunwang.marketing.query.BaseQuery" resultType="java.lang.Integer" >
        select
            count(*)
        from marketing_banner_details t
		<include refid="base_where" />
    </select>
    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.Integer">
		select
        <include refid="baseColumn" />
		from marketing_banner_details
		where id = #{id,jdbcType=INTEGER}
    </select>
    <select id="findBannerDetailVo" resultMap="BannerDetailVoMap">
        select
        <include refid="baseColumn" />
        from marketing_banner_details t
        <include refid="base_where" />
        order by sort
    </select>
    <insert id="save" parameterType="bannerDetails" >
		insert into marketing_banner_details 
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="name != null">
                name,
            </if>
            <if test="bannerId != null">
                banner_id,
            </if>
            <if test="imgId != null">
                img_id,
            </if>
            <if test="linkUrl != null">
                link_url,
            </if>
            <if test="state != null">
                state,
            </if>
            <if test="sort != null">
                sort,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="userAdd != null">
                user_add,
            </if>
            <if test="timeAdd != null">
                time_add,
            </if>
            <if test="userEdit != null">
                user_edit,
            </if>
            <if test="timeEdit != null">
                time_edit,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="bannerId != null">
                #{bannerId,jdbcType=INTEGER},
            </if>
            <if test="imgId != null">
                #{imgId,jdbcType=INTEGER},
            </if>
            <if test="linkUrl != null">
                #{linkUrl,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                #{state,jdbcType=INTEGER},
            </if>
            <if test="sort != null">
                #{sort,jdbcType=INTEGER},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="userAdd != null">
                #{userAdd,jdbcType=VARCHAR},
            </if>
            <if test="timeAdd != null">
                #{timeAdd,jdbcType=TIMESTAMP},
            </if>
            <if test="userEdit != null">
                #{userEdit,jdbcType=VARCHAR},
            </if>
            <if test="timeEdit != null">
                #{timeEdit,jdbcType=TIMESTAMP},
            </if>
        </trim>
	</insert>
	<update id="update" parameterType="bannerDetails" >
		update marketing_banner_details
        <set>
            <if test="id != null">
     	        id = #{id,jdbcType=INTEGER},
            </if>
            <if test="name != null">
     	        name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="bannerId != null">
     	        banner_id = #{bannerId,jdbcType=INTEGER},
            </if>
            <if test="imgId != null">
     	        img_id = #{imgId,jdbcType=INTEGER},
            </if>
            <if test="linkUrl != null">
     	        link_url = #{linkUrl,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
     	        state = #{state,jdbcType=INTEGER},
            </if>
            <if test="sort != null">
     	        sort = #{sort,jdbcType=INTEGER},
            </if>
            <if test="remark != null">
     	        remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="userAdd != null">
     	        user_add = #{userAdd,jdbcType=VARCHAR},
            </if>
            <if test="timeAdd != null">
     	        time_add = #{timeAdd,jdbcType=TIMESTAMP},
            </if>
            <if test="userEdit != null">
     	        user_edit = #{userEdit,jdbcType=VARCHAR},
            </if>
            <if test="timeEdit != null">
     	        time_edit = #{timeEdit,jdbcType=TIMESTAMP},
            </if>
        </set>
		<trim prefix="where" prefixOverrides="and|or"> 
            id = #{id,jdbcType=INTEGER}
        </trim>
	</update>
</mapper>
