<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="{&quot;currentConversationId&quot;:&quot;b82f4962-394e-4b1a-ae0d-3c6ad4d8883a&quot;,&quot;conversations&quot;:{&quot;b82f4962-394e-4b1a-ae0d-3c6ad4d8883a&quot;:{&quot;id&quot;:&quot;b82f4962-394e-4b1a-ae0d-3c6ad4d8883a&quot;,&quot;name&quot;:&quot;Welcome to the Augment Agent&quot;,&quot;createdAtIso&quot;:&quot;2025-07-02T05:23:23.131Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-07-09T01:39:59.217Z&quot;,&quot;chatHistory&quot;:[{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;c068d88d-0fe4-4e2b-ae75-42bf6e18e685&quot;,&quot;request_message&quot;:&quot;做了什么&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;做了什么&quot;}]}]},&quot;mentioned_items&quot;:[],&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_01P9qCyenaAGfAjmk5vGJ6bg&quot;,&quot;tool_name&quot;:&quot;view&quot;,&quot;input_json&quot;:&quot;{\&quot;path\&quot;: \&quot;marketing-web/src/main/java/com/shunwang/marketing/controller/AiController.java\&quot;, \&quot;type\&quot;: \&quot;file\&quot;}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:0,&quot;type&quot;:0,&quot;content&quot;:&quot;我来帮您查看这个文件的内容，了解它做了什么。\n&quot;},{&quot;id&quot;:2,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:3,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:0,&quot;text_node&quot;:{&quot;content&quot;:&quot;做了什么&quot;}},{&quot;id&quot;:1,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;D:/develop/IDEAworkplace/com.shunwang.marketingWeb&quot;,&quot;folderRoot&quot;:&quot;D:/develop/IDEAworkplace/com.shunwang.marketingWeb&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-07-02T05:26:27.446Z&quot;,&quot;response_text&quot;:&quot;我来帮您查看这个文件的内容，了解它做了什么。\n&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;f3c6dd4f-5750-4cc9-91ba-28bce275ae44&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:0,&quot;content&quot;:&quot;这个 `AiController.java` 文件是一个 Spring Boot 控制器，主要实现了 AI 聊天功能的后端接口。让我总结一下它的主要功能：\n\n## 主要功能\n\n### 1. **基础信息接口** (`/ai/basic`)\n- 随机推荐 3 个 AI 问题供用户选择\n- 检查用户是否已授权使用 AI 功能\n- 如果未授权，返回用户协议、隐私政策和 AI 协议的链接\n\n### 2. **提示词接口** (`/ai/prompt`)\n- 返回 AI 首页的提示词内容\n\n### 3. **历史记录接口** (`/ai/historyRecord`)\n- 获取当前用户的 AI 对话历史记录（分页）\n\n### 4. **用户授权接口** (`/ai/authorize`)\n- 用户同意 AI 使用授权\n- 在数据库中记录用户的授权状态\n\n### 5. **页面跳转** (`/ai/goPage`)\n- 跳转到 AI 聊天页面\n\n### 6. **AI 聊天接口** (`/ai/chat`)\n- 核心聊天功能，使用 Server-Sent Events (SSE) 实现实时流式响应\n- 包含频率限制（3秒内只能发送一次请求）\n- 调用 `aiService.commonAiBuss()` 处理实际的 AI 对话\n\n## 技术特点\n\n1. **流式响应**: 使用 SSE (Server-Sent Events) 实现实时的流式 AI 回复\n2. **频率限制**: 使用 Redis 实现请求频率控制，防止用户频繁调用\n3. **用户授权管理**: 通过数据库记录用户是否同意使用 AI 功能\n4. **随机推荐**: 从内容库中随机选择问题推荐给用户\n5. **跨域支持**: 聊天接口支持跨域请求\n\n这是一个典型的 AI 聊天应用的后端控制器，提供了完整的用户交互流程，从授权到聊天的全套功能。\n&quot;},{&quot;id&quot;:2,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:3,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;Here's the result of running `cat -n` on marketing-web/src/main/java/com/shunwang/marketing/controller/AiController.java:\n     1\tpackage com.shunwang.marketing.controller;\n     2\t\n     3\timport cn.hutool.core.util.RandomUtil;\n     4\timport com.shunwang.marketing.cacheService.CacheKeyConstant;\n     5\timport com.shunwang.marketing.cacheService.RedisOperation;\n     6\timport com.shunwang.marketing.enums.ResourcesEnum;\n     7\timport com.shunwang.marketing.enums.marketing.BussMemberRelationEnum;\n     8\timport com.shunwang.marketing.enums.marketing.ContentEnum;\n     9\timport com.shunwang.marketing.exception.CommonException;\n    10\timport com.shunwang.marketing.pojo.marketing.AiRecord;\n    11\timport com.shunwang.marketing.pojo.marketing.BussMemberRelation;\n    12\timport com.shunwang.marketing.pojo.marketing.Content;\n    13\timport com.shunwang.marketing.query.marketing.BussMemberRelationQuery;\n    14\timport com.shunwang.marketing.query.marketing.ContentQuery;\n    15\timport com.shunwang.marketing.result.Result;\n    16\timport com.shunwang.marketing.result.ResultGenerator;\n    17\timport com.shunwang.marketing.service.config.ResourcesService;\n    18\timport com.shunwang.marketing.service.marketing.AiRecordService;\n    19\timport com.shunwang.marketing.service.marketing.AiService;\n    20\timport com.shunwang.marketing.service.marketing.BussMemberRelationService;\n    21\timport com.shunwang.marketing.service.marketing.ContentService;\n    22\timport com.shunwang.marketing.util.MemberUtil;\n    23\timport lombok.Data;\n    24\timport lombok.extern.slf4j.Slf4j;\n    25\timport org.springframework.beans.factory.annotation.Autowired;\n    26\timport org.springframework.stereotype.Controller;\n    27\timport org.springframework.validation.annotation.Validated;\n    28\timport org.springframework.web.bind.annotation.CrossOrigin;\n    29\timport org.springframework.web.bind.annotation.RequestMapping;\n    30\timport org.springframework.web.bind.annotation.ResponseBody;\n    31\timport org.springframework.web.servlet.mvc.method.annotation.SseEmitter;\n    32\t\n    33\timport javax.validation.constraints.NotBlank;\n    34\timport java.util.List;\n    35\timport java.util.Map;\n    36\timport java.util.concurrent.TimeUnit;\n    37\timport java.util.stream.Collectors;\n    38\t\n    39\t/**\n    40\t * AI接口\n    41\t *\n    42\t * @apiNote AI接口\n    43\t */\n    44\t@Validated\n    45\t@Controller\n    46\t@RequestMapping(\&quot;/ai\&quot;)\n    47\t@Slf4j\n    48\tpublic class AiController {\n    49\t    @Autowired\n    50\t    private AiRecordService aiRecordService;\n    51\t    @Autowired\n    52\t    private ContentService contentService;\n    53\t    @Autowired\n    54\t    private BussMemberRelationService bussMemberRelationService;\n    55\t    @Autowired\n    56\t    ResourcesService resourcesService;\n    57\t    @Autowired\n    58\t    AiService aiService;\n    59\t    @Autowired\n    60\t    RedisOperation redisOperation;\n    61\t\n    62\t    /**\n    63\t     * 推荐问题\n    64\t     *\n    65\t     * @return\n    66\t     */\n    67\t    @ResponseBody\n    68\t    @RequestMapping(\&quot;basic\&quot;)\n    69\t    public Result&lt;AiBasicInfoVo&gt; basic() {\n    70\t        AiBasicInfoVo result = new AiBasicInfoVo();\n    71\t        //推荐问题，3个\n    72\t        ContentQuery query = new ContentQuery();\n    73\t        query.setType(ContentEnum.TypeEnum.AI.getValue());\n    74\t        List&lt;Content&gt; aiQuestionsContent = contentService.find(query);\n    75\t        List&lt;String&gt; aiQuestions =\n    76\t                RandomUtil.randomEleSet(aiQuestionsContent, Math.min(aiQuestionsContent.size(), 3))\n    77\t                        .stream().map(Content::getTitle).collect(Collectors.toList());\n    78\t        result.setQuestions(aiQuestions);\n    79\t\n    80\t        //用户授权\n    81\t        BussMemberRelationQuery relationQuery = new BussMemberRelationQuery();\n    82\t        relationQuery.setBussType(BussMemberRelationEnum.BussTypeEnum.AI_AUTHORIZE.getValue());\n    83\t        relationQuery.setBussId(BussMemberRelationEnum.DEFAULT_BUSS_ID);\n    84\t        relationQuery.setMemberId(MemberUtil.getNotNullMemberId());\n    85\t        boolean authorize = bussMemberRelationService.findOne(relationQuery) != null;\n    86\t        result.setAuthorize(authorize);\n    87\t\n    88\t        //未授权填充超链接\n    89\t        if (!authorize) {\n    90\t            result.setUserAgreement(resourcesService.getStringValueByTypeAndNameEhCache(ResourcesEnum.TypeName.AI_USER_AGREEMENT));\n    91\t            result.setPrivacy(resourcesService.getStringValueByTypeAndNameEhCache(ResourcesEnum.TypeName.AI_PRIVACY));\n    92\t            result.setAiAgreement(resourcesService.getStringValueByTypeAndNameEhCache(ResourcesEnum.TypeName.AI_AI_AGREEMENT));\n    93\t        }\n    94\t        return ResultGenerator.newSuccessResult(result);\n    95\t\n    96\t    }\n    97\t\n    98\t    /**\n    99\t     * 首页提示词\n   100\t     *\n   101\t     * @return\n   102\t     */\n   103\t    @ResponseBody\n   104\t    @RequestMapping(\&quot;prompt\&quot;)\n   105\t    public Result&lt;String&gt; prompt() {\n   106\t        return ResultGenerator.newSuccessResult(resourcesService.getStringValueByTypeAndNameEhCache(ResourcesEnum.TypeName.AI_PROMPT));\n   107\t\n   108\t    }\n   109\t\n   110\t    @Data\n   111\t    static class AiBasicInfoVo {\n   112\t        private List&lt;String&gt; questions;\n   113\t        private boolean authorize;\n   114\t        private String userAgreement;\n   115\t        private String privacy;\n   116\t        private String aiAgreement;\n   117\t    }\n   118\t\n   119\t\n   120\t    /**\n   121\t     * 对话记录\n   122\t     *\n   123\t     * @return\n   124\t     */\n   125\t    @ResponseBody\n   126\t    @RequestMapping(\&quot;historyRecord\&quot;)\n   127\t    public Result historyRecord(AiRecord pageable) {\n   128\t        pageable.setMemberId(MemberUtil.getNotNullMemberId());\n   129\t        Map&lt;String, Object&gt; result = aiRecordService.getPageMap(pageable);\n   130\t        return ResultGenerator.newSuccessResult(result);\n   131\t    }\n   132\t\n   133\t    /**\n   134\t     * 用户同意ai授权\n   135\t     *\n   136\t     * @return\n   137\t     */\n   138\t    @ResponseBody\n   139\t    @RequestMapping(\&quot;authorize\&quot;)\n   140\t    public Result authorize() {\n   141\t        BussMemberRelation one = bussMemberRelationService.findOne(MemberUtil.getNotNullMemberId(), BussMemberRelationEnum.BussTypeEnum.AI_AUTHORIZE.getValue(), 0);\n   142\t        if (one == null) {\n   143\t            BussMemberRelation relation = new BussMemberRelation();\n   144\t            relation.setBussId(BussMemberRelationEnum.DEFAULT_BUSS_ID);\n   145\t            relation.setMemberId(MemberUtil.getNotNullMemberId());\n   146\t            relation.setBussType(BussMemberRelationEnum.BussTypeEnum.AI_AUTHORIZE.getValue());\n   147\t            bussMemberRelationService.save(relation);\n   148\t        }\n   149\t        return ResultGenerator.newSuccessResult(one);\n   150\t    }\n   151\t\n   152\t    /**\n   153\t     * ai聊天页面\n   154\t     *\n   155\t     * @return\n   156\t     */\n   157\t    @RequestMapping(\&quot;goPage\&quot;)\n   158\t    public String goPage() {\n   159\t        return \&quot;ai/index\&quot;;\n   160\t    }\n   161\t\n   162\t\n   163\t    /**\n   164\t     * 聊天\n   165\t     *\n   166\t     * @return sse连接\n   167\t     */\n   168\t    @CrossOrigin\n   169\t    @ResponseBody\n   170\t    @RequestMapping(\&quot;chat\&quot;)\n   171\t    public SseEmitter chat(@NotBlank(message = \&quot;问题内容不能为空\&quot;) String question) {\n   172\t        if (!redisOperation.setIfAbsent(CacheKeyConstant.AI_CHAT + MemberUtil.getNotNullMemberId(), 1, 3, TimeUnit.SECONDS)) {\n   173\t            throw new CommonException(\&quot;操作频繁\&quot;);\n   174\t        }\n   175\t        return aiService.commonAiBuss(question);\n   176\t    }\n   177\t\n   178\t\n   179\t}\n   180\t\nTotal lines in file: 180\n&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_01P9qCyenaAGfAjmk5vGJ6bg&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;D:/develop/IDEAworkplace/com.shunwang.marketingWeb&quot;,&quot;folderRoot&quot;:&quot;D:/develop/IDEAworkplace/com.shunwang.marketingWeb&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-07-02T05:26:32.547Z&quot;,&quot;response_text&quot;:&quot;这个 `AiController.java` 文件是一个 Spring Boot 控制器，主要实现了 AI 聊天功能的后端接口。让我总结一下它的主要功能：\n\n## 主要功能\n\n### 1. **基础信息接口** (`/ai/basic`)\n- 随机推荐 3 个 AI 问题供用户选择\n- 检查用户是否已授权使用 AI 功能\n- 如果未授权，返回用户协议、隐私政策和 AI 协议的链接\n\n### 2. **提示词接口** (`/ai/prompt`)\n- 返回 AI 首页的提示词内容\n\n### 3. **历史记录接口** (`/ai/historyRecord`)\n- 获取当前用户的 AI 对话历史记录（分页）\n\n### 4. **用户授权接口** (`/ai/authorize`)\n- 用户同意 AI 使用授权\n- 在数据库中记录用户的授权状态\n\n### 5. **页面跳转** (`/ai/goPage`)\n- 跳转到 AI 聊天页面\n\n### 6. **AI 聊天接口** (`/ai/chat`)\n- 核心聊天功能，使用 Server-Sent Events (SSE) 实现实时流式响应\n- 包含频率限制（3秒内只能发送一次请求）\n- 调用 `aiService.commonAiBuss()` 处理实际的 AI 对话\n\n## 技术特点\n\n1. **流式响应**: 使用 SSE (Server-Sent Events) 实现实时的流式 AI 回复\n2. **频率限制**: 使用 Redis 实现请求频率控制，防止用户频繁调用\n3. **用户授权管理**: 通过数据库记录用户是否同意使用 AI 功能\n4. **随机推荐**: 从内容库中随机选择问题推荐给用户\n5. **跨域支持**: 聊天接口支持跨域请求\n\n这是一个典型的 AI 聊天应用的后端控制器，提供了完整的用户交互流程，从授权到聊天的全套功能。\n&quot;,&quot;workspace_file_chunks&quot;:[]}],&quot;feedbackStates&quot;:{&quot;temp-fe-6fb753eb-e3d3-48f0-a9cf-50bfc59dc5fd&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-8c238503-0561-4ecb-8dc9-fd4b4f6c1129&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;}},&quot;toolUseStates&quot;:{&quot;c068d88d-0fe4-4e2b-ae75-42bf6e18e685;toolu_01P9qCyenaAGfAjmk5vGJ6bg&quot;:{&quot;requestId&quot;:&quot;c068d88d-0fe4-4e2b-ae75-42bf6e18e685&quot;,&quot;toolUseId&quot;:&quot;toolu_01P9qCyenaAGfAjmk5vGJ6bg&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;Here's the result of running `cat -n` on marketing-web/src/main/java/com/shunwang/marketing/controller/AiController.java:\n     1\tpackage com.shunwang.marketing.controller;\n     2\t\n     3\timport cn.hutool.core.util.RandomUtil;\n     4\timport com.shunwang.marketing.cacheService.CacheKeyConstant;\n     5\timport com.shunwang.marketing.cacheService.RedisOperation;\n     6\timport com.shunwang.marketing.enums.ResourcesEnum;\n     7\timport com.shunwang.marketing.enums.marketing.BussMemberRelationEnum;\n     8\timport com.shunwang.marketing.enums.marketing.ContentEnum;\n     9\timport com.shunwang.marketing.exception.CommonException;\n    10\timport com.shunwang.marketing.pojo.marketing.AiRecord;\n    11\timport com.shunwang.marketing.pojo.marketing.BussMemberRelation;\n    12\timport com.shunwang.marketing.pojo.marketing.Content;\n    13\timport com.shunwang.marketing.query.marketing.BussMemberRelationQuery;\n    14\timport com.shunwang.marketing.query.marketing.ContentQuery;\n    15\timport com.shunwang.marketing.result.Result;\n    16\timport com.shunwang.marketing.result.ResultGenerator;\n    17\timport com.shunwang.marketing.service.config.ResourcesService;\n    18\timport com.shunwang.marketing.service.marketing.AiRecordService;\n    19\timport com.shunwang.marketing.service.marketing.AiService;\n    20\timport com.shunwang.marketing.service.marketing.BussMemberRelationService;\n    21\timport com.shunwang.marketing.service.marketing.ContentService;\n    22\timport com.shunwang.marketing.util.MemberUtil;\n    23\timport lombok.Data;\n    24\timport lombok.extern.slf4j.Slf4j;\n    25\timport org.springframework.beans.factory.annotation.Autowired;\n    26\timport org.springframework.stereotype.Controller;\n    27\timport org.springframework.validation.annotation.Validated;\n    28\timport org.springframework.web.bind.annotation.CrossOrigin;\n    29\timport org.springframework.web.bind.annotation.RequestMapping;\n    30\timport org.springframework.web.bind.annotation.ResponseBody;\n    31\timport org.springframework.web.servlet.mvc.method.annotation.SseEmitter;\n    32\t\n    33\timport javax.validation.constraints.NotBlank;\n    34\timport java.util.List;\n    35\timport java.util.Map;\n    36\timport java.util.concurrent.TimeUnit;\n    37\timport java.util.stream.Collectors;\n    38\t\n    39\t/**\n    40\t * AI接口\n    41\t *\n    42\t * @apiNote AI接口\n    43\t */\n    44\t@Validated\n    45\t@Controller\n    46\t@RequestMapping(\&quot;/ai\&quot;)\n    47\t@Slf4j\n    48\tpublic class AiController {\n    49\t    @Autowired\n    50\t    private AiRecordService aiRecordService;\n    51\t    @Autowired\n    52\t    private ContentService contentService;\n    53\t    @Autowired\n    54\t    private BussMemberRelationService bussMemberRelationService;\n    55\t    @Autowired\n    56\t    ResourcesService resourcesService;\n    57\t    @Autowired\n    58\t    AiService aiService;\n    59\t    @Autowired\n    60\t    RedisOperation redisOperation;\n    61\t\n    62\t    /**\n    63\t     * 推荐问题\n    64\t     *\n    65\t     * @return\n    66\t     */\n    67\t    @ResponseBody\n    68\t    @RequestMapping(\&quot;basic\&quot;)\n    69\t    public Result&lt;AiBasicInfoVo&gt; basic() {\n    70\t        AiBasicInfoVo result = new AiBasicInfoVo();\n    71\t        //推荐问题，3个\n    72\t        ContentQuery query = new ContentQuery();\n    73\t        query.setType(ContentEnum.TypeEnum.AI.getValue());\n    74\t        List&lt;Content&gt; aiQuestionsContent = contentService.find(query);\n    75\t        List&lt;String&gt; aiQuestions =\n    76\t                RandomUtil.randomEleSet(aiQuestionsContent, Math.min(aiQuestionsContent.size(), 3))\n    77\t                        .stream().map(Content::getTitle).collect(Collectors.toList());\n    78\t        result.setQuestions(aiQuestions);\n    79\t\n    80\t        //用户授权\n    81\t        BussMemberRelationQuery relationQuery = new BussMemberRelationQuery();\n    82\t        relationQuery.setBussType(BussMemberRelationEnum.BussTypeEnum.AI_AUTHORIZE.getValue());\n    83\t        relationQuery.setBussId(BussMemberRelationEnum.DEFAULT_BUSS_ID);\n    84\t        relationQuery.setMemberId(MemberUtil.getNotNullMemberId());\n    85\t        boolean authorize = bussMemberRelationService.findOne(relationQuery) != null;\n    86\t        result.setAuthorize(authorize);\n    87\t\n    88\t        //未授权填充超链接\n    89\t        if (!authorize) {\n    90\t            result.setUserAgreement(resourcesService.getStringValueByTypeAndNameEhCache(ResourcesEnum.TypeName.AI_USER_AGREEMENT));\n    91\t            result.setPrivacy(resourcesService.getStringValueByTypeAndNameEhCache(ResourcesEnum.TypeName.AI_PRIVACY));\n    92\t            result.setAiAgreement(resourcesService.getStringValueByTypeAndNameEhCache(ResourcesEnum.TypeName.AI_AI_AGREEMENT));\n    93\t        }\n    94\t        return ResultGenerator.newSuccessResult(result);\n    95\t\n    96\t    }\n    97\t\n    98\t    /**\n    99\t     * 首页提示词\n   100\t     *\n   101\t     * @return\n   102\t     */\n   103\t    @ResponseBody\n   104\t    @RequestMapping(\&quot;prompt\&quot;)\n   105\t    public Result&lt;String&gt; prompt() {\n   106\t        return ResultGenerator.newSuccessResult(resourcesService.getStringValueByTypeAndNameEhCache(ResourcesEnum.TypeName.AI_PROMPT));\n   107\t\n   108\t    }\n   109\t\n   110\t    @Data\n   111\t    static class AiBasicInfoVo {\n   112\t        private List&lt;String&gt; questions;\n   113\t        private boolean authorize;\n   114\t        private String userAgreement;\n   115\t        private String privacy;\n   116\t        private String aiAgreement;\n   117\t    }\n   118\t\n   119\t\n   120\t    /**\n   121\t     * 对话记录\n   122\t     *\n   123\t     * @return\n   124\t     */\n   125\t    @ResponseBody\n   126\t    @RequestMapping(\&quot;historyRecord\&quot;)\n   127\t    public Result historyRecord(AiRecord pageable) {\n   128\t        pageable.setMemberId(MemberUtil.getNotNullMemberId());\n   129\t        Map&lt;String, Object&gt; result = aiRecordService.getPageMap(pageable);\n   130\t        return ResultGenerator.newSuccessResult(result);\n   131\t    }\n   132\t\n   133\t    /**\n   134\t     * 用户同意ai授权\n   135\t     *\n   136\t     * @return\n   137\t     */\n   138\t    @ResponseBody\n   139\t    @RequestMapping(\&quot;authorize\&quot;)\n   140\t    public Result authorize() {\n   141\t        BussMemberRelation one = bussMemberRelationService.findOne(MemberUtil.getNotNullMemberId(), BussMemberRelationEnum.BussTypeEnum.AI_AUTHORIZE.getValue(), 0);\n   142\t        if (one == null) {\n   143\t            BussMemberRelation relation = new BussMemberRelation();\n   144\t            relation.setBussId(BussMemberRelationEnum.DEFAULT_BUSS_ID);\n   145\t            relation.setMemberId(MemberUtil.getNotNullMemberId());\n   146\t            relation.setBussType(BussMemberRelationEnum.BussTypeEnum.AI_AUTHORIZE.getValue());\n   147\t            bussMemberRelationService.save(relation);\n   148\t        }\n   149\t        return ResultGenerator.newSuccessResult(one);\n   150\t    }\n   151\t\n   152\t    /**\n   153\t     * ai聊天页面\n   154\t     *\n   155\t     * @return\n   156\t     */\n   157\t    @RequestMapping(\&quot;goPage\&quot;)\n   158\t    public String goPage() {\n   159\t        return \&quot;ai/index\&quot;;\n   160\t    }\n   161\t\n   162\t\n   163\t    /**\n   164\t     * 聊天\n   165\t     *\n   166\t     * @return sse连接\n   167\t     */\n   168\t    @CrossOrigin\n   169\t    @ResponseBody\n   170\t    @RequestMapping(\&quot;chat\&quot;)\n   171\t    public SseEmitter chat(@NotBlank(message = \&quot;问题内容不能为空\&quot;) String question) {\n   172\t        if (!redisOperation.setIfAbsent(CacheKeyConstant.AI_CHAT + MemberUtil.getNotNullMemberId(), 1, 3, TimeUnit.SECONDS)) {\n   173\t            throw new CommonException(\&quot;操作频繁\&quot;);\n   174\t        }\n   175\t        return aiService.commonAiBuss(question);\n   176\t    }\n   177\t\n   178\t\n   179\t}\n   180\t\nTotal lines in file: 180\n&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}}},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;mentioned_items&quot;:[],&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:true,&quot;extraData&quot;:{&quot;isAgentConversation&quot;:true,&quot;hasAgentOnboarded&quot;:true,&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;471b492b-59bf-4ee3-aaab-90bc334403eb&quot;}},&quot;agentExecutionMode&quot;:&quot;manual&quot;,&quot;isPanelCollapsed&quot;:true,&quot;displayedAnnouncements&quot;:[],&quot;sortConversationsBy&quot;:&quot;lastMessageTimestamp&quot;,&quot;sendMode&quot;:&quot;send&quot;}" />
      </map>
    </option>
  </component>
</project>