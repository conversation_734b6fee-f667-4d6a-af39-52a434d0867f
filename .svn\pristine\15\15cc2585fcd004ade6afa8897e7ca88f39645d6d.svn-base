package com.shunwang.marketing.service.marketing.promotionPopupWindow;

import com.shunwang.marketing.enums.marketing.PromotionPopupWindowEnum;
import com.shunwang.marketing.pojo.marketing.vo.PromotionPopupWindowVo;
import org.springframework.stereotype.Component;


@Component
public class StrategyProcessor8 extends AbstractStrategyProcessor {
    @Override
    public boolean doProcess(PromotionPopupWindowVo ppw) {
        return true;
    }


    @Override
    String strategyValue() {
        return PromotionPopupWindowEnum.StrategyEnum.EVERY.getValue();
    }

    @Override
    public boolean needLogin() {
        return false;
    }
}
