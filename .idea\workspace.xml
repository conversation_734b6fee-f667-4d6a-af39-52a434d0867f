<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="ALL" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="dc273e6a-69b5-4d20-92d0-734d2833ef35" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/marketing-dao/src/main/java/com/shunwang/marketing/pojo/marketing/ResourcePrivilegeContent.java" beforeDir="false" afterPath="$PROJECT_DIR$/marketing-dao/src/main/java/com/shunwang/marketing/pojo/marketing/ResourcePrivilegeContent.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/marketing-dao/src/main/java/com/shunwang/marketing/query/marketing/ResourcePrivilegeContentQuery.java" beforeDir="false" afterPath="$PROJECT_DIR$/marketing-dao/src/main/java/com/shunwang/marketing/query/marketing/ResourcePrivilegeContentQuery.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/marketing-dao/src/main/resources/mybatis/marketing/resourcePrivilegeContentMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/marketing-dao/src/main/resources/mybatis/marketing/resourcePrivilegeContentMapper.xml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="CompilerWorkspaceConfiguration">
    <option name="MAKE_PROJECT_ON_SAVE" value="true" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Interface" />
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="jar://$PROJECT_DIR$/../../java/jdk1.8/src.zip!/java/util/Comparator.java" root0="SKIP_INSPECTION" />
  </component>
  <component name="JRebelWorkspace">
    <option name="jrebelEnabledAutocompile" value="true" />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="$PROJECT_DIR$/../../apache-maven-3.6.3" />
        <option name="localRepository" value="D:\develop\apache-maven-3.6.3\repository" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="D:\develop\apache-maven-3.6.3\conf\setting-shunwang.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 5
}</component>
  <component name="ProjectId" id="2mdOimIuBul1cxNoJPQyRfO7B2X" />
  <component name="ProjectViewState">
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Application.AiController.executor&quot;: &quot;Run&quot;,
    &quot;Application.AiGameItemServiceImpl.executor&quot;: &quot;Debug&quot;,
    &quot;Application.AiServiceimpl.executor&quot;: &quot;Run&quot;,
    &quot;Application.MarketingApplication.executor&quot;: &quot;JRebel Debug&quot;,
    &quot;Application.OutSiteController.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.AiTest.executor&quot;: &quot;Debug&quot;,
    &quot;JUnit.AiTest.test.executor&quot;: &quot;JRebel Debug&quot;,
    &quot;JUnit.AiTest.test1.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.PromotionPopupWindowTest.TestContext.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.TencentOrderTest.finishOrderTest.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.TokenToUidTest.test.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.UserHistoryBehaviorTest.test.executor&quot;: &quot;Debug&quot;,
    &quot;Remote JVM Debug.测试.executor&quot;: &quot;Debug&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;Spring Boot.MarketingApplication.executor&quot;: &quot;JRebel Debug&quot;,
    &quot;WebServerToolWindowFactoryState&quot;: &quot;false&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/develop/IDEAworkplace/java-mindMap&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;Project&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.15&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.0&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;spring.configuration.checksum&quot;: &quot;bc74a4396ac63e0d15d5887ffe713f74&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RebelAgentSelection">
    <selection>jr</selection>
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\develop\IDEAworkplace\com.shunwang.marketingWeb\marketing-web\src\main\resources\static\aiGame" />
      <recent name="D:\develop\IDEAworkplace\com.shunwang.marketingWeb\marketing-web\src\main\resources\templates\aiGame" />
      <recent name="D:\develop\IDEAworkplace\com.shunwang.marketingWeb\marketing-web\src\main\resources\static\images\mp\static" />
      <recent name="D:\develop\IDEAworkplace\com.shunwang.marketingWeb\marketing-web\src\main\resources\templates\ai" />
      <recent name="D:\develop\IDEAworkplace\com.shunwang.marketingWeb\marketing-web\src\main\resources\static\ai" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\develop\IDEAworkplace\com.shunwang.marketingWeb\marketing-web\src\main\resources\static\images\mp\static" />
      <recent name="D:\develop\IDEAworkplace\com.shunwang.marketingWeb\marketing-web\src\main\resources\templates\aiGame" />
      <recent name="D:\develop\IDEAworkplace\com.shunwang.marketingWeb\marketing-web\src\main\resources\static\tencent\static" />
      <recent name="D:\develop\IDEAworkplace\com.shunwang.marketingWeb\marketing-web\src\main\resources\templates" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.shunwang.marketing.pojo.marketing.vo" />
      <recent name="com.shunwang.marketing.manager.bigData" />
      <recent name="com.shunwang.marketing.manager.request.ai" />
      <recent name="com.shunwang.marketing.enums.marketing" />
      <recent name="com.shunwang.marketing.config" />
    </key>
  </component>
  <component name="RunManager" selected="Spring Boot.MarketingApplication">
    <configuration name="MarketingApplication" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.shunwang.marketing.MarketingApplication" />
      <module name="marketing-web" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.shunwang.marketing.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="AiTest" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="marketing-web" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.shunwang.marketing.manager.ai.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.shunwang.marketing.manager.ai" />
      <option name="MAIN_CLASS_NAME" value="com.shunwang.marketing.manager.ai.AiTest" />
      <option name="TEST_OBJECT" value="class" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="AiTest.test" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="marketing-web" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.shunwang.marketing.manager.ai.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.shunwang.marketing.manager.ai" />
      <option name="MAIN_CLASS_NAME" value="com.shunwang.marketing.manager.ai.AiTest" />
      <option name="METHOD_NAME" value="test" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="AiTest.test1" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="marketing-web" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.shunwang.marketing.manager.ai.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.shunwang.marketing.manager.ai" />
      <option name="MAIN_CLASS_NAME" value="com.shunwang.marketing.manager.ai.AiTest" />
      <option name="METHOD_NAME" value="test1" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="UserHistoryBehaviorTest.test" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="marketing-web" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.shunwang.marketing.manager.bigData.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.shunwang.marketing.manager.bigData" />
      <option name="MAIN_CLASS_NAME" value="com.shunwang.marketing.manager.bigData.UserHistoryBehaviorTest" />
      <option name="METHOD_NAME" value="test" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="测试" type="Remote">
      <option name="USE_SOCKET_TRANSPORT" value="true" />
      <option name="SERVER_MODE" value="false" />
      <option name="SHMEM_ADDRESS" />
      <option name="HOST" value="**************" />
      <option name="PORT" value="8787" />
      <option name="AUTO_RESTART" value="false" />
      <RunnerSettings RunnerId="Debug">
        <option name="DEBUG_PORT" value="8787" />
        <option name="LOCAL" value="false" />
      </RunnerSettings>
      <method v="2" />
    </configuration>
    <configuration name="MarketingApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <additionalParameters>
        <param>
          <option name="enabled" value="true" />
          <option name="name" value="MARKETING_FRONT_CONFIG_HOME" />
          <option name="value" value="D:\develop\IDEAworkplace\com.shunwang.marketingWeb\marketing-web\src\main\config" />
        </param>
        <param>
          <option name="enabled" value="true" />
          <option name="name" value="SMSSDK_CONFIG_HOME" />
          <option name="value" value="D:\develop\IDEAworkplace\com.shunwang.marketingWeb\marketing-web\src\main\config" />
        </param>
      </additionalParameters>
      <module name="marketing-web" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.shunwang.marketing.MarketingApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <list>
      <item itemvalue="Application.MarketingApplication" />
      <item itemvalue="JUnit.UserHistoryBehaviorTest.test" />
      <item itemvalue="JUnit.AiTest" />
      <item itemvalue="JUnit.AiTest.test1" />
      <item itemvalue="JUnit.AiTest.test" />
      <item itemvalue="Remote JVM Debug.测试" />
      <item itemvalue="Spring Boot.MarketingApplication" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="JUnit.UserHistoryBehaviorTest.test" />
        <item itemvalue="JUnit.AiTest.test" />
        <item itemvalue="JUnit.AiTest.test1" />
        <item itemvalue="JUnit.AiTest" />
        <item itemvalue="Application.MarketingApplication" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9f38398b9061-39b83d9b5494-intellij.indexing.shared.core-IU-241.19072.14" />
        <option value="bundled-js-predefined-1d06a55b98c1-0b3e54e931b4-JavaScript-IU-241.19072.14" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="SvnConfiguration" cleanupOnStartRun="true">
    <configuration>C:\Users\<USER>\AppData\Roaming\Subversion</configuration>
    <supportedVersion>125</supportedVersion>
  </component>
  <component name="SvnFileUrlMappingImpl">
    <option name="myMappingRoots">
      <list>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="D:\develop\IDEAworkplace\com.shunwang.marketingWeb" />
          <option name="myCopyRoot" value="D:\develop\IDEAworkplace\com.shunwang.marketingWeb" />
        </SvnCopyRootSimple>
      </list>
    </option>
    <option name="myMoreRealMappingRoots">
      <list>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="D:\develop\IDEAworkplace\com.shunwang.marketingWeb" />
          <option name="myCopyRoot" value="D:\develop\IDEAworkplace\com.shunwang.marketingWeb" />
        </SvnCopyRootSimple>
      </list>
    </option>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="dc273e6a-69b5-4d20-92d0-734d2833ef35" name="Changes" comment="" />
      <created>1727403115635</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1727403115635</updated>
      <workItem from="1727403116686" duration="7707000" />
      <workItem from="1727572155543" duration="5255000" />
      <workItem from="1728349741536" duration="2125000" />
      <workItem from="1728436220095" duration="2305000" />
      <workItem from="1728440004661" duration="400000" />
      <workItem from="1728440414702" duration="108000" />
      <workItem from="1728440562312" duration="24000" />
      <workItem from="1728440590773" duration="37000" />
      <workItem from="1728440634157" duration="87000" />
      <workItem from="1728440732892" duration="83000" />
      <workItem from="1728440823210" duration="380000" />
      <workItem from="1728441212406" duration="1461000" />
      <workItem from="1728442692604" duration="14000" />
      <workItem from="1728442713065" duration="69000" />
      <workItem from="1728442789488" duration="370000" />
      <workItem from="1728443388753" duration="339000" />
      <workItem from="1728522518602" duration="8981000" />
      <workItem from="1728608906009" duration="13500000" />
      <workItem from="1728695382912" duration="1581000" />
      <workItem from="1728868110557" duration="1659000" />
      <workItem from="1728954552074" duration="630000" />
      <workItem from="1729041317675" duration="8034000" />
      <workItem from="1729127304260" duration="2521000" />
      <workItem from="1729153110333" duration="35000" />
      <workItem from="1729214146840" duration="180000" />
      <workItem from="1729217433444" duration="3251000" />
      <workItem from="1729472968906" duration="8061000" />
      <workItem from="1729504249774" duration="464000" />
      <workItem from="1729559332869" duration="17945000" />
      <workItem from="1729645681981" duration="13242000" />
      <workItem from="1729732131384" duration="12216000" />
      <workItem from="1729818858561" duration="9586000" />
      <workItem from="1730077745097" duration="9062000" />
      <workItem from="1730164180245" duration="9872000" />
      <workItem from="1730250456185" duration="12041000" />
      <workItem from="1730336976996" duration="13830000" />
      <workItem from="1730423711856" duration="19274000" />
      <workItem from="1730682486089" duration="18025000" />
      <workItem from="1730769001187" duration="11721000" />
      <workItem from="1730865594658" duration="4387000" />
      <workItem from="1730941812353" duration="11382000" />
      <workItem from="1731028298442" duration="8647000" />
      <workItem from="1731287386391" duration="3432000" />
      <workItem from="1731319624045" duration="556000" />
      <workItem from="1731373718688" duration="10736000" />
      <workItem from="1731460099240" duration="2079000" />
      <workItem from="1731546856698" duration="13393000" />
      <workItem from="1731632961398" duration="16973000" />
      <workItem from="1731892270597" duration="8965000" />
      <workItem from="1731978522881" duration="8424000" />
      <workItem from="1732000384522" duration="602000" />
      <workItem from="1732064988714" duration="13771000" />
      <workItem from="1732151403872" duration="2984000" />
      <workItem from="1732237752556" duration="10196000" />
      <workItem from="1732497031127" duration="8718000" />
      <workItem from="1732583388104" duration="4155000" />
      <workItem from="1732669817525" duration="8435000" />
      <workItem from="1732756190079" duration="18918000" />
      <workItem from="1732842723725" duration="10716000" />
      <workItem from="1733102042266" duration="3181000" />
      <workItem from="1733188158069" duration="1342000" />
      <workItem from="1733274555871" duration="4547000" />
      <workItem from="1733361036521" duration="595000" />
      <workItem from="1733706537116" duration="16603000" />
      <workItem from="1733793037148" duration="8778000" />
      <workItem from="1734066845209" duration="307000" />
      <workItem from="1734067534701" duration="434000" />
      <workItem from="1735092887742" duration="30000" />
      <workItem from="1735105770900" duration="3777000" />
      <workItem from="1735175588529" duration="4104000" />
      <workItem from="1735521081275" duration="5509000" />
      <workItem from="1735607483570" duration="1719000" />
      <workItem from="1735610632803" duration="4008000" />
      <workItem from="1735781204563" duration="7765000" />
      <workItem from="1735866596169" duration="16998000" />
      <workItem from="1736125675399" duration="726000" />
      <workItem from="1736212107439" duration="2085000" />
      <workItem from="1736298431480" duration="2080000" />
      <workItem from="1736843406683" duration="1795000" />
      <workItem from="1736903415364" duration="27000" />
      <workItem from="1736989720748" duration="555000" />
      <workItem from="1737076270430" duration="594000" />
      <workItem from="1737083432559" duration="29000" />
      <workItem from="1737335490237" duration="29000" />
      <workItem from="1737335939968" duration="3714000" />
      <workItem from="1737359738274" duration="38000" />
      <workItem from="1737422906895" duration="4305000" />
      <workItem from="1737508246966" duration="5426000" />
      <workItem from="1737525170437" duration="1891000" />
      <workItem from="1737853636391" duration="2614000" />
      <workItem from="1737940074333" duration="22000" />
      <workItem from="1738900795103" duration="1665000" />
      <workItem from="1738906844475" duration="7624000" />
      <workItem from="1738976961350" duration="5433000" />
      <workItem from="1738993605789" duration="3508000" />
      <workItem from="1739149753053" duration="7317000" />
      <workItem from="1739236339940" duration="13864000" />
      <workItem from="1739322679357" duration="2349000" />
      <workItem from="1739517948927" duration="590000" />
      <workItem from="1739948716712" duration="22000" />
      <workItem from="1740449339118" duration="14257000" />
      <workItem from="1740532090625" duration="2239000" />
      <workItem from="1740534358375" duration="77000" />
      <workItem from="1740534442093" duration="16626000" />
      <workItem from="1740618497222" duration="4211000" />
      <workItem from="1740632934845" duration="9098000" />
      <workItem from="1740704920936" duration="12345000" />
      <workItem from="1740964441750" duration="15242000" />
      <workItem from="1741050765611" duration="530000" />
      <workItem from="1741136868584" duration="6485000" />
      <workItem from="1741223300105" duration="624000" />
      <workItem from="1741569806168" duration="9403000" />
      <workItem from="1741914563878" duration="9452000" />
      <workItem from="1741932435210" duration="7520000" />
      <workItem from="1742173721944" duration="7838000" />
      <workItem from="1742260132630" duration="16220000" />
      <workItem from="1742519410582" duration="1262000" />
      <workItem from="1742778594326" duration="6628000" />
      <workItem from="1742865119309" duration="7796000" />
      <workItem from="1743037943476" duration="1338000" />
      <workItem from="1743124202435" duration="2008000" />
      <workItem from="1743383674434" duration="7169000" />
      <workItem from="1743469814529" duration="11489000" />
      <workItem from="1743556380113" duration="5607000" />
      <workItem from="1743988298408" duration="1943000" />
      <workItem from="1744077929934" duration="595000" />
      <workItem from="1744161020363" duration="9140000" />
      <workItem from="1744247444008" duration="6314000" />
      <workItem from="1744333770152" duration="6677000" />
      <workItem from="1744593140156" duration="1975000" />
      <workItem from="1744679615869" duration="866000" />
      <workItem from="1744852523311" duration="623000" />
      <workItem from="1744940132795" duration="5814000" />
      <workItem from="1745198487644" duration="21390000" />
      <workItem from="1745284207286" duration="9159000" />
      <workItem from="1745370707530" duration="3035000" />
      <workItem from="1745457035452" duration="593000" />
      <workItem from="1745550708397" duration="4032000" />
      <workItem from="1745716353152" duration="640000" />
      <workItem from="1745802577867" duration="937000" />
      <workItem from="1745804833344" duration="595000" />
      <workItem from="1746771649423" duration="434000" />
      <workItem from="1747032312174" duration="594000" />
      <workItem from="1747033347419" duration="1252000" />
      <workItem from="1747098776875" duration="11021000" />
      <workItem from="1747185376595" duration="4473000" />
      <workItem from="1747277631581" duration="1272000" />
      <workItem from="1747297158677" duration="13000" />
      <workItem from="1747360053526" duration="18000" />
      <workItem from="1747882618088" duration="51000" />
      <workItem from="1747882894976" duration="355000" />
      <workItem from="1748394567381" duration="6121000" />
      <workItem from="1748481092876" duration="598000" />
      <workItem from="1748567286406" duration="1570000" />
      <workItem from="1749004379503" duration="80000" />
      <workItem from="1749005830797" duration="4000" />
      <workItem from="1749087763367" duration="2886000" />
      <workItem from="1749194521269" duration="4713000" />
      <workItem from="1749431478676" duration="7400000" />
      <workItem from="1749518100456" duration="8547000" />
      <workItem from="1749550300871" duration="302000" />
      <workItem from="1749604172374" duration="4241000" />
      <workItem from="1749628798800" duration="2200000" />
      <workItem from="1749690556387" duration="7887000" />
      <workItem from="1750036256533" duration="3469000" />
      <workItem from="1750144640727" duration="11000" />
      <workItem from="1750211258612" duration="6533000" />
      <workItem from="1750298339386" duration="15771000" />
      <workItem from="1750382017137" duration="3079000" />
      <workItem from="1750388634451" duration="14977000" />
      <workItem from="1750641025500" duration="16033000" />
      <workItem from="1750727499426" duration="10503000" />
      <workItem from="1750813945429" duration="5671000" />
      <workItem from="1750900346389" duration="1863000" />
      <workItem from="1751008882232" duration="8321000" />
      <workItem from="1751245738602" duration="4644000" />
      <workItem from="1751262479218" duration="409000" />
      <workItem from="1751265735095" duration="11000" />
      <workItem from="1751267652789" duration="94000" />
      <workItem from="1751272129241" duration="1185000" />
      <workItem from="1751277525495" duration="87000" />
      <workItem from="1751422583840" duration="2439000" />
      <workItem from="1751426115124" duration="317000" />
      <workItem from="1751426448219" duration="347000" />
      <workItem from="1751426803461" duration="80000" />
      <workItem from="1751427150119" duration="2633000" />
      <workItem from="1751434055329" duration="129000" />
      <workItem from="1751434206487" duration="305000" />
      <workItem from="1751438370469" duration="2094000" />
      <workItem from="1751448353277" duration="845000" />
      <workItem from="1751505307824" duration="12566000" />
      <workItem from="1751592275199" duration="3160000" />
      <workItem from="1751606219882" duration="4454000" />
      <workItem from="1751869571008" duration="2905000" />
      <workItem from="1751937776973" duration="14009000" />
      <workItem from="1752025195437" duration="64000" />
      <workItem from="1752025434023" duration="74000" />
      <workItem from="1752031668843" duration="10828000" />
      <workItem from="1752109705630" duration="8827000" />
      <workItem from="1752196231091" duration="9851000" />
      <workItem from="1752455306297" duration="971000" />
      <workItem from="1752457111903" duration="1298000" />
      <workItem from="1752479996576" duration="2524000" />
      <workItem from="1752541790170" duration="1899000" />
      <workItem from="1752628235238" duration="690000" />
      <workItem from="1752655901522" duration="59000" />
      <workItem from="1752717479660" duration="1040000" />
      <workItem from="1752734444117" duration="712000" />
      <workItem from="1752745331210" duration="291000" />
      <workItem from="1752745633982" duration="204000" />
      <workItem from="1753080181968" duration="564000" />
      <workItem from="1753081582214" duration="1623000" />
      <workItem from="1753084631981" duration="4431000" />
      <workItem from="1753146774942" duration="11134000" />
      <workItem from="1753233488865" duration="2801000" />
      <workItem from="1753236832047" duration="4146000" />
      <workItem from="1753337285230" duration="116000" />
      <workItem from="1753410622297" duration="151000" />
      <workItem from="1753424978645" duration="6732000" />
      <workItem from="1753665340269" duration="13208000" />
      <workItem from="1753751625999" duration="1884000" />
      <workItem from="1753782863228" duration="69000" />
      <workItem from="1753837748419" duration="920000" />
      <workItem from="1753844325234" duration="19000" />
      <workItem from="1753858465944" duration="383000" />
      <workItem from="1753860121398" duration="804000" />
      <workItem from="1753861752689" duration="31000" />
      <workItem from="1753861929108" duration="1000" />
      <workItem from="1753862913999" duration="551000" />
      <workItem from="1753863477358" duration="683000" />
      <workItem from="1753864996167" duration="50000" />
      <workItem from="1753924508357" duration="642000" />
      <workItem from="1753928977327" duration="196000" />
      <workItem from="1753953609569" duration="1523000" />
      <workItem from="1754010847877" duration="52000" />
      <workItem from="1754028226229" duration="2766000" />
      <workItem from="1754269917399" duration="63000" />
      <workItem from="1754291241278" duration="17000" />
      <workItem from="1754364475944" duration="4000" />
      <workItem from="1754456964945" duration="9193000" />
      <workItem from="1754466657463" duration="136000" />
      <workItem from="1754961923666" duration="270000" />
      <workItem from="1754962209292" duration="1478000" />
      <workItem from="1754966939688" duration="268000" />
      <workItem from="1754983653253" duration="27000" />
      <workItem from="1755065396659" duration="1000" />
      <workItem from="1755065403199" duration="682000" />
      <workItem from="1755237324965" duration="599000" />
      <workItem from="1755238046858" duration="22000" />
      <workItem from="1755244118871" duration="154000" />
      <workItem from="1755247111244" duration="38000" />
      <workItem from="1755247513046" duration="178000" />
      <workItem from="1755248567623" duration="238000" />
      <workItem from="1755249398859" duration="58000" />
      <workItem from="1755495757294" duration="325000" />
      <workItem from="1755590113350" duration="146000" />
      <workItem from="1755672726983" duration="219000" />
      <workItem from="1755742276489" duration="360000" />
      <workItem from="1755743137641" duration="11796000" />
      <workItem from="1755826674354" duration="3736000" />
      <workItem from="1755844570984" duration="1617000" />
      <workItem from="1756085628277" duration="14000" />
      <workItem from="1756088628682" duration="2222000" />
      <workItem from="1756113431053" duration="604000" />
      <workItem from="1756175802791" duration="1593000" />
      <workItem from="1756260121246" duration="3586000" />
      <workItem from="1756287417744" duration="35000" />
      <workItem from="1756349856686" duration="1588000" />
      <workItem from="1756368171562" duration="36000" />
      <workItem from="1756795345836" duration="601000" />
      <workItem from="1756798303287" duration="225000" />
      <workItem from="1756799522720" duration="14000" />
      <workItem from="1756882173472" duration="4517000" />
      <workItem from="1756955118491" duration="1931000" />
    </task>
    <task id="LOCAL-00080" summary="全量图片">
      <option name="closed" value="true" />
      <created>1748414069624</created>
      <option name="number" value="00080" />
      <option name="presentableId" value="LOCAL-00080" />
      <option name="project" value="LOCAL" />
      <updated>1748414069625</updated>
    </task>
    <task id="LOCAL-00081" summary="同步主干">
      <option name="closed" value="true" />
      <created>1749004423437</created>
      <option name="number" value="00081" />
      <option name="presentableId" value="LOCAL-00081" />
      <option name="project" value="LOCAL" />
      <updated>1749004423437</updated>
    </task>
    <task id="LOCAL-00082" summary="图片资源接口">
      <option name="closed" value="true" />
      <created>1749437526314</created>
      <option name="number" value="00082" />
      <option name="presentableId" value="LOCAL-00082" />
      <option name="project" value="LOCAL" />
      <updated>1749437526314</updated>
    </task>
    <task id="LOCAL-00083" summary="启动参数">
      <option name="closed" value="true" />
      <created>1749525262964</created>
      <option name="number" value="00083" />
      <option name="presentableId" value="LOCAL-00083" />
      <option name="project" value="LOCAL" />
      <updated>1749525262964</updated>
    </task>
    <task id="LOCAL-00084" summary="缓存时间修改">
      <option name="closed" value="true" />
      <created>1749534088617</created>
      <option name="number" value="00084" />
      <option name="presentableId" value="LOCAL-00084" />
      <option name="project" value="LOCAL" />
      <updated>1749534088617</updated>
    </task>
    <task id="LOCAL-00085" summary="游戏特权添加状态">
      <option name="closed" value="true" />
      <created>1749629196283</created>
      <option name="number" value="00085" />
      <option name="presentableId" value="LOCAL-00085" />
      <option name="project" value="LOCAL" />
      <updated>1749629196283</updated>
    </task>
    <task id="LOCAL-00086" summary="同步主干">
      <option name="closed" value="true" />
      <created>1749696464126</created>
      <option name="number" value="00086" />
      <option name="presentableId" value="LOCAL-00086" />
      <option name="project" value="LOCAL" />
      <updated>1749696464126</updated>
    </task>
    <task id="LOCAL-00087" summary="基类">
      <option name="closed" value="true" />
      <created>1750311465042</created>
      <option name="number" value="00087" />
      <option name="presentableId" value="LOCAL-00087" />
      <option name="project" value="LOCAL" />
      <updated>1750311465042</updated>
    </task>
    <task id="LOCAL-00088" summary="猜角色">
      <option name="closed" value="true" />
      <created>1750756647730</created>
      <option name="number" value="00088" />
      <option name="presentableId" value="LOCAL-00088" />
      <option name="project" value="LOCAL" />
      <updated>1750756647731</updated>
    </task>
    <task id="LOCAL-00089" summary="同步主干">
      <option name="closed" value="true" />
      <created>1751009275244</created>
      <option name="number" value="00089" />
      <option name="presentableId" value="LOCAL-00089" />
      <option name="project" value="LOCAL" />
      <updated>1751009275244</updated>
    </task>
    <task id="LOCAL-00090" summary="页面部署|代码优化">
      <option name="closed" value="true" />
      <created>1751015899653</created>
      <option name="number" value="00090" />
      <option name="presentableId" value="LOCAL-00090" />
      <option name="project" value="LOCAL" />
      <updated>1751015899653</updated>
    </task>
    <task id="LOCAL-00091" summary="入口名称">
      <option name="closed" value="true" />
      <created>1751424521944</created>
      <option name="number" value="00091" />
      <option name="presentableId" value="LOCAL-00091" />
      <option name="project" value="LOCAL" />
      <updated>1751424521944</updated>
    </task>
    <task id="LOCAL-00092" summary="领奖接口增加图片返回">
      <option name="closed" value="true" />
      <created>1751510920942</created>
      <option name="number" value="00092" />
      <option name="presentableId" value="LOCAL-00092" />
      <option name="project" value="LOCAL" />
      <updated>1751510920942</updated>
    </task>
    <task id="LOCAL-00093" summary="静态资源替换">
      <option name="closed" value="true" />
      <created>1751523826433</created>
      <option name="number" value="00093" />
      <option name="presentableId" value="LOCAL-00093" />
      <option name="project" value="LOCAL" />
      <updated>1751523826433</updated>
    </task>
    <task id="LOCAL-00094" summary="接口增加校验">
      <option name="closed" value="true" />
      <created>1751527829585</created>
      <option name="number" value="00094" />
      <option name="presentableId" value="LOCAL-00094" />
      <option name="project" value="LOCAL" />
      <updated>1751527829585</updated>
    </task>
    <task id="LOCAL-00095" summary="聊天token扣减">
      <option name="closed" value="true" />
      <created>1751528685142</created>
      <option name="number" value="00095" />
      <option name="presentableId" value="LOCAL-00095" />
      <option name="project" value="LOCAL" />
      <updated>1751528685142</updated>
    </task>
    <task id="LOCAL-00096" summary="同步主干">
      <option name="closed" value="true" />
      <created>1751610335052</created>
      <option name="number" value="00096" />
      <option name="presentableId" value="LOCAL-00096" />
      <option name="project" value="LOCAL" />
      <updated>1751610335053</updated>
    </task>
    <task id="LOCAL-00097" summary="添加日志">
      <option name="closed" value="true" />
      <created>1751939358450</created>
      <option name="number" value="00097" />
      <option name="presentableId" value="LOCAL-00097" />
      <option name="project" value="LOCAL" />
      <updated>1751939358450</updated>
    </task>
    <task id="LOCAL-00098" summary="网吧特权接口修改">
      <option name="closed" value="true" />
      <created>1752117597698</created>
      <option name="number" value="00098" />
      <option name="presentableId" value="LOCAL-00098" />
      <option name="project" value="LOCAL" />
      <updated>1752117597699</updated>
    </task>
    <task id="LOCAL-00099" summary="增加等级返回">
      <option name="closed" value="true" />
      <created>1753084930306</created>
      <option name="number" value="00099" />
      <option name="presentableId" value="LOCAL-00099" />
      <option name="project" value="LOCAL" />
      <updated>1753084930306</updated>
    </task>
    <task id="LOCAL-00100" summary="模型失效">
      <option name="closed" value="true" />
      <created>1753147486568</created>
      <option name="number" value="00100" />
      <option name="presentableId" value="LOCAL-00100" />
      <option name="project" value="LOCAL" />
      <updated>1753147486568</updated>
    </task>
    <task id="LOCAL-00101" summary="ai游戏系统对话">
      <option name="closed" value="true" />
      <created>1753170180317</created>
      <option name="number" value="00101" />
      <option name="presentableId" value="LOCAL-00101" />
      <option name="project" value="LOCAL" />
      <updated>1753170180317</updated>
    </task>
    <task id="LOCAL-00102" summary="裁剪记录">
      <option name="closed" value="true" />
      <created>1753235569539</created>
      <option name="number" value="00102" />
      <option name="presentableId" value="LOCAL-00102" />
      <option name="project" value="LOCAL" />
      <updated>1753235569540</updated>
    </task>
    <task id="LOCAL-00103" summary="添加模型状态判断">
      <option name="closed" value="true" />
      <created>1753240986927</created>
      <option name="number" value="00103" />
      <option name="presentableId" value="LOCAL-00103" />
      <option name="project" value="LOCAL" />
      <updated>1753240986927</updated>
    </task>
    <task id="LOCAL-00104" summary="猜谜次数">
      <option name="closed" value="true" />
      <created>1753337374719</created>
      <option name="number" value="00104" />
      <option name="presentableId" value="LOCAL-00104" />
      <option name="project" value="LOCAL" />
      <updated>1753337374719</updated>
    </task>
    <task id="LOCAL-00105" summary="优化">
      <option name="closed" value="true" />
      <created>1753426210673</created>
      <option name="number" value="00105" />
      <option name="presentableId" value="LOCAL-00105" />
      <option name="project" value="LOCAL" />
      <updated>1753426210673</updated>
    </task>
    <task id="LOCAL-00106" summary="优化">
      <option name="closed" value="true" />
      <created>1753431845085</created>
      <option name="number" value="00106" />
      <option name="presentableId" value="LOCAL-00106" />
      <option name="project" value="LOCAL" />
      <updated>1753431845085</updated>
    </task>
    <task id="LOCAL-00107" summary="获取模型逻辑">
      <option name="closed" value="true" />
      <created>1753683861938</created>
      <option name="number" value="00107" />
      <option name="presentableId" value="LOCAL-00107" />
      <option name="project" value="LOCAL" />
      <updated>1753683861938</updated>
    </task>
    <task id="LOCAL-00108" summary="发送邮件区分业务">
      <option name="closed" value="true" />
      <created>1753684240659</created>
      <option name="number" value="00108" />
      <option name="presentableId" value="LOCAL-00108" />
      <option name="project" value="LOCAL" />
      <updated>1753684240659</updated>
    </task>
    <task id="LOCAL-00109" summary="静态资源">
      <option name="closed" value="true" />
      <created>1753684334263</created>
      <option name="number" value="00109" />
      <option name="presentableId" value="LOCAL-00109" />
      <option name="project" value="LOCAL" />
      <updated>1753684334263</updated>
    </task>
    <task id="LOCAL-00110" summary="静态资源">
      <option name="closed" value="true" />
      <created>1753687152100</created>
      <option name="number" value="00110" />
      <option name="presentableId" value="LOCAL-00110" />
      <option name="project" value="LOCAL" />
      <updated>1753687152100</updated>
    </task>
    <task id="LOCAL-00111" summary="修改">
      <option name="closed" value="true" />
      <created>1753687701180</created>
      <option name="number" value="00111" />
      <option name="presentableId" value="LOCAL-00111" />
      <option name="project" value="LOCAL" />
      <updated>1753687701181</updated>
    </task>
    <task id="LOCAL-00112" summary="修改">
      <option name="closed" value="true" />
      <created>1753687783788</created>
      <option name="number" value="00112" />
      <option name="presentableId" value="LOCAL-00112" />
      <option name="project" value="LOCAL" />
      <updated>1753687783788</updated>
    </task>
    <task id="LOCAL-00113" summary="同步主干">
      <option name="closed" value="true" />
      <created>1753753183290</created>
      <option name="number" value="00113" />
      <option name="presentableId" value="LOCAL-00113" />
      <option name="project" value="LOCAL" />
      <updated>1753753183295</updated>
    </task>
    <task id="LOCAL-00114" summary="添加日志">
      <option name="closed" value="true" />
      <created>1753844151603</created>
      <option name="number" value="00114" />
      <option name="presentableId" value="LOCAL-00114" />
      <option name="project" value="LOCAL" />
      <updated>1753844151603</updated>
    </task>
    <task id="LOCAL-00115" summary="添加日志">
      <option name="closed" value="true" />
      <created>1753844249002</created>
      <option name="number" value="00115" />
      <option name="presentableId" value="LOCAL-00115" />
      <option name="project" value="LOCAL" />
      <updated>1753844249002</updated>
    </task>
    <task id="LOCAL-00116" summary="接口过拦截器，日志处理">
      <option name="closed" value="true" />
      <created>1753860323645</created>
      <option name="number" value="00116" />
      <option name="presentableId" value="LOCAL-00116" />
      <option name="project" value="LOCAL" />
      <updated>1753860323645</updated>
    </task>
    <task id="LOCAL-00117" summary="query参数">
      <option name="closed" value="true" />
      <created>1754456980653</created>
      <option name="number" value="00117" />
      <option name="presentableId" value="LOCAL-00117" />
      <option name="project" value="LOCAL" />
      <updated>1754456980653</updated>
    </task>
    <task id="LOCAL-00118" summary="接入ai部门">
      <option name="closed" value="true" />
      <created>1754466524073</created>
      <option name="number" value="00118" />
      <option name="presentableId" value="LOCAL-00118" />
      <option name="project" value="LOCAL" />
      <updated>1754466524073</updated>
    </task>
    <task id="LOCAL-00119" summary="接入ai部门">
      <option name="closed" value="true" />
      <created>1754466694425</created>
      <option name="number" value="00119" />
      <option name="presentableId" value="LOCAL-00119" />
      <option name="project" value="LOCAL" />
      <updated>1754466694425</updated>
    </task>
    <task id="LOCAL-00120" summary="同步主干">
      <option name="closed" value="true" />
      <created>1754963486000</created>
      <option name="number" value="00120" />
      <option name="presentableId" value="LOCAL-00120" />
      <option name="project" value="LOCAL" />
      <updated>1754963486000</updated>
    </task>
    <task id="LOCAL-00121" summary="排序参数">
      <option name="closed" value="true" />
      <created>1755237926223</created>
      <option name="number" value="00121" />
      <option name="presentableId" value="LOCAL-00121" />
      <option name="project" value="LOCAL" />
      <updated>1755237926223</updated>
    </task>
    <task id="LOCAL-00122" summary="同步主干">
      <option name="closed" value="true" />
      <created>1755244166880</created>
      <option name="number" value="00122" />
      <option name="presentableId" value="LOCAL-00122" />
      <option name="project" value="LOCAL" />
      <updated>1755244166880</updated>
    </task>
    <task id="LOCAL-00123" summary="用户历史数据匹配">
      <option name="closed" value="true" />
      <created>1755762975611</created>
      <option name="number" value="00123" />
      <option name="presentableId" value="LOCAL-00123" />
      <option name="project" value="LOCAL" />
      <updated>1755762975611</updated>
    </task>
    <task id="LOCAL-00124" summary="用户历史数据匹配">
      <option name="closed" value="true" />
      <created>1755763172496</created>
      <option name="number" value="00124" />
      <option name="presentableId" value="LOCAL-00124" />
      <option name="project" value="LOCAL" />
      <updated>1755763172496</updated>
    </task>
    <task id="LOCAL-00125" summary="还原主干">
      <option name="closed" value="true" />
      <created>1755763228077</created>
      <option name="number" value="00125" />
      <option name="presentableId" value="LOCAL-00125" />
      <option name="project" value="LOCAL" />
      <updated>1755763228077</updated>
    </task>
    <task id="LOCAL-00126" summary="模型查找">
      <option name="closed" value="true" />
      <created>1755764096040</created>
      <option name="number" value="00126" />
      <option name="presentableId" value="LOCAL-00126" />
      <option name="project" value="LOCAL" />
      <updated>1755764096040</updated>
    </task>
    <task id="LOCAL-00127" summary="特权匹配">
      <option name="closed" value="true" />
      <created>1755765807160</created>
      <option name="number" value="00127" />
      <option name="presentableId" value="LOCAL-00127" />
      <option name="project" value="LOCAL" />
      <updated>1755765807160</updated>
    </task>
    <task id="LOCAL-00128" summary="同步主干">
      <option name="closed" value="true" />
      <created>1756883671533</created>
      <option name="number" value="00128" />
      <option name="presentableId" value="LOCAL-00128" />
      <option name="project" value="LOCAL" />
      <updated>1756883671533</updated>
    </task>
    <option name="localTasksCounter" value="129" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="UnknownFeatures">
    <option featureType="dependencySupport" implementationName="java:org.springframework:spring-core" />
    <option featureType="dependencySupport" implementationName="java:jakarta.validation:jakarta.validation-api" />
    <option featureType="dependencySupport" implementationName="java:io.projectreactor:reactor-core" />
    <option featureType="dependencySupport" implementationName="java:org.springframework.data:spring-data-commons" />
    <option featureType="dependencySupport" implementationName="java:org.hibernate.validator:hibernate-validator" />
    <option featureType="dependencySupport" implementationName="java:javax.validation:validation-api" />
    <option featureType="dependencySupport" implementationName="java:org.springframework:spring-webmvc" />
    <option featureType="dependencySupport" implementationName="java:org.projectlombok:lombok" />
    <option featureType="dependencySupport" implementationName="java:io.reactivex.rxjava2:rxjava" />
    <option featureType="dependencySupport" implementationName="java:junit:junit" />
    <option featureType="dependencySupport" implementationName="java:org.springframework:spring-websocket" />
    <option featureType="dependencySupport" implementationName="java:org.thymeleaf:thymeleaf" />
    <option featureType="dependencySupport" implementationName="java:org.springframework:spring-messaging" />
    <option featureType="dependencySupport" implementationName="java:org.springframework.boot:spring-boot" />
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CHECK_CODE_SMELLS_BEFORE_PROJECT_COMMIT" value="false" />
    <option name="SHOW_FILE_HISTORY_DETAILS" value="false" />
    <MESSAGE value="接口增加校验" />
    <MESSAGE value="聊天token扣减" />
    <MESSAGE value="静态资源替换" />
    <MESSAGE value="网吧特权接口修改" />
    <MESSAGE value="增加等级返回" />
    <MESSAGE value="模型失效" />
    <MESSAGE value="ai游戏系统对话" />
    <MESSAGE value="裁剪记录" />
    <MESSAGE value="添加模型状态判断" />
    <MESSAGE value="猜谜次数" />
    <MESSAGE value="优化" />
    <MESSAGE value="获取模型逻辑" />
    <MESSAGE value="发送邮件区分业务" />
    <MESSAGE value="静态资源" />
    <MESSAGE value="修改" />
    <MESSAGE value="添加日志" />
    <MESSAGE value="接口过拦截器，日志处理" />
    <MESSAGE value="query参数" />
    <MESSAGE value="接入ai部门" />
    <MESSAGE value="排序参数" />
    <MESSAGE value="用户历史数据匹配" />
    <MESSAGE value="还原主干" />
    <MESSAGE value="模型查找" />
    <MESSAGE value="特权匹配" />
    <MESSAGE value="同步主干" />
    <option name="LAST_COMMIT_MESSAGE" value="同步主干" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/marketing-service/src/main/java/com/shunwang/marketing/service/marketing/impl/GiftBagServiceImpl.java</url>
          <line>302</line>
          <option name="timeStamp" value="2" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/marketing-web/src/test/java/com/shunwang/marketing/manager/ai/AiTest.java</url>
          <line>58</line>
          <option name="timeStamp" value="8" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>