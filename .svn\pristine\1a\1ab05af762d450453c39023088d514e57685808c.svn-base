package com.shunwang.marketing.service.marketing.promotionPopupWindow;

import com.shunwang.marketing.pojo.marketing.vo.PromotionPopupWindowVo;
import org.springframework.util.CollectionUtils;

public abstract class AbstractStrategyProcessor implements WindowProcessor {
    @Override
    public boolean match(PromotionPopupWindowVo ppw) {
        if (CollectionUtils.isEmpty(ppw.getStrategyList())) {
            return false;
        }
        return ppw.getStrategyList().stream().anyMatch((item) -> item.equals(strategyValue()));
    }

    abstract String strategyValue();
}
