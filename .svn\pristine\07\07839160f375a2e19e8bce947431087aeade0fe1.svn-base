package com.shunwang.marketing.service.marketing.impl;

import com.shunwang.marketing.dao.marketing.TencentOrderNoticeMapper;
import com.shunwang.marketing.dao.BaseMapper;
import org.springframework.beans.factory.annotation.Autowired;
import com.shunwang.marketing.pojo.marketing.TencentOrderNotice;
import com.shunwang.marketing.service.impl.BaseServiceImpl;
import com.shunwang.marketing.service.marketing.TencentOrderNoticeService;
import org.springframework.stereotype.Component;
@Component
public class TencentOrderNoticeServiceImpl extends BaseServiceImpl<TencentOrderNotice> implements TencentOrderNoticeService {
    @Autowired
    private TencentOrderNoticeMapper tencentOrderNoticeMapper;
    @Override
    public BaseMapper<TencentOrderNotice> getDao() {
        return tencentOrderNoticeMapper;
    }



}
