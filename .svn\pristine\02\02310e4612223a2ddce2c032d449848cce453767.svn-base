<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商品结果页</title>
    <link rel="stylesheet" th:href="@{${staticServer} + '/tencent/css/payResult.css'}">
    <link rel="stylesheet" th:href="@{${staticServer} + '/tencent/css/reset.css'}">
</head>
<!-- 内联修改背景色 -->
<body th:styleappend="'background:'+ ${goods.payPageBackground}">
<main class="main">
    <!-- 支付结果 成功-->
    <div class="pay-result success" th:if="${order.payState} eq 2">
        <div class="img-box">
            <img th:src="@{${staticServer} + '/tencent/static/pay-success.png'}" alt="">
            <div class="img-desc">支付成功</div>
        </div>
        <span th:text="${goods.payCopyWriting}">· 您的顺网会员大礼包已开通，您选择的特权【泡泡加速器】特权已发放，有效期至2024年10月31日</span>
    </div>
    <!-- 支付结果 失败-->
    <div class="pay-result fail" th:if="${order.payState} eq 3">
        <div class="img-box">
            <img th:src="@{${staticServer} + '/tencent/static/pay-fail.png'}" alt="">
            <div class="img-desc">支付失败</div>
        </div>
        <span>部分商品发放失败，请联系客服</span>
        <div class="qrcode-wrapper">
            <div class="qrcode-bg">
                <img th:src="@{${staticServer} + '/tencent/static/qrcode.jpg'}" alt="" class="qrcode"></img>
            </div>
        </div>
    </div>

</main>

<script th:inline="javascript">
    function report() {
        $.ajax({
            type: 'POST',
            url: '/bigData/tencentReport',
            data: {projectId: 'minipay', businessId: 'Tencent', body: buildBody()},
            dataType: 'json',
            success: function(data) {},
            error: function(xhr, type) {}
        });
    }
    function buildBody() {
        return "支付成功页" + "\t" + "pv" + [[${order.memberId}]] + "\t" + new Date().getTime() + "\t" + [[${order.orderNo}]];
    }

    report();
</script>
</body>
</html>