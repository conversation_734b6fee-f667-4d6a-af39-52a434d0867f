package com.shunwang.marketing.service.marketing.impl;

import com.shunwang.marketing.dao.BaseMapper;
import com.shunwang.marketing.dao.marketing.BarLevelMapper;
import com.shunwang.marketing.pojo.marketing.BarLevel;
import com.shunwang.marketing.service.impl.BaseServiceImpl;
import com.shunwang.marketing.service.marketing.BarLevelPrivilegeService;
import com.shunwang.marketing.service.marketing.BarLevelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class BarLevelServiceImpl extends BaseServiceImpl<BarLevel> implements BarLevelService {
    @Autowired
    private BarLevelMapper barLevelMapper;

    @Autowired
    private BarLevelPrivilegeService barLevelPrivilegeService;

    @Override
    public BaseMapper<BarLevel> getDao() {
        return barLevelMapper;
    }


}
