<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="60 seconds" debug="false">
    <contextName>MARKETING</contextName>
    <property name="logPattern" value="[%d{yyyy-MM-dd HH:mm:ss.SSS}][%contextName][%X{kd-traceId:-N/A},%X{kd-spanId:-N/A},%X{kd-parent-spanId:-N/A}][%p][%t][%logger{36}.%L]-%m%n"/>

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${logPattern}</pattern>
            <!-- 控制台也要使用UTF-8，不要使用GBK，否则会中文乱码 -->
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <appender name="appLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${MARKETING_FRONT_LOG_HOME}/app/app.log</File>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${MARKETING_FRONT_LOG_HOME}/app/app_%d.log</fileNamePattern>
            <maxHistory>6</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>${logPattern}</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <appender name="sqlLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${MARKETING_FRONT_LOG_HOME}/sql/sql.log</File>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${MARKETING_FRONT_LOG_HOME}/sql/sql_%d.log</fileNamePattern>
            <maxHistory>6</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>${logPattern}</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <appender name="errorLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${MARKETING_FRONT_LOG_HOME}/app/error.log</File>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${MARKETING_FRONT_LOG_HOME}/app/error_%d.log</fileNamePattern>
            <maxHistory>6</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>${logPattern}</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!-- 计费上机上报数据 -->
    <appender name="reportLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${MARKETING_FRONT_LOG_HOME}/report/report.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${MARKETING_FRONT_LOG_HOME}/report/report_%d.log</fileNamePattern>
            <maxHistory>5</maxHistory>
        </rollingPolicy>

        <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
            <providers class="net.logstash.logback.composite.loggingevent.LoggingEventJsonProviders">
                <pattern>
                    <pattern>
                        {
                        "date":"%d{yyyy-MM-dd HH:mm:ss.SSS}",
                        "msg":"#asJson{%msg}"
                        }
                    </pattern>
                </pattern>
            </providers>
        </encoder>
    </appender>
    <!-- 控制台输出日志级别 -->
    <root level="info">
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="appLog"/>
    </root>
    <logger name="com.shunwang" level="DEBUG">
        <appender-ref ref="appLog"/>
        <appender-ref ref="errorLog"/>
    </logger>

    <logger name="com.shunwang.marketing.dao" level="DEBUG">
        <appender-ref ref="sqlLog"/>
        <appender-ref ref="errorLog"/>
    </logger>

    <logger name="com.shunwang.marketing.ReportLog" level="INFO" additivity="true">
        <appender-ref ref="reportLog"/>
    </logger>

    <logger name="com.shunwang.marketing.aspect.EncryptAspect" level="OFF">
    </logger>

    <logger name="com.shunwang.plugin.ibatis" level="off">
    </logger>

</configuration>