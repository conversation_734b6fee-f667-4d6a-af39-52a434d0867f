<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shunwang.marketing.dao.marketing.AiGameMapper">
    <resultMap id="BaseResultMap" type="aiGame">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="rule" column="rule" jdbcType="LONGVARCHAR"/>
        <result property="type" column="type" jdbcType="INTEGER"/>
        <result property="shareTitle" column="share_title" jdbcType="VARCHAR"/>
        <result property="shareImg" column="share_img" jdbcType="INTEGER"/>
        <result property="sharePath" column="share_path" jdbcType="VARCHAR"/>
        <result property="addTime" column="add_time" jdbcType="TIMESTAMP"/>
        <result property="editTime" column="edit_time" jdbcType="TIMESTAMP"/>
        <result property="addUser" column="add_user" jdbcType="VARCHAR"/>
        <result property="editUser" column="edit_user" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="modelId" column="model_id" jdbcType="INTEGER"/>
        <result property="state" column="state" jdbcType="INTEGER"/>
        <result property="gameItemConfig" column="game_item_config" jdbcType="VARCHAR"/>
        <result property="guideLanguage" column="guide_language" jdbcType="VARCHAR"/>
        <result property="bgImg" column="bg_img" jdbcType="INTEGER"/>

    </resultMap>
    <sql id="baseColumn">
        id,
        name,
        rule,
        type,
        share_title,
        share_img,
        share_path,
        add_time,
        edit_time,
        add_user,
        edit_user,
        remark,
        model_id,
        game_item_config,
        state,
        guide_language,
        bg_img
    </sql>
    <sql id="base_where">
        <trim prefix="where" prefixOverrides="and|or">
            <if test="id != null">
                and id = #{id,jdbcType=INTEGER}
            </if>
            <if test="name != null and name != ''">
                and name = #{name,jdbcType=VARCHAR}
            </if>
            <if test="rule != null and rule != ''">
                and rule = #{rule,jdbcType=LONGVARCHAR}
            </if>
            <if test="type != null">
                and type = #{type,jdbcType=INTEGER}
            </if>
            <if test="shareTitle != null and shareTitle != ''">
                and share_title = #{shareTitle,jdbcType=VARCHAR}
            </if>
            <if test="shareImg != null">
                and share_img = #{shareImg,jdbcType=INTEGER}
            </if>
            <if test="sharePath != null and sharePath != ''">
                and share_path = #{sharePath,jdbcType=VARCHAR}
            </if>
            <if test="addTime != null">
                and add_time = #{addTime,jdbcType=TIMESTAMP}
            </if>
            <if test="editTime != null">
                and edit_time = #{editTime,jdbcType=TIMESTAMP}
            </if>
            <if test="addUser != null and addUser != ''">
                and add_user = #{addUser,jdbcType=VARCHAR}
            </if>
            <if test="editUser != null and editUser != ''">
                and edit_user = #{editUser,jdbcType=VARCHAR}
            </if>
            <if test="remark != null and remark != ''">
                and remark = #{remark,jdbcType=VARCHAR}
            </if>
            <if test="modelId != null">
                and model_id = #{modelId,jdbcType=INTEGER}
            </if>
            <if test="state != null">
                and state = #{state,jdbcType=INTEGER}
            </if>
        </trim>
    </sql>
    <select id="find" parameterType="com.shunwang.marketing.query.BaseQuery" resultMap="BaseResultMap">
        select
        <include refid="baseColumn"/>
        from marketing_ai_game t
        <include refid="base_where"/>
    </select>
    <select id="findCnt" parameterType="com.shunwang.marketing.query.BaseQuery" resultType="java.lang.Integer">
        select
            count(*)
        from marketing_ai_game t
        <include refid="base_where"/>
    </select>
    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select
        <include refid="baseColumn"/>
        from marketing_ai_game
        where id = #{id,jdbcType=INTEGER}
    </select>
</mapper> 
