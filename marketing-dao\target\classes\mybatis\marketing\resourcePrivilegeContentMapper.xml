<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shunwang.marketing.dao.marketing.ResourcePrivilegeContentMapper">
    <resultMap id="BaseResultMap" type="resourcePrivilegeContent">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="level" column="level" jdbcType="TINYINT"/>
        <result property="privilegeType" column="privilege_type" jdbcType="TINYINT"/>
        <result property="privilegeRelateId" column="privilege_relate_id" jdbcType="INTEGER"/>
        <result property="resourcePrivilegeId" column="resource_privilege_id" jdbcType="INTEGER"/>
        <result property="hoverImg" column="hover_img" jdbcType="VARCHAR"/>
        <result property="description" column="description" jdbcType="VARCHAR"/>
        <result property="type" column="type" jdbcType="INTEGER"/>
        <result property="openType" column="open_type" jdbcType="INTEGER"/>
        <result property="openUrl" column="open_url" jdbcType="VARCHAR"/>
        <result property="detail" column="detail" jdbcType="VARCHAR"/>
        <result property="showImg" column="show_img" jdbcType="VARCHAR"/>
        <result property="args" column="args" jdbcType="VARCHAR"/>
        <result property="startTime" column="start_time" jdbcType="TIMESTAMP"/>
        <result property="endTime" column="end_time" jdbcType="TIMESTAMP"/>

        <result property="resourceName" column="resource_name" jdbcType="VARCHAR"/>
        <result property="resourceAlias" column="resource_alias" jdbcType="VARCHAR"/>
        <result property="resourceId" column="resource_id" jdbcType="INTEGER"/>
        <result property="sourceType" column="source_type" jdbcType="INTEGER"/>
        <result property="markType" column="mark_type" jdbcType="INTEGER"/>
        <result property="sort" column="sort" jdbcType="INTEGER"/>
        <result property="buss" column="buss" jdbcType="INTEGER"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="baseColumn">
        id,
        level,
        privilege_type,
        privilege_relate_id,
        resource_privilege_id,
        hover_img,
        description,
        type,
        open_type,
        open_url,
        detail,
        show_img,
        args,
        start_time,
        end_time,
          sort,
        buss,
        remark
    </sql>
    <sql id="base_where" >
        <trim prefix="where" prefixOverrides="and|or">
            <if test="id != null ">
     	        and id = #{id,jdbcType=INTEGER}
            </if>
            <if test="level != null ">
     	        and level = #{level,jdbcType=TINYINT}
            </if>
            <if test="privilegeType != null ">
     	        and privilege_type = #{privilegeType,jdbcType=TINYINT}
            </if>
            <if test="privilegeRelateId != null ">
     	        and privilege_relate_id = #{privilegeRelateId,jdbcType=INTEGER}
            </if>
            <if test="resourcePrivilegeId != null ">
     	        and resource_privilege_id = #{resourcePrivilegeId,jdbcType=INTEGER}
            </if>
            <if test="hoverImg != null and hoverImg != ''">
                and hover_img = #{hoverImg,jdbcType=VARCHAR}
            </if>
            <if test="description != null and description != ''">
                and description = #{description,jdbcType=VARCHAR}
            </if>
            <if test="type != null">
                and type = #{type,jdbcType=INTEGER}
            </if>
            <if test="openType != null">
                and open_type = #{openType,jdbcType=INTEGER}
            </if>
            <if test="openUrl != null and openUrl != ''">
                and open_url = #{openUrl,jdbcType=VARCHAR}
            </if>
            <if test="detail != null and detail != ''">
                and detail = #{detail,jdbcType=VARCHAR}
            </if>
            <if test="showImg != null and showImg != ''">
                and show_img = #{showImg,jdbcType=VARCHAR}
            </if>
            <if test="args != null and args != ''">
                and args = #{args,jdbcType=VARCHAR}
            </if>
            <if test="startTime != null">
                and start_time = #{startTime,jdbcType=TIMESTAMP}
            </if>
            <if test="endTime != null">
                and end_time = #{endTime,jdbcType=TIMESTAMP}
            </if>
            <if test="buss != null">
                and buss = #{buss,jdbcType=INTEGER}
            </if>
            <if test="remark != null and remark != ''">
                and remark = #{remark,jdbcType=VARCHAR}
            </if>
        </trim>
    </sql>
    <select id="find" parameterType="com.shunwang.marketing.query.BaseQuery" resultMap="BaseResultMap" >
        select 
        <include refid="baseColumn" />
        from marketing_resource_privilege_content t 
        <include refid="base_where" />
    </select>
    <select id="findCnt" parameterType="com.shunwang.marketing.query.BaseQuery" resultType="java.lang.Integer" >
        select
            count(*)
        from marketing_resource_privilege_content t
        <include refid="base_where" />
    </select>
    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select
        <include refid="baseColumn" />
        from marketing_resource_privilege_content
        where id = #{id,jdbcType=INTEGER}
    </select>

    <select id="findByResourcePrivilegeIdsAndLevelsAndIds" resultMap="BaseResultMap">
        <foreach collection="resourcePrivilegeContentQueries" item="query" separator=" union all ">
            select
            <include refid="baseColumn" />
            from marketing_resource_privilege_content
            <where>
                id in
                <foreach collection="resourcePrivilegeContentIds" open="(" close=")" item="id" separator=",">
                    #{id,javaType=INTEGER}
                </foreach>
                and resource_privilege_id = #{query.resourcePrivilegeId,jdbcType=VARCHAR}
                and level = #{query.level,jdbcType=INTEGER}
            </where>
        </foreach>

    </select>
    <select id="findOnlineByBarLevelId"
            resultMap="BaseResultMap">
        select b.resource_name, b.resource_alias, b.resource_id,b.source_type,b.mark_type, c.*
        from marketing_bar_level_resource a,
             marketing_resource_privilege b,
             marketing_resource_privilege_content c
        where a.resource_privilege_id = b.id
          and a.resource_privilege_id = c.resource_privilege_id
          and c.start_time &lt; NOW()
          and c.end_time > NOW()
          and a.bar_level_id=#{barLevelId,jdbcType=INTEGER}

    </select>
    <insert id="save" parameterType="resourcePrivilegeContent" keyProperty="id" useGeneratedKeys="true">
        insert into marketing_resource_privilege_content 
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null">
                id,
            </if>
            <if test="level != null">
                level,
            </if>
            <if test="privilegeType != null">
                privilege_type,
            </if>
            <if test="privilegeRelateId != null">
                privilege_relate_id,
            </if>
            <if test="resourcePrivilegeId != null">
                resource_privilege_id,
            </if>
            <if test="hoverImg != null and hoverImg != ''">
                hover_img,
            </if>
            <if test="description != null and description != ''">
                description,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="openType != null">
                open_type,
            </if>
            <if test="openUrl != null and openUrl != ''">
                open_url,
            </if>
            <if test="detail != null and detail != ''">
                detail,
            </if>
            <if test="showImg != null and showImg != ''">
                show_img,
            </if>
            <if test="args != null and args != ''">
                args,
            </if>
            <if test="startTime != null">
                start_time,
            </if>
            <if test="endTime != null">
                end_time,
            </if>
            <if test="buss != null">
                buss,
            </if>
            <if test="remark != null and remark != ''">
                remark,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="id != null">
                #{id,jdbcType=INTEGER},
            </if>
            <if test="level != null">
                #{level,jdbcType=TINYINT},
            </if>
            <if test="privilegeType != null">
                #{privilegeType,jdbcType=TINYINT},
            </if>
            <if test="privilegeRelateId != null">
                #{privilegeRelateId,jdbcType=INTEGER},
            </if>
            <if test="resourcePrivilegeId != null">
                #{resourcePrivilegeId,jdbcType=INTEGER},
            </if>
            <if test="hoverImg != null and hoverImg != ''">
                #{hoverImg,jdbcType=VARCHAR},
            </if>
            <if test="description != null and description != ''">
                #{description,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                #{type,jdbcType=INTEGER},
            </if>
            <if test="openType != null">
                #{openType,jdbcType=INTEGER},
            </if>
            <if test="openUrl != null and openUrl != ''">
                #{openUrl,jdbcType=VARCHAR},
            </if>
            <if test="detail != null and detail != ''">
                #{detail,jdbcType=VARCHAR},
            </if>
            <if test="showImg != null and showImg != ''">
                #{showImg,jdbcType=VARCHAR},
            </if>
            <if test="args != null and args != ''">
                #{args,jdbcType=VARCHAR},
            </if>
            <if test="startTime != null">
                #{startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="endTime != null">
                #{endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="buss != null">
                #{buss,jdbcType=INTEGER},
            </if>
            <if test="remark != null and remark != ''">
                #{remark,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="resourcePrivilegeContent" >
        update marketing_resource_privilege_content
        <set>
            <if test="id != null">
     	        id = #{id,jdbcType=INTEGER},
            </if>
            <if test="level != null">
     	        level = #{level,jdbcType=TINYINT},
            </if>
            <if test="privilegeType != null">
     	        privilege_type = #{privilegeType,jdbcType=TINYINT},
            </if>
            <if test="privilegeRelateId != null">
     	        privilege_relate_id = #{privilegeRelateId,jdbcType=INTEGER},
            </if>
            <if test="resourcePrivilegeId != null">
     	        resource_privilege_id = #{resourcePrivilegeId,jdbcType=INTEGER},
            </if>
            <if test="hoverImg != null and hoverImg != ''">
                hover_img = #{hoverImg,jdbcType=VARCHAR},
            </if>
            <if test="description != null and description != ''">
                description = #{description,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                type = #{type,jdbcType=INTEGER},
            </if>
            <if test="openType != null">
                open_type = #{openType,jdbcType=INTEGER},
            </if>
            <if test="openUrl != null and openUrl != ''">
                open_url = #{openUrl,jdbcType=VARCHAR},
            </if>
            <if test="detail != null and detail != ''">
                detail = #{detail,jdbcType=VARCHAR},
            </if>
            <if test="showImg != null and showImg != ''">
                show_img = #{showImg,jdbcType=VARCHAR},
            </if>
            <if test="args != null and args != ''">
                args = #{args,jdbcType=VARCHAR},
            </if>
            <if test="startTime != null">
                start_time = #{startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="endTime != null">
                end_time = #{endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="buss != null">
                buss = #{buss,jdbcType=INTEGER},
            </if>
            <if test="remark != null and remark != ''">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
        </set>
        <trim prefix="where" prefixOverrides="and|or"> 
            id = #{id,jdbcType=INTEGER}
        </trim>
    </update>

    <delete id="delete" parameterType="integer">
        delete from marketing_resource_privilege_content where id = #{id,jdbcType=INTEGER}
    </delete>
</mapper>
