# 实施计划

- [ ] 1. 添加 Spring AI 依赖和基础配置


  - 在父项目 pom.xml 中添加 Spring AI BOM 依赖管理
  - 在 marketing-web 模块添加 spring-ai-openai-spring-boot-starter 依赖
  - 创建基础的 application.yml 配置文件模板
  - _需求: 1.1, 3.1_

- [ ] 2. 创建 Spring AI 配置类和属性类
  - 实现 SpringAiProperties 配置属性类，支持多种 AI 提供商配置
  - 创建 SpringAiConfig 配置类，定义 ChatClient Bean
  - 实现 PromptTemplateManager 类，管理系统提示词模板
  - _需求: 1.1, 3.2, 4.4_

- [ ] 3. 实现新的 AI 聊天服务类
  - 创建 SpringAiChatService 类，封装 Spring AI ChatClient 调用逻辑
  - 实现流式响应处理，支持 SSE 输出格式
  - 添加错误处理和重试机制
  - _需求: 1.2, 1.3, 3.3_

- [ ] 4. 重构 AiController 聊天接口
  - 修改 chat 方法，使用新的 SpringAiChatService 替代原有 AiService
  - 保持现有的频率限制和用户授权验证逻辑
  - 确保 SSE 响应格式与原有实现完全兼容
  - _需求: 1.4, 2.1, 4.1, 4.2_

- [ ] 5. 实现对话记录保存功能
  - 在 SpringAiChatService 中集成 AiRecordService 调用
  - 确保对话记录包含用户问题、AI 回答和时间戳
  - 保持与现有 AiRecord 数据结构的完全兼容
  - _需求: 5.1, 5.2, 5.4_

- [ ] 6. 创建异常处理和降级机制
  - 定义 SpringAiException 异常层次结构
  - 实现全局异常处理器，处理 AI 服务相关异常
  - 添加服务降级逻辑，AI 服务不可用时返回友好提示
  - _需求: 3.3, 4.3_

- [ ] 7. 编写单元测试
  - 为 SpringAiChatService 编写单元测试，Mock ChatClient 行为
  - 为重构后的 AiController 编写单元测试，验证所有端点功能
  - 为配置类编写测试，验证不同配置下的 Bean 创建
  - _需求: 1.1, 1.4, 2.1_

- [ ] 8. 编写集成测试
  - 创建集成测试类，使用 TestContainers 进行数据库测试
  - 实现 AI 提供商集成测试，验证真实 API 调用
  - 测试频率限制和用户授权功能的集成
  - _需求: 2.2, 4.1, 4.2, 5.3_

- [ ] 9. 添加配置开关和兼容性支持
  - 添加配置属性控制使用新旧 AI 实现的开关
  - 保留原有 AiService 作为备用实现
  - 实现平滑切换机制，支持运行时切换
  - _需求: 3.1, 3.2_

- [ ] 10. 性能测试和优化
  - 编写并发测试，模拟多用户同时聊天场景
  - 测试 SSE 流式响应在高并发下的性能表现
  - 进行内存使用测试，确保无内存泄漏
  - _需求: 4.3_

- [ ] 11. 文档更新和部署准备
  - 更新 README 文档，说明新的 AI 配置方式
  - 创建部署指南，包含环境变量和配置说明
  - 准备生产环境配置模板
  - _需求: 3.1, 3.2_

- [ ] 12. 最终集成和验证
  - 运行完整的测试套件，确保所有功能正常
  - 验证所有现有 API 端点的响应格式兼容性
  - 进行端到端测试，确保前端功能无影响
  - _需求: 1.4, 2.1, 2.2, 2.3, 2.4_