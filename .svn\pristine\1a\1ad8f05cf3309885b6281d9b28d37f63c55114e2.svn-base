package com.shunwang.marketing.context;

import org.springframework.beans.BeansException;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.BeanFactoryAware;
import org.springframework.stereotype.Component;

@Component
public class SpringContext implements BeanFactoryAware {
    private static BeanFactory factory;

    @Override
    public void setBeanFactory(BeanFactory beanFactory) throws BeansException {
        factory = beanFactory;
    }

    public static Object getBean(String beanName) {
        try {
            return factory.getBean(beanName);
        } catch (BeansException e) {
            return null;
        }
    }

    public static <T> T getBean(String beanName, Class<T> clazz) {
        try {
            return factory.getBean(beanName, clazz);
        } catch (BeansException e) {
            return null;
        }
    }

    public static <T> T getBean(Class<T> clazz) {
        try {
            return factory.getBean(clazz);
        } catch (BeansException e) {
            return null;
        }
    }
}
