package com.shunwang.marketing.manager.request.ai;

import com.shunwang.marketing.manager.IResponse;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;



@Data
public class ChatRequest extends BaseAiRequest<IResponse> {
    private String model;
    private List<AiRequestMessage> messages;
    private String baseUrl;
    private String apiKey;

    @Override
    public Map<String, Object> buildObjParams() {
        Map<String, Object> map = new HashMap<>();
        map.put("model", model);
        map.put("messages", messages);
        map.put("stream", false);
        return map;
    }

    @Override
    public Class<IResponse> getResponseClass() {
        return IResponse.class;
    }


    @Override
    public Integer getInterfaceKey() {
        return -1;
    }

    @Data
    public static class AiRequestMessage{
        private String role;
        private String content;
    }
}
