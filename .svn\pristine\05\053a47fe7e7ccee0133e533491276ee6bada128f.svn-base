package com.shunwang.marketing.pojo.ai;

import com.shunwang.util.json.GsonUtil;
import lombok.Data;

@Data
public class AiReplyMessage {
    private Integer code;
    private String messageId;
    private Boolean finish;
    private String msg;
    private String content;

    public static String newErrorJsonMsg(String msg) {
            AiReplyMessage message = new AiReplyMessage();
            message.setCode(-1);
            message.setMsg(msg);
            return GsonUtil.toJson(message);
    }
}
