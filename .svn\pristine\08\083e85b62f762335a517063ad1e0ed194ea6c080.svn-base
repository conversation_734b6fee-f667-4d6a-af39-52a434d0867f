package com.shunwang.marketing.pojo.marketing;

import com.shunwang.marketing.pojo.BaseObject;
import lombok.Data;

import java.util.Date;

@Data
public class Task extends BaseObject {
	private static final long serialVersionUID = 1L;
	private Integer id;
	private String title;
	private Integer type;
	private Integer cntLimit;
	private Integer rewardType;
	private String reward;
	private Integer sort;
	private Integer condition;
	private Integer partner;
	private Date timeAdd;
	private Date timeEdit;
	private String userAdd;
	private String userEdit;
	private String remark;

	private Integer tool;
	private String toolCondition;
}
