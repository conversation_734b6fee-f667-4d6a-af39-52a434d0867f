package com.shunwang.marketing.service.marketing;

import com.shunwang.marketing.pojo.marketing.BagOrder;
import com.shunwang.marketing.pojo.marketing.GiftBag;
import com.shunwang.marketing.service.BaseService;

import java.util.List;

public interface GiftBagService extends BaseService<GiftBag> {

    /**
     * 根据渠道号获取礼包配置
     * @param channel
     * @return
     */
    List<GiftBag> getByChannel(String channel, Integer memberId);

    /**
     * 检查礼包以及礼包内奖品的库存
     * @param giftBag
     * @return
     */
    boolean checkStock(GiftBag giftBag);

    /**
     * 返回前端展示需要的我的礼包数据
     * @return
     */
    List<GiftBag> myGiftBag(List<BagOrder> bagOrderList, Integer memberId);

    List<GiftBag> findNoTagsData(String channel);

    /**
     * 领取礼包以及礼包的奖品
     * @param bagId
     */
    void receiveGiftBag(Integer bagId);

    Object buy(GiftBag bag,boolean limit);

    /**
     * 只获取匹配用户标签的一个礼包
     * @param channel
     * @param memberId
     * @return
     */
    GiftBag getMatchByChannel(String channel, Integer memberId, Integer platform);

    List<GiftBag> findOnlineByIds(List<Integer> ids);

    /**
     * 是否达到领取限制
     * @param memberId
     * @param bagId
     * @param giftBag
     * @return
     */
    boolean isReachLimit(Integer memberId, Integer bagId, GiftBag giftBag);
}
