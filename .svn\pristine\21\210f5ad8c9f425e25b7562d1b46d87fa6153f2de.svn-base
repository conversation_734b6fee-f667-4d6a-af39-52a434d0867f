<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shunwang.marketing.dao.marketing.ScoreMapper">
    <resultMap id="BaseResultMap" type="score">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="memberId" column="member_id" jdbcType="INTEGER"/>
        <result property="score" column="score" jdbcType="INTEGER"/>
        <result property="frostScore" column="frost_score" jdbcType="INTEGER"/>
        <result property="state" column="state" jdbcType="INTEGER"/>
        <result property="timeAdd" column="time_add" jdbcType="TIMESTAMP"/>
    </resultMap>
    <sql id="baseColumn">
        id,
        member_id,
        score,
        frost_score,
        state,
        time_add
    </sql>
    <sql id="base_where" >
        <trim prefix="where" prefixOverrides="and|or">
            <if test="id != null ">
     	        and id = #{id,jdbcType=INTEGER}
            </if>
            <if test="memberId != null ">
     	        and member_id = #{memberId,jdbcType=INTEGER}
            </if>
            <if test="score != null ">
     	        and score = #{score,jdbcType=INTEGER}
            </if>
            <if test="frostScore != null ">
     	        and frost_score = #{frostScore,jdbcType=INTEGER}
            </if>
            <if test="state != null ">
     	        and state = #{state,jdbcType=INTEGER}
            </if>
            <if test="timeAdd != null ">
     	        and time_add = #{timeAdd,jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>
	<select id="find" parameterType="com.shunwang.marketing.query.BaseQuery" resultMap="BaseResultMap" >
		select 
        <include refid="baseColumn" />
		from marketing_score t 
		<include refid="base_where" />
	</select>
    <select id="findOne" parameterType="com.shunwang.marketing.query.BaseQuery" resultMap="BaseResultMap" >
        select
        <include refid="baseColumn" />
        from marketing_score t
        <include refid="base_where" />
        limit 1
    </select>
    <select id="findCnt" parameterType="com.shunwang.marketing.query.BaseQuery" resultType="java.lang.Integer" >
        select
            count(*)
        from marketing_score t
		<include refid="base_where" />
    </select>
    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.Integer">
		select
        <include refid="baseColumn" />
		from marketing_score
		where id = #{id,jdbcType=INTEGER}
    </select>
    <select id="getForUpdate" resultMap="BaseResultMap">
        select
        <include refid="baseColumn"></include>
        from marketing_score
        where id = #{id,jdbcType=INTEGER} for update
    </select>
    <insert id="save" parameterType="score" useGeneratedKeys="true" keyProperty="id">
		insert into marketing_score
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="memberId != null">
                member_id,
            </if>
            <if test="score != null">
                score,
            </if>
            <if test="frostScore != null">
                frost_score,
            </if>
            <if test="state != null">
                state,
            </if>
            <if test="timeAdd != null">
                time_add,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="memberId != null">
                #{memberId,jdbcType=INTEGER},
            </if>
            <if test="score != null">
                #{score,jdbcType=INTEGER},
            </if>
            <if test="frostScore != null">
                #{frostScore,jdbcType=INTEGER},
            </if>
            <if test="state != null">
                #{state,jdbcType=INTEGER},
            </if>
            <if test="timeAdd != null">
                #{timeAdd,jdbcType=TIMESTAMP},
            </if>
        </trim>
	</insert>
	<update id="update" parameterType="score" >
		update marketing_score
        <set>
            <if test="id != null">
     	        id = #{id,jdbcType=INTEGER},
            </if>
            <if test="memberId != null">
     	        member_id = #{memberId,jdbcType=INTEGER},
            </if>
            <if test="score != null">
     	        score = #{score,jdbcType=INTEGER},
            </if>
            <if test="frostScore != null">
     	        frost_score = #{frostScore,jdbcType=INTEGER},
            </if>
            <if test="state != null">
     	        state = #{state,jdbcType=INTEGER},
            </if>
            <if test="timeAdd != null">
     	        time_add = #{timeAdd,jdbcType=TIMESTAMP},
            </if>
        </set>
		<trim prefix="where" prefixOverrides="and|or"> 
            id = #{id,jdbcType=INTEGER}
        </trim>
	</update>
    <update id="changeTotalScore">
        update marketing_score
        set score=score
        <if test="type==1">
            +#{score,jdbcType=INTEGER}
        </if>
        <if test="type==2">
            -#{score,jdbcType=INTEGER}
        </if>
        where member_id=#{memberId,jdbcType=INTEGER}
    </update>
    <update id="changeFrostScore">
        update marketing_score set frost_score=frost_score
        <if test="type==3">
            +#{score,jdbcType=INTEGER}
        </if>
        <if test="type==5">
            -#{score,jdbcType=INTEGER}
        </if>
        where member_id=#{memberId,jdbcType=INTEGER}
    </update>
    <update id="unFrostAndDec">
        update marketing_score
        set frost_score=frost_score - #{score,jdbcType=INTEGER} , score = score - #{score,jdbcType=INTEGER}
        where member_id = #{memberId,jdbcType=INTEGER}
    </update>
</mapper>
