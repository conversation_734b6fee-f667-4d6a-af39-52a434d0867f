package com.shunwang.marketing.query.marketing;

import com.shunwang.marketing.query.BaseQuery;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
@Data
public class MoneySavingCardQuery extends BaseQuery {

	private Integer id;
	private String cardName;
	private String channel;
	private String tags;
	private Integer cnt;
	private BigDecimal price;
	private BigDecimal worth;
	private Integer sort;
	private Integer state;
	private Integer unlockedCnt;
	private String remark;
	private String userAdd;
	private Date timeAdd;
	private String userEdit;
	private Date timeEdit;
	private Integer cardType;
	private Integer taskRuleId;
	private String taskPicFirst;
	private String taskPicSecond;

}
