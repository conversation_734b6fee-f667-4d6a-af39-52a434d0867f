package com.shunwang.marketing.manager.service;

import com.google.gson.reflect.TypeToken;
import com.shunwang.api.core.CommonResponse;
import com.shunwang.api.core.http.DefaultHttpClientBuilder;
import com.shunwang.api.core.http.HttpClient;
import com.shunwang.marketing.exception.UserApiBusinessException;
import com.shunwang.marketing.pojo.config.Interfaces;
import com.shunwang.marketing.service.config.InterfacesService;
import com.shunwang.userapi.client.UserApiConfig;
import com.shunwang.userapi.client.UserApiInvokerTemplate;
import com.shunwang.userapi.client.interceptor.KedouTraceInterceptor;
import com.shunwang.userapi.client.model.common.*;
import com.shunwang.userapi.client.model.files.UploadImageHeadRequest;
import com.shunwang.userapi.client.model.sms.SendCodeForLoginRequest;
import com.shunwang.userapi.client.model.sms.SendCodeForLoginResponse;
import com.shunwang.userapi.client.model.sms.ValidCodeForLoginRequest;
import com.shunwang.userapi.client.model.user.*;
import com.shunwang.util.lang.StringUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import okio.Buffer;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.shunwang.userapi.client.model.common.GatewayServiceName.*;

/**
 * 调用 userapi 包装类
 **/
@Slf4j
@Service
public class UserApiServiceClient{
    public static final Integer SUCCESS = 0;

    @Autowired
    private static InterfacesService interfacesService;

    @Autowired
    public void setConfigInterfaceService(InterfacesService configInterfaceService) {
        interfacesService = configInterfaceService;
    }


    /**
     * 获取用户
     * @return
     */
    public UserQueryResponse getUser(UserQueryRequest request) {
        return execute(USER_QUERY, request, UserQueryResponse.class);
    }

    /**
     * 修改用户信息
     * @return
     */
    public CommonResponse updateUserInfo(UpdateUserInfoRequest request) {
        return execute(USER_UPDATE_INFO, request, CommonResponse.class);
    }

    /**
     * 获取openIdUnionId
     * @return
     */
    public WxOpenIdUnionIdQueryResponse getOpenIdUnionId(WxOpenIdUnionIdQueryRequest request) {
        return execute(WECHAT_OPENID_UNIONID_QUERY, request, WxOpenIdUnionIdQueryResponse.class);
    }

    /**
     * 获取但账户信息
     * @return
     */
    public SingleAccountQueryResponse getSingleAccount(SingleAccountQueryRequest request) {
        return execute(SINGLE_QUERY, request, SingleAccountQueryResponse.class);
    }

    /**
     * 获取但账户信息
     * @return
     */
    public WechatAccessTokenQueryResponse getAccessToken(WechatAccessTokenQueryRequest request) {
        return execute(WECHAT_TOKEN_QUERY, request, WechatAccessTokenQueryResponse.class);
    }

    /**
     * 获取网吧身份证绑定
     * @return
     */
    public NetBarBindQueryResponse getWxIdcardBind(NetBarBindQueryRequest request) {
        return execute(NET_BAR_BIND_QUERY, request, NetBarBindQueryResponse.class);
    }

    /**
     * 第三方账号查询用户
     * @return
     */
    public UserQueryResponse getUserByOutSite(UserQueryByOutSiteRequest request) {
        return execute(USER_QUERY_BY_OUT_SITE, request, UserQueryResponse.class);
    }

    /**
     * 网吧授权状态更新
     * @param request
     * @return
     */
    public CommonResponse authUpdate(NetBarAuthUpdateRequest request){
        return UserApiInstance.INSTANCE.instance.postForObject(NET_BAR_AUTH_UPDATE.getName(), request, CommonResponse.class);
    }

    /**
     * 网吧授权查询接口
     * @param request
     * @return
     */
    public CommonResponse<List<NetBarAuthQueryResponse>> authQuery(NetBarAuthQueryRequest request){
        return UserApiInstance.INSTANCE.instance.postForObject(
                        NET_BAR_AUTH_QUERY.getName(),
                        request,
                        TypeToken.getParameterized(ArrayList.class, NetBarAuthQueryResponse.class).getType());
    }

    /**
     * 网吧查询接口
     * @param request
     * @return
     */
    public CommonResponse<NetBarInfoQueryResponse> infoQuery(NetBarInfoQueryRequest request){
        return UserApiInstance.INSTANCE.instance.postForObject(NET_BAR_INFO_QUERY.getName(), request, NetBarInfoQueryResponse.class);
    }

    /**
     * 单账号用户信息查询接口
     * @param request
     * @return
     */
    public CommonResponse singleQuery(SingleAccountQueryRequest request){
        return UserApiInstance.INSTANCE.instance.postForObject(SINGLE_QUERY.getName(), request, SingleAccountQueryResponse.class);
    }

    /**
     * 单账号解绑接口
     * @param request
     * @return
     */
    public CommonResponse singleUnbind(SingleAccountUnbindRequest request){
        return UserApiInstance.INSTANCE.instance.postForObject(SINGLE_UNBIND.getName(), request, CommonResponse.class);
    }

    public CommonResponse uploadHeadImg(UploadImageHeadRequest request){
        return UserApiInstance.INSTANCE_HEAD.instance.postForObject(FILES_IMAGE_UPLOAD_HEAD.getName(),request,CommonResponse.class);
    }

    /**
     * 登录发送验证码接口
     * @param request
     * @return
     */
    public CommonResponse smsSendLogin(SendCodeForLoginRequest request){
        return UserApiInstance.INSTANCE.instance.postForObject(SMS_SEND_LOGIN.getName(),request,CommonResponse.class);
    }

    /**
     * 注册登录验证短信
     * @param request
     * @return
     */
    public CommonResponse<SendCodeForLoginResponse> smsValidLogin(ValidCodeForLoginRequest request){
        return UserApiInstance.INSTANCE.instance.postForObject(SMS_VALID_LOGIN,request);
    }

    private <REQ, RESP> ArrayList<RESP> executeForList(GatewayServiceName serviceName, REQ req, Class<RESP> respClass) {

        CommonResponse<ArrayList<RESP>> commonResponse = UserApiInstance.INSTANCE.instance.postForObject(serviceName.getName(), req,
                TypeToken.getParameterized(ArrayList.class, respClass).getType());
        if (SUCCESS.equals(commonResponse.getCode())) {
            return commonResponse.getData();
        }

        throw new UserApiBusinessException(commonResponse.getCode(), commonResponse.getMessage());
    }

    private <REQ, RESP> RESP execute(GatewayServiceName serviceName, REQ req, Class<RESP> respClass) {
        CommonResponse<RESP> commonResponse = UserApiInstance.INSTANCE.instance.postForObject(serviceName.getName(), req, respClass);
        if (SUCCESS.equals(commonResponse.getCode())) {
            return commonResponse.getData();
        }
        throw new UserApiBusinessException(commonResponse.getCode(), commonResponse.getMessage());
    }

    private enum UserApiInstance {
        INSTANCE(5000, 5000, 5000, null),
        INSTANCE_HEAD(5000, 5000, 5000, "/files/image/upload_head"),
        INSTANCE_IMAGE(5000, 5000, 5000, "/files/image/upload_secure");

        private final UserApiInvokerTemplate instance;

        UserApiInstance(int readTimeout, int connectedTimeout, int writeTimeout, String url) {
            Interfaces param = new Interfaces();
            param.setInterfaceKey(10000);
            Interfaces setting = interfacesService.selectByPrimaryKeyEnabled(10000);
            if (setting == null) {
                log.error("接口[{}]配置不存在或未启用，请检查接口配置表", 10000);
                throw new UserApiBusinessException("接口配置不存在");
            }


            Interceptor loggingInterceptor = new Interceptor() {
                @NotNull
                @Override
                public Response intercept(@NotNull Chain chain) throws IOException {
                    Request request = chain.request();
                    RequestBody requestBody = request.body();
                    Buffer buffer = new Buffer();
                    requestBody.writeTo(buffer);
                    log.info(buffer.readString(StandardCharsets.UTF_8));
                    return chain.proceed(request);
                }
            };
            OkHttpClient okHttpClient = new OkHttpClient.Builder()
                    .connectionPool(new ConnectionPool(5, 7L, TimeUnit.SECONDS))
                    .addInterceptor(loggingInterceptor)
                    .addInterceptor(new KedouTraceInterceptor())
                    .build();


            UserApiConfig userApiConfig = new UserApiConfig.Builder()
                    .appId(setting.getInterfacePartnerId())
                    .url(StringUtil.isBlank(url) ? setting.getInterfaceUrl1() : setting.getInterfaceUrl1().replace("/gateway.do", "") + url)
                    .privateKey(setting.getMerchantPrivateKey())
                    .aesKey(setting.getInterfaceMd5Key())
                    .build();


            HttpClient httpClient = new DefaultHttpClientBuilder()
                    .okHttpClient(okHttpClient)
                    .connectTimeoutMs(connectedTimeout)
                    .readTimeoutMs(readTimeout)
                    .writeTimeoutMs(writeTimeout)
                    .build();

            instance = new UserApiInvokerTemplate(httpClient, userApiConfig);
        }

        public UserApiInvokerTemplate getInstance() {
            return instance;
        }
    }
}
