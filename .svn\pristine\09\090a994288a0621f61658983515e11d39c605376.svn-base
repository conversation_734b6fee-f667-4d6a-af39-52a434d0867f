package com.shunwang.marketing.enums;

import lombok.Getter;
import lombok.Setter;

public interface WeixinEnum {

    enum accountTypeEnum {
        MAIN(0, "主体"),
        OFFICIAL_ACCOUNTS(1, "公众号"),
        MINI_APP(2, "小程序"),
        ;
        accountTypeEnum(Integer value, String name) {
            this.value = value;
            this.name = name;
        }

        private @Getter @Setter Integer value;
        private @Getter @Setter String name;
    }


}
