package com.shunwang.marketing.pojo.marketing;

import com.shunwang.encrypt.core.annotation.EncryptEnabled;
import com.shunwang.encrypt.core.annotation.EncryptField;
import com.shunwang.marketing.pojo.BaseObject;
import lombok.Data;

import java.util.Date;

@EncryptEnabled
@Data
public class MiniBindPhone extends BaseObject {
	private static final long serialVersionUID = 1L;
	private Integer id;
	private String unit;
	private String siteid;
	@EncryptField(ref = "mobileCoded")
	private String mobile;
	private String mobileCoded;
	private Integer state;
	private String source;
	private String cost;
	private Date timeAdd;

}
