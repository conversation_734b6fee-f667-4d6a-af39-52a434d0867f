package com.shunwang.marketing.service.marketing;

import com.shunwang.marketing.pojo.ai.AiReplyMessage;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

public interface AiService {
    SseEmitter commonAiBuss(String question);
    AiReplyMessage gameAiBuss(String question, String sessionId, String buss, boolean system);
    SseEmitter createSseEmitter(Integer memberId, String sessionId);
}
