package com.shunwang.marketing.service.marketing;

import com.shunwang.marketing.pojo.marketing.PayOrderRecord;
import com.shunwang.marketing.pojo.marketing.Pond;
import com.shunwang.marketing.pojo.marketing.PondOrder;
import com.shunwang.marketing.pojo.marketing.StoreOrder;
import com.shunwang.marketing.service.BaseService;
public interface PondOrderService extends BaseService<PondOrder> {
    PondOrder initAndSave(Pond pond,Integer times,String orderNo,String payOrderNo);

    PondOrder getByOrderNo(String orderNo);

    void finishOrder(String orderNo, String disburseNo);

    void refund(String orderNo);

    void doRefund(Integer refundState, String orderNo);
}
