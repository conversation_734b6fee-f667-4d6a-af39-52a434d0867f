package com.shunwang.marketing.service.marketing.impl;

import com.shunwang.marketing.dao.BaseMapper;
import com.shunwang.marketing.dao.marketing.AiGameMapper;
import com.shunwang.marketing.pojo.marketing.AiGame;
import com.shunwang.marketing.service.impl.BaseServiceImpl;
import com.shunwang.marketing.service.marketing.AiGameService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class AiGameServiceImpl extends BaseServiceImpl<AiGame> implements AiGameService {
    @Autowired
    private AiGameMapper aiGameMapper;

    @Override
    public BaseMapper getDao() {
        return aiGameMapper;
    }
} 