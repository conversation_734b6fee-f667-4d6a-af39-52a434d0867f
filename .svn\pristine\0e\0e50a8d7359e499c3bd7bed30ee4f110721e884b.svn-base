package com.shunwang.marketing.context;

import com.shunwang.marketing.enums.ResourcesEnum;
import com.shunwang.marketing.pojo.config.Resources;
import com.shunwang.marketing.service.config.ResourcesService;
import com.shunwang.util.lang.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class ResourcesContext {

    private static ResourcesService resourcesService;

    @Autowired
    public void setResourcesService(ResourcesService resourcesService) {
        ResourcesContext.resourcesService = resourcesService;
    }

    public static String getStringValueWithState(ResourcesEnum.TypeName typeName) {
        Resources resources = resourcesService.getByTypeAndName(typeName);
        if (resources == null || ResourcesEnum.StateEnum.ENABLE.getValue() != resources.getState()) {
            return StringUtil.EMPTY_STRING;
        }
        return resources.getValue();
    }

    public static String getStringValue(ResourcesEnum.TypeName typeName) {
        return resourcesService.getStringValueByTypeAndName(typeName);
    }

    public static Integer getIntegerValue(ResourcesEnum.TypeName typeName) {
        return resourcesService.getIntValueByTypeAndName(typeName);
    }
}
