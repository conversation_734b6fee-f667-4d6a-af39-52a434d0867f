package com.shunwang.marketing.service.marketing.impl;

import com.shunwang.marketing.constant.SysconfigConstant;
import com.shunwang.marketing.context.ThreadLocalContext;
import com.shunwang.marketing.context.UserContext;
import com.shunwang.marketing.dao.BaseMapper;
import com.shunwang.marketing.dao.marketing.PrizeMapper;
import com.shunwang.marketing.dto.UserInfo;
import com.shunwang.marketing.enums.PayEnum;
import com.shunwang.marketing.enums.marketing.*;
import com.shunwang.marketing.manager.paopao.SendGiftService;
import com.shunwang.marketing.manager.request.game.SendCouponRequest;
import com.shunwang.marketing.manager.request.swpay.OrderQueryNoPartnerRequest;
import com.shunwang.marketing.manager.response.game.SendCouponResponse;
import com.shunwang.marketing.manager.response.swpay.OrderQueryNoPartnerResponse;
import com.shunwang.marketing.manager.service.GameServiceClient;
import com.shunwang.marketing.manager.service.SwpayServiceClient;
import com.shunwang.marketing.pojo.marketing.*;
import com.shunwang.marketing.pojo.personal.Member;
import com.shunwang.marketing.send.email.EmailSender;
import com.shunwang.marketing.service.impl.BaseServiceImpl;
import com.shunwang.marketing.service.marketing.*;
import com.shunwang.marketing.service.personal.MemberService;
import com.shunwang.marketing.util.MemberUtil;
import com.shunwang.util.date.DateUtil;
import com.shunwang.util.lang.StringUtil;
import com.shunwang.util.math.BigDecimalUtil;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Slf4j
@Component
public class PrizeServiceImpl extends BaseServiceImpl<Prize> implements PrizeService {
    private static final Logger LOGGER = LoggerFactory.getLogger(PrizeServiceImpl.class);

    @Autowired
    private PrizeMapper prizeMapper;
    @Autowired
    private PrizeRecordService prizeRecordService;
    @Autowired
    private WelfareService welfareService;
    @Autowired
    private GiftBagService giftBagService;
    @Autowired
    private BagOrderService bagOrderService;
    @Autowired
    private RedPacketRecordService redPacketRecordService;
    @Autowired
    @Lazy
    private PrizeService prizeService;
    @Autowired
    private LotteriesCntService lotteriesCntService;
    @Autowired
    private SendGiftService sendGiftService;
    @Autowired
    private EmailSender emailSender;
    @Autowired
    ScoreService scoreService;
    @Autowired
    MemberTagService memberTagService;
    @Autowired
    BussMemberRelationService bussMemberRelationService;
    @Autowired
    PondService pondService;

    @Autowired
    private MemberService memberService;

    private final String emptyPrizeName = "谢谢参与";

    @Override
    public BaseMapper getDao() {
        return prizeMapper;
    }


    @Cacheable(cacheNames = "cache_5_minutes", key = "'m_'+#root.methodName + '_' + #pondId", unless = "#result == null")
    public List<Prize> getByPond(Integer pondId){
        return prizeMapper.getByPond(pondId);
    }

    public Prize getLosingLotteryPrize(Integer pondId){

        Prize prize = prizeMapper.getLosingLotteryPrize(pondId);
        if (prize == null){
            prize = new Prize();
            prize.setPrizeName(emptyPrizeName);
            prize.setId(0);
            prize.setPrizeType(WelfareEnum.TypeEnum.FICTION.getValue());
            prize.setWelfareId(0);
            prize.setCnt(1);
            return prize;
        }
        return prize;
    }

    /**
     * 从奖池抽奖
     * @param pondId
     * @return
     */
    public Prize toWinPrize(Integer pondId, String channel){
        List<Prize> prizes = getByPond(pondId);
        //定向的奖品返回概率最高的，无定向奖品随机抽取
        List<Prize> specificPrizeList = prizes.stream()
                .filter((prize) -> { //先过滤出需要定向的
                    if (StringUtil.isNotBlank(prize.getTags()) && memberTagService.isMatching(prize.getTags(), MemberUtil.getNotNullMemberId())) {
                        return true;
                    }
                    if (bussMemberRelationService.isSpecific(MemberUtil.getNotNullMemberId(), BussMemberRelationEnum.BussTypeEnum.PRIZE.getValue(), prize.getId())) {
                        return true;
                    }
                    return false;
                })
                .filter((prize) -> isReachLimit(channel, pondId, prize)) //再过滤出没达到购买次数限制的
                .sorted((p1, p2) -> p2.getProbability().compareTo(p1.getProbability())).collect(Collectors.toList());

        Prize winPrize = CollectionUtils.isEmpty(specificPrizeList) ? getRandomPrize(prizes) : specificPrizeList.get(0);

        if (winPrize == null) {
            log.error("概率抽奖未中奖，请检查奖品概率总和是否为100%");
            return getLosingLotteryPrize(pondId);
        }

        if (!isReachLimit(channel, pondId, winPrize)) {
            return getLosingLotteryPrize(pondId);
        }
        return winPrize;
    }

    /**
     * 是否能够抽中次奖品
     * @param channel
     * @param pondId
     * @param winPrize
     * @return
     */
    private boolean isReachLimit(String channel, Integer pondId, Prize winPrize) {

        if ((winPrize.getDayLimit() == null || winPrize.getTimesLimit() == null) && winPrize.getCntLimit() == null){
            return true;
        }

        //查询用户在当前奖池中抽奖的次数
        Integer drawCnt = 0;
        boolean cntLimit = false;
        if (winPrize.getCntLimit() != null && winPrize.getCntLimit() > 0){
            //查询用户在当前奖池中抽奖的次数
            drawCnt = prizeRecordService.getUserPondPrizeCnt(MemberUtil.getNotNullMemberId(), pondId);
            cntLimit = winPrize.getCntLimit() <= drawCnt;
        }
        if (winPrize.getCntLimit() == null){
            cntLimit = true;
        }


        //查询此奖品，用户N天内抽到了几次
        boolean dayTimesLimit = false;
        if (winPrize.getDayLimit() != null && winPrize.getTimesLimit() != null) {
            Integer cnt = 0;
            Date startTime = DateUtil.addDay(new Date(), -winPrize.getDayLimit());
            if (PrizeEnum.PrizeTypeEnum.GIFT_BAG.getValue() == winPrize.getPrizeType().intValue()) {//礼包类型
                cnt = bagOrderService.getCntByDayLimit(winPrize.getBagId(), MemberUtil.getNotNullMemberId(), startTime);
            } else {
                cnt = prizeRecordService.getCntByDayLimit(winPrize.getId(), MemberUtil.getNotNullMemberId(), startTime, channel, "抽奖");
            }
            dayTimesLimit = cnt < winPrize.getTimesLimit();
        }
        if (winPrize.getDayLimit() == null || winPrize.getTimesLimit() == null){
            dayTimesLimit = true;
        }
        //判断抽中奖品是否达到了上限，或者未达到抽奖次数限制
        return dayTimesLimit && cntLimit;
    }

    @Nullable
    private Prize getRandomPrize(List<Prize> prizes) {
        int randomNumber = (int)(Math.random() * 10000 + 1);
        Prize winPrize = null;
        for (Prize prize : prizes){
            randomNumber = randomNumber - BigDecimalUtil.multiply(prize.getProbability(), new BigDecimal(100)).intValue();
            if (randomNumber <= 0){
                winPrize = prize;
                break;
            }
        }
        return winPrize;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public List<Prize> winPrize(String orderNo, Integer pondId, Integer memberId, String channel, boolean decCnt, Integer times) {
        //连抽抽中多个奖品扣减库存，可能存在死锁
        return IntStream.range(0, times)
                .mapToObj((idx) -> prizeService.winPrize(orderNo, pondId, memberId, 0, channel, decCnt)).collect(Collectors.toList());
    }

    /**
     * 抽奖
     * @param orderNo 订单号
     * @param pondId 奖池
     * @param memberId 用户id
     * @param winCnt 用户因为无库存未中奖，递归调用抽奖当前次数
     * @return
     */
    @Transactional(rollbackFor = Throwable.class)
    public Prize winPrize(String orderNo, Integer pondId, Integer memberId, Integer winCnt, String channel,boolean decCnt){
        winCnt = winCnt == null ? 0 : winCnt;

        Pond pond = pondService.get(pondId);
        Prize prize = toWinPrize(pondId, channel);
        if (prize != null){
            if (prize.getCnt() <= 0){
                log.info("奖品库存不足，抽奖失败prizeId=[{}],memberId=[{}]", prize.getId(), memberId);
                prize = getLosingLotteryPrize(pondId);
            }
            //如果中奖奖品是礼包类型
            if (PrizeEnum.PrizeTypeEnum.GIFT_BAG.getValue() == prize.getPrizeType().intValue()){
                boolean result = processGiftBagPrize(prize, pondId, memberId);
                int prizeResult = prizeService.decreaseCnt(prize);
                log.info("用户[{}],抽中礼包[{}]", memberId, prize.getBagId());
                //扣减抽奖次数
                if (decCnt){
                    lotteriesCntService.subtractCnt(memberId,pondId);
                }
                return result && prizeResult > 0 ? prize : getLosingLotteryPrize(pondId);
            }
            Welfare welfare = welfareService.get(prize.getWelfareId());
            if (welfare == null){
                log.info("权益获取失败，抽奖失败prizeId=[{}],WelfareId=[{}]", prize.getId(), prize.getWelfareId());
                prize = getLosingLotteryPrize(pondId);
            }
            //判断权益是否在活动时间内,未上线
            if (welfare != null && DateUtil.compare(welfare.getStartTime(), new Date() , 1) > 0){
                log.info("权益未上线，抽奖失败pondId=[{}],WelfareId=[{}]", pondId, prize.getWelfareId());
                prize = getLosingLotteryPrize(pondId);
            }
            //已下线
            if (welfare != null && DateUtil.compare(new Date(), welfare.getEndTime(), 1) > 0){
                log.info("权益已下线，抽奖失败pondId=[{}],WelfareId=[{}]", pondId, prize.getWelfareId());
                prize = getLosingLotteryPrize(pondId);
            }
            if (welfare != null && welfare.getCnt() <= 0){
                log.info("权益库存不足，抽奖失败pondId=[{}],WelfareId=[{}]", pondId, prize.getWelfareId());
                emailSender.buildStockWarnEmail(welfare.getCnt(),"权益库存报警，id=" + welfare.getId() + ",数量：" + welfare.getCnt());
                prize = getLosingLotteryPrize(pondId);
            }
            //如果未中奖，权益改为未中奖奖品对应的权益类型
            if (emptyPrizeName.equals(prize.getPrizeName())){
                //确保付费奖池必中奖
                if (pond.getType().intValue() == PondEnum.TypeEnum.PAY.getValue()){
                    while (emptyPrizeName.equals(prize.getPrizeName()) && winCnt < 30){
                        log.info("付费奖池[{}]抽奖失败，重新给用户抽奖memberId=[{}],WelfareId=[{}],次数=[{}]", pond.getName(), memberId, prize.getWelfareId(), winCnt);
                        winCnt++;
                        return winPrize(orderNo, pondId, memberId, winCnt, channel,decCnt);
                    }
                }else{
                    while (prize.getWelfareId() == 0 && winCnt < 3){
                        log.info("用户抽奖奖品不足，重新给用户抽奖memberId=[{}],WelfareId=[{}]", memberId, prize.getWelfareId());
                        winCnt++;
                        return winPrize(orderNo, pondId, memberId, winCnt, channel,decCnt);
                    }
                }
                welfare = welfareService.get(prize.getWelfareId());
            }

            String cdkey = null;
            if (welfare != null && welfare.getType().intValue() == WelfareEnum.TypeEnum.PC_CDKEY.getValue()){
                cdkey = welfareService.receiveCdkeyWelfare(welfare, MemberUtil.getUserInfoByMemberId(memberId), true);
                if (StringUtil.isBlank(cdkey)){
                    log.info("权益兑换码库存不足，抽奖失败pondId=[{}],WelfareId=[{}]", pondId, prize.getWelfareId());
                    prize = getLosingLotteryPrize(pondId);
                    welfare = welfareService.get(prize.getWelfareId());
                }
                prize.setRemark(cdkey);
            }

            if (welfare != null && WelfareEnum.TypeEnum.SCORE.getValue().equals(welfare.getType())) {
                scoreService.updateScoreAndTakeRecord(welfare.getScoreCnt(), ScoreRecordEnum.TypeEnum.ADD, ScoreRecordEnum.SourceTypeEnum.PRIZE.getValue(), "抽奖");
            }

            //普通奖池如果抽中奖品，但是没有库存或奖品配置问题，重新给用户抽奖, 最多抽3次
            if (emptyPrizeName.equals(prize.getPrizeName()) && winCnt < 3){
                winCnt++;
                log.info("用户抽奖奖品不足，重新给用户抽奖memberId=[{}],WelfareId=[{}]", memberId, prize.getWelfareId());
                return winPrize(orderNo, pondId, memberId, winCnt, channel,decCnt);
            }

            prize = processOrdinaryPrize(prize, welfare, orderNo, pondId, cdkey,channel);
            prize.setBusinessLine(welfare.getBusinessLine());
            log.info("用户中奖[{}],memberId=[{}]", prize.getPrizeName(), memberId);
        }

        //扣减抽奖次数
        if (decCnt){
            lotteriesCntService.subtractCnt(MemberUtil.getNotNullMemberId(),pondId);
        }

        return prize;
    }

    /**
     * 发放红包优惠券
     * @return
     */
    public boolean processRedPacketPrize(Welfare welfare, String source, Integer bagId, String bagOrderNo, UserInfo userInfo){
        String orderNo = "";
        RedPacketRecord redPacketRecord = new RedPacketRecord();
        //游戏发券
        if (welfare != null && WelfareEnum.BussinessLineEnum.GAME.getValue() == welfare.getBusinessLine().intValue()){
            orderNo = sendCoupon(welfare, source, userInfo.getMemberId(), userInfo.getMemberName());
            redPacketRecord.setState(RedPacketRecordEnum.StateEnum.NOT_USED.getValue());
        }
        //泡泡发券
        else if (welfare != null && WelfareEnum.BussinessLineEnum.PAOPAO.getValue() == welfare.getBusinessLine().intValue()) {
            orderNo = sendGiftService.buildOrderNo();
            Integer giftType = WelfareEnum.BussinessTypeEnum.TIME.getValue() == welfare.getBusinessType().intValue() ? 1 : 2;
            boolean result = sendGiftService.executeSendGift(UserContext.getUserId(), giftType, welfare.getGoodsId(), orderNo);
            if (result){
                redPacketRecord.setState(RedPacketRecordEnum.StateEnum.NOT_USED.getValue());
            } else {
                redPacketRecord.setState(RedPacketRecordEnum.StateEnum.NOT_SEND.getValue());
            }
        }

        if (StringUtil.isBlank(orderNo)){
            return false;
        }

        Date now = new Date();

        redPacketRecord.setWelfareId(welfare.getId());
        redPacketRecord.setMemberId(userInfo.getMemberId());
        redPacketRecord.setOrderNo(orderNo);
        redPacketRecord.setSendTime(now);
        redPacketRecord.setExpireTime(DateUtil.addDay(now,welfare.getEffectiveDays()));
        redPacketRecord.setBagId(bagId);
        redPacketRecord.setSource(source);
        redPacketRecord.setBagOrderNo(bagOrderNo);
        redPacketRecordService.save(redPacketRecord);

        return true;
    }

    /**
     * 调用游戏接口发放优惠券
     * @param welfare
     * @param sourceName
     * @return 订单号
     */
    public String sendCoupon(Welfare welfare, String sourceName, Integer userId, String userName){
        SendCouponRequest request = new SendCouponRequest();
        Date now = new Date();
        request.setUserId(Long.parseLong(userId.toString()));
        request.setUserName(userName);
        request.setCouponType(welfare.getBusinessType());
        request.setCouponValue(welfare.getFaceValue().toString());
        if (welfare.getLimiting() != null){
            request.setCouponCondition(welfare.getLimiting().toString());
        }
        request.setSourceName(sourceName);
        request.setGameType(SysconfigConstant.GameTypeEnum.All.getValue());
        request.setPlatform("marketing");
        request.setCouponName(welfare.getName());
        request.setBeginTime(DateUtil.ymdhmsFormat(now));
        request.setEndTime(DateUtil.ymdhmsFormat(DateUtil.addDay(now,welfare.getEffectiveDays())));

        SendCouponResponse response = GameServiceClient.execute(request);
        if (response.isSuccess()){
            log.info("用户红包发放成功：权益id=[{}]，用户id=[{}]", welfare.getId(), userId);
            return request.getOrderNo();
        }

        log.info("用户红包发放失败：权益id=[{}]，用户id=[{}]", welfare.getId(), userId);
        return null;
    }

    /**
     * 处理中奖奖品
     * @param prize
     * @param welfare
     * @param payOrderNo
     * @param pondId
     * @param cdkey
     * @return
     */
    private Prize processOrdinaryPrize(Prize prize, Welfare welfare, String payOrderNo, Integer pondId, String cdkey,String channel){

        //红包类型的奖品
        if (PrizeEnum.PrizeTypeEnum.RED_PACKET.getValue() == prize.getPrizeType().intValue()){
            boolean isSuccess = processRedPacketPrize(welfare, "抽奖"+ThreadLocalContext.getTempData(ThreadLocalContext.CHANNEL), null, null, MemberUtil.getNotNullUserInfo());
            if (!isSuccess){//如果发放失败
                return null;
            }
        }

        //先减奖品和权益的库存
        int prizeResult = this.decreaseCnt(prize);
        int welfareResult = welfareService.decreaseCnt(welfare);
        if (prizeResult < 1 || welfareResult < 1){
            prize = getLosingLotteryPrize(pondId);
        }

        //存储订单信息
        PrizeRecord prizeRecord = new PrizeRecord();
        prizeRecord.setPrizeId(prize.getId());
        prizeRecord.setMemberId(MemberUtil.getNotNullMemberId());
        prizeRecord.setPrizeName(prize.getPrizeName());
        prizeRecord.setPrizeType(prize.getPrizeType());
        prizeRecord.setWelfareId(prize.getWelfareId());
        prizeRecord.setPondId(pondId);
        prizeRecord.setPayOrderNo(payOrderNo);
        prizeRecord.setSendTime(new Date());
        prizeRecord.setSource("抽奖");
        prizeRecord.setChannel(channel);
        prizeRecord.setRemark(cdkey);

        prizeRecordService.save(prizeRecord);

        return prize;
    }

    /**
     * 发放礼包奖品
     * @param prizeBag
     * @return
     */
    private boolean processGiftBagPrize(Prize prizeBag, Integer pondId, Integer memberId) {
        GiftBag giftBag = giftBagService.get(prizeBag.getBagId());
        if (giftBag == null){
            return false;
        }
        if (!giftBagService.checkStock(giftBag)){
            log.info("库存不足，无法给用户发放奖品，礼包id：[{}]", giftBag.getId());
            return false;
        }
        giftBag.setCnt(giftBag.getCnt() - 1);
        giftBagService.update(giftBag);

        BagOrder bagOrder = bagOrderService.saveCjOrder(prizeBag.getBagId(), memberId);

        List<Prize> prizes = getByBagId(giftBag.getId());
        for (Prize p : prizes){
            //礼包内商品有多个，需要发放多次
            for (int i = 0; i < p.getBagPrizeCnt(); i++){
                Welfare welfare = welfareService.get(p.getWelfareId());
                if (PrizeEnum.PrizeTypeEnum.RED_PACKET.getValue() == p.getPrizeType().intValue()){
                    //先减权益的库存
                    int welfareResult = welfareService.decreaseCnt(welfare);
                    if (welfareResult < 1){
                        log.info("发放礼包奖品，礼包内奖品库存不足，无法给用户发放奖品，奖品id：[{}], 礼包id：[{}]", p.getId(), giftBag.getId());
                        return false;
                    }
                    processRedPacketPrize(welfare, "抽奖"+ThreadLocalContext.getTempData(ThreadLocalContext.CHANNEL), giftBag.getId(), bagOrder.getOrderNo(), MemberUtil.getNotNullUserInfo());
                } else {
                    bagOrderService.givePrize(p, welfare, bagOrder, MemberUtil.getUserInfoByMemberId(memberId), giftBag, pondId,"抽奖");
                }
            }
        }

        return true;
    }

    /**
     * 检查奖品及对应的权益是否可用
     * @param prize
     * @return
     */
    public boolean checkPrize(Prize prize){

        Welfare welfare = welfareService.get(prize.getWelfareId());
        if (welfare == null){
            log.info("权益获取失败，抽奖失败prizeId=[{}],WelfareId=[{}]", prize.getId(), prize.getWelfareId());
            return false;
        }
        //判断权益是否在活动时间内,未上线
        if (DateUtil.compare(welfare.getStartTime(), new Date() , 1) > 0){
            log.info("权益未上线，WelfareId=[{}]", prize.getWelfareId());
            return false;
        }
        //已下线
        if (DateUtil.compare(new Date(), welfare.getEndTime(), 1) > 0){
            log.info("权益已下线，WelfareId=[{}]", prize.getWelfareId());
            return false;
        }
        if (welfare.getCnt() <= 0){
            log.info("权益库存不足，WelfareId=[{}]", prize.getWelfareId());
            return false;
        }

        return true;
    }


    public boolean checkCouldWinPrize(String orderNo){
        if (StringUtil.isBlank(orderNo)){
            return false;
        }
        //调用pay接口查询订单是否合法
        OrderQueryNoPartnerRequest request = new OrderQueryNoPartnerRequest();
        request.setOrderNo(orderNo);

        try {
            OrderQueryNoPartnerResponse response = SwpayServiceClient.execute(request);
            if (response == null) {
                return false;
            }
            if (PayEnum.payStateEnum.FINISH.getValue().equals(response.getPayState())) {
                return true;
            }
        } catch (Exception e){
            LOGGER.error("调用pay接口查询订单异常：" + e.getMessage());
            return false;
        }
        return false;
    }

    /**
     * 减少库存
     * @param prize
     * @return
     */
    public int decreaseCnt(Prize prize){
        emailSender.buildStockWarnEmail(prize.getCnt(),"奖品库存报警，id=" + prize.getId() + ",数量：" + prize.getCnt());
        int prizeResult = prizeMapper.decreaseCnt(prize);
        if (prizeResult < 1){
            prize = this.prizeMapper.get(prize.getId());
            if (prize == null || prize.getCnt() < 1){
                return 0;
            }
            this.decreaseCnt(prize);
        }

        return prizeResult;
    }

    /**
     * 获取礼包内的奖品
     * @param bagId
     * @return
     */
    public List<Prize> getByBagId(Integer bagId){
        return prizeMapper.getByBagId(bagId);
    }

}
