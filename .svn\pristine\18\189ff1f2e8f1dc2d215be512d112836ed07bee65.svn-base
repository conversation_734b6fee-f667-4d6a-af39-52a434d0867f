package com.shunwang.marketing.enums.marketing;

import lombok.Getter;
import lombok.Setter;

public interface TencentOrderNoticeEnum {

    enum StateEnum {
        CREATE(1,"创建"),
        SUCCESS(2,"成功"),
        ;

        StateEnum(int value, String name) {
            this.value = value;
            this.name = name;
        }

        private @Getter Integer value;
        private @Getter String name;
    }


}
