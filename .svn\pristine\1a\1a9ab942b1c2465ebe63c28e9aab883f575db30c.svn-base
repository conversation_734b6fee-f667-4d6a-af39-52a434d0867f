(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))n(o);new MutationObserver(o=>{for(const s of o)if(s.type==="childList")for(const i of s.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&n(i)}).observe(document,{childList:!0,subtree:!0});function r(o){const s={};return o.integrity&&(s.integrity=o.integrity),o.referrerPolicy&&(s.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?s.credentials="include":o.crossOrigin==="anonymous"?s.credentials="omit":s.credentials="same-origin",s}function n(o){if(o.ep)return;o.ep=!0;const s=r(o);fetch(o.href,s)}})();/**
* @vue/shared v3.5.13
* (c) 2018-present <PERSON><PERSON> (<PERSON>) <PERSON> and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function Cs(e){const t=Object.create(null);for(const r of e.split(","))t[r]=1;return r=>r in t}const ye={},rr=[],mt=()=>{},fu=()=>!1,bn=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),Ts=e=>e.startsWith("onUpdate:"),Ge=Object.assign,Is=(e,t)=>{const r=e.indexOf(t);r>-1&&e.splice(r,1)},du=Object.prototype.hasOwnProperty,fe=(e,t)=>du.call(e,t),X=Array.isArray,nr=e=>An(e)==="[object Map]",ol=e=>An(e)==="[object Set]",ee=e=>typeof e=="function",Ce=e=>typeof e=="string",Et=e=>typeof e=="symbol",_e=e=>e!==null&&typeof e=="object",sl=e=>(_e(e)||ee(e))&&ee(e.then)&&ee(e.catch),il=Object.prototype.toString,An=e=>il.call(e),pu=e=>An(e).slice(8,-1),al=e=>An(e)==="[object Object]",ks=e=>Ce(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,Or=Cs(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),wn=e=>{const t=Object.create(null);return r=>t[r]||(t[r]=e(r))},hu=/-(\w)/g,Dt=wn(e=>e.replace(hu,(t,r)=>r?r.toUpperCase():"")),mu=/\B([A-Z])/g,Qt=wn(e=>e.replace(mu,"-$1").toLowerCase()),ll=wn(e=>e.charAt(0).toUpperCase()+e.slice(1)),Xn=wn(e=>e?`on${ll(e)}`:""),kt=(e,t)=>!Object.is(e,t),Zn=(e,...t)=>{for(let r=0;r<e.length;r++)e[r](...t)},cl=(e,t,r,n=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:n,value:r})},yu=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let ci;const Sn=()=>ci||(ci=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function En(e){if(X(e)){const t={};for(let r=0;r<e.length;r++){const n=e[r],o=Ce(n)?Au(n):En(n);if(o)for(const s in o)t[s]=o[s]}return t}else if(Ce(e)||_e(e))return e}const gu=/;(?![^(]*\))/g,vu=/:([^]+)/,bu=/\/\*[^]*?\*\//g;function Au(e){const t={};return e.replace(bu,"").split(gu).forEach(r=>{if(r){const n=r.split(vu);n.length>1&&(t[n[0].trim()]=n[1].trim())}}),t}function yt(e){let t="";if(Ce(e))t=e;else if(X(e))for(let r=0;r<e.length;r++){const n=yt(e[r]);n&&(t+=n+" ")}else if(_e(e))for(const r in e)e[r]&&(t+=r+" ");return t.trim()}const wu="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",Su=Cs(wu);function ul(e){return!!e||e===""}const fl=e=>!!(e&&e.__v_isRef===!0),Ue=e=>Ce(e)?e:e==null?"":X(e)||_e(e)&&(e.toString===il||!ee(e.toString))?fl(e)?Ue(e.value):JSON.stringify(e,dl,2):String(e),dl=(e,t)=>fl(t)?dl(e,t.value):nr(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((r,[n,o],s)=>(r[Yn(n,s)+" =>"]=o,r),{})}:ol(t)?{[`Set(${t.size})`]:[...t.values()].map(r=>Yn(r))}:Et(t)?Yn(t):_e(t)&&!X(t)&&!al(t)?String(t):t,Yn=(e,t="")=>{var r;return Et(e)?`Symbol(${(r=e.description)!=null?r:t})`:e};/**
* @vue/reactivity v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let et;class Eu{constructor(t=!1){this.detached=t,this._active=!0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=et,!t&&et&&(this.index=(et.scopes||(et.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,r;if(this.scopes)for(t=0,r=this.scopes.length;t<r;t++)this.scopes[t].pause();for(t=0,r=this.effects.length;t<r;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,r;if(this.scopes)for(t=0,r=this.scopes.length;t<r;t++)this.scopes[t].resume();for(t=0,r=this.effects.length;t<r;t++)this.effects[t].resume()}}run(t){if(this._active){const r=et;try{return et=this,t()}finally{et=r}}}on(){et=this}off(){et=this.parent}stop(t){if(this._active){this._active=!1;let r,n;for(r=0,n=this.effects.length;r<n;r++)this.effects[r].stop();for(this.effects.length=0,r=0,n=this.cleanups.length;r<n;r++)this.cleanups[r]();if(this.cleanups.length=0,this.scopes){for(r=0,n=this.scopes.length;r<n;r++)this.scopes[r].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const o=this.parent.scopes.pop();o&&o!==this&&(this.parent.scopes[this.index]=o,o.index=this.index)}this.parent=void 0}}}function _u(){return et}let be;const eo=new WeakSet;class pl{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,et&&et.active&&et.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,eo.has(this)&&(eo.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||ml(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,ui(this),yl(this);const t=be,r=at;be=this,at=!0;try{return this.fn()}finally{gl(this),be=t,at=r,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)Ms(t);this.deps=this.depsTail=void 0,ui(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?eo.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){cs(this)&&this.run()}get dirty(){return cs(this)}}let hl=0,xr,Pr;function ml(e,t=!1){if(e.flags|=8,t){e.next=Pr,Pr=e;return}e.next=xr,xr=e}function Ds(){hl++}function Ns(){if(--hl>0)return;if(Pr){let t=Pr;for(Pr=void 0;t;){const r=t.next;t.next=void 0,t.flags&=-9,t=r}}let e;for(;xr;){let t=xr;for(xr=void 0;t;){const r=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(n){e||(e=n)}t=r}}if(e)throw e}function yl(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function gl(e){let t,r=e.depsTail,n=r;for(;n;){const o=n.prevDep;n.version===-1?(n===r&&(r=o),Ms(n),Ru(n)):t=n,n.dep.activeLink=n.prevActiveLink,n.prevActiveLink=void 0,n=o}e.deps=t,e.depsTail=r}function cs(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(vl(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function vl(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===Dr))return;e.globalVersion=Dr;const t=e.dep;if(e.flags|=2,t.version>0&&!e.isSSR&&e.deps&&!cs(e)){e.flags&=-3;return}const r=be,n=at;be=e,at=!0;try{yl(e);const o=e.fn(e._value);(t.version===0||kt(o,e._value))&&(e._value=o,t.version++)}catch(o){throw t.version++,o}finally{be=r,at=n,gl(e),e.flags&=-3}}function Ms(e,t=!1){const{dep:r,prevSub:n,nextSub:o}=e;if(n&&(n.nextSub=o,e.prevSub=void 0),o&&(o.prevSub=n,e.nextSub=void 0),r.subs===e&&(r.subs=n,!n&&r.computed)){r.computed.flags&=-5;for(let s=r.computed.deps;s;s=s.nextDep)Ms(s,!0)}!t&&!--r.sc&&r.map&&r.map.delete(r.key)}function Ru(e){const{prevDep:t,nextDep:r}=e;t&&(t.nextDep=r,e.prevDep=void 0),r&&(r.prevDep=t,e.nextDep=void 0)}let at=!0;const bl=[];function Mt(){bl.push(at),at=!1}function Ft(){const e=bl.pop();at=e===void 0?!0:e}function ui(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const r=be;be=void 0;try{t()}finally{be=r}}}let Dr=0;class Ou{constructor(t,r){this.sub=t,this.dep=r,this.version=r.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class Fs{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0}track(t){if(!be||!at||be===this.computed)return;let r=this.activeLink;if(r===void 0||r.sub!==be)r=this.activeLink=new Ou(be,this),be.deps?(r.prevDep=be.depsTail,be.depsTail.nextDep=r,be.depsTail=r):be.deps=be.depsTail=r,Al(r);else if(r.version===-1&&(r.version=this.version,r.nextDep)){const n=r.nextDep;n.prevDep=r.prevDep,r.prevDep&&(r.prevDep.nextDep=n),r.prevDep=be.depsTail,r.nextDep=void 0,be.depsTail.nextDep=r,be.depsTail=r,be.deps===r&&(be.deps=n)}return r}trigger(t){this.version++,Dr++,this.notify(t)}notify(t){Ds();try{for(let r=this.subs;r;r=r.prevSub)r.sub.notify()&&r.sub.dep.notify()}finally{Ns()}}}function Al(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let n=t.deps;n;n=n.nextDep)Al(n)}const r=e.dep.subs;r!==e&&(e.prevSub=r,r&&(r.nextSub=e)),e.dep.subs=e}}const ln=new WeakMap,Wt=Symbol(""),us=Symbol(""),Nr=Symbol("");function qe(e,t,r){if(at&&be){let n=ln.get(e);n||ln.set(e,n=new Map);let o=n.get(r);o||(n.set(r,o=new Fs),o.map=n,o.key=r),o.track()}}function wt(e,t,r,n,o,s){const i=ln.get(e);if(!i){Dr++;return}const a=l=>{l&&l.trigger()};if(Ds(),t==="clear")i.forEach(a);else{const l=X(e),u=l&&ks(r);if(l&&r==="length"){const c=Number(n);i.forEach((f,h)=>{(h==="length"||h===Nr||!Et(h)&&h>=c)&&a(f)})}else switch((r!==void 0||i.has(void 0))&&a(i.get(r)),u&&a(i.get(Nr)),t){case"add":l?u&&a(i.get("length")):(a(i.get(Wt)),nr(e)&&a(i.get(us)));break;case"delete":l||(a(i.get(Wt)),nr(e)&&a(i.get(us)));break;case"set":nr(e)&&a(i.get(Wt));break}}Ns()}function xu(e,t){const r=ln.get(e);return r&&r.get(t)}function Yt(e){const t=ce(e);return t===e?t:(qe(t,"iterate",Nr),st(e)?t:t.map(He))}function _n(e){return qe(e=ce(e),"iterate",Nr),e}const Pu={__proto__:null,[Symbol.iterator](){return to(this,Symbol.iterator,He)},concat(...e){return Yt(this).concat(...e.map(t=>X(t)?Yt(t):t))},entries(){return to(this,"entries",e=>(e[1]=He(e[1]),e))},every(e,t){return vt(this,"every",e,t,void 0,arguments)},filter(e,t){return vt(this,"filter",e,t,r=>r.map(He),arguments)},find(e,t){return vt(this,"find",e,t,He,arguments)},findIndex(e,t){return vt(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return vt(this,"findLast",e,t,He,arguments)},findLastIndex(e,t){return vt(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return vt(this,"forEach",e,t,void 0,arguments)},includes(...e){return ro(this,"includes",e)},indexOf(...e){return ro(this,"indexOf",e)},join(e){return Yt(this).join(e)},lastIndexOf(...e){return ro(this,"lastIndexOf",e)},map(e,t){return vt(this,"map",e,t,void 0,arguments)},pop(){return vr(this,"pop")},push(...e){return vr(this,"push",e)},reduce(e,...t){return fi(this,"reduce",e,t)},reduceRight(e,...t){return fi(this,"reduceRight",e,t)},shift(){return vr(this,"shift")},some(e,t){return vt(this,"some",e,t,void 0,arguments)},splice(...e){return vr(this,"splice",e)},toReversed(){return Yt(this).toReversed()},toSorted(e){return Yt(this).toSorted(e)},toSpliced(...e){return Yt(this).toSpliced(...e)},unshift(...e){return vr(this,"unshift",e)},values(){return to(this,"values",He)}};function to(e,t,r){const n=_n(e),o=n[t]();return n!==e&&!st(e)&&(o._next=o.next,o.next=()=>{const s=o._next();return s.value&&(s.value=r(s.value)),s}),o}const Cu=Array.prototype;function vt(e,t,r,n,o,s){const i=_n(e),a=i!==e&&!st(e),l=i[t];if(l!==Cu[t]){const f=l.apply(e,s);return a?He(f):f}let u=r;i!==e&&(a?u=function(f,h){return r.call(this,He(f),h,e)}:r.length>2&&(u=function(f,h){return r.call(this,f,h,e)}));const c=l.call(i,u,n);return a&&o?o(c):c}function fi(e,t,r,n){const o=_n(e);let s=r;return o!==e&&(st(e)?r.length>3&&(s=function(i,a,l){return r.call(this,i,a,l,e)}):s=function(i,a,l){return r.call(this,i,He(a),l,e)}),o[t](s,...n)}function ro(e,t,r){const n=ce(e);qe(n,"iterate",Nr);const o=n[t](...r);return(o===-1||o===!1)&&Us(r[0])?(r[0]=ce(r[0]),n[t](...r)):o}function vr(e,t,r=[]){Mt(),Ds();const n=ce(e)[t].apply(e,r);return Ns(),Ft(),n}const Tu=Cs("__proto__,__v_isRef,__isVue"),wl=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(Et));function Iu(e){Et(e)||(e=String(e));const t=ce(this);return qe(t,"has",e),t.hasOwnProperty(e)}class Sl{constructor(t=!1,r=!1){this._isReadonly=t,this._isShallow=r}get(t,r,n){if(r==="__v_skip")return t.__v_skip;const o=this._isReadonly,s=this._isShallow;if(r==="__v_isReactive")return!o;if(r==="__v_isReadonly")return o;if(r==="__v_isShallow")return s;if(r==="__v_raw")return n===(o?s?Hu:Ol:s?Rl:_l).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(n)?t:void 0;const i=X(t);if(!o){let l;if(i&&(l=Pu[r]))return l;if(r==="hasOwnProperty")return Iu}const a=Reflect.get(t,r,Le(t)?t:n);return(Et(r)?wl.has(r):Tu(r))||(o||qe(t,"get",r),s)?a:Le(a)?i&&ks(r)?a:a.value:_e(a)?o?Pl(a):Rn(a):a}}class El extends Sl{constructor(t=!1){super(!1,t)}set(t,r,n,o){let s=t[r];if(!this._isShallow){const l=Vt(s);if(!st(n)&&!Vt(n)&&(s=ce(s),n=ce(n)),!X(t)&&Le(s)&&!Le(n))return l?!1:(s.value=n,!0)}const i=X(t)&&ks(r)?Number(r)<t.length:fe(t,r),a=Reflect.set(t,r,n,Le(t)?t:o);return t===ce(o)&&(i?kt(n,s)&&wt(t,"set",r,n):wt(t,"add",r,n)),a}deleteProperty(t,r){const n=fe(t,r);t[r];const o=Reflect.deleteProperty(t,r);return o&&n&&wt(t,"delete",r,void 0),o}has(t,r){const n=Reflect.has(t,r);return(!Et(r)||!wl.has(r))&&qe(t,"has",r),n}ownKeys(t){return qe(t,"iterate",X(t)?"length":Wt),Reflect.ownKeys(t)}}class ku extends Sl{constructor(t=!1){super(!0,t)}set(t,r){return!0}deleteProperty(t,r){return!0}}const Du=new El,Nu=new ku,Mu=new El(!0);const fs=e=>e,Xr=e=>Reflect.getPrototypeOf(e);function Fu(e,t,r){return function(...n){const o=this.__v_raw,s=ce(o),i=nr(s),a=e==="entries"||e===Symbol.iterator&&i,l=e==="keys"&&i,u=o[e](...n),c=r?fs:t?ds:He;return!t&&qe(s,"iterate",l?us:Wt),{next(){const{value:f,done:h}=u.next();return h?{value:f,done:h}:{value:a?[c(f[0]),c(f[1])]:c(f),done:h}},[Symbol.iterator](){return this}}}}function Zr(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function Bu(e,t){const r={get(o){const s=this.__v_raw,i=ce(s),a=ce(o);e||(kt(o,a)&&qe(i,"get",o),qe(i,"get",a));const{has:l}=Xr(i),u=t?fs:e?ds:He;if(l.call(i,o))return u(s.get(o));if(l.call(i,a))return u(s.get(a));s!==i&&s.get(o)},get size(){const o=this.__v_raw;return!e&&qe(ce(o),"iterate",Wt),Reflect.get(o,"size",o)},has(o){const s=this.__v_raw,i=ce(s),a=ce(o);return e||(kt(o,a)&&qe(i,"has",o),qe(i,"has",a)),o===a?s.has(o):s.has(o)||s.has(a)},forEach(o,s){const i=this,a=i.__v_raw,l=ce(a),u=t?fs:e?ds:He;return!e&&qe(l,"iterate",Wt),a.forEach((c,f)=>o.call(s,u(c),u(f),i))}};return Ge(r,e?{add:Zr("add"),set:Zr("set"),delete:Zr("delete"),clear:Zr("clear")}:{add(o){!t&&!st(o)&&!Vt(o)&&(o=ce(o));const s=ce(this);return Xr(s).has.call(s,o)||(s.add(o),wt(s,"add",o,o)),this},set(o,s){!t&&!st(s)&&!Vt(s)&&(s=ce(s));const i=ce(this),{has:a,get:l}=Xr(i);let u=a.call(i,o);u||(o=ce(o),u=a.call(i,o));const c=l.call(i,o);return i.set(o,s),u?kt(s,c)&&wt(i,"set",o,s):wt(i,"add",o,s),this},delete(o){const s=ce(this),{has:i,get:a}=Xr(s);let l=i.call(s,o);l||(o=ce(o),l=i.call(s,o)),a&&a.call(s,o);const u=s.delete(o);return l&&wt(s,"delete",o,void 0),u},clear(){const o=ce(this),s=o.size!==0,i=o.clear();return s&&wt(o,"clear",void 0,void 0),i}}),["keys","values","entries",Symbol.iterator].forEach(o=>{r[o]=Fu(o,e,t)}),r}function Bs(e,t){const r=Bu(e,t);return(n,o,s)=>o==="__v_isReactive"?!e:o==="__v_isReadonly"?e:o==="__v_raw"?n:Reflect.get(fe(r,o)&&o in n?r:n,o,s)}const Lu={get:Bs(!1,!1)},Uu={get:Bs(!1,!0)},qu={get:Bs(!0,!1)};const _l=new WeakMap,Rl=new WeakMap,Ol=new WeakMap,Hu=new WeakMap;function ju(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function $u(e){return e.__v_skip||!Object.isExtensible(e)?0:ju(pu(e))}function Rn(e){return Vt(e)?e:Ls(e,!1,Du,Lu,_l)}function xl(e){return Ls(e,!1,Mu,Uu,Rl)}function Pl(e){return Ls(e,!0,Nu,qu,Ol)}function Ls(e,t,r,n,o){if(!_e(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const s=o.get(e);if(s)return s;const i=$u(e);if(i===0)return e;const a=new Proxy(e,i===2?n:r);return o.set(e,a),a}function or(e){return Vt(e)?or(e.__v_raw):!!(e&&e.__v_isReactive)}function Vt(e){return!!(e&&e.__v_isReadonly)}function st(e){return!!(e&&e.__v_isShallow)}function Us(e){return e?!!e.__v_raw:!1}function ce(e){const t=e&&e.__v_raw;return t?ce(t):e}function Gu(e){return!fe(e,"__v_skip")&&Object.isExtensible(e)&&cl(e,"__v_skip",!0),e}const He=e=>_e(e)?Rn(e):e,ds=e=>_e(e)?Pl(e):e;function Le(e){return e?e.__v_isRef===!0:!1}function xe(e){return Cl(e,!1)}function Wu(e){return Cl(e,!0)}function Cl(e,t){return Le(e)?e:new Ku(e,t)}class Ku{constructor(t,r){this.dep=new Fs,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=r?t:ce(t),this._value=r?t:He(t),this.__v_isShallow=r}get value(){return this.dep.track(),this._value}set value(t){const r=this._rawValue,n=this.__v_isShallow||st(t)||Vt(t);t=n?t:ce(t),kt(t,r)&&(this._rawValue=t,this._value=n?t:He(t),this.dep.trigger())}}function ne(e){return Le(e)?e.value:e}const Vu={get:(e,t,r)=>t==="__v_raw"?e:ne(Reflect.get(e,t,r)),set:(e,t,r,n)=>{const o=e[t];return Le(o)&&!Le(r)?(o.value=r,!0):Reflect.set(e,t,r,n)}};function Tl(e){return or(e)?e:new Proxy(e,Vu)}function zu(e){const t=X(e)?new Array(e.length):{};for(const r in e)t[r]=Ju(e,r);return t}class Qu{constructor(t,r,n){this._object=t,this._key=r,this._defaultValue=n,this.__v_isRef=!0,this._value=void 0}get value(){const t=this._object[this._key];return this._value=t===void 0?this._defaultValue:t}set value(t){this._object[this._key]=t}get dep(){return xu(ce(this._object),this._key)}}function Ju(e,t,r){const n=e[t];return Le(n)?n:new Qu(e,t,r)}class Xu{constructor(t,r,n){this.fn=t,this.setter=r,this._value=void 0,this.dep=new Fs(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=Dr-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!r,this.isSSR=n}notify(){if(this.flags|=16,!(this.flags&8)&&be!==this)return ml(this,!0),!0}get value(){const t=this.dep.track();return vl(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function Zu(e,t,r=!1){let n,o;return ee(e)?n=e:(n=e.get,o=e.set),new Xu(n,o,r)}const Yr={},cn=new WeakMap;let jt;function Yu(e,t=!1,r=jt){if(r){let n=cn.get(r);n||cn.set(r,n=[]),n.push(e)}}function ef(e,t,r=ye){const{immediate:n,deep:o,once:s,scheduler:i,augmentJob:a,call:l}=r,u=_=>o?_:st(_)||o===!1||o===0?St(_,1):St(_);let c,f,h,d,g=!1,w=!1;if(Le(e)?(f=()=>e.value,g=st(e)):or(e)?(f=()=>u(e),g=!0):X(e)?(w=!0,g=e.some(_=>or(_)||st(_)),f=()=>e.map(_=>{if(Le(_))return _.value;if(or(_))return u(_);if(ee(_))return l?l(_,2):_()})):ee(e)?t?f=l?()=>l(e,2):e:f=()=>{if(h){Mt();try{h()}finally{Ft()}}const _=jt;jt=c;try{return l?l(e,3,[d]):e(d)}finally{jt=_}}:f=mt,t&&o){const _=f,k=o===!0?1/0:o;f=()=>St(_(),k)}const y=_u(),v=()=>{c.stop(),y&&y.active&&Is(y.effects,c)};if(s&&t){const _=t;t=(...k)=>{_(...k),v()}}let b=w?new Array(e.length).fill(Yr):Yr;const R=_=>{if(!(!(c.flags&1)||!c.dirty&&!_))if(t){const k=c.run();if(o||g||(w?k.some((U,B)=>kt(U,b[B])):kt(k,b))){h&&h();const U=jt;jt=c;try{const B=[k,b===Yr?void 0:w&&b[0]===Yr?[]:b,d];l?l(t,3,B):t(...B),b=k}finally{jt=U}}}else c.run()};return a&&a(R),c=new pl(f),c.scheduler=i?()=>i(R,!1):R,d=_=>Yu(_,!1,c),h=c.onStop=()=>{const _=cn.get(c);if(_){if(l)l(_,4);else for(const k of _)k();cn.delete(c)}},t?n?R(!0):b=c.run():i?i(R.bind(null,!0),!0):c.run(),v.pause=c.pause.bind(c),v.resume=c.resume.bind(c),v.stop=v,v}function St(e,t=1/0,r){if(t<=0||!_e(e)||e.__v_skip||(r=r||new Set,r.has(e)))return e;if(r.add(e),t--,Le(e))St(e.value,t,r);else if(X(e))for(let n=0;n<e.length;n++)St(e[n],t,r);else if(ol(e)||nr(e))e.forEach(n=>{St(n,t,r)});else if(al(e)){for(const n in e)St(e[n],t,r);for(const n of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,n)&&St(e[n],t,r)}return e}/**
* @vue/runtime-core v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function $r(e,t,r,n){try{return n?e(...n):e()}catch(o){On(o,t,r)}}function gt(e,t,r,n){if(ee(e)){const o=$r(e,t,r,n);return o&&sl(o)&&o.catch(s=>{On(s,t,r)}),o}if(X(e)){const o=[];for(let s=0;s<e.length;s++)o.push(gt(e[s],t,r,n));return o}}function On(e,t,r,n=!0){const o=t?t.vnode:null,{errorHandler:s,throwUnhandledErrorInProduction:i}=t&&t.appContext.config||ye;if(t){let a=t.parent;const l=t.proxy,u=`https://vuejs.org/error-reference/#runtime-${r}`;for(;a;){const c=a.ec;if(c){for(let f=0;f<c.length;f++)if(c[f](e,l,u)===!1)return}a=a.parent}if(s){Mt(),$r(s,null,10,[e,l,u]),Ft();return}}tf(e,r,o,n,i)}function tf(e,t,r,n=!0,o=!1){if(o)throw e;console.error(e)}const ze=[];let pt=-1;const sr=[];let Pt=null,er=0;const Il=Promise.resolve();let un=null;function xn(e){const t=un||Il;return e?t.then(this?e.bind(this):e):t}function rf(e){let t=pt+1,r=ze.length;for(;t<r;){const n=t+r>>>1,o=ze[n],s=Mr(o);s<e||s===e&&o.flags&2?t=n+1:r=n}return t}function qs(e){if(!(e.flags&1)){const t=Mr(e),r=ze[ze.length-1];!r||!(e.flags&2)&&t>=Mr(r)?ze.push(e):ze.splice(rf(t),0,e),e.flags|=1,kl()}}function kl(){un||(un=Il.then(Nl))}function nf(e){X(e)?sr.push(...e):Pt&&e.id===-1?Pt.splice(er+1,0,e):e.flags&1||(sr.push(e),e.flags|=1),kl()}function di(e,t,r=pt+1){for(;r<ze.length;r++){const n=ze[r];if(n&&n.flags&2){if(e&&n.id!==e.uid)continue;ze.splice(r,1),r--,n.flags&4&&(n.flags&=-2),n(),n.flags&4||(n.flags&=-2)}}}function Dl(e){if(sr.length){const t=[...new Set(sr)].sort((r,n)=>Mr(r)-Mr(n));if(sr.length=0,Pt){Pt.push(...t);return}for(Pt=t,er=0;er<Pt.length;er++){const r=Pt[er];r.flags&4&&(r.flags&=-2),r.flags&8||r(),r.flags&=-2}Pt=null,er=0}}const Mr=e=>e.id==null?e.flags&2?-1:1/0:e.id;function Nl(e){try{for(pt=0;pt<ze.length;pt++){const t=ze[pt];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),$r(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;pt<ze.length;pt++){const t=ze[pt];t&&(t.flags&=-2)}pt=-1,ze.length=0,Dl(),un=null,(ze.length||sr.length)&&Nl()}}let Be=null,Pn=null;function fn(e){const t=Be;return Be=e,Pn=e&&e.type.__scopeId||null,t}function of(e){Pn=e}function sf(){Pn=null}function Hs(e,t=Be,r){if(!t||e._n)return e;const n=(...o)=>{n._d&&wi(-1);const s=fn(t);let i;try{i=e(...o)}finally{fn(s),n._d&&wi(1)}return i};return n._n=!0,n._c=!0,n._d=!0,n}function js(e,t){if(Be===null)return e;const r=Nn(Be),n=e.dirs||(e.dirs=[]);for(let o=0;o<t.length;o++){let[s,i,a,l=ye]=t[o];s&&(ee(s)&&(s={mounted:s,updated:s}),s.deep&&St(i),n.push({dir:s,instance:r,value:i,oldValue:void 0,arg:a,modifiers:l}))}return e}function qt(e,t,r,n){const o=e.dirs,s=t&&t.dirs;for(let i=0;i<o.length;i++){const a=o[i];s&&(a.oldValue=s[i].value);let l=a.dir[n];l&&(Mt(),gt(l,r,8,[e.el,a,e,t]),Ft())}}const af=Symbol("_vte"),lf=e=>e.__isTeleport;function $s(e,t){e.shapeFlag&6&&e.component?(e.transition=t,$s(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}/*! #__NO_SIDE_EFFECTS__ */function Cn(e,t){return ee(e)?Ge({name:e.name},t,{setup:e}):e}function Ml(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function dn(e,t,r,n,o=!1){if(X(e)){e.forEach((g,w)=>dn(g,t&&(X(t)?t[w]:t),r,n,o));return}if(ir(n)&&!o){n.shapeFlag&512&&n.type.__asyncResolved&&n.component.subTree.component&&dn(e,t,r,n.component.subTree);return}const s=n.shapeFlag&4?Nn(n.component):n.el,i=o?null:s,{i:a,r:l}=e,u=t&&t.r,c=a.refs===ye?a.refs={}:a.refs,f=a.setupState,h=ce(f),d=f===ye?()=>!1:g=>fe(h,g);if(u!=null&&u!==l&&(Ce(u)?(c[u]=null,d(u)&&(f[u]=null)):Le(u)&&(u.value=null)),ee(l))$r(l,a,12,[i,c]);else{const g=Ce(l),w=Le(l);if(g||w){const y=()=>{if(e.f){const v=g?d(l)?f[l]:c[l]:l.value;o?X(v)&&Is(v,s):X(v)?v.includes(s)||v.push(s):g?(c[l]=[s],d(l)&&(f[l]=c[l])):(l.value=[s],e.k&&(c[e.k]=l.value))}else g?(c[l]=i,d(l)&&(f[l]=i)):w&&(l.value=i,e.k&&(c[e.k]=i))};i?(y.id=-1,Ye(y,r)):y()}}}Sn().requestIdleCallback;Sn().cancelIdleCallback;const ir=e=>!!e.type.__asyncLoader,Fl=e=>e.type.__isKeepAlive;function cf(e,t){Bl(e,"a",t)}function uf(e,t){Bl(e,"da",t)}function Bl(e,t,r=je){const n=e.__wdc||(e.__wdc=()=>{let o=r;for(;o;){if(o.isDeactivated)return;o=o.parent}return e()});if(Tn(t,n,r),r){let o=r.parent;for(;o&&o.parent;)Fl(o.parent.vnode)&&ff(n,t,r,o),o=o.parent}}function ff(e,t,r,n){const o=Tn(t,e,n,!0);In(()=>{Is(n[t],o)},r)}function Tn(e,t,r=je,n=!1){if(r){const o=r[e]||(r[e]=[]),s=t.__weh||(t.__weh=(...i)=>{Mt();const a=Gr(r),l=gt(t,r,e,i);return a(),Ft(),l});return n?o.unshift(s):o.push(s),s}}const _t=e=>(t,r=je)=>{(!Ur||e==="sp")&&Tn(e,(...n)=>t(...n),r)},df=_t("bm"),Bt=_t("m"),pf=_t("bu"),hf=_t("u"),mf=_t("bum"),In=_t("um"),yf=_t("sp"),gf=_t("rtg"),vf=_t("rtc");function bf(e,t=je){Tn("ec",e,t)}const Af=Symbol.for("v-ndc");function pn(e,t,r,n){let o;const s=r,i=X(e);if(i||Ce(e)){const a=i&&or(e);let l=!1;a&&(l=!st(e),e=_n(e)),o=new Array(e.length);for(let u=0,c=e.length;u<c;u++)o[u]=t(l?He(e[u]):e[u],u,void 0,s)}else if(typeof e=="number"){o=new Array(e);for(let a=0;a<e;a++)o[a]=t(a+1,a,void 0,s)}else if(_e(e))if(e[Symbol.iterator])o=Array.from(e,(a,l)=>t(a,l,void 0,s));else{const a=Object.keys(e);o=new Array(a.length);for(let l=0,u=a.length;l<u;l++){const c=a[l];o[l]=t(e[c],c,l,s)}}else o=[];return o}function en(e,t,r={},n,o){if(Be.ce||Be.parent&&ir(Be.parent)&&Be.parent.ce)return t!=="default"&&(r.name=t),ie(),Br(Fe,null,[Se("slot",r,n&&n())],64);let s=e[t];s&&s._c&&(s._d=!1),ie();const i=s&&Ll(s(r)),a=r.key||i&&i.key,l=Br(Fe,{key:(a&&!Et(a)?a:`_${t}`)+(!i&&n?"_fb":"")},i||(n?n():[]),i&&e._===1?64:-2);return s&&s._c&&(s._d=!0),l}function Ll(e){return e.some(t=>Lr(t)?!(t.type===Nt||t.type===Fe&&!Ll(t.children)):!0)?e:null}const ps=e=>e?ic(e)?Nn(e):ps(e.parent):null,Cr=Ge(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>ps(e.parent),$root:e=>ps(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>ql(e),$forceUpdate:e=>e.f||(e.f=()=>{qs(e.update)}),$nextTick:e=>e.n||(e.n=xn.bind(e.proxy)),$watch:e=>Hf.bind(e)}),no=(e,t)=>e!==ye&&!e.__isScriptSetup&&fe(e,t),wf={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:r,setupState:n,data:o,props:s,accessCache:i,type:a,appContext:l}=e;let u;if(t[0]!=="$"){const d=i[t];if(d!==void 0)switch(d){case 1:return n[t];case 2:return o[t];case 4:return r[t];case 3:return s[t]}else{if(no(n,t))return i[t]=1,n[t];if(o!==ye&&fe(o,t))return i[t]=2,o[t];if((u=e.propsOptions[0])&&fe(u,t))return i[t]=3,s[t];if(r!==ye&&fe(r,t))return i[t]=4,r[t];hs&&(i[t]=0)}}const c=Cr[t];let f,h;if(c)return t==="$attrs"&&qe(e.attrs,"get",""),c(e);if((f=a.__cssModules)&&(f=f[t]))return f;if(r!==ye&&fe(r,t))return i[t]=4,r[t];if(h=l.config.globalProperties,fe(h,t))return h[t]},set({_:e},t,r){const{data:n,setupState:o,ctx:s}=e;return no(o,t)?(o[t]=r,!0):n!==ye&&fe(n,t)?(n[t]=r,!0):fe(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(s[t]=r,!0)},has({_:{data:e,setupState:t,accessCache:r,ctx:n,appContext:o,propsOptions:s}},i){let a;return!!r[i]||e!==ye&&fe(e,i)||no(t,i)||(a=s[0])&&fe(a,i)||fe(n,i)||fe(Cr,i)||fe(o.config.globalProperties,i)},defineProperty(e,t,r){return r.get!=null?e._.accessCache[t]=0:fe(r,"value")&&this.set(e,t,r.value,null),Reflect.defineProperty(e,t,r)}};function pi(e){return X(e)?e.reduce((t,r)=>(t[r]=null,t),{}):e}let hs=!0;function Sf(e){const t=ql(e),r=e.proxy,n=e.ctx;hs=!1,t.beforeCreate&&hi(t.beforeCreate,e,"bc");const{data:o,computed:s,methods:i,watch:a,provide:l,inject:u,created:c,beforeMount:f,mounted:h,beforeUpdate:d,updated:g,activated:w,deactivated:y,beforeDestroy:v,beforeUnmount:b,destroyed:R,unmounted:_,render:k,renderTracked:U,renderTriggered:B,errorCaptured:W,serverPrefetch:Z,expose:z,inheritAttrs:se,components:K,directives:me,filters:ge}=t;if(u&&Ef(u,n,null),i)for(const re in i){const j=i[re];ee(j)&&(n[re]=j.bind(r))}if(o){const re=o.call(r,r);_e(re)&&(e.data=Rn(re))}if(hs=!0,s)for(const re in s){const j=s[re],Re=ee(j)?j.bind(r,r):ee(j.get)?j.get.bind(r,r):mt,ke=!ee(j)&&ee(j.set)?j.set.bind(r):mt,De=ot({get:Re,set:ke});Object.defineProperty(n,re,{enumerable:!0,configurable:!0,get:()=>De.value,set:Oe=>De.value=Oe})}if(a)for(const re in a)Ul(a[re],n,r,re);if(l){const re=ee(l)?l.call(r):l;Reflect.ownKeys(re).forEach(j=>{lr(j,re[j])})}c&&hi(c,e,"c");function he(re,j){X(j)?j.forEach(Re=>re(Re.bind(r))):j&&re(j.bind(r))}if(he(df,f),he(Bt,h),he(pf,d),he(hf,g),he(cf,w),he(uf,y),he(bf,W),he(vf,U),he(gf,B),he(mf,b),he(In,_),he(yf,Z),X(z))if(z.length){const re=e.exposed||(e.exposed={});z.forEach(j=>{Object.defineProperty(re,j,{get:()=>r[j],set:Re=>r[j]=Re})})}else e.exposed||(e.exposed={});k&&e.render===mt&&(e.render=k),se!=null&&(e.inheritAttrs=se),K&&(e.components=K),me&&(e.directives=me),Z&&Ml(e)}function Ef(e,t,r=mt){X(e)&&(e=ms(e));for(const n in e){const o=e[n];let s;_e(o)?"default"in o?s=Ie(o.from||n,o.default,!0):s=Ie(o.from||n):s=Ie(o),Le(s)?Object.defineProperty(t,n,{enumerable:!0,configurable:!0,get:()=>s.value,set:i=>s.value=i}):t[n]=s}}function hi(e,t,r){gt(X(e)?e.map(n=>n.bind(t.proxy)):e.bind(t.proxy),t,r)}function Ul(e,t,r,n){let o=n.includes(".")?ec(r,n):()=>r[n];if(Ce(e)){const s=t[e];ee(s)&&lt(o,s)}else if(ee(e))lt(o,e.bind(r));else if(_e(e))if(X(e))e.forEach(s=>Ul(s,t,r,n));else{const s=ee(e.handler)?e.handler.bind(r):t[e.handler];ee(s)&&lt(o,s,e)}}function ql(e){const t=e.type,{mixins:r,extends:n}=t,{mixins:o,optionsCache:s,config:{optionMergeStrategies:i}}=e.appContext,a=s.get(t);let l;return a?l=a:!o.length&&!r&&!n?l=t:(l={},o.length&&o.forEach(u=>hn(l,u,i,!0)),hn(l,t,i)),_e(t)&&s.set(t,l),l}function hn(e,t,r,n=!1){const{mixins:o,extends:s}=t;s&&hn(e,s,r,!0),o&&o.forEach(i=>hn(e,i,r,!0));for(const i in t)if(!(n&&i==="expose")){const a=_f[i]||r&&r[i];e[i]=a?a(e[i],t[i]):t[i]}return e}const _f={data:mi,props:yi,emits:yi,methods:Rr,computed:Rr,beforeCreate:Ve,created:Ve,beforeMount:Ve,mounted:Ve,beforeUpdate:Ve,updated:Ve,beforeDestroy:Ve,beforeUnmount:Ve,destroyed:Ve,unmounted:Ve,activated:Ve,deactivated:Ve,errorCaptured:Ve,serverPrefetch:Ve,components:Rr,directives:Rr,watch:Of,provide:mi,inject:Rf};function mi(e,t){return t?e?function(){return Ge(ee(e)?e.call(this,this):e,ee(t)?t.call(this,this):t)}:t:e}function Rf(e,t){return Rr(ms(e),ms(t))}function ms(e){if(X(e)){const t={};for(let r=0;r<e.length;r++)t[e[r]]=e[r];return t}return e}function Ve(e,t){return e?[...new Set([].concat(e,t))]:t}function Rr(e,t){return e?Ge(Object.create(null),e,t):t}function yi(e,t){return e?X(e)&&X(t)?[...new Set([...e,...t])]:Ge(Object.create(null),pi(e),pi(t??{})):t}function Of(e,t){if(!e)return t;if(!t)return e;const r=Ge(Object.create(null),e);for(const n in t)r[n]=Ve(e[n],t[n]);return r}function Hl(){return{app:null,config:{isNativeTag:fu,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let xf=0;function Pf(e,t){return function(n,o=null){ee(n)||(n=Ge({},n)),o!=null&&!_e(o)&&(o=null);const s=Hl(),i=new WeakSet,a=[];let l=!1;const u=s.app={_uid:xf++,_component:n,_props:o,_container:null,_context:s,_instance:null,version:ld,get config(){return s.config},set config(c){},use(c,...f){return i.has(c)||(c&&ee(c.install)?(i.add(c),c.install(u,...f)):ee(c)&&(i.add(c),c(u,...f))),u},mixin(c){return s.mixins.includes(c)||s.mixins.push(c),u},component(c,f){return f?(s.components[c]=f,u):s.components[c]},directive(c,f){return f?(s.directives[c]=f,u):s.directives[c]},mount(c,f,h){if(!l){const d=u._ceVNode||Se(n,o);return d.appContext=s,h===!0?h="svg":h===!1&&(h=void 0),e(d,c,h),l=!0,u._container=c,c.__vue_app__=u,Nn(d.component)}},onUnmount(c){a.push(c)},unmount(){l&&(gt(a,u._instance,16),e(null,u._container),delete u._container.__vue_app__)},provide(c,f){return s.provides[c]=f,u},runWithContext(c){const f=ar;ar=u;try{return c()}finally{ar=f}}};return u}}let ar=null;function lr(e,t){if(je){let r=je.provides;const n=je.parent&&je.parent.provides;n===r&&(r=je.provides=Object.create(n)),r[e]=t}}function Ie(e,t,r=!1){const n=je||Be;if(n||ar){const o=ar?ar._context.provides:n?n.parent==null?n.vnode.appContext&&n.vnode.appContext.provides:n.parent.provides:void 0;if(o&&e in o)return o[e];if(arguments.length>1)return r&&ee(t)?t.call(n&&n.proxy):t}}const jl={},$l=()=>Object.create(jl),Gl=e=>Object.getPrototypeOf(e)===jl;function Cf(e,t,r,n=!1){const o={},s=$l();e.propsDefaults=Object.create(null),Wl(e,t,o,s);for(const i in e.propsOptions[0])i in o||(o[i]=void 0);r?e.props=n?o:xl(o):e.type.props?e.props=o:e.props=s,e.attrs=s}function Tf(e,t,r,n){const{props:o,attrs:s,vnode:{patchFlag:i}}=e,a=ce(o),[l]=e.propsOptions;let u=!1;if((n||i>0)&&!(i&16)){if(i&8){const c=e.vnode.dynamicProps;for(let f=0;f<c.length;f++){let h=c[f];if(kn(e.emitsOptions,h))continue;const d=t[h];if(l)if(fe(s,h))d!==s[h]&&(s[h]=d,u=!0);else{const g=Dt(h);o[g]=ys(l,a,g,d,e,!1)}else d!==s[h]&&(s[h]=d,u=!0)}}}else{Wl(e,t,o,s)&&(u=!0);let c;for(const f in a)(!t||!fe(t,f)&&((c=Qt(f))===f||!fe(t,c)))&&(l?r&&(r[f]!==void 0||r[c]!==void 0)&&(o[f]=ys(l,a,f,void 0,e,!0)):delete o[f]);if(s!==a)for(const f in s)(!t||!fe(t,f))&&(delete s[f],u=!0)}u&&wt(e.attrs,"set","")}function Wl(e,t,r,n){const[o,s]=e.propsOptions;let i=!1,a;if(t)for(let l in t){if(Or(l))continue;const u=t[l];let c;o&&fe(o,c=Dt(l))?!s||!s.includes(c)?r[c]=u:(a||(a={}))[c]=u:kn(e.emitsOptions,l)||(!(l in n)||u!==n[l])&&(n[l]=u,i=!0)}if(s){const l=ce(r),u=a||ye;for(let c=0;c<s.length;c++){const f=s[c];r[f]=ys(o,l,f,u[f],e,!fe(u,f))}}return i}function ys(e,t,r,n,o,s){const i=e[r];if(i!=null){const a=fe(i,"default");if(a&&n===void 0){const l=i.default;if(i.type!==Function&&!i.skipFactory&&ee(l)){const{propsDefaults:u}=o;if(r in u)n=u[r];else{const c=Gr(o);n=u[r]=l.call(null,t),c()}}else n=l;o.ce&&o.ce._setProp(r,n)}i[0]&&(s&&!a?n=!1:i[1]&&(n===""||n===Qt(r))&&(n=!0))}return n}const If=new WeakMap;function Kl(e,t,r=!1){const n=r?If:t.propsCache,o=n.get(e);if(o)return o;const s=e.props,i={},a=[];let l=!1;if(!ee(e)){const c=f=>{l=!0;const[h,d]=Kl(f,t,!0);Ge(i,h),d&&a.push(...d)};!r&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}if(!s&&!l)return _e(e)&&n.set(e,rr),rr;if(X(s))for(let c=0;c<s.length;c++){const f=Dt(s[c]);gi(f)&&(i[f]=ye)}else if(s)for(const c in s){const f=Dt(c);if(gi(f)){const h=s[c],d=i[f]=X(h)||ee(h)?{type:h}:Ge({},h),g=d.type;let w=!1,y=!0;if(X(g))for(let v=0;v<g.length;++v){const b=g[v],R=ee(b)&&b.name;if(R==="Boolean"){w=!0;break}else R==="String"&&(y=!1)}else w=ee(g)&&g.name==="Boolean";d[0]=w,d[1]=y,(w||fe(d,"default"))&&a.push(f)}}const u=[i,a];return _e(e)&&n.set(e,u),u}function gi(e){return e[0]!=="$"&&!Or(e)}const Vl=e=>e[0]==="_"||e==="$stable",Gs=e=>X(e)?e.map(ht):[ht(e)],kf=(e,t,r)=>{if(t._n)return t;const n=Hs((...o)=>Gs(t(...o)),r);return n._c=!1,n},zl=(e,t,r)=>{const n=e._ctx;for(const o in e){if(Vl(o))continue;const s=e[o];if(ee(s))t[o]=kf(o,s,n);else if(s!=null){const i=Gs(s);t[o]=()=>i}}},Ql=(e,t)=>{const r=Gs(t);e.slots.default=()=>r},Jl=(e,t,r)=>{for(const n in t)(r||n!=="_")&&(e[n]=t[n])},Df=(e,t,r)=>{const n=e.slots=$l();if(e.vnode.shapeFlag&32){const o=t._;o?(Jl(n,t,r),r&&cl(n,"_",o,!0)):zl(t,n)}else t&&Ql(e,t)},Nf=(e,t,r)=>{const{vnode:n,slots:o}=e;let s=!0,i=ye;if(n.shapeFlag&32){const a=t._;a?r&&a===1?s=!1:Jl(o,t,r):(s=!t.$stable,zl(t,o)),i=t}else t&&(Ql(e,t),i={default:1});if(s)for(const a in o)!Vl(a)&&i[a]==null&&delete o[a]},Ye=zf;function Mf(e){return Ff(e)}function Ff(e,t){const r=Sn();r.__VUE__=!0;const{insert:n,remove:o,patchProp:s,createElement:i,createText:a,createComment:l,setText:u,setElementText:c,parentNode:f,nextSibling:h,setScopeId:d=mt,insertStaticContent:g}=e,w=(p,m,A,x=null,C=null,P=null,F=void 0,L=null,M=!!m.dynamicChildren)=>{if(p===m)return;p&&!br(p,m)&&(x=T(p),Oe(p,C,P,!0),p=null),m.patchFlag===-2&&(M=!1,m.dynamicChildren=null);const{type:I,ref:V,shapeFlag:q}=m;switch(I){case Dn:y(p,m,A,x);break;case Nt:v(p,m,A,x);break;case so:p==null&&b(m,A,x,F);break;case Fe:K(p,m,A,x,C,P,F,L,M);break;default:q&1?k(p,m,A,x,C,P,F,L,M):q&6?me(p,m,A,x,C,P,F,L,M):(q&64||q&128)&&I.process(p,m,A,x,C,P,F,L,M,N)}V!=null&&C&&dn(V,p&&p.ref,P,m||p,!m)},y=(p,m,A,x)=>{if(p==null)n(m.el=a(m.children),A,x);else{const C=m.el=p.el;m.children!==p.children&&u(C,m.children)}},v=(p,m,A,x)=>{p==null?n(m.el=l(m.children||""),A,x):m.el=p.el},b=(p,m,A,x)=>{[p.el,p.anchor]=g(p.children,m,A,x,p.el,p.anchor)},R=({el:p,anchor:m},A,x)=>{let C;for(;p&&p!==m;)C=h(p),n(p,A,x),p=C;n(m,A,x)},_=({el:p,anchor:m})=>{let A;for(;p&&p!==m;)A=h(p),o(p),p=A;o(m)},k=(p,m,A,x,C,P,F,L,M)=>{m.type==="svg"?F="svg":m.type==="math"&&(F="mathml"),p==null?U(m,A,x,C,P,F,L,M):Z(p,m,C,P,F,L,M)},U=(p,m,A,x,C,P,F,L)=>{let M,I;const{props:V,shapeFlag:q,transition:G,dirs:J}=p;if(M=p.el=i(p.type,P,V&&V.is,V),q&8?c(M,p.children):q&16&&W(p.children,M,null,x,C,oo(p,P),F,L),J&&qt(p,null,x,"created"),B(M,p,p.scopeId,F,x),V){for(const pe in V)pe!=="value"&&!Or(pe)&&s(M,pe,null,V[pe],P,x);"value"in V&&s(M,"value",null,V.value,P),(I=V.onVnodeBeforeMount)&&ft(I,x,p)}J&&qt(p,null,x,"beforeMount");const oe=Bf(C,G);oe&&G.beforeEnter(M),n(M,m,A),((I=V&&V.onVnodeMounted)||oe||J)&&Ye(()=>{I&&ft(I,x,p),oe&&G.enter(M),J&&qt(p,null,x,"mounted")},C)},B=(p,m,A,x,C)=>{if(A&&d(p,A),x)for(let P=0;P<x.length;P++)d(p,x[P]);if(C){let P=C.subTree;if(m===P||rc(P.type)&&(P.ssContent===m||P.ssFallback===m)){const F=C.vnode;B(p,F,F.scopeId,F.slotScopeIds,C.parent)}}},W=(p,m,A,x,C,P,F,L,M=0)=>{for(let I=M;I<p.length;I++){const V=p[I]=L?Ct(p[I]):ht(p[I]);w(null,V,m,A,x,C,P,F,L)}},Z=(p,m,A,x,C,P,F)=>{const L=m.el=p.el;let{patchFlag:M,dynamicChildren:I,dirs:V}=m;M|=p.patchFlag&16;const q=p.props||ye,G=m.props||ye;let J;if(A&&Ht(A,!1),(J=G.onVnodeBeforeUpdate)&&ft(J,A,m,p),V&&qt(m,p,A,"beforeUpdate"),A&&Ht(A,!0),(q.innerHTML&&G.innerHTML==null||q.textContent&&G.textContent==null)&&c(L,""),I?z(p.dynamicChildren,I,L,A,x,oo(m,C),P):F||j(p,m,L,null,A,x,oo(m,C),P,!1),M>0){if(M&16)se(L,q,G,A,C);else if(M&2&&q.class!==G.class&&s(L,"class",null,G.class,C),M&4&&s(L,"style",q.style,G.style,C),M&8){const oe=m.dynamicProps;for(let pe=0;pe<oe.length;pe++){const ae=oe[pe],Te=q[ae],Ne=G[ae];(Ne!==Te||ae==="value")&&s(L,ae,Te,Ne,C,A)}}M&1&&p.children!==m.children&&c(L,m.children)}else!F&&I==null&&se(L,q,G,A,C);((J=G.onVnodeUpdated)||V)&&Ye(()=>{J&&ft(J,A,m,p),V&&qt(m,p,A,"updated")},x)},z=(p,m,A,x,C,P,F)=>{for(let L=0;L<m.length;L++){const M=p[L],I=m[L],V=M.el&&(M.type===Fe||!br(M,I)||M.shapeFlag&70)?f(M.el):A;w(M,I,V,null,x,C,P,F,!0)}},se=(p,m,A,x,C)=>{if(m!==A){if(m!==ye)for(const P in m)!Or(P)&&!(P in A)&&s(p,P,m[P],null,C,x);for(const P in A){if(Or(P))continue;const F=A[P],L=m[P];F!==L&&P!=="value"&&s(p,P,L,F,C,x)}"value"in A&&s(p,"value",m.value,A.value,C)}},K=(p,m,A,x,C,P,F,L,M)=>{const I=m.el=p?p.el:a(""),V=m.anchor=p?p.anchor:a("");let{patchFlag:q,dynamicChildren:G,slotScopeIds:J}=m;J&&(L=L?L.concat(J):J),p==null?(n(I,A,x),n(V,A,x),W(m.children||[],A,V,C,P,F,L,M)):q>0&&q&64&&G&&p.dynamicChildren?(z(p.dynamicChildren,G,A,C,P,F,L),(m.key!=null||C&&m===C.subTree)&&Xl(p,m,!0)):j(p,m,A,V,C,P,F,L,M)},me=(p,m,A,x,C,P,F,L,M)=>{m.slotScopeIds=L,p==null?m.shapeFlag&512?C.ctx.activate(m,A,x,F,M):ge(m,A,x,C,P,F,M):Pe(p,m,M)},ge=(p,m,A,x,C,P,F)=>{const L=p.component=td(p,x,C);if(Fl(p)&&(L.ctx.renderer=N),nd(L,!1,F),L.asyncDep){if(C&&C.registerDep(L,he,F),!p.el){const M=L.subTree=Se(Nt);v(null,M,m,A)}}else he(L,p,m,A,C,P,F)},Pe=(p,m,A)=>{const x=m.component=p.component;if(Kf(p,m,A))if(x.asyncDep&&!x.asyncResolved){re(x,m,A);return}else x.next=m,x.update();else m.el=p.el,x.vnode=m},he=(p,m,A,x,C,P,F)=>{const L=()=>{if(p.isMounted){let{next:q,bu:G,u:J,parent:oe,vnode:pe}=p;{const nt=Zl(p);if(nt){q&&(q.el=pe.el,re(p,q,F)),nt.asyncDep.then(()=>{p.isUnmounted||L()});return}}let ae=q,Te;Ht(p,!1),q?(q.el=pe.el,re(p,q,F)):q=pe,G&&Zn(G),(Te=q.props&&q.props.onVnodeBeforeUpdate)&&ft(Te,oe,q,pe),Ht(p,!0);const Ne=bi(p),Xe=p.subTree;p.subTree=Ne,w(Xe,Ne,f(Xe.el),T(Xe),p,C,P),q.el=Ne.el,ae===null&&Vf(p,Ne.el),J&&Ye(J,C),(Te=q.props&&q.props.onVnodeUpdated)&&Ye(()=>ft(Te,oe,q,pe),C)}else{let q;const{el:G,props:J}=m,{bm:oe,m:pe,parent:ae,root:Te,type:Ne}=p,Xe=ir(m);Ht(p,!1),oe&&Zn(oe),!Xe&&(q=J&&J.onVnodeBeforeMount)&&ft(q,ae,m),Ht(p,!0);{Te.ce&&Te.ce._injectChildStyle(Ne);const nt=p.subTree=bi(p);w(null,nt,A,x,p,C,P),m.el=nt.el}if(pe&&Ye(pe,C),!Xe&&(q=J&&J.onVnodeMounted)){const nt=m;Ye(()=>ft(q,ae,nt),C)}(m.shapeFlag&256||ae&&ir(ae.vnode)&&ae.vnode.shapeFlag&256)&&p.a&&Ye(p.a,C),p.isMounted=!0,m=A=x=null}};p.scope.on();const M=p.effect=new pl(L);p.scope.off();const I=p.update=M.run.bind(M),V=p.job=M.runIfDirty.bind(M);V.i=p,V.id=p.uid,M.scheduler=()=>qs(V),Ht(p,!0),I()},re=(p,m,A)=>{m.component=p;const x=p.vnode.props;p.vnode=m,p.next=null,Tf(p,m.props,x,A),Nf(p,m.children,A),Mt(),di(p),Ft()},j=(p,m,A,x,C,P,F,L,M=!1)=>{const I=p&&p.children,V=p?p.shapeFlag:0,q=m.children,{patchFlag:G,shapeFlag:J}=m;if(G>0){if(G&128){ke(I,q,A,x,C,P,F,L,M);return}else if(G&256){Re(I,q,A,x,C,P,F,L,M);return}}J&8?(V&16&&Ae(I,C,P),q!==I&&c(A,q)):V&16?J&16?ke(I,q,A,x,C,P,F,L,M):Ae(I,C,P,!0):(V&8&&c(A,""),J&16&&W(q,A,x,C,P,F,L,M))},Re=(p,m,A,x,C,P,F,L,M)=>{p=p||rr,m=m||rr;const I=p.length,V=m.length,q=Math.min(I,V);let G;for(G=0;G<q;G++){const J=m[G]=M?Ct(m[G]):ht(m[G]);w(p[G],J,A,null,C,P,F,L,M)}I>V?Ae(p,C,P,!0,!1,q):W(m,A,x,C,P,F,L,M,q)},ke=(p,m,A,x,C,P,F,L,M)=>{let I=0;const V=m.length;let q=p.length-1,G=V-1;for(;I<=q&&I<=G;){const J=p[I],oe=m[I]=M?Ct(m[I]):ht(m[I]);if(br(J,oe))w(J,oe,A,null,C,P,F,L,M);else break;I++}for(;I<=q&&I<=G;){const J=p[q],oe=m[G]=M?Ct(m[G]):ht(m[G]);if(br(J,oe))w(J,oe,A,null,C,P,F,L,M);else break;q--,G--}if(I>q){if(I<=G){const J=G+1,oe=J<V?m[J].el:x;for(;I<=G;)w(null,m[I]=M?Ct(m[I]):ht(m[I]),A,oe,C,P,F,L,M),I++}}else if(I>G)for(;I<=q;)Oe(p[I],C,P,!0),I++;else{const J=I,oe=I,pe=new Map;for(I=oe;I<=G;I++){const Me=m[I]=M?Ct(m[I]):ht(m[I]);Me.key!=null&&pe.set(Me.key,I)}let ae,Te=0;const Ne=G-oe+1;let Xe=!1,nt=0;const Lt=new Array(Ne);for(I=0;I<Ne;I++)Lt[I]=0;for(I=J;I<=q;I++){const Me=p[I];if(Te>=Ne){Oe(Me,C,P,!0);continue}let S;if(Me.key!=null)S=pe.get(Me.key);else for(ae=oe;ae<=G;ae++)if(Lt[ae-oe]===0&&br(Me,m[ae])){S=ae;break}S===void 0?Oe(Me,C,P,!0):(Lt[S-oe]=I+1,S>=nt?nt=S:Xe=!0,w(Me,m[S],A,null,C,P,F,L,M),Te++)}const Jt=Xe?Lf(Lt):rr;for(ae=Jt.length-1,I=Ne-1;I>=0;I--){const Me=oe+I,S=m[Me],E=Me+1<V?m[Me+1].el:x;Lt[I]===0?w(null,S,A,E,C,P,F,L,M):Xe&&(ae<0||I!==Jt[ae]?De(S,A,E,2):ae--)}}},De=(p,m,A,x,C=null)=>{const{el:P,type:F,transition:L,children:M,shapeFlag:I}=p;if(I&6){De(p.component.subTree,m,A,x);return}if(I&128){p.suspense.move(m,A,x);return}if(I&64){F.move(p,m,A,N);return}if(F===Fe){n(P,m,A);for(let q=0;q<M.length;q++)De(M[q],m,A,x);n(p.anchor,m,A);return}if(F===so){R(p,m,A);return}if(x!==2&&I&1&&L)if(x===0)L.beforeEnter(P),n(P,m,A),Ye(()=>L.enter(P),C);else{const{leave:q,delayLeave:G,afterLeave:J}=L,oe=()=>n(P,m,A),pe=()=>{q(P,()=>{oe(),J&&J()})};G?G(P,oe,pe):pe()}else n(P,m,A)},Oe=(p,m,A,x=!1,C=!1)=>{const{type:P,props:F,ref:L,children:M,dynamicChildren:I,shapeFlag:V,patchFlag:q,dirs:G,cacheIndex:J}=p;if(q===-2&&(C=!1),L!=null&&dn(L,null,A,p,!0),J!=null&&(m.renderCache[J]=void 0),V&256){m.ctx.deactivate(p);return}const oe=V&1&&G,pe=!ir(p);let ae;if(pe&&(ae=F&&F.onVnodeBeforeUnmount)&&ft(ae,m,p),V&6)Qe(p.component,A,x);else{if(V&128){p.suspense.unmount(A,x);return}oe&&qt(p,null,m,"beforeUnmount"),V&64?p.type.remove(p,m,A,N,x):I&&!I.hasOnce&&(P!==Fe||q>0&&q&64)?Ae(I,m,A,!1,!0):(P===Fe&&q&384||!C&&V&16)&&Ae(M,m,A),x&&We(p)}(pe&&(ae=F&&F.onVnodeUnmounted)||oe)&&Ye(()=>{ae&&ft(ae,m,p),oe&&qt(p,null,m,"unmounted")},A)},We=p=>{const{type:m,el:A,anchor:x,transition:C}=p;if(m===Fe){Ke(A,x);return}if(m===so){_(p);return}const P=()=>{o(A),C&&!C.persisted&&C.afterLeave&&C.afterLeave()};if(p.shapeFlag&1&&C&&!C.persisted){const{leave:F,delayLeave:L}=C,M=()=>F(A,P);L?L(p.el,P,M):M()}else P()},Ke=(p,m)=>{let A;for(;p!==m;)A=h(p),o(p),p=A;o(m)},Qe=(p,m,A)=>{const{bum:x,scope:C,job:P,subTree:F,um:L,m:M,a:I}=p;vi(M),vi(I),x&&Zn(x),C.stop(),P&&(P.flags|=8,Oe(F,p,m,A)),L&&Ye(L,m),Ye(()=>{p.isUnmounted=!0},m),m&&m.pendingBranch&&!m.isUnmounted&&p.asyncDep&&!p.asyncResolved&&p.suspenseId===m.pendingId&&(m.deps--,m.deps===0&&m.resolve())},Ae=(p,m,A,x=!1,C=!1,P=0)=>{for(let F=P;F<p.length;F++)Oe(p[F],m,A,x,C)},T=p=>{if(p.shapeFlag&6)return T(p.component.subTree);if(p.shapeFlag&128)return p.suspense.next();const m=h(p.anchor||p.el),A=m&&m[af];return A?h(A):m};let H=!1;const D=(p,m,A)=>{p==null?m._vnode&&Oe(m._vnode,null,null,!0):w(m._vnode||null,p,m,null,null,null,A),m._vnode=p,H||(H=!0,di(),Dl(),H=!1)},N={p:w,um:Oe,m:De,r:We,mt:ge,mc:W,pc:j,pbc:z,n:T,o:e};return{render:D,hydrate:void 0,createApp:Pf(D)}}function oo({type:e,props:t},r){return r==="svg"&&e==="foreignObject"||r==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:r}function Ht({effect:e,job:t},r){r?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function Bf(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function Xl(e,t,r=!1){const n=e.children,o=t.children;if(X(n)&&X(o))for(let s=0;s<n.length;s++){const i=n[s];let a=o[s];a.shapeFlag&1&&!a.dynamicChildren&&((a.patchFlag<=0||a.patchFlag===32)&&(a=o[s]=Ct(o[s]),a.el=i.el),!r&&a.patchFlag!==-2&&Xl(i,a)),a.type===Dn&&(a.el=i.el)}}function Lf(e){const t=e.slice(),r=[0];let n,o,s,i,a;const l=e.length;for(n=0;n<l;n++){const u=e[n];if(u!==0){if(o=r[r.length-1],e[o]<u){t[n]=o,r.push(n);continue}for(s=0,i=r.length-1;s<i;)a=s+i>>1,e[r[a]]<u?s=a+1:i=a;u<e[r[s]]&&(s>0&&(t[n]=r[s-1]),r[s]=n)}}for(s=r.length,i=r[s-1];s-- >0;)r[s]=i,i=t[i];return r}function Zl(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:Zl(t)}function vi(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const Uf=Symbol.for("v-scx"),qf=()=>Ie(Uf);function lt(e,t,r){return Yl(e,t,r)}function Yl(e,t,r=ye){const{immediate:n,deep:o,flush:s,once:i}=r,a=Ge({},r),l=t&&n||!t&&s!=="post";let u;if(Ur){if(s==="sync"){const d=qf();u=d.__watcherHandles||(d.__watcherHandles=[])}else if(!l){const d=()=>{};return d.stop=mt,d.resume=mt,d.pause=mt,d}}const c=je;a.call=(d,g,w)=>gt(d,c,g,w);let f=!1;s==="post"?a.scheduler=d=>{Ye(d,c&&c.suspense)}:s!=="sync"&&(f=!0,a.scheduler=(d,g)=>{g?d():qs(d)}),a.augmentJob=d=>{t&&(d.flags|=4),f&&(d.flags|=2,c&&(d.id=c.uid,d.i=c))};const h=ef(e,t,a);return Ur&&(u?u.push(h):l&&h()),h}function Hf(e,t,r){const n=this.proxy,o=Ce(e)?e.includes(".")?ec(n,e):()=>n[e]:e.bind(n,n);let s;ee(t)?s=t:(s=t.handler,r=t);const i=Gr(this),a=Yl(o,s.bind(n),r);return i(),a}function ec(e,t){const r=t.split(".");return()=>{let n=e;for(let o=0;o<r.length&&n;o++)n=n[r[o]];return n}}const jf=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${Dt(t)}Modifiers`]||e[`${Qt(t)}Modifiers`];function $f(e,t,...r){if(e.isUnmounted)return;const n=e.vnode.props||ye;let o=r;const s=t.startsWith("update:"),i=s&&jf(n,t.slice(7));i&&(i.trim&&(o=r.map(c=>Ce(c)?c.trim():c)),i.number&&(o=r.map(yu)));let a,l=n[a=Xn(t)]||n[a=Xn(Dt(t))];!l&&s&&(l=n[a=Xn(Qt(t))]),l&&gt(l,e,6,o);const u=n[a+"Once"];if(u){if(!e.emitted)e.emitted={};else if(e.emitted[a])return;e.emitted[a]=!0,gt(u,e,6,o)}}function tc(e,t,r=!1){const n=t.emitsCache,o=n.get(e);if(o!==void 0)return o;const s=e.emits;let i={},a=!1;if(!ee(e)){const l=u=>{const c=tc(u,t,!0);c&&(a=!0,Ge(i,c))};!r&&t.mixins.length&&t.mixins.forEach(l),e.extends&&l(e.extends),e.mixins&&e.mixins.forEach(l)}return!s&&!a?(_e(e)&&n.set(e,null),null):(X(s)?s.forEach(l=>i[l]=null):Ge(i,s),_e(e)&&n.set(e,i),i)}function kn(e,t){return!e||!bn(t)?!1:(t=t.slice(2).replace(/Once$/,""),fe(e,t[0].toLowerCase()+t.slice(1))||fe(e,Qt(t))||fe(e,t))}function bi(e){const{type:t,vnode:r,proxy:n,withProxy:o,propsOptions:[s],slots:i,attrs:a,emit:l,render:u,renderCache:c,props:f,data:h,setupState:d,ctx:g,inheritAttrs:w}=e,y=fn(e);let v,b;try{if(r.shapeFlag&4){const _=o||n,k=_;v=ht(u.call(k,_,c,f,d,h,g)),b=a}else{const _=t;v=ht(_.length>1?_(f,{attrs:a,slots:i,emit:l}):_(f,null)),b=t.props?a:Gf(a)}}catch(_){Tr.length=0,On(_,e,1),v=Se(Nt)}let R=v;if(b&&w!==!1){const _=Object.keys(b),{shapeFlag:k}=R;_.length&&k&7&&(s&&_.some(Ts)&&(b=Wf(b,s)),R=cr(R,b,!1,!0))}return r.dirs&&(R=cr(R,null,!1,!0),R.dirs=R.dirs?R.dirs.concat(r.dirs):r.dirs),r.transition&&$s(R,r.transition),v=R,fn(y),v}const Gf=e=>{let t;for(const r in e)(r==="class"||r==="style"||bn(r))&&((t||(t={}))[r]=e[r]);return t},Wf=(e,t)=>{const r={};for(const n in e)(!Ts(n)||!(n.slice(9)in t))&&(r[n]=e[n]);return r};function Kf(e,t,r){const{props:n,children:o,component:s}=e,{props:i,children:a,patchFlag:l}=t,u=s.emitsOptions;if(t.dirs||t.transition)return!0;if(r&&l>=0){if(l&1024)return!0;if(l&16)return n?Ai(n,i,u):!!i;if(l&8){const c=t.dynamicProps;for(let f=0;f<c.length;f++){const h=c[f];if(i[h]!==n[h]&&!kn(u,h))return!0}}}else return(o||a)&&(!a||!a.$stable)?!0:n===i?!1:n?i?Ai(n,i,u):!0:!!i;return!1}function Ai(e,t,r){const n=Object.keys(t);if(n.length!==Object.keys(e).length)return!0;for(let o=0;o<n.length;o++){const s=n[o];if(t[s]!==e[s]&&!kn(r,s))return!0}return!1}function Vf({vnode:e,parent:t},r){for(;t;){const n=t.subTree;if(n.suspense&&n.suspense.activeBranch===e&&(n.el=e.el),n===e)(e=t.vnode).el=r,t=t.parent;else break}}const rc=e=>e.__isSuspense;function zf(e,t){t&&t.pendingBranch?X(e)?t.effects.push(...e):t.effects.push(e):nf(e)}const Fe=Symbol.for("v-fgt"),Dn=Symbol.for("v-txt"),Nt=Symbol.for("v-cmt"),so=Symbol.for("v-stc"),Tr=[];let tt=null;function ie(e=!1){Tr.push(tt=e?null:[])}function Qf(){Tr.pop(),tt=Tr[Tr.length-1]||null}let Fr=1;function wi(e,t=!1){Fr+=e,e<0&&tt&&t&&(tt.hasOnce=!0)}function nc(e){return e.dynamicChildren=Fr>0?tt||rr:null,Qf(),Fr>0&&tt&&tt.push(e),e}function de(e,t,r,n,o,s){return nc($(e,t,r,n,o,s,!0))}function Br(e,t,r,n,o){return nc(Se(e,t,r,n,o,!0))}function Lr(e){return e?e.__v_isVNode===!0:!1}function br(e,t){return e.type===t.type&&e.key===t.key}const oc=({key:e})=>e??null,tn=({ref:e,ref_key:t,ref_for:r})=>(typeof e=="number"&&(e=""+e),e!=null?Ce(e)||Le(e)||ee(e)?{i:Be,r:e,k:t,f:!!r}:e:null);function $(e,t=null,r=null,n=0,o=null,s=e===Fe?0:1,i=!1,a=!1){const l={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&oc(t),ref:t&&tn(t),scopeId:Pn,slotScopeIds:null,children:r,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:s,patchFlag:n,dynamicProps:o,dynamicChildren:null,appContext:null,ctx:Be};return a?(Ws(l,r),s&128&&e.normalize(l)):r&&(l.shapeFlag|=Ce(r)?8:16),Fr>0&&!i&&tt&&(l.patchFlag>0||s&6)&&l.patchFlag!==32&&tt.push(l),l}const Se=Jf;function Jf(e,t=null,r=null,n=0,o=null,s=!1){if((!e||e===Af)&&(e=Nt),Lr(e)){const a=cr(e,t,!0);return r&&Ws(a,r),Fr>0&&!s&&tt&&(a.shapeFlag&6?tt[tt.indexOf(e)]=a:tt.push(a)),a.patchFlag=-2,a}if(ad(e)&&(e=e.__vccOpts),t){t=Xf(t);let{class:a,style:l}=t;a&&!Ce(a)&&(t.class=yt(a)),_e(l)&&(Us(l)&&!X(l)&&(l=Ge({},l)),t.style=En(l))}const i=Ce(e)?1:rc(e)?128:lf(e)?64:_e(e)?4:ee(e)?2:0;return $(e,t,r,n,o,i,s,!0)}function Xf(e){return e?Us(e)||Gl(e)?Ge({},e):e:null}function cr(e,t,r=!1,n=!1){const{props:o,ref:s,patchFlag:i,children:a,transition:l}=e,u=t?Zf(o||{},t):o,c={__v_isVNode:!0,__v_skip:!0,type:e.type,props:u,key:u&&oc(u),ref:t&&t.ref?r&&s?X(s)?s.concat(tn(t)):[s,tn(t)]:tn(t):s,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:a,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Fe?i===-1?16:i|16:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:l,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&cr(e.ssContent),ssFallback:e.ssFallback&&cr(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return l&&n&&$s(c,l.clone(c)),c}function sc(e=" ",t=0){return Se(Dn,null,e,t)}function It(e="",t=!1){return t?(ie(),Br(Nt,null,e)):Se(Nt,null,e)}function ht(e){return e==null||typeof e=="boolean"?Se(Nt):X(e)?Se(Fe,null,e.slice()):Lr(e)?Ct(e):Se(Dn,null,String(e))}function Ct(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:cr(e)}function Ws(e,t){let r=0;const{shapeFlag:n}=e;if(t==null)t=null;else if(X(t))r=16;else if(typeof t=="object")if(n&65){const o=t.default;o&&(o._c&&(o._d=!1),Ws(e,o()),o._c&&(o._d=!0));return}else{r=32;const o=t._;!o&&!Gl(t)?t._ctx=Be:o===3&&Be&&(Be.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else ee(t)?(t={default:t,_ctx:Be},r=32):(t=String(t),n&64?(r=16,t=[sc(t)]):r=8);e.children=t,e.shapeFlag|=r}function Zf(...e){const t={};for(let r=0;r<e.length;r++){const n=e[r];for(const o in n)if(o==="class")t.class!==n.class&&(t.class=yt([t.class,n.class]));else if(o==="style")t.style=En([t.style,n.style]);else if(bn(o)){const s=t[o],i=n[o];i&&s!==i&&!(X(s)&&s.includes(i))&&(t[o]=s?[].concat(s,i):i)}else o!==""&&(t[o]=n[o])}return t}function ft(e,t,r,n=null){gt(e,t,7,[r,n])}const Yf=Hl();let ed=0;function td(e,t,r){const n=e.type,o=(t?t.appContext:e.appContext)||Yf,s={uid:ed++,vnode:e,type:n,parent:t,appContext:o,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new Eu(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(o.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Kl(n,o),emitsOptions:tc(n,o),emit:null,emitted:null,propsDefaults:ye,inheritAttrs:n.inheritAttrs,ctx:ye,data:ye,props:ye,attrs:ye,slots:ye,refs:ye,setupState:ye,setupContext:null,suspense:r,suspenseId:r?r.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return s.ctx={_:s},s.root=t?t.root:s,s.emit=$f.bind(null,s),e.ce&&e.ce(s),s}let je=null;const rd=()=>je||Be;let mn,gs;{const e=Sn(),t=(r,n)=>{let o;return(o=e[r])||(o=e[r]=[]),o.push(n),s=>{o.length>1?o.forEach(i=>i(s)):o[0](s)}};mn=t("__VUE_INSTANCE_SETTERS__",r=>je=r),gs=t("__VUE_SSR_SETTERS__",r=>Ur=r)}const Gr=e=>{const t=je;return mn(e),e.scope.on(),()=>{e.scope.off(),mn(t)}},Si=()=>{je&&je.scope.off(),mn(null)};function ic(e){return e.vnode.shapeFlag&4}let Ur=!1;function nd(e,t=!1,r=!1){t&&gs(t);const{props:n,children:o}=e.vnode,s=ic(e);Cf(e,n,s,t),Df(e,o,r);const i=s?od(e,t):void 0;return t&&gs(!1),i}function od(e,t){const r=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,wf);const{setup:n}=r;if(n){Mt();const o=e.setupContext=n.length>1?id(e):null,s=Gr(e),i=$r(n,e,0,[e.props,o]),a=sl(i);if(Ft(),s(),(a||e.sp)&&!ir(e)&&Ml(e),a){if(i.then(Si,Si),t)return i.then(l=>{Ei(e,l)}).catch(l=>{On(l,e,0)});e.asyncDep=i}else Ei(e,i)}else ac(e)}function Ei(e,t,r){ee(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:_e(t)&&(e.setupState=Tl(t)),ac(e)}function ac(e,t,r){const n=e.type;e.render||(e.render=n.render||mt);{const o=Gr(e);Mt();try{Sf(e)}finally{Ft(),o()}}}const sd={get(e,t){return qe(e,"get",""),e[t]}};function id(e){const t=r=>{e.exposed=r||{}};return{attrs:new Proxy(e.attrs,sd),slots:e.slots,emit:e.emit,expose:t}}function Nn(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(Tl(Gu(e.exposed)),{get(t,r){if(r in t)return t[r];if(r in Cr)return Cr[r](e)},has(t,r){return r in t||r in Cr}})):e.proxy}function ad(e){return ee(e)&&"__vccOpts"in e}const ot=(e,t)=>Zu(e,t,Ur);function lc(e,t,r){const n=arguments.length;return n===2?_e(t)&&!X(t)?Lr(t)?Se(e,null,[t]):Se(e,t):Se(e,null,t):(n>3?r=Array.prototype.slice.call(arguments,2):n===3&&Lr(r)&&(r=[r]),Se(e,t,r))}const ld="3.5.13";/**
* @vue/runtime-dom v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let vs;const _i=typeof window<"u"&&window.trustedTypes;if(_i)try{vs=_i.createPolicy("vue",{createHTML:e=>e})}catch{}const cc=vs?e=>vs.createHTML(e):e=>e,cd="http://www.w3.org/2000/svg",ud="http://www.w3.org/1998/Math/MathML",At=typeof document<"u"?document:null,Ri=At&&At.createElement("template"),fd={insert:(e,t,r)=>{t.insertBefore(e,r||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,r,n)=>{const o=t==="svg"?At.createElementNS(cd,e):t==="mathml"?At.createElementNS(ud,e):r?At.createElement(e,{is:r}):At.createElement(e);return e==="select"&&n&&n.multiple!=null&&o.setAttribute("multiple",n.multiple),o},createText:e=>At.createTextNode(e),createComment:e=>At.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>At.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,r,n,o,s){const i=r?r.previousSibling:t.lastChild;if(o&&(o===s||o.nextSibling))for(;t.insertBefore(o.cloneNode(!0),r),!(o===s||!(o=o.nextSibling)););else{Ri.innerHTML=cc(n==="svg"?`<svg>${e}</svg>`:n==="mathml"?`<math>${e}</math>`:e);const a=Ri.content;if(n==="svg"||n==="mathml"){const l=a.firstChild;for(;l.firstChild;)a.appendChild(l.firstChild);a.removeChild(l)}t.insertBefore(a,r)}return[i?i.nextSibling:t.firstChild,r?r.previousSibling:t.lastChild]}},dd=Symbol("_vtc");function pd(e,t,r){const n=e[dd];n&&(t=(t?[t,...n]:[...n]).join(" ")),t==null?e.removeAttribute("class"):r?e.setAttribute("class",t):e.className=t}const yn=Symbol("_vod"),uc=Symbol("_vsh"),Ks={beforeMount(e,{value:t},{transition:r}){e[yn]=e.style.display==="none"?"":e.style.display,r&&t?r.beforeEnter(e):Ar(e,t)},mounted(e,{value:t},{transition:r}){r&&t&&r.enter(e)},updated(e,{value:t,oldValue:r},{transition:n}){!t!=!r&&(n?t?(n.beforeEnter(e),Ar(e,!0),n.enter(e)):n.leave(e,()=>{Ar(e,!1)}):Ar(e,t))},beforeUnmount(e,{value:t}){Ar(e,t)}};function Ar(e,t){e.style.display=t?e[yn]:"none",e[uc]=!t}const hd=Symbol(""),md=/(^|;)\s*display\s*:/;function yd(e,t,r){const n=e.style,o=Ce(r);let s=!1;if(r&&!o){if(t)if(Ce(t))for(const i of t.split(";")){const a=i.slice(0,i.indexOf(":")).trim();r[a]==null&&rn(n,a,"")}else for(const i in t)r[i]==null&&rn(n,i,"");for(const i in r)i==="display"&&(s=!0),rn(n,i,r[i])}else if(o){if(t!==r){const i=n[hd];i&&(r+=";"+i),n.cssText=r,s=md.test(r)}}else t&&e.removeAttribute("style");yn in e&&(e[yn]=s?n.display:"",e[uc]&&(n.display="none"))}const Oi=/\s*!important$/;function rn(e,t,r){if(X(r))r.forEach(n=>rn(e,t,n));else if(r==null&&(r=""),t.startsWith("--"))e.setProperty(t,r);else{const n=gd(e,t);Oi.test(r)?e.setProperty(Qt(n),r.replace(Oi,""),"important"):e[n]=r}}const xi=["Webkit","Moz","ms"],io={};function gd(e,t){const r=io[t];if(r)return r;let n=Dt(t);if(n!=="filter"&&n in e)return io[t]=n;n=ll(n);for(let o=0;o<xi.length;o++){const s=xi[o]+n;if(s in e)return io[t]=s}return t}const Pi="http://www.w3.org/1999/xlink";function Ci(e,t,r,n,o,s=Su(t)){n&&t.startsWith("xlink:")?r==null?e.removeAttributeNS(Pi,t.slice(6,t.length)):e.setAttributeNS(Pi,t,r):r==null||s&&!ul(r)?e.removeAttribute(t):e.setAttribute(t,s?"":Et(r)?String(r):r)}function Ti(e,t,r,n,o){if(t==="innerHTML"||t==="textContent"){r!=null&&(e[t]=t==="innerHTML"?cc(r):r);return}const s=e.tagName;if(t==="value"&&s!=="PROGRESS"&&!s.includes("-")){const a=s==="OPTION"?e.getAttribute("value")||"":e.value,l=r==null?e.type==="checkbox"?"on":"":String(r);(a!==l||!("_value"in e))&&(e.value=l),r==null&&e.removeAttribute(t),e._value=r;return}let i=!1;if(r===""||r==null){const a=typeof e[t];a==="boolean"?r=ul(r):r==null&&a==="string"?(r="",i=!0):a==="number"&&(r=0,i=!0)}try{e[t]=r}catch{}i&&e.removeAttribute(o||t)}function vd(e,t,r,n){e.addEventListener(t,r,n)}function bd(e,t,r,n){e.removeEventListener(t,r,n)}const Ii=Symbol("_vei");function Ad(e,t,r,n,o=null){const s=e[Ii]||(e[Ii]={}),i=s[t];if(n&&i)i.value=n;else{const[a,l]=wd(t);if(n){const u=s[t]=_d(n,o);vd(e,a,u,l)}else i&&(bd(e,a,i,l),s[t]=void 0)}}const ki=/(?:Once|Passive|Capture)$/;function wd(e){let t;if(ki.test(e)){t={};let n;for(;n=e.match(ki);)e=e.slice(0,e.length-n[0].length),t[n[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):Qt(e.slice(2)),t]}let ao=0;const Sd=Promise.resolve(),Ed=()=>ao||(Sd.then(()=>ao=0),ao=Date.now());function _d(e,t){const r=n=>{if(!n._vts)n._vts=Date.now();else if(n._vts<=r.attached)return;gt(Rd(n,r.value),t,5,[n])};return r.value=e,r.attached=Ed(),r}function Rd(e,t){if(X(t)){const r=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{r.call(e),e._stopped=!0},t.map(n=>o=>!o._stopped&&n&&n(o))}else return t}const Di=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,Od=(e,t,r,n,o,s)=>{const i=o==="svg";t==="class"?pd(e,n,i):t==="style"?yd(e,r,n):bn(t)?Ts(t)||Ad(e,t,r,n,s):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):xd(e,t,n,i))?(Ti(e,t,n),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&Ci(e,t,n,i,s,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!Ce(n))?Ti(e,Dt(t),n,s,t):(t==="true-value"?e._trueValue=n:t==="false-value"&&(e._falseValue=n),Ci(e,t,n,i))};function xd(e,t,r,n){if(n)return!!(t==="innerHTML"||t==="textContent"||t in e&&Di(t)&&ee(r));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const o=e.tagName;if(o==="IMG"||o==="VIDEO"||o==="CANVAS"||o==="SOURCE")return!1}return Di(t)&&Ce(r)?!1:t in e}const Pd=Ge({patchProp:Od},fd);let Ni;function fc(){return Ni||(Ni=Mf(Pd))}const lo=(...e)=>{fc().render(...e)},Cd=(...e)=>{const t=fc().createApp(...e),{mount:r}=t;return t.mount=n=>{const o=Id(n);if(!o)return;const s=t._component;!ee(s)&&!s.render&&!s.template&&(s.template=o.innerHTML),o.nodeType===1&&(o.textContent="");const i=r(o,!1,Td(o));return o instanceof Element&&(o.removeAttribute("v-cloak"),o.setAttribute("data-v-app","")),i},t};function Td(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function Id(e){return Ce(e)?document.querySelector(e):e}/*!
  * vue-router v4.5.0
  * (c) 2024 Eduardo San Martin Morote
  * @license MIT
  */const tr=typeof document<"u";function dc(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function kd(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&dc(e.default)}const ue=Object.assign;function co(e,t){const r={};for(const n in t){const o=t[n];r[n]=ct(o)?o.map(e):e(o)}return r}const Ir=()=>{},ct=Array.isArray,pc=/#/g,Dd=/&/g,Nd=/\//g,Md=/=/g,Fd=/\?/g,hc=/\+/g,Bd=/%5B/g,Ld=/%5D/g,mc=/%5E/g,Ud=/%60/g,yc=/%7B/g,qd=/%7C/g,gc=/%7D/g,Hd=/%20/g;function Vs(e){return encodeURI(""+e).replace(qd,"|").replace(Bd,"[").replace(Ld,"]")}function jd(e){return Vs(e).replace(yc,"{").replace(gc,"}").replace(mc,"^")}function bs(e){return Vs(e).replace(hc,"%2B").replace(Hd,"+").replace(pc,"%23").replace(Dd,"%26").replace(Ud,"`").replace(yc,"{").replace(gc,"}").replace(mc,"^")}function $d(e){return bs(e).replace(Md,"%3D")}function Gd(e){return Vs(e).replace(pc,"%23").replace(Fd,"%3F")}function Wd(e){return e==null?"":Gd(e).replace(Nd,"%2F")}function qr(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const Kd=/\/$/,Vd=e=>e.replace(Kd,"");function uo(e,t,r="/"){let n,o={},s="",i="";const a=t.indexOf("#");let l=t.indexOf("?");return a<l&&a>=0&&(l=-1),l>-1&&(n=t.slice(0,l),s=t.slice(l+1,a>-1?a:t.length),o=e(s)),a>-1&&(n=n||t.slice(0,a),i=t.slice(a,t.length)),n=Xd(n??t,r),{fullPath:n+(s&&"?")+s+i,path:n,query:o,hash:qr(i)}}function zd(e,t){const r=t.query?e(t.query):"";return t.path+(r&&"?")+r+(t.hash||"")}function Mi(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function Qd(e,t,r){const n=t.matched.length-1,o=r.matched.length-1;return n>-1&&n===o&&ur(t.matched[n],r.matched[o])&&vc(t.params,r.params)&&e(t.query)===e(r.query)&&t.hash===r.hash}function ur(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function vc(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const r in e)if(!Jd(e[r],t[r]))return!1;return!0}function Jd(e,t){return ct(e)?Fi(e,t):ct(t)?Fi(t,e):e===t}function Fi(e,t){return ct(t)?e.length===t.length&&e.every((r,n)=>r===t[n]):e.length===1&&e[0]===t}function Xd(e,t){if(e.startsWith("/"))return e;if(!e)return t;const r=t.split("/"),n=e.split("/"),o=n[n.length-1];(o===".."||o===".")&&n.push("");let s=r.length-1,i,a;for(i=0;i<n.length;i++)if(a=n[i],a!==".")if(a==="..")s>1&&s--;else break;return r.slice(0,s).join("/")+"/"+n.slice(i).join("/")}const xt={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var Hr;(function(e){e.pop="pop",e.push="push"})(Hr||(Hr={}));var kr;(function(e){e.back="back",e.forward="forward",e.unknown=""})(kr||(kr={}));function Zd(e){if(!e)if(tr){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),Vd(e)}const Yd=/^[^#]+#/;function ep(e,t){return e.replace(Yd,"#")+t}function tp(e,t){const r=document.documentElement.getBoundingClientRect(),n=e.getBoundingClientRect();return{behavior:t.behavior,left:n.left-r.left-(t.left||0),top:n.top-r.top-(t.top||0)}}const Mn=()=>({left:window.scrollX,top:window.scrollY});function rp(e){let t;if("el"in e){const r=e.el,n=typeof r=="string"&&r.startsWith("#"),o=typeof r=="string"?n?document.getElementById(r.slice(1)):document.querySelector(r):r;if(!o)return;t=tp(o,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function Bi(e,t){return(history.state?history.state.position-t:-1)+e}const As=new Map;function np(e,t){As.set(e,t)}function op(e){const t=As.get(e);return As.delete(e),t}let sp=()=>location.protocol+"//"+location.host;function bc(e,t){const{pathname:r,search:n,hash:o}=t,s=e.indexOf("#");if(s>-1){let a=o.includes(e.slice(s))?e.slice(s).length:1,l=o.slice(a);return l[0]!=="/"&&(l="/"+l),Mi(l,"")}return Mi(r,e)+n+o}function ip(e,t,r,n){let o=[],s=[],i=null;const a=({state:h})=>{const d=bc(e,location),g=r.value,w=t.value;let y=0;if(h){if(r.value=d,t.value=h,i&&i===g){i=null;return}y=w?h.position-w.position:0}else n(d);o.forEach(v=>{v(r.value,g,{delta:y,type:Hr.pop,direction:y?y>0?kr.forward:kr.back:kr.unknown})})};function l(){i=r.value}function u(h){o.push(h);const d=()=>{const g=o.indexOf(h);g>-1&&o.splice(g,1)};return s.push(d),d}function c(){const{history:h}=window;h.state&&h.replaceState(ue({},h.state,{scroll:Mn()}),"")}function f(){for(const h of s)h();s=[],window.removeEventListener("popstate",a),window.removeEventListener("beforeunload",c)}return window.addEventListener("popstate",a),window.addEventListener("beforeunload",c,{passive:!0}),{pauseListeners:l,listen:u,destroy:f}}function Li(e,t,r,n=!1,o=!1){return{back:e,current:t,forward:r,replaced:n,position:window.history.length,scroll:o?Mn():null}}function ap(e){const{history:t,location:r}=window,n={value:bc(e,r)},o={value:t.state};o.value||s(n.value,{back:null,current:n.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function s(l,u,c){const f=e.indexOf("#"),h=f>-1?(r.host&&document.querySelector("base")?e:e.slice(f))+l:sp()+e+l;try{t[c?"replaceState":"pushState"](u,"",h),o.value=u}catch(d){console.error(d),r[c?"replace":"assign"](h)}}function i(l,u){const c=ue({},t.state,Li(o.value.back,l,o.value.forward,!0),u,{position:o.value.position});s(l,c,!0),n.value=l}function a(l,u){const c=ue({},o.value,t.state,{forward:l,scroll:Mn()});s(c.current,c,!0);const f=ue({},Li(n.value,l,null),{position:c.position+1},u);s(l,f,!1),n.value=l}return{location:n,state:o,push:a,replace:i}}function lp(e){e=Zd(e);const t=ap(e),r=ip(e,t.state,t.location,t.replace);function n(s,i=!0){i||r.pauseListeners(),history.go(s)}const o=ue({location:"",base:e,go:n,createHref:ep.bind(null,e)},t,r);return Object.defineProperty(o,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(o,"state",{enumerable:!0,get:()=>t.state.value}),o}function cp(e){return e=location.host?e||location.pathname+location.search:"",e.includes("#")||(e+="#"),lp(e)}function up(e){return typeof e=="string"||e&&typeof e=="object"}function Ac(e){return typeof e=="string"||typeof e=="symbol"}const wc=Symbol("");var Ui;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(Ui||(Ui={}));function fr(e,t){return ue(new Error,{type:e,[wc]:!0},t)}function bt(e,t){return e instanceof Error&&wc in e&&(t==null||!!(e.type&t))}const qi="[^/]+?",fp={sensitive:!1,strict:!1,start:!0,end:!0},dp=/[.+*?^${}()[\]/\\]/g;function pp(e,t){const r=ue({},fp,t),n=[];let o=r.start?"^":"";const s=[];for(const u of e){const c=u.length?[]:[90];r.strict&&!u.length&&(o+="/");for(let f=0;f<u.length;f++){const h=u[f];let d=40+(r.sensitive?.25:0);if(h.type===0)f||(o+="/"),o+=h.value.replace(dp,"\\$&"),d+=40;else if(h.type===1){const{value:g,repeatable:w,optional:y,regexp:v}=h;s.push({name:g,repeatable:w,optional:y});const b=v||qi;if(b!==qi){d+=10;try{new RegExp(`(${b})`)}catch(_){throw new Error(`Invalid custom RegExp for param "${g}" (${b}): `+_.message)}}let R=w?`((?:${b})(?:/(?:${b}))*)`:`(${b})`;f||(R=y&&u.length<2?`(?:/${R})`:"/"+R),y&&(R+="?"),o+=R,d+=20,y&&(d+=-8),w&&(d+=-20),b===".*"&&(d+=-50)}c.push(d)}n.push(c)}if(r.strict&&r.end){const u=n.length-1;n[u][n[u].length-1]+=.7000000000000001}r.strict||(o+="/?"),r.end?o+="$":r.strict&&!o.endsWith("/")&&(o+="(?:/|$)");const i=new RegExp(o,r.sensitive?"":"i");function a(u){const c=u.match(i),f={};if(!c)return null;for(let h=1;h<c.length;h++){const d=c[h]||"",g=s[h-1];f[g.name]=d&&g.repeatable?d.split("/"):d}return f}function l(u){let c="",f=!1;for(const h of e){(!f||!c.endsWith("/"))&&(c+="/"),f=!1;for(const d of h)if(d.type===0)c+=d.value;else if(d.type===1){const{value:g,repeatable:w,optional:y}=d,v=g in u?u[g]:"";if(ct(v)&&!w)throw new Error(`Provided param "${g}" is an array but it is not repeatable (* or + modifiers)`);const b=ct(v)?v.join("/"):v;if(!b)if(y)h.length<2&&(c.endsWith("/")?c=c.slice(0,-1):f=!0);else throw new Error(`Missing required param "${g}"`);c+=b}}return c||"/"}return{re:i,score:n,keys:s,parse:a,stringify:l}}function hp(e,t){let r=0;for(;r<e.length&&r<t.length;){const n=t[r]-e[r];if(n)return n;r++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function Sc(e,t){let r=0;const n=e.score,o=t.score;for(;r<n.length&&r<o.length;){const s=hp(n[r],o[r]);if(s)return s;r++}if(Math.abs(o.length-n.length)===1){if(Hi(n))return 1;if(Hi(o))return-1}return o.length-n.length}function Hi(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const mp={type:0,value:""},yp=/[a-zA-Z0-9_]/;function gp(e){if(!e)return[[]];if(e==="/")return[[mp]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(d){throw new Error(`ERR (${r})/"${u}": ${d}`)}let r=0,n=r;const o=[];let s;function i(){s&&o.push(s),s=[]}let a=0,l,u="",c="";function f(){u&&(r===0?s.push({type:0,value:u}):r===1||r===2||r===3?(s.length>1&&(l==="*"||l==="+")&&t(`A repeatable param (${u}) must be alone in its segment. eg: '/:ids+.`),s.push({type:1,value:u,regexp:c,repeatable:l==="*"||l==="+",optional:l==="*"||l==="?"})):t("Invalid state to consume buffer"),u="")}function h(){u+=l}for(;a<e.length;){if(l=e[a++],l==="\\"&&r!==2){n=r,r=4;continue}switch(r){case 0:l==="/"?(u&&f(),i()):l===":"?(f(),r=1):h();break;case 4:h(),r=n;break;case 1:l==="("?r=2:yp.test(l)?h():(f(),r=0,l!=="*"&&l!=="?"&&l!=="+"&&a--);break;case 2:l===")"?c[c.length-1]=="\\"?c=c.slice(0,-1)+l:r=3:c+=l;break;case 3:f(),r=0,l!=="*"&&l!=="?"&&l!=="+"&&a--,c="";break;default:t("Unknown state");break}}return r===2&&t(`Unfinished custom RegExp for param "${u}"`),f(),i(),o}function vp(e,t,r){const n=pp(gp(e.path),r),o=ue(n,{record:e,parent:t,children:[],alias:[]});return t&&!o.record.aliasOf==!t.record.aliasOf&&t.children.push(o),o}function bp(e,t){const r=[],n=new Map;t=Wi({strict:!1,end:!0,sensitive:!1},t);function o(f){return n.get(f)}function s(f,h,d){const g=!d,w=$i(f);w.aliasOf=d&&d.record;const y=Wi(t,f),v=[w];if("alias"in f){const _=typeof f.alias=="string"?[f.alias]:f.alias;for(const k of _)v.push($i(ue({},w,{components:d?d.record.components:w.components,path:k,aliasOf:d?d.record:w})))}let b,R;for(const _ of v){const{path:k}=_;if(h&&k[0]!=="/"){const U=h.record.path,B=U[U.length-1]==="/"?"":"/";_.path=h.record.path+(k&&B+k)}if(b=vp(_,h,y),d?d.alias.push(b):(R=R||b,R!==b&&R.alias.push(b),g&&f.name&&!Gi(b)&&i(f.name)),Ec(b)&&l(b),w.children){const U=w.children;for(let B=0;B<U.length;B++)s(U[B],b,d&&d.children[B])}d=d||b}return R?()=>{i(R)}:Ir}function i(f){if(Ac(f)){const h=n.get(f);h&&(n.delete(f),r.splice(r.indexOf(h),1),h.children.forEach(i),h.alias.forEach(i))}else{const h=r.indexOf(f);h>-1&&(r.splice(h,1),f.record.name&&n.delete(f.record.name),f.children.forEach(i),f.alias.forEach(i))}}function a(){return r}function l(f){const h=Sp(f,r);r.splice(h,0,f),f.record.name&&!Gi(f)&&n.set(f.record.name,f)}function u(f,h){let d,g={},w,y;if("name"in f&&f.name){if(d=n.get(f.name),!d)throw fr(1,{location:f});y=d.record.name,g=ue(ji(h.params,d.keys.filter(R=>!R.optional).concat(d.parent?d.parent.keys.filter(R=>R.optional):[]).map(R=>R.name)),f.params&&ji(f.params,d.keys.map(R=>R.name))),w=d.stringify(g)}else if(f.path!=null)w=f.path,d=r.find(R=>R.re.test(w)),d&&(g=d.parse(w),y=d.record.name);else{if(d=h.name?n.get(h.name):r.find(R=>R.re.test(h.path)),!d)throw fr(1,{location:f,currentLocation:h});y=d.record.name,g=ue({},h.params,f.params),w=d.stringify(g)}const v=[];let b=d;for(;b;)v.unshift(b.record),b=b.parent;return{name:y,path:w,params:g,matched:v,meta:wp(v)}}e.forEach(f=>s(f));function c(){r.length=0,n.clear()}return{addRoute:s,resolve:u,removeRoute:i,clearRoutes:c,getRoutes:a,getRecordMatcher:o}}function ji(e,t){const r={};for(const n of t)n in e&&(r[n]=e[n]);return r}function $i(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:Ap(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function Ap(e){const t={},r=e.props||!1;if("component"in e)t.default=r;else for(const n in e.components)t[n]=typeof r=="object"?r[n]:r;return t}function Gi(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function wp(e){return e.reduce((t,r)=>ue(t,r.meta),{})}function Wi(e,t){const r={};for(const n in e)r[n]=n in t?t[n]:e[n];return r}function Sp(e,t){let r=0,n=t.length;for(;r!==n;){const s=r+n>>1;Sc(e,t[s])<0?n=s:r=s+1}const o=Ep(e);return o&&(n=t.lastIndexOf(o,n-1)),n}function Ep(e){let t=e;for(;t=t.parent;)if(Ec(t)&&Sc(e,t)===0)return t}function Ec({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function _p(e){const t={};if(e===""||e==="?")return t;const n=(e[0]==="?"?e.slice(1):e).split("&");for(let o=0;o<n.length;++o){const s=n[o].replace(hc," "),i=s.indexOf("="),a=qr(i<0?s:s.slice(0,i)),l=i<0?null:qr(s.slice(i+1));if(a in t){let u=t[a];ct(u)||(u=t[a]=[u]),u.push(l)}else t[a]=l}return t}function Ki(e){let t="";for(let r in e){const n=e[r];if(r=$d(r),n==null){n!==void 0&&(t+=(t.length?"&":"")+r);continue}(ct(n)?n.map(s=>s&&bs(s)):[n&&bs(n)]).forEach(s=>{s!==void 0&&(t+=(t.length?"&":"")+r,s!=null&&(t+="="+s))})}return t}function Rp(e){const t={};for(const r in e){const n=e[r];n!==void 0&&(t[r]=ct(n)?n.map(o=>o==null?null:""+o):n==null?n:""+n)}return t}const Op=Symbol(""),Vi=Symbol(""),zs=Symbol(""),_c=Symbol(""),ws=Symbol("");function wr(){let e=[];function t(n){return e.push(n),()=>{const o=e.indexOf(n);o>-1&&e.splice(o,1)}}function r(){e=[]}return{add:t,list:()=>e.slice(),reset:r}}function Tt(e,t,r,n,o,s=i=>i()){const i=n&&(n.enterCallbacks[o]=n.enterCallbacks[o]||[]);return()=>new Promise((a,l)=>{const u=h=>{h===!1?l(fr(4,{from:r,to:t})):h instanceof Error?l(h):up(h)?l(fr(2,{from:t,to:h})):(i&&n.enterCallbacks[o]===i&&typeof h=="function"&&i.push(h),a())},c=s(()=>e.call(n&&n.instances[o],t,r,u));let f=Promise.resolve(c);e.length<3&&(f=f.then(u)),f.catch(h=>l(h))})}function fo(e,t,r,n,o=s=>s()){const s=[];for(const i of e)for(const a in i.components){let l=i.components[a];if(!(t!=="beforeRouteEnter"&&!i.instances[a]))if(dc(l)){const c=(l.__vccOpts||l)[t];c&&s.push(Tt(c,r,n,i,a,o))}else{let u=l();s.push(()=>u.then(c=>{if(!c)throw new Error(`Couldn't resolve component "${a}" at "${i.path}"`);const f=kd(c)?c.default:c;i.mods[a]=c,i.components[a]=f;const d=(f.__vccOpts||f)[t];return d&&Tt(d,r,n,i,a,o)()}))}}return s}function zi(e){const t=Ie(zs),r=Ie(_c),n=ot(()=>{const l=ne(e.to);return t.resolve(l)}),o=ot(()=>{const{matched:l}=n.value,{length:u}=l,c=l[u-1],f=r.matched;if(!c||!f.length)return-1;const h=f.findIndex(ur.bind(null,c));if(h>-1)return h;const d=Qi(l[u-2]);return u>1&&Qi(c)===d&&f[f.length-1].path!==d?f.findIndex(ur.bind(null,l[u-2])):h}),s=ot(()=>o.value>-1&&Ip(r.params,n.value.params)),i=ot(()=>o.value>-1&&o.value===r.matched.length-1&&vc(r.params,n.value.params));function a(l={}){if(Tp(l)){const u=t[ne(e.replace)?"replace":"push"](ne(e.to)).catch(Ir);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>u),u}return Promise.resolve()}return{route:n,href:ot(()=>n.value.href),isActive:s,isExactActive:i,navigate:a}}function xp(e){return e.length===1?e[0]:e}const Pp=Cn({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"}},useLink:zi,setup(e,{slots:t}){const r=Rn(zi(e)),{options:n}=Ie(zs),o=ot(()=>({[Ji(e.activeClass,n.linkActiveClass,"router-link-active")]:r.isActive,[Ji(e.exactActiveClass,n.linkExactActiveClass,"router-link-exact-active")]:r.isExactActive}));return()=>{const s=t.default&&xp(t.default(r));return e.custom?s:lc("a",{"aria-current":r.isExactActive?e.ariaCurrentValue:null,href:r.href,onClick:r.navigate,class:o.value},s)}}}),Cp=Pp;function Tp(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function Ip(e,t){for(const r in t){const n=t[r],o=e[r];if(typeof n=="string"){if(n!==o)return!1}else if(!ct(o)||o.length!==n.length||n.some((s,i)=>s!==o[i]))return!1}return!0}function Qi(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const Ji=(e,t,r)=>e??t??r,kp=Cn({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:r}){const n=Ie(ws),o=ot(()=>e.route||n.value),s=Ie(Vi,0),i=ot(()=>{let u=ne(s);const{matched:c}=o.value;let f;for(;(f=c[u])&&!f.components;)u++;return u}),a=ot(()=>o.value.matched[i.value]);lr(Vi,ot(()=>i.value+1)),lr(Op,a),lr(ws,o);const l=xe();return lt(()=>[l.value,a.value,e.name],([u,c,f],[h,d,g])=>{c&&(c.instances[f]=u,d&&d!==c&&u&&u===h&&(c.leaveGuards.size||(c.leaveGuards=d.leaveGuards),c.updateGuards.size||(c.updateGuards=d.updateGuards))),u&&c&&(!d||!ur(c,d)||!h)&&(c.enterCallbacks[f]||[]).forEach(w=>w(u))},{flush:"post"}),()=>{const u=o.value,c=e.name,f=a.value,h=f&&f.components[c];if(!h)return Xi(r.default,{Component:h,route:u});const d=f.props[c],g=d?d===!0?u.params:typeof d=="function"?d(u):d:null,y=lc(h,ue({},g,t,{onVnodeUnmounted:v=>{v.component.isUnmounted&&(f.instances[c]=null)},ref:l}));return Xi(r.default,{Component:y,route:u})||y}}});function Xi(e,t){if(!e)return null;const r=e(t);return r.length===1?r[0]:r}const Rc=kp;function Dp(e){const t=bp(e.routes,e),r=e.parseQuery||_p,n=e.stringifyQuery||Ki,o=e.history,s=wr(),i=wr(),a=wr(),l=Wu(xt);let u=xt;tr&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const c=co.bind(null,T=>""+T),f=co.bind(null,Wd),h=co.bind(null,qr);function d(T,H){let D,N;return Ac(T)?(D=t.getRecordMatcher(T),N=H):N=T,t.addRoute(N,D)}function g(T){const H=t.getRecordMatcher(T);H&&t.removeRoute(H)}function w(){return t.getRoutes().map(T=>T.record)}function y(T){return!!t.getRecordMatcher(T)}function v(T,H){if(H=ue({},H||l.value),typeof T=="string"){const A=uo(r,T,H.path),x=t.resolve({path:A.path},H),C=o.createHref(A.fullPath);return ue(A,x,{params:h(x.params),hash:qr(A.hash),redirectedFrom:void 0,href:C})}let D;if(T.path!=null)D=ue({},T,{path:uo(r,T.path,H.path).path});else{const A=ue({},T.params);for(const x in A)A[x]==null&&delete A[x];D=ue({},T,{params:f(A)}),H.params=f(H.params)}const N=t.resolve(D,H),Q=T.hash||"";N.params=c(h(N.params));const p=zd(n,ue({},T,{hash:jd(Q),path:N.path})),m=o.createHref(p);return ue({fullPath:p,hash:Q,query:n===Ki?Rp(T.query):T.query||{}},N,{redirectedFrom:void 0,href:m})}function b(T){return typeof T=="string"?uo(r,T,l.value.path):ue({},T)}function R(T,H){if(u!==T)return fr(8,{from:H,to:T})}function _(T){return B(T)}function k(T){return _(ue(b(T),{replace:!0}))}function U(T){const H=T.matched[T.matched.length-1];if(H&&H.redirect){const{redirect:D}=H;let N=typeof D=="function"?D(T):D;return typeof N=="string"&&(N=N.includes("?")||N.includes("#")?N=b(N):{path:N},N.params={}),ue({query:T.query,hash:T.hash,params:N.path!=null?{}:T.params},N)}}function B(T,H){const D=u=v(T),N=l.value,Q=T.state,p=T.force,m=T.replace===!0,A=U(D);if(A)return B(ue(b(A),{state:typeof A=="object"?ue({},Q,A.state):Q,force:p,replace:m}),H||D);const x=D;x.redirectedFrom=H;let C;return!p&&Qd(n,N,D)&&(C=fr(16,{to:x,from:N}),De(N,N,!0,!1)),(C?Promise.resolve(C):z(x,N)).catch(P=>bt(P)?bt(P,2)?P:ke(P):j(P,x,N)).then(P=>{if(P){if(bt(P,2))return B(ue({replace:m},b(P.to),{state:typeof P.to=="object"?ue({},Q,P.to.state):Q,force:p}),H||x)}else P=K(x,N,!0,m,Q);return se(x,N,P),P})}function W(T,H){const D=R(T,H);return D?Promise.reject(D):Promise.resolve()}function Z(T){const H=Ke.values().next().value;return H&&typeof H.runWithContext=="function"?H.runWithContext(T):T()}function z(T,H){let D;const[N,Q,p]=Np(T,H);D=fo(N.reverse(),"beforeRouteLeave",T,H);for(const A of N)A.leaveGuards.forEach(x=>{D.push(Tt(x,T,H))});const m=W.bind(null,T,H);return D.push(m),Ae(D).then(()=>{D=[];for(const A of s.list())D.push(Tt(A,T,H));return D.push(m),Ae(D)}).then(()=>{D=fo(Q,"beforeRouteUpdate",T,H);for(const A of Q)A.updateGuards.forEach(x=>{D.push(Tt(x,T,H))});return D.push(m),Ae(D)}).then(()=>{D=[];for(const A of p)if(A.beforeEnter)if(ct(A.beforeEnter))for(const x of A.beforeEnter)D.push(Tt(x,T,H));else D.push(Tt(A.beforeEnter,T,H));return D.push(m),Ae(D)}).then(()=>(T.matched.forEach(A=>A.enterCallbacks={}),D=fo(p,"beforeRouteEnter",T,H,Z),D.push(m),Ae(D))).then(()=>{D=[];for(const A of i.list())D.push(Tt(A,T,H));return D.push(m),Ae(D)}).catch(A=>bt(A,8)?A:Promise.reject(A))}function se(T,H,D){a.list().forEach(N=>Z(()=>N(T,H,D)))}function K(T,H,D,N,Q){const p=R(T,H);if(p)return p;const m=H===xt,A=tr?history.state:{};D&&(N||m?o.replace(T.fullPath,ue({scroll:m&&A&&A.scroll},Q)):o.push(T.fullPath,Q)),l.value=T,De(T,H,D,m),ke()}let me;function ge(){me||(me=o.listen((T,H,D)=>{if(!Qe.listening)return;const N=v(T),Q=U(N);if(Q){B(ue(Q,{replace:!0,force:!0}),N).catch(Ir);return}u=N;const p=l.value;tr&&np(Bi(p.fullPath,D.delta),Mn()),z(N,p).catch(m=>bt(m,12)?m:bt(m,2)?(B(ue(b(m.to),{force:!0}),N).then(A=>{bt(A,20)&&!D.delta&&D.type===Hr.pop&&o.go(-1,!1)}).catch(Ir),Promise.reject()):(D.delta&&o.go(-D.delta,!1),j(m,N,p))).then(m=>{m=m||K(N,p,!1),m&&(D.delta&&!bt(m,8)?o.go(-D.delta,!1):D.type===Hr.pop&&bt(m,20)&&o.go(-1,!1)),se(N,p,m)}).catch(Ir)}))}let Pe=wr(),he=wr(),re;function j(T,H,D){ke(T);const N=he.list();return N.length?N.forEach(Q=>Q(T,H,D)):console.error(T),Promise.reject(T)}function Re(){return re&&l.value!==xt?Promise.resolve():new Promise((T,H)=>{Pe.add([T,H])})}function ke(T){return re||(re=!T,ge(),Pe.list().forEach(([H,D])=>T?D(T):H()),Pe.reset()),T}function De(T,H,D,N){const{scrollBehavior:Q}=e;if(!tr||!Q)return Promise.resolve();const p=!D&&op(Bi(T.fullPath,0))||(N||!D)&&history.state&&history.state.scroll||null;return xn().then(()=>Q(T,H,p)).then(m=>m&&rp(m)).catch(m=>j(m,T,H))}const Oe=T=>o.go(T);let We;const Ke=new Set,Qe={currentRoute:l,listening:!0,addRoute:d,removeRoute:g,clearRoutes:t.clearRoutes,hasRoute:y,getRoutes:w,resolve:v,options:e,push:_,replace:k,go:Oe,back:()=>Oe(-1),forward:()=>Oe(1),beforeEach:s.add,beforeResolve:i.add,afterEach:a.add,onError:he.add,isReady:Re,install(T){const H=this;T.component("RouterLink",Cp),T.component("RouterView",Rc),T.config.globalProperties.$router=H,Object.defineProperty(T.config.globalProperties,"$route",{enumerable:!0,get:()=>ne(l)}),tr&&!We&&l.value===xt&&(We=!0,_(o.location).catch(Q=>{}));const D={};for(const Q in xt)Object.defineProperty(D,Q,{get:()=>l.value[Q],enumerable:!0});T.provide(zs,H),T.provide(_c,xl(D)),T.provide(ws,l);const N=T.unmount;Ke.add(T),T.unmount=function(){Ke.delete(T),Ke.size<1&&(u=xt,me&&me(),me=null,l.value=xt,We=!1,re=!1),N()}}};function Ae(T){return T.reduce((H,D)=>H.then(()=>Z(D)),Promise.resolve())}return Qe}function Np(e,t){const r=[],n=[],o=[],s=Math.max(t.matched.length,e.matched.length);for(let i=0;i<s;i++){const a=t.matched[i];a&&(e.matched.find(u=>ur(u,a))?n.push(a):r.push(a));const l=e.matched[i];l&&(t.matched.find(u=>ur(u,l))||o.push(l))}return[r,n,o]}const Mp={class:"wrapper"},Fp={__name:"App",setup(e){return(t,r)=>(ie(),de("div",null,[r[0]||(r[0]=$("header",null,null,-1)),$("div",Mp,[Se(ne(Rc))]),r[1]||(r[1]=$("footer",null,null,-1))]))}},Bp="https://static.kedou.com/marketingWeb/static/1.0.0/gameGuess/assets/icon-share-Cvew7QHn.png";var Zi=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function Lp(e){if(e.__esModule)return e;var t=e.default;if(typeof t=="function"){var r=function n(){return this instanceof n?Reflect.construct(t,arguments,this.constructor):t.apply(this,arguments)};r.prototype=t.prototype}else r={};return Object.defineProperty(r,"__esModule",{value:!0}),Object.keys(e).forEach(function(n){var o=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(r,n,o.get?o:{enumerable:!0,get:function(){return e[n]}})}),r}var po,Yi;function hr(){return Yi||(Yi=1,po=TypeError),po}const Up={},qp=Object.freeze(Object.defineProperty({__proto__:null,default:Up},Symbol.toStringTag,{value:"Module"})),Hp=Lp(qp);var ho,ea;function Fn(){if(ea)return ho;ea=1;var e=typeof Map=="function"&&Map.prototype,t=Object.getOwnPropertyDescriptor&&e?Object.getOwnPropertyDescriptor(Map.prototype,"size"):null,r=e&&t&&typeof t.get=="function"?t.get:null,n=e&&Map.prototype.forEach,o=typeof Set=="function"&&Set.prototype,s=Object.getOwnPropertyDescriptor&&o?Object.getOwnPropertyDescriptor(Set.prototype,"size"):null,i=o&&s&&typeof s.get=="function"?s.get:null,a=o&&Set.prototype.forEach,l=typeof WeakMap=="function"&&WeakMap.prototype,u=l?WeakMap.prototype.has:null,c=typeof WeakSet=="function"&&WeakSet.prototype,f=c?WeakSet.prototype.has:null,h=typeof WeakRef=="function"&&WeakRef.prototype,d=h?WeakRef.prototype.deref:null,g=Boolean.prototype.valueOf,w=Object.prototype.toString,y=Function.prototype.toString,v=String.prototype.match,b=String.prototype.slice,R=String.prototype.replace,_=String.prototype.toUpperCase,k=String.prototype.toLowerCase,U=RegExp.prototype.test,B=Array.prototype.concat,W=Array.prototype.join,Z=Array.prototype.slice,z=Math.floor,se=typeof BigInt=="function"?BigInt.prototype.valueOf:null,K=Object.getOwnPropertySymbols,me=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?Symbol.prototype.toString:null,ge=typeof Symbol=="function"&&typeof Symbol.iterator=="object",Pe=typeof Symbol=="function"&&Symbol.toStringTag&&(typeof Symbol.toStringTag===ge||!0)?Symbol.toStringTag:null,he=Object.prototype.propertyIsEnumerable,re=(typeof Reflect=="function"?Reflect.getPrototypeOf:Object.getPrototypeOf)||([].__proto__===Array.prototype?function(S){return S.__proto__}:null);function j(S,E){if(S===1/0||S===-1/0||S!==S||S&&S>-1e3&&S<1e3||U.call(/e/,E))return E;var le=/[0-9](?=(?:[0-9]{3})+(?![0-9]))/g;if(typeof S=="number"){var ve=S<0?-z(-S):z(S);if(ve!==S){var we=String(ve),te=b.call(E,we.length+1);return R.call(we,le,"$&_")+"."+R.call(R.call(te,/([0-9]{3})/g,"$&_"),/_$/,"")}}return R.call(E,le,"$&_")}var Re=Hp,ke=Re.custom,De=A(ke)?ke:null,Oe={__proto__:null,double:'"',single:"'"},We={__proto__:null,double:/(["\\])/g,single:/(['\\])/g};ho=function S(E,le,ve,we){var te=le||{};if(P(te,"quoteStyle")&&!P(Oe,te.quoteStyle))throw new TypeError('option "quoteStyle" must be "single" or "double"');if(P(te,"maxStringLength")&&(typeof te.maxStringLength=="number"?te.maxStringLength<0&&te.maxStringLength!==1/0:te.maxStringLength!==null))throw new TypeError('option "maxStringLength", if provided, must be a positive integer, Infinity, or `null`');var Rt=P(te,"customInspect")?te.customInspect:!0;if(typeof Rt!="boolean"&&Rt!=="symbol")throw new TypeError("option \"customInspect\", if provided, must be `true`, `false`, or `'symbol'`");if(P(te,"indent")&&te.indent!==null&&te.indent!=="	"&&!(parseInt(te.indent,10)===te.indent&&te.indent>0))throw new TypeError('option "indent" must be "\\t", an integer > 0, or `null`');if(P(te,"numericSeparator")&&typeof te.numericSeparator!="boolean")throw new TypeError('option "numericSeparator", if provided, must be `true` or `false`');var Ut=te.numericSeparator;if(typeof E>"u")return"undefined";if(E===null)return"null";if(typeof E=="boolean")return E?"true":"false";if(typeof E=="string")return pe(E,te);if(typeof E=="number"){if(E===0)return 1/0/E>0?"0":"-0";var Ze=String(E);return Ut?j(E,Ze):Ze}if(typeof E=="bigint"){var Ot=String(E)+"n";return Ut?j(E,Ot):Ot}var Gn=typeof te.depth>"u"?5:te.depth;if(typeof ve>"u"&&(ve=0),ve>=Gn&&Gn>0&&typeof E=="object")return T(E)?"[Array]":"[Object]";var Xt=Lt(te,ve);if(typeof we>"u")we=[];else if(M(we,E)>=0)return"[Circular]";function it(Zt,Jr,uu){if(Jr&&(we=Z.call(we),we.push(Jr)),uu){var li={depth:te.depth};return P(te,"quoteStyle")&&(li.quoteStyle=te.quoteStyle),S(Zt,li,ve+1,we)}return S(Zt,te,ve+1,we)}if(typeof E=="function"&&!D(E)){var ti=L(E),ri=Me(E,it);return"[Function"+(ti?": "+ti:" (anonymous)")+"]"+(ri.length>0?" { "+W.call(ri,", ")+" }":"")}if(A(E)){var ni=ge?R.call(String(E),/^(Symbol\(.*\))_[^)]*$/,"$1"):me.call(E);return typeof E=="object"&&!ge?Te(ni):ni}if(oe(E)){for(var gr="<"+k.call(String(E.nodeName)),Wn=E.attributes||[],Qr=0;Qr<Wn.length;Qr++)gr+=" "+Wn[Qr].name+"="+Ke(Qe(Wn[Qr].value),"double",te);return gr+=">",E.childNodes&&E.childNodes.length&&(gr+="..."),gr+="</"+k.call(String(E.nodeName))+">",gr}if(T(E)){if(E.length===0)return"[]";var Kn=Me(E,it);return Xt&&!nt(Kn)?"["+Jt(Kn,Xt)+"]":"[ "+W.call(Kn,", ")+" ]"}if(N(E)){var Vn=Me(E,it);return!("cause"in Error.prototype)&&"cause"in E&&!he.call(E,"cause")?"{ ["+String(E)+"] "+W.call(B.call("[cause]: "+it(E.cause),Vn),", ")+" }":Vn.length===0?"["+String(E)+"]":"{ ["+String(E)+"] "+W.call(Vn,", ")+" }"}if(typeof E=="object"&&Rt){if(De&&typeof E[De]=="function"&&Re)return Re(E,{depth:Gn-ve});if(Rt!=="symbol"&&typeof E.inspect=="function")return E.inspect()}if(I(E)){var oi=[];return n&&n.call(E,function(Zt,Jr){oi.push(it(Jr,E,!0)+" => "+it(Zt,E))}),Xe("Map",r.call(E),oi,Xt)}if(G(E)){var si=[];return a&&a.call(E,function(Zt){si.push(it(Zt,E))}),Xe("Set",i.call(E),si,Xt)}if(V(E))return Ne("WeakMap");if(J(E))return Ne("WeakSet");if(q(E))return Ne("WeakRef");if(p(E))return Te(it(Number(E)));if(x(E))return Te(it(se.call(E)));if(m(E))return Te(g.call(E));if(Q(E))return Te(it(String(E)));if(typeof window<"u"&&E===window)return"{ [object Window] }";if(typeof globalThis<"u"&&E===globalThis||typeof Zi<"u"&&E===Zi)return"{ [object globalThis] }";if(!H(E)&&!D(E)){var zn=Me(E,it),ii=re?re(E)===Object.prototype:E instanceof Object||E.constructor===Object,Qn=E instanceof Object?"":"null prototype",ai=!ii&&Pe&&Object(E)===E&&Pe in E?b.call(F(E),8,-1):Qn?"Object":"",cu=ii||typeof E.constructor!="function"?"":E.constructor.name?E.constructor.name+" ":"",Jn=cu+(ai||Qn?"["+W.call(B.call([],ai||[],Qn||[]),": ")+"] ":"");return zn.length===0?Jn+"{}":Xt?Jn+"{"+Jt(zn,Xt)+"}":Jn+"{ "+W.call(zn,", ")+" }"}return String(E)};function Ke(S,E,le){var ve=le.quoteStyle||E,we=Oe[ve];return we+S+we}function Qe(S){return R.call(String(S),/"/g,"&quot;")}function Ae(S){return!Pe||!(typeof S=="object"&&(Pe in S||typeof S[Pe]<"u"))}function T(S){return F(S)==="[object Array]"&&Ae(S)}function H(S){return F(S)==="[object Date]"&&Ae(S)}function D(S){return F(S)==="[object RegExp]"&&Ae(S)}function N(S){return F(S)==="[object Error]"&&Ae(S)}function Q(S){return F(S)==="[object String]"&&Ae(S)}function p(S){return F(S)==="[object Number]"&&Ae(S)}function m(S){return F(S)==="[object Boolean]"&&Ae(S)}function A(S){if(ge)return S&&typeof S=="object"&&S instanceof Symbol;if(typeof S=="symbol")return!0;if(!S||typeof S!="object"||!me)return!1;try{return me.call(S),!0}catch{}return!1}function x(S){if(!S||typeof S!="object"||!se)return!1;try{return se.call(S),!0}catch{}return!1}var C=Object.prototype.hasOwnProperty||function(S){return S in this};function P(S,E){return C.call(S,E)}function F(S){return w.call(S)}function L(S){if(S.name)return S.name;var E=v.call(y.call(S),/^function\s*([\w$]+)/);return E?E[1]:null}function M(S,E){if(S.indexOf)return S.indexOf(E);for(var le=0,ve=S.length;le<ve;le++)if(S[le]===E)return le;return-1}function I(S){if(!r||!S||typeof S!="object")return!1;try{r.call(S);try{i.call(S)}catch{return!0}return S instanceof Map}catch{}return!1}function V(S){if(!u||!S||typeof S!="object")return!1;try{u.call(S,u);try{f.call(S,f)}catch{return!0}return S instanceof WeakMap}catch{}return!1}function q(S){if(!d||!S||typeof S!="object")return!1;try{return d.call(S),!0}catch{}return!1}function G(S){if(!i||!S||typeof S!="object")return!1;try{i.call(S);try{r.call(S)}catch{return!0}return S instanceof Set}catch{}return!1}function J(S){if(!f||!S||typeof S!="object")return!1;try{f.call(S,f);try{u.call(S,u)}catch{return!0}return S instanceof WeakSet}catch{}return!1}function oe(S){return!S||typeof S!="object"?!1:typeof HTMLElement<"u"&&S instanceof HTMLElement?!0:typeof S.nodeName=="string"&&typeof S.getAttribute=="function"}function pe(S,E){if(S.length>E.maxStringLength){var le=S.length-E.maxStringLength,ve="... "+le+" more character"+(le>1?"s":"");return pe(b.call(S,0,E.maxStringLength),E)+ve}var we=We[E.quoteStyle||"single"];we.lastIndex=0;var te=R.call(R.call(S,we,"\\$1"),/[\x00-\x1f]/g,ae);return Ke(te,"single",E)}function ae(S){var E=S.charCodeAt(0),le={8:"b",9:"t",10:"n",12:"f",13:"r"}[E];return le?"\\"+le:"\\x"+(E<16?"0":"")+_.call(E.toString(16))}function Te(S){return"Object("+S+")"}function Ne(S){return S+" { ? }"}function Xe(S,E,le,ve){var we=ve?Jt(le,ve):W.call(le,", ");return S+" ("+E+") {"+we+"}"}function nt(S){for(var E=0;E<S.length;E++)if(M(S[E],`
`)>=0)return!1;return!0}function Lt(S,E){var le;if(S.indent==="	")le="	";else if(typeof S.indent=="number"&&S.indent>0)le=W.call(Array(S.indent+1)," ");else return null;return{base:le,prev:W.call(Array(E+1),le)}}function Jt(S,E){if(S.length===0)return"";var le=`
`+E.prev+E.base;return le+W.call(S,","+le)+`
`+E.prev}function Me(S,E){var le=T(S),ve=[];if(le){ve.length=S.length;for(var we=0;we<S.length;we++)ve[we]=P(S,we)?E(S[we],S):""}var te=typeof K=="function"?K(S):[],Rt;if(ge){Rt={};for(var Ut=0;Ut<te.length;Ut++)Rt["$"+te[Ut]]=te[Ut]}for(var Ze in S)P(S,Ze)&&(le&&String(Number(Ze))===Ze&&Ze<S.length||ge&&Rt["$"+Ze]instanceof Symbol||(U.call(/[^\w$]/,Ze)?ve.push(E(Ze,S)+": "+E(S[Ze],S)):ve.push(Ze+": "+E(S[Ze],S))));if(typeof K=="function")for(var Ot=0;Ot<te.length;Ot++)he.call(S,te[Ot])&&ve.push("["+E(te[Ot])+"]: "+E(S[te[Ot]],S));return ve}return ho}var mo,ta;function jp(){if(ta)return mo;ta=1;var e=Fn(),t=hr(),r=function(a,l,u){for(var c=a,f;(f=c.next)!=null;c=f)if(f.key===l)return c.next=f.next,u||(f.next=a.next,a.next=f),f},n=function(a,l){if(a){var u=r(a,l);return u&&u.value}},o=function(a,l,u){var c=r(a,l);c?c.value=u:a.next={key:l,next:a.next,value:u}},s=function(a,l){return a?!!r(a,l):!1},i=function(a,l){if(a)return r(a,l,!0)};return mo=function(){var l,u={assert:function(c){if(!u.has(c))throw new t("Side channel does not contain "+e(c))},delete:function(c){var f=l&&l.next,h=i(l,c);return h&&f&&f===h&&(l=void 0),!!h},get:function(c){return n(l,c)},has:function(c){return s(l,c)},set:function(c,f){l||(l={next:void 0}),o(l,c,f)}};return u},mo}var yo,ra;function Oc(){return ra||(ra=1,yo=Object),yo}var go,na;function $p(){return na||(na=1,go=Error),go}var vo,oa;function Gp(){return oa||(oa=1,vo=EvalError),vo}var bo,sa;function Wp(){return sa||(sa=1,bo=RangeError),bo}var Ao,ia;function Kp(){return ia||(ia=1,Ao=ReferenceError),Ao}var wo,aa;function Vp(){return aa||(aa=1,wo=SyntaxError),wo}var So,la;function zp(){return la||(la=1,So=URIError),So}var Eo,ca;function Qp(){return ca||(ca=1,Eo=Math.abs),Eo}var _o,ua;function Jp(){return ua||(ua=1,_o=Math.floor),_o}var Ro,fa;function Xp(){return fa||(fa=1,Ro=Math.max),Ro}var Oo,da;function Zp(){return da||(da=1,Oo=Math.min),Oo}var xo,pa;function Yp(){return pa||(pa=1,xo=Math.pow),xo}var Po,ha;function eh(){return ha||(ha=1,Po=Math.round),Po}var Co,ma;function th(){return ma||(ma=1,Co=Number.isNaN||function(t){return t!==t}),Co}var To,ya;function rh(){if(ya)return To;ya=1;var e=th();return To=function(r){return e(r)||r===0?r:r<0?-1:1},To}var Io,ga;function nh(){return ga||(ga=1,Io=Object.getOwnPropertyDescriptor),Io}var ko,va;function xc(){if(va)return ko;va=1;var e=nh();if(e)try{e([],"length")}catch{e=null}return ko=e,ko}var Do,ba;function oh(){if(ba)return Do;ba=1;var e=Object.defineProperty||!1;if(e)try{e({},"a",{value:1})}catch{e=!1}return Do=e,Do}var No,Aa;function sh(){return Aa||(Aa=1,No=function(){if(typeof Symbol!="function"||typeof Object.getOwnPropertySymbols!="function")return!1;if(typeof Symbol.iterator=="symbol")return!0;var t={},r=Symbol("test"),n=Object(r);if(typeof r=="string"||Object.prototype.toString.call(r)!=="[object Symbol]"||Object.prototype.toString.call(n)!=="[object Symbol]")return!1;var o=42;t[r]=o;for(var s in t)return!1;if(typeof Object.keys=="function"&&Object.keys(t).length!==0||typeof Object.getOwnPropertyNames=="function"&&Object.getOwnPropertyNames(t).length!==0)return!1;var i=Object.getOwnPropertySymbols(t);if(i.length!==1||i[0]!==r||!Object.prototype.propertyIsEnumerable.call(t,r))return!1;if(typeof Object.getOwnPropertyDescriptor=="function"){var a=Object.getOwnPropertyDescriptor(t,r);if(a.value!==o||a.enumerable!==!0)return!1}return!0}),No}var Mo,wa;function ih(){if(wa)return Mo;wa=1;var e=typeof Symbol<"u"&&Symbol,t=sh();return Mo=function(){return typeof e!="function"||typeof Symbol!="function"||typeof e("foo")!="symbol"||typeof Symbol("bar")!="symbol"?!1:t()},Mo}var Fo,Sa;function Pc(){return Sa||(Sa=1,Fo=typeof Reflect<"u"&&Reflect.getPrototypeOf||null),Fo}var Bo,Ea;function Cc(){if(Ea)return Bo;Ea=1;var e=Oc();return Bo=e.getPrototypeOf||null,Bo}var Lo,_a;function ah(){if(_a)return Lo;_a=1;var e="Function.prototype.bind called on incompatible ",t=Object.prototype.toString,r=Math.max,n="[object Function]",o=function(l,u){for(var c=[],f=0;f<l.length;f+=1)c[f]=l[f];for(var h=0;h<u.length;h+=1)c[h+l.length]=u[h];return c},s=function(l,u){for(var c=[],f=u,h=0;f<l.length;f+=1,h+=1)c[h]=l[f];return c},i=function(a,l){for(var u="",c=0;c<a.length;c+=1)u+=a[c],c+1<a.length&&(u+=l);return u};return Lo=function(l){var u=this;if(typeof u!="function"||t.apply(u)!==n)throw new TypeError(e+u);for(var c=s(arguments,1),f,h=function(){if(this instanceof f){var v=u.apply(this,o(c,arguments));return Object(v)===v?v:this}return u.apply(l,o(c,arguments))},d=r(0,u.length-c.length),g=[],w=0;w<d;w++)g[w]="$"+w;if(f=Function("binder","return function ("+i(g,",")+"){ return binder.apply(this,arguments); }")(h),u.prototype){var y=function(){};y.prototype=u.prototype,f.prototype=new y,y.prototype=null}return f},Lo}var Uo,Ra;function Bn(){if(Ra)return Uo;Ra=1;var e=ah();return Uo=Function.prototype.bind||e,Uo}var qo,Oa;function Qs(){return Oa||(Oa=1,qo=Function.prototype.call),qo}var Ho,xa;function Tc(){return xa||(xa=1,Ho=Function.prototype.apply),Ho}var jo,Pa;function lh(){return Pa||(Pa=1,jo=typeof Reflect<"u"&&Reflect&&Reflect.apply),jo}var $o,Ca;function ch(){if(Ca)return $o;Ca=1;var e=Bn(),t=Tc(),r=Qs(),n=lh();return $o=n||e.call(r,t),$o}var Go,Ta;function Ic(){if(Ta)return Go;Ta=1;var e=Bn(),t=hr(),r=Qs(),n=ch();return Go=function(s){if(s.length<1||typeof s[0]!="function")throw new t("a function is required");return n(e,r,s)},Go}var Wo,Ia;function uh(){if(Ia)return Wo;Ia=1;var e=Ic(),t=xc(),r;try{r=[].__proto__===Array.prototype}catch(i){if(!i||typeof i!="object"||!("code"in i)||i.code!=="ERR_PROTO_ACCESS")throw i}var n=!!r&&t&&t(Object.prototype,"__proto__"),o=Object,s=o.getPrototypeOf;return Wo=n&&typeof n.get=="function"?e([n.get]):typeof s=="function"?function(a){return s(a==null?a:o(a))}:!1,Wo}var Ko,ka;function fh(){if(ka)return Ko;ka=1;var e=Pc(),t=Cc(),r=uh();return Ko=e?function(o){return e(o)}:t?function(o){if(!o||typeof o!="object"&&typeof o!="function")throw new TypeError("getProto: not an object");return t(o)}:r?function(o){return r(o)}:null,Ko}var Vo,Da;function dh(){if(Da)return Vo;Da=1;var e=Function.prototype.call,t=Object.prototype.hasOwnProperty,r=Bn();return Vo=r.call(e,t),Vo}var zo,Na;function Js(){if(Na)return zo;Na=1;var e,t=Oc(),r=$p(),n=Gp(),o=Wp(),s=Kp(),i=Vp(),a=hr(),l=zp(),u=Qp(),c=Jp(),f=Xp(),h=Zp(),d=Yp(),g=eh(),w=rh(),y=Function,v=function(D){try{return y('"use strict"; return ('+D+").constructor;")()}catch{}},b=xc(),R=oh(),_=function(){throw new a},k=b?function(){try{return arguments.callee,_}catch{try{return b(arguments,"callee").get}catch{return _}}}():_,U=ih()(),B=fh(),W=Cc(),Z=Pc(),z=Tc(),se=Qs(),K={},me=typeof Uint8Array>"u"||!B?e:B(Uint8Array),ge={__proto__:null,"%AggregateError%":typeof AggregateError>"u"?e:AggregateError,"%Array%":Array,"%ArrayBuffer%":typeof ArrayBuffer>"u"?e:ArrayBuffer,"%ArrayIteratorPrototype%":U&&B?B([][Symbol.iterator]()):e,"%AsyncFromSyncIteratorPrototype%":e,"%AsyncFunction%":K,"%AsyncGenerator%":K,"%AsyncGeneratorFunction%":K,"%AsyncIteratorPrototype%":K,"%Atomics%":typeof Atomics>"u"?e:Atomics,"%BigInt%":typeof BigInt>"u"?e:BigInt,"%BigInt64Array%":typeof BigInt64Array>"u"?e:BigInt64Array,"%BigUint64Array%":typeof BigUint64Array>"u"?e:BigUint64Array,"%Boolean%":Boolean,"%DataView%":typeof DataView>"u"?e:DataView,"%Date%":Date,"%decodeURI%":decodeURI,"%decodeURIComponent%":decodeURIComponent,"%encodeURI%":encodeURI,"%encodeURIComponent%":encodeURIComponent,"%Error%":r,"%eval%":eval,"%EvalError%":n,"%Float16Array%":typeof Float16Array>"u"?e:Float16Array,"%Float32Array%":typeof Float32Array>"u"?e:Float32Array,"%Float64Array%":typeof Float64Array>"u"?e:Float64Array,"%FinalizationRegistry%":typeof FinalizationRegistry>"u"?e:FinalizationRegistry,"%Function%":y,"%GeneratorFunction%":K,"%Int8Array%":typeof Int8Array>"u"?e:Int8Array,"%Int16Array%":typeof Int16Array>"u"?e:Int16Array,"%Int32Array%":typeof Int32Array>"u"?e:Int32Array,"%isFinite%":isFinite,"%isNaN%":isNaN,"%IteratorPrototype%":U&&B?B(B([][Symbol.iterator]())):e,"%JSON%":typeof JSON=="object"?JSON:e,"%Map%":typeof Map>"u"?e:Map,"%MapIteratorPrototype%":typeof Map>"u"||!U||!B?e:B(new Map()[Symbol.iterator]()),"%Math%":Math,"%Number%":Number,"%Object%":t,"%Object.getOwnPropertyDescriptor%":b,"%parseFloat%":parseFloat,"%parseInt%":parseInt,"%Promise%":typeof Promise>"u"?e:Promise,"%Proxy%":typeof Proxy>"u"?e:Proxy,"%RangeError%":o,"%ReferenceError%":s,"%Reflect%":typeof Reflect>"u"?e:Reflect,"%RegExp%":RegExp,"%Set%":typeof Set>"u"?e:Set,"%SetIteratorPrototype%":typeof Set>"u"||!U||!B?e:B(new Set()[Symbol.iterator]()),"%SharedArrayBuffer%":typeof SharedArrayBuffer>"u"?e:SharedArrayBuffer,"%String%":String,"%StringIteratorPrototype%":U&&B?B(""[Symbol.iterator]()):e,"%Symbol%":U?Symbol:e,"%SyntaxError%":i,"%ThrowTypeError%":k,"%TypedArray%":me,"%TypeError%":a,"%Uint8Array%":typeof Uint8Array>"u"?e:Uint8Array,"%Uint8ClampedArray%":typeof Uint8ClampedArray>"u"?e:Uint8ClampedArray,"%Uint16Array%":typeof Uint16Array>"u"?e:Uint16Array,"%Uint32Array%":typeof Uint32Array>"u"?e:Uint32Array,"%URIError%":l,"%WeakMap%":typeof WeakMap>"u"?e:WeakMap,"%WeakRef%":typeof WeakRef>"u"?e:WeakRef,"%WeakSet%":typeof WeakSet>"u"?e:WeakSet,"%Function.prototype.call%":se,"%Function.prototype.apply%":z,"%Object.defineProperty%":R,"%Object.getPrototypeOf%":W,"%Math.abs%":u,"%Math.floor%":c,"%Math.max%":f,"%Math.min%":h,"%Math.pow%":d,"%Math.round%":g,"%Math.sign%":w,"%Reflect.getPrototypeOf%":Z};if(B)try{null.error}catch(D){var Pe=B(B(D));ge["%Error.prototype%"]=Pe}var he=function D(N){var Q;if(N==="%AsyncFunction%")Q=v("async function () {}");else if(N==="%GeneratorFunction%")Q=v("function* () {}");else if(N==="%AsyncGeneratorFunction%")Q=v("async function* () {}");else if(N==="%AsyncGenerator%"){var p=D("%AsyncGeneratorFunction%");p&&(Q=p.prototype)}else if(N==="%AsyncIteratorPrototype%"){var m=D("%AsyncGenerator%");m&&B&&(Q=B(m.prototype))}return ge[N]=Q,Q},re={__proto__:null,"%ArrayBufferPrototype%":["ArrayBuffer","prototype"],"%ArrayPrototype%":["Array","prototype"],"%ArrayProto_entries%":["Array","prototype","entries"],"%ArrayProto_forEach%":["Array","prototype","forEach"],"%ArrayProto_keys%":["Array","prototype","keys"],"%ArrayProto_values%":["Array","prototype","values"],"%AsyncFunctionPrototype%":["AsyncFunction","prototype"],"%AsyncGenerator%":["AsyncGeneratorFunction","prototype"],"%AsyncGeneratorPrototype%":["AsyncGeneratorFunction","prototype","prototype"],"%BooleanPrototype%":["Boolean","prototype"],"%DataViewPrototype%":["DataView","prototype"],"%DatePrototype%":["Date","prototype"],"%ErrorPrototype%":["Error","prototype"],"%EvalErrorPrototype%":["EvalError","prototype"],"%Float32ArrayPrototype%":["Float32Array","prototype"],"%Float64ArrayPrototype%":["Float64Array","prototype"],"%FunctionPrototype%":["Function","prototype"],"%Generator%":["GeneratorFunction","prototype"],"%GeneratorPrototype%":["GeneratorFunction","prototype","prototype"],"%Int8ArrayPrototype%":["Int8Array","prototype"],"%Int16ArrayPrototype%":["Int16Array","prototype"],"%Int32ArrayPrototype%":["Int32Array","prototype"],"%JSONParse%":["JSON","parse"],"%JSONStringify%":["JSON","stringify"],"%MapPrototype%":["Map","prototype"],"%NumberPrototype%":["Number","prototype"],"%ObjectPrototype%":["Object","prototype"],"%ObjProto_toString%":["Object","prototype","toString"],"%ObjProto_valueOf%":["Object","prototype","valueOf"],"%PromisePrototype%":["Promise","prototype"],"%PromiseProto_then%":["Promise","prototype","then"],"%Promise_all%":["Promise","all"],"%Promise_reject%":["Promise","reject"],"%Promise_resolve%":["Promise","resolve"],"%RangeErrorPrototype%":["RangeError","prototype"],"%ReferenceErrorPrototype%":["ReferenceError","prototype"],"%RegExpPrototype%":["RegExp","prototype"],"%SetPrototype%":["Set","prototype"],"%SharedArrayBufferPrototype%":["SharedArrayBuffer","prototype"],"%StringPrototype%":["String","prototype"],"%SymbolPrototype%":["Symbol","prototype"],"%SyntaxErrorPrototype%":["SyntaxError","prototype"],"%TypedArrayPrototype%":["TypedArray","prototype"],"%TypeErrorPrototype%":["TypeError","prototype"],"%Uint8ArrayPrototype%":["Uint8Array","prototype"],"%Uint8ClampedArrayPrototype%":["Uint8ClampedArray","prototype"],"%Uint16ArrayPrototype%":["Uint16Array","prototype"],"%Uint32ArrayPrototype%":["Uint32Array","prototype"],"%URIErrorPrototype%":["URIError","prototype"],"%WeakMapPrototype%":["WeakMap","prototype"],"%WeakSetPrototype%":["WeakSet","prototype"]},j=Bn(),Re=dh(),ke=j.call(se,Array.prototype.concat),De=j.call(z,Array.prototype.splice),Oe=j.call(se,String.prototype.replace),We=j.call(se,String.prototype.slice),Ke=j.call(se,RegExp.prototype.exec),Qe=/[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g,Ae=/\\(\\)?/g,T=function(N){var Q=We(N,0,1),p=We(N,-1);if(Q==="%"&&p!=="%")throw new i("invalid intrinsic syntax, expected closing `%`");if(p==="%"&&Q!=="%")throw new i("invalid intrinsic syntax, expected opening `%`");var m=[];return Oe(N,Qe,function(A,x,C,P){m[m.length]=C?Oe(P,Ae,"$1"):x||A}),m},H=function(N,Q){var p=N,m;if(Re(re,p)&&(m=re[p],p="%"+m[0]+"%"),Re(ge,p)){var A=ge[p];if(A===K&&(A=he(p)),typeof A>"u"&&!Q)throw new a("intrinsic "+N+" exists, but is not available. Please file an issue!");return{alias:m,name:p,value:A}}throw new i("intrinsic "+N+" does not exist!")};return zo=function(N,Q){if(typeof N!="string"||N.length===0)throw new a("intrinsic name must be a non-empty string");if(arguments.length>1&&typeof Q!="boolean")throw new a('"allowMissing" argument must be a boolean');if(Ke(/^%?[^%]*%?$/,N)===null)throw new i("`%` may not be present anywhere but at the beginning and end of the intrinsic name");var p=T(N),m=p.length>0?p[0]:"",A=H("%"+m+"%",Q),x=A.name,C=A.value,P=!1,F=A.alias;F&&(m=F[0],De(p,ke([0,1],F)));for(var L=1,M=!0;L<p.length;L+=1){var I=p[L],V=We(I,0,1),q=We(I,-1);if((V==='"'||V==="'"||V==="`"||q==='"'||q==="'"||q==="`")&&V!==q)throw new i("property names with quotes must have matching quotes");if((I==="constructor"||!M)&&(P=!0),m+="."+I,x="%"+m+"%",Re(ge,x))C=ge[x];else if(C!=null){if(!(I in C)){if(!Q)throw new a("base intrinsic for "+N+" exists, but the property is not available.");return}if(b&&L+1>=p.length){var G=b(C,I);M=!!G,M&&"get"in G&&!("originalValue"in G.get)?C=G.get:C=C[I]}else M=Re(C,I),C=C[I];M&&!P&&(ge[x]=C)}}return C},zo}var Qo,Ma;function kc(){if(Ma)return Qo;Ma=1;var e=Js(),t=Ic(),r=t([e("%String.prototype.indexOf%")]);return Qo=function(o,s){var i=e(o,!!s);return typeof i=="function"&&r(o,".prototype.")>-1?t([i]):i},Qo}var Jo,Fa;function Dc(){if(Fa)return Jo;Fa=1;var e=Js(),t=kc(),r=Fn(),n=hr(),o=e("%Map%",!0),s=t("Map.prototype.get",!0),i=t("Map.prototype.set",!0),a=t("Map.prototype.has",!0),l=t("Map.prototype.delete",!0),u=t("Map.prototype.size",!0);return Jo=!!o&&function(){var f,h={assert:function(d){if(!h.has(d))throw new n("Side channel does not contain "+r(d))},delete:function(d){if(f){var g=l(f,d);return u(f)===0&&(f=void 0),g}return!1},get:function(d){if(f)return s(f,d)},has:function(d){return f?a(f,d):!1},set:function(d,g){f||(f=new o),i(f,d,g)}};return h},Jo}var Xo,Ba;function ph(){if(Ba)return Xo;Ba=1;var e=Js(),t=kc(),r=Fn(),n=Dc(),o=hr(),s=e("%WeakMap%",!0),i=t("WeakMap.prototype.get",!0),a=t("WeakMap.prototype.set",!0),l=t("WeakMap.prototype.has",!0),u=t("WeakMap.prototype.delete",!0);return Xo=s?function(){var f,h,d={assert:function(g){if(!d.has(g))throw new o("Side channel does not contain "+r(g))},delete:function(g){if(s&&g&&(typeof g=="object"||typeof g=="function")){if(f)return u(f,g)}else if(n&&h)return h.delete(g);return!1},get:function(g){return s&&g&&(typeof g=="object"||typeof g=="function")&&f?i(f,g):h&&h.get(g)},has:function(g){return s&&g&&(typeof g=="object"||typeof g=="function")&&f?l(f,g):!!h&&h.has(g)},set:function(g,w){s&&g&&(typeof g=="object"||typeof g=="function")?(f||(f=new s),a(f,g,w)):n&&(h||(h=n()),h.set(g,w))}};return d}:n,Xo}var Zo,La;function hh(){if(La)return Zo;La=1;var e=hr(),t=Fn(),r=jp(),n=Dc(),o=ph(),s=o||n||r;return Zo=function(){var a,l={assert:function(u){if(!l.has(u))throw new e("Side channel does not contain "+t(u))},delete:function(u){return!!a&&a.delete(u)},get:function(u){return a&&a.get(u)},has:function(u){return!!a&&a.has(u)},set:function(u,c){a||(a=s()),a.set(u,c)}};return l},Zo}var Yo,Ua;function Xs(){if(Ua)return Yo;Ua=1;var e=String.prototype.replace,t=/%20/g,r={RFC1738:"RFC1738",RFC3986:"RFC3986"};return Yo={default:r.RFC3986,formatters:{RFC1738:function(n){return e.call(n,t,"+")},RFC3986:function(n){return String(n)}},RFC1738:r.RFC1738,RFC3986:r.RFC3986},Yo}var es,qa;function Nc(){if(qa)return es;qa=1;var e=Xs(),t=Object.prototype.hasOwnProperty,r=Array.isArray,n=function(){for(var y=[],v=0;v<256;++v)y.push("%"+((v<16?"0":"")+v.toString(16)).toUpperCase());return y}(),o=function(v){for(;v.length>1;){var b=v.pop(),R=b.obj[b.prop];if(r(R)){for(var _=[],k=0;k<R.length;++k)typeof R[k]<"u"&&_.push(R[k]);b.obj[b.prop]=_}}},s=function(v,b){for(var R=b&&b.plainObjects?{__proto__:null}:{},_=0;_<v.length;++_)typeof v[_]<"u"&&(R[_]=v[_]);return R},i=function y(v,b,R){if(!b)return v;if(typeof b!="object"&&typeof b!="function"){if(r(v))v.push(b);else if(v&&typeof v=="object")(R&&(R.plainObjects||R.allowPrototypes)||!t.call(Object.prototype,b))&&(v[b]=!0);else return[v,b];return v}if(!v||typeof v!="object")return[v].concat(b);var _=v;return r(v)&&!r(b)&&(_=s(v,R)),r(v)&&r(b)?(b.forEach(function(k,U){if(t.call(v,U)){var B=v[U];B&&typeof B=="object"&&k&&typeof k=="object"?v[U]=y(B,k,R):v.push(k)}else v[U]=k}),v):Object.keys(b).reduce(function(k,U){var B=b[U];return t.call(k,U)?k[U]=y(k[U],B,R):k[U]=B,k},_)},a=function(v,b){return Object.keys(b).reduce(function(R,_){return R[_]=b[_],R},v)},l=function(y,v,b){var R=y.replace(/\+/g," ");if(b==="iso-8859-1")return R.replace(/%[0-9a-f]{2}/gi,unescape);try{return decodeURIComponent(R)}catch{return R}},u=1024,c=function(v,b,R,_,k){if(v.length===0)return v;var U=v;if(typeof v=="symbol"?U=Symbol.prototype.toString.call(v):typeof v!="string"&&(U=String(v)),R==="iso-8859-1")return escape(U).replace(/%u[0-9a-f]{4}/gi,function(me){return"%26%23"+parseInt(me.slice(2),16)+"%3B"});for(var B="",W=0;W<U.length;W+=u){for(var Z=U.length>=u?U.slice(W,W+u):U,z=[],se=0;se<Z.length;++se){var K=Z.charCodeAt(se);if(K===45||K===46||K===95||K===126||K>=48&&K<=57||K>=65&&K<=90||K>=97&&K<=122||k===e.RFC1738&&(K===40||K===41)){z[z.length]=Z.charAt(se);continue}if(K<128){z[z.length]=n[K];continue}if(K<2048){z[z.length]=n[192|K>>6]+n[128|K&63];continue}if(K<55296||K>=57344){z[z.length]=n[224|K>>12]+n[128|K>>6&63]+n[128|K&63];continue}se+=1,K=65536+((K&1023)<<10|Z.charCodeAt(se)&1023),z[z.length]=n[240|K>>18]+n[128|K>>12&63]+n[128|K>>6&63]+n[128|K&63]}B+=z.join("")}return B},f=function(v){for(var b=[{obj:{o:v},prop:"o"}],R=[],_=0;_<b.length;++_)for(var k=b[_],U=k.obj[k.prop],B=Object.keys(U),W=0;W<B.length;++W){var Z=B[W],z=U[Z];typeof z=="object"&&z!==null&&R.indexOf(z)===-1&&(b.push({obj:U,prop:Z}),R.push(z))}return o(b),v},h=function(v){return Object.prototype.toString.call(v)==="[object RegExp]"},d=function(v){return!v||typeof v!="object"?!1:!!(v.constructor&&v.constructor.isBuffer&&v.constructor.isBuffer(v))},g=function(v,b){return[].concat(v,b)},w=function(v,b){if(r(v)){for(var R=[],_=0;_<v.length;_+=1)R.push(b(v[_]));return R}return b(v)};return es={arrayToObject:s,assign:a,combine:g,compact:f,decode:l,encode:c,isBuffer:d,isRegExp:h,maybeMap:w,merge:i},es}var ts,Ha;function mh(){if(Ha)return ts;Ha=1;var e=hh(),t=Nc(),r=Xs(),n=Object.prototype.hasOwnProperty,o={brackets:function(y){return y+"[]"},comma:"comma",indices:function(y,v){return y+"["+v+"]"},repeat:function(y){return y}},s=Array.isArray,i=Array.prototype.push,a=function(w,y){i.apply(w,s(y)?y:[y])},l=Date.prototype.toISOString,u=r.default,c={addQueryPrefix:!1,allowDots:!1,allowEmptyArrays:!1,arrayFormat:"indices",charset:"utf-8",charsetSentinel:!1,commaRoundTrip:!1,delimiter:"&",encode:!0,encodeDotInKeys:!1,encoder:t.encode,encodeValuesOnly:!1,filter:void 0,format:u,formatter:r.formatters[u],indices:!1,serializeDate:function(y){return l.call(y)},skipNulls:!1,strictNullHandling:!1},f=function(y){return typeof y=="string"||typeof y=="number"||typeof y=="boolean"||typeof y=="symbol"||typeof y=="bigint"},h={},d=function w(y,v,b,R,_,k,U,B,W,Z,z,se,K,me,ge,Pe,he,re){for(var j=y,Re=re,ke=0,De=!1;(Re=Re.get(h))!==void 0&&!De;){var Oe=Re.get(y);if(ke+=1,typeof Oe<"u"){if(Oe===ke)throw new RangeError("Cyclic object value");De=!0}typeof Re.get(h)>"u"&&(ke=0)}if(typeof Z=="function"?j=Z(v,j):j instanceof Date?j=K(j):b==="comma"&&s(j)&&(j=t.maybeMap(j,function(x){return x instanceof Date?K(x):x})),j===null){if(k)return W&&!Pe?W(v,c.encoder,he,"key",me):v;j=""}if(f(j)||t.isBuffer(j)){if(W){var We=Pe?v:W(v,c.encoder,he,"key",me);return[ge(We)+"="+ge(W(j,c.encoder,he,"value",me))]}return[ge(v)+"="+ge(String(j))]}var Ke=[];if(typeof j>"u")return Ke;var Qe;if(b==="comma"&&s(j))Pe&&W&&(j=t.maybeMap(j,W)),Qe=[{value:j.length>0?j.join(",")||null:void 0}];else if(s(Z))Qe=Z;else{var Ae=Object.keys(j);Qe=z?Ae.sort(z):Ae}var T=B?String(v).replace(/\./g,"%2E"):String(v),H=R&&s(j)&&j.length===1?T+"[]":T;if(_&&s(j)&&j.length===0)return H+"[]";for(var D=0;D<Qe.length;++D){var N=Qe[D],Q=typeof N=="object"&&N&&typeof N.value<"u"?N.value:j[N];if(!(U&&Q===null)){var p=se&&B?String(N).replace(/\./g,"%2E"):String(N),m=s(j)?typeof b=="function"?b(H,p):H:H+(se?"."+p:"["+p+"]");re.set(y,ke);var A=e();A.set(h,re),a(Ke,w(Q,m,b,R,_,k,U,B,b==="comma"&&Pe&&s(j)?null:W,Z,z,se,K,me,ge,Pe,he,A))}}return Ke},g=function(y){if(!y)return c;if(typeof y.allowEmptyArrays<"u"&&typeof y.allowEmptyArrays!="boolean")throw new TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(typeof y.encodeDotInKeys<"u"&&typeof y.encodeDotInKeys!="boolean")throw new TypeError("`encodeDotInKeys` option can only be `true` or `false`, when provided");if(y.encoder!==null&&typeof y.encoder<"u"&&typeof y.encoder!="function")throw new TypeError("Encoder has to be a function.");var v=y.charset||c.charset;if(typeof y.charset<"u"&&y.charset!=="utf-8"&&y.charset!=="iso-8859-1")throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var b=r.default;if(typeof y.format<"u"){if(!n.call(r.formatters,y.format))throw new TypeError("Unknown format option provided.");b=y.format}var R=r.formatters[b],_=c.filter;(typeof y.filter=="function"||s(y.filter))&&(_=y.filter);var k;if(y.arrayFormat in o?k=y.arrayFormat:"indices"in y?k=y.indices?"indices":"repeat":k=c.arrayFormat,"commaRoundTrip"in y&&typeof y.commaRoundTrip!="boolean")throw new TypeError("`commaRoundTrip` must be a boolean, or absent");var U=typeof y.allowDots>"u"?y.encodeDotInKeys===!0?!0:c.allowDots:!!y.allowDots;return{addQueryPrefix:typeof y.addQueryPrefix=="boolean"?y.addQueryPrefix:c.addQueryPrefix,allowDots:U,allowEmptyArrays:typeof y.allowEmptyArrays=="boolean"?!!y.allowEmptyArrays:c.allowEmptyArrays,arrayFormat:k,charset:v,charsetSentinel:typeof y.charsetSentinel=="boolean"?y.charsetSentinel:c.charsetSentinel,commaRoundTrip:!!y.commaRoundTrip,delimiter:typeof y.delimiter>"u"?c.delimiter:y.delimiter,encode:typeof y.encode=="boolean"?y.encode:c.encode,encodeDotInKeys:typeof y.encodeDotInKeys=="boolean"?y.encodeDotInKeys:c.encodeDotInKeys,encoder:typeof y.encoder=="function"?y.encoder:c.encoder,encodeValuesOnly:typeof y.encodeValuesOnly=="boolean"?y.encodeValuesOnly:c.encodeValuesOnly,filter:_,format:b,formatter:R,serializeDate:typeof y.serializeDate=="function"?y.serializeDate:c.serializeDate,skipNulls:typeof y.skipNulls=="boolean"?y.skipNulls:c.skipNulls,sort:typeof y.sort=="function"?y.sort:null,strictNullHandling:typeof y.strictNullHandling=="boolean"?y.strictNullHandling:c.strictNullHandling}};return ts=function(w,y){var v=w,b=g(y),R,_;typeof b.filter=="function"?(_=b.filter,v=_("",v)):s(b.filter)&&(_=b.filter,R=_);var k=[];if(typeof v!="object"||v===null)return"";var U=o[b.arrayFormat],B=U==="comma"&&b.commaRoundTrip;R||(R=Object.keys(v)),b.sort&&R.sort(b.sort);for(var W=e(),Z=0;Z<R.length;++Z){var z=R[Z],se=v[z];b.skipNulls&&se===null||a(k,d(se,z,U,B,b.allowEmptyArrays,b.strictNullHandling,b.skipNulls,b.encodeDotInKeys,b.encode?b.encoder:null,b.filter,b.sort,b.allowDots,b.serializeDate,b.format,b.formatter,b.encodeValuesOnly,b.charset,W))}var K=k.join(b.delimiter),me=b.addQueryPrefix===!0?"?":"";return b.charsetSentinel&&(b.charset==="iso-8859-1"?me+="utf8=%26%2310003%3B&":me+="utf8=%E2%9C%93&"),K.length>0?me+K:""},ts}var rs,ja;function yh(){if(ja)return rs;ja=1;var e=Nc(),t=Object.prototype.hasOwnProperty,r=Array.isArray,n={allowDots:!1,allowEmptyArrays:!1,allowPrototypes:!1,allowSparse:!1,arrayLimit:20,charset:"utf-8",charsetSentinel:!1,comma:!1,decodeDotInKeys:!1,decoder:e.decode,delimiter:"&",depth:5,duplicates:"combine",ignoreQueryPrefix:!1,interpretNumericEntities:!1,parameterLimit:1e3,parseArrays:!0,plainObjects:!1,strictDepth:!1,strictNullHandling:!1,throwOnLimitExceeded:!1},o=function(h){return h.replace(/&#(\d+);/g,function(d,g){return String.fromCharCode(parseInt(g,10))})},s=function(h,d,g){if(h&&typeof h=="string"&&d.comma&&h.indexOf(",")>-1)return h.split(",");if(d.throwOnLimitExceeded&&g>=d.arrayLimit)throw new RangeError("Array limit exceeded. Only "+d.arrayLimit+" element"+(d.arrayLimit===1?"":"s")+" allowed in an array.");return h},i="utf8=%26%2310003%3B",a="utf8=%E2%9C%93",l=function(d,g){var w={__proto__:null},y=g.ignoreQueryPrefix?d.replace(/^\?/,""):d;y=y.replace(/%5B/gi,"[").replace(/%5D/gi,"]");var v=g.parameterLimit===1/0?void 0:g.parameterLimit,b=y.split(g.delimiter,g.throwOnLimitExceeded?v+1:v);if(g.throwOnLimitExceeded&&b.length>v)throw new RangeError("Parameter limit exceeded. Only "+v+" parameter"+(v===1?"":"s")+" allowed.");var R=-1,_,k=g.charset;if(g.charsetSentinel)for(_=0;_<b.length;++_)b[_].indexOf("utf8=")===0&&(b[_]===a?k="utf-8":b[_]===i&&(k="iso-8859-1"),R=_,_=b.length);for(_=0;_<b.length;++_)if(_!==R){var U=b[_],B=U.indexOf("]="),W=B===-1?U.indexOf("="):B+1,Z,z;W===-1?(Z=g.decoder(U,n.decoder,k,"key"),z=g.strictNullHandling?null:""):(Z=g.decoder(U.slice(0,W),n.decoder,k,"key"),z=e.maybeMap(s(U.slice(W+1),g,r(w[Z])?w[Z].length:0),function(K){return g.decoder(K,n.decoder,k,"value")})),z&&g.interpretNumericEntities&&k==="iso-8859-1"&&(z=o(String(z))),U.indexOf("[]=")>-1&&(z=r(z)?[z]:z);var se=t.call(w,Z);se&&g.duplicates==="combine"?w[Z]=e.combine(w[Z],z):(!se||g.duplicates==="last")&&(w[Z]=z)}return w},u=function(h,d,g,w){var y=0;if(h.length>0&&h[h.length-1]==="[]"){var v=h.slice(0,-1).join("");y=Array.isArray(d)&&d[v]?d[v].length:0}for(var b=w?d:s(d,g,y),R=h.length-1;R>=0;--R){var _,k=h[R];if(k==="[]"&&g.parseArrays)_=g.allowEmptyArrays&&(b===""||g.strictNullHandling&&b===null)?[]:e.combine([],b);else{_=g.plainObjects?{__proto__:null}:{};var U=k.charAt(0)==="["&&k.charAt(k.length-1)==="]"?k.slice(1,-1):k,B=g.decodeDotInKeys?U.replace(/%2E/g,"."):U,W=parseInt(B,10);!g.parseArrays&&B===""?_={0:b}:!isNaN(W)&&k!==B&&String(W)===B&&W>=0&&g.parseArrays&&W<=g.arrayLimit?(_=[],_[W]=b):B!=="__proto__"&&(_[B]=b)}b=_}return b},c=function(d,g,w,y){if(d){var v=w.allowDots?d.replace(/\.([^.[]+)/g,"[$1]"):d,b=/(\[[^[\]]*])/,R=/(\[[^[\]]*])/g,_=w.depth>0&&b.exec(v),k=_?v.slice(0,_.index):v,U=[];if(k){if(!w.plainObjects&&t.call(Object.prototype,k)&&!w.allowPrototypes)return;U.push(k)}for(var B=0;w.depth>0&&(_=R.exec(v))!==null&&B<w.depth;){if(B+=1,!w.plainObjects&&t.call(Object.prototype,_[1].slice(1,-1))&&!w.allowPrototypes)return;U.push(_[1])}if(_){if(w.strictDepth===!0)throw new RangeError("Input depth exceeded depth option of "+w.depth+" and strictDepth is true");U.push("["+v.slice(_.index)+"]")}return u(U,g,w,y)}},f=function(d){if(!d)return n;if(typeof d.allowEmptyArrays<"u"&&typeof d.allowEmptyArrays!="boolean")throw new TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(typeof d.decodeDotInKeys<"u"&&typeof d.decodeDotInKeys!="boolean")throw new TypeError("`decodeDotInKeys` option can only be `true` or `false`, when provided");if(d.decoder!==null&&typeof d.decoder<"u"&&typeof d.decoder!="function")throw new TypeError("Decoder has to be a function.");if(typeof d.charset<"u"&&d.charset!=="utf-8"&&d.charset!=="iso-8859-1")throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");if(typeof d.throwOnLimitExceeded<"u"&&typeof d.throwOnLimitExceeded!="boolean")throw new TypeError("`throwOnLimitExceeded` option must be a boolean");var g=typeof d.charset>"u"?n.charset:d.charset,w=typeof d.duplicates>"u"?n.duplicates:d.duplicates;if(w!=="combine"&&w!=="first"&&w!=="last")throw new TypeError("The duplicates option must be either combine, first, or last");var y=typeof d.allowDots>"u"?d.decodeDotInKeys===!0?!0:n.allowDots:!!d.allowDots;return{allowDots:y,allowEmptyArrays:typeof d.allowEmptyArrays=="boolean"?!!d.allowEmptyArrays:n.allowEmptyArrays,allowPrototypes:typeof d.allowPrototypes=="boolean"?d.allowPrototypes:n.allowPrototypes,allowSparse:typeof d.allowSparse=="boolean"?d.allowSparse:n.allowSparse,arrayLimit:typeof d.arrayLimit=="number"?d.arrayLimit:n.arrayLimit,charset:g,charsetSentinel:typeof d.charsetSentinel=="boolean"?d.charsetSentinel:n.charsetSentinel,comma:typeof d.comma=="boolean"?d.comma:n.comma,decodeDotInKeys:typeof d.decodeDotInKeys=="boolean"?d.decodeDotInKeys:n.decodeDotInKeys,decoder:typeof d.decoder=="function"?d.decoder:n.decoder,delimiter:typeof d.delimiter=="string"||e.isRegExp(d.delimiter)?d.delimiter:n.delimiter,depth:typeof d.depth=="number"||d.depth===!1?+d.depth:n.depth,duplicates:w,ignoreQueryPrefix:d.ignoreQueryPrefix===!0,interpretNumericEntities:typeof d.interpretNumericEntities=="boolean"?d.interpretNumericEntities:n.interpretNumericEntities,parameterLimit:typeof d.parameterLimit=="number"?d.parameterLimit:n.parameterLimit,parseArrays:d.parseArrays!==!1,plainObjects:typeof d.plainObjects=="boolean"?d.plainObjects:n.plainObjects,strictDepth:typeof d.strictDepth=="boolean"?!!d.strictDepth:n.strictDepth,strictNullHandling:typeof d.strictNullHandling=="boolean"?d.strictNullHandling:n.strictNullHandling,throwOnLimitExceeded:typeof d.throwOnLimitExceeded=="boolean"?d.throwOnLimitExceeded:!1}};return rs=function(h,d){var g=f(d);if(h===""||h===null||typeof h>"u")return g.plainObjects?{__proto__:null}:{};for(var w=typeof h=="string"?l(h,g):h,y=g.plainObjects?{__proto__:null}:{},v=Object.keys(w),b=0;b<v.length;++b){var R=v[b],_=c(R,w[R],g,typeof h=="string");y=e.merge(y,_,g)}return g.allowSparse===!0?y:e.compact(y)},rs}var ns,$a;function gh(){if($a)return ns;$a=1;var e=mh(),t=yh(),r=Xs();return ns={formats:r,parse:t,stringify:e},ns}var vh=gh();function Mc(e,t){return function(){return e.apply(t,arguments)}}const{toString:bh}=Object.prototype,{getPrototypeOf:Zs}=Object,Ln=(e=>t=>{const r=bh.call(t);return e[r]||(e[r]=r.slice(8,-1).toLowerCase())})(Object.create(null)),ut=e=>(e=e.toLowerCase(),t=>Ln(t)===e),Un=e=>t=>typeof t===e,{isArray:mr}=Array,jr=Un("undefined");function Ah(e){return e!==null&&!jr(e)&&e.constructor!==null&&!jr(e.constructor)&&rt(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const Fc=ut("ArrayBuffer");function wh(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&Fc(e.buffer),t}const Sh=Un("string"),rt=Un("function"),Bc=Un("number"),qn=e=>e!==null&&typeof e=="object",Eh=e=>e===!0||e===!1,nn=e=>{if(Ln(e)!=="object")return!1;const t=Zs(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)},_h=ut("Date"),Rh=ut("File"),Oh=ut("Blob"),xh=ut("FileList"),Ph=e=>qn(e)&&rt(e.pipe),Ch=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||rt(e.append)&&((t=Ln(e))==="formdata"||t==="object"&&rt(e.toString)&&e.toString()==="[object FormData]"))},Th=ut("URLSearchParams"),[Ih,kh,Dh,Nh]=["ReadableStream","Request","Response","Headers"].map(ut),Mh=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function Wr(e,t,{allOwnKeys:r=!1}={}){if(e===null||typeof e>"u")return;let n,o;if(typeof e!="object"&&(e=[e]),mr(e))for(n=0,o=e.length;n<o;n++)t.call(null,e[n],n,e);else{const s=r?Object.getOwnPropertyNames(e):Object.keys(e),i=s.length;let a;for(n=0;n<i;n++)a=s[n],t.call(null,e[a],a,e)}}function Lc(e,t){t=t.toLowerCase();const r=Object.keys(e);let n=r.length,o;for(;n-- >0;)if(o=r[n],t===o.toLowerCase())return o;return null}const Gt=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,Uc=e=>!jr(e)&&e!==Gt;function Ss(){const{caseless:e}=Uc(this)&&this||{},t={},r=(n,o)=>{const s=e&&Lc(t,o)||o;nn(t[s])&&nn(n)?t[s]=Ss(t[s],n):nn(n)?t[s]=Ss({},n):mr(n)?t[s]=n.slice():t[s]=n};for(let n=0,o=arguments.length;n<o;n++)arguments[n]&&Wr(arguments[n],r);return t}const Fh=(e,t,r,{allOwnKeys:n}={})=>(Wr(t,(o,s)=>{r&&rt(o)?e[s]=Mc(o,r):e[s]=o},{allOwnKeys:n}),e),Bh=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),Lh=(e,t,r,n)=>{e.prototype=Object.create(t.prototype,n),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),r&&Object.assign(e.prototype,r)},Uh=(e,t,r,n)=>{let o,s,i;const a={};if(t=t||{},e==null)return t;do{for(o=Object.getOwnPropertyNames(e),s=o.length;s-- >0;)i=o[s],(!n||n(i,e,t))&&!a[i]&&(t[i]=e[i],a[i]=!0);e=r!==!1&&Zs(e)}while(e&&(!r||r(e,t))&&e!==Object.prototype);return t},qh=(e,t,r)=>{e=String(e),(r===void 0||r>e.length)&&(r=e.length),r-=t.length;const n=e.indexOf(t,r);return n!==-1&&n===r},Hh=e=>{if(!e)return null;if(mr(e))return e;let t=e.length;if(!Bc(t))return null;const r=new Array(t);for(;t-- >0;)r[t]=e[t];return r},jh=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&Zs(Uint8Array)),$h=(e,t)=>{const n=(e&&e[Symbol.iterator]).call(e);let o;for(;(o=n.next())&&!o.done;){const s=o.value;t.call(e,s[0],s[1])}},Gh=(e,t)=>{let r;const n=[];for(;(r=e.exec(t))!==null;)n.push(r);return n},Wh=ut("HTMLFormElement"),Kh=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(r,n,o){return n.toUpperCase()+o}),Ga=(({hasOwnProperty:e})=>(t,r)=>e.call(t,r))(Object.prototype),Vh=ut("RegExp"),qc=(e,t)=>{const r=Object.getOwnPropertyDescriptors(e),n={};Wr(r,(o,s)=>{let i;(i=t(o,s,e))!==!1&&(n[s]=i||o)}),Object.defineProperties(e,n)},zh=e=>{qc(e,(t,r)=>{if(rt(e)&&["arguments","caller","callee"].indexOf(r)!==-1)return!1;const n=e[r];if(rt(n)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+r+"'")})}})},Qh=(e,t)=>{const r={},n=o=>{o.forEach(s=>{r[s]=!0})};return mr(e)?n(e):n(String(e).split(t)),r},Jh=()=>{},Xh=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function Zh(e){return!!(e&&rt(e.append)&&e[Symbol.toStringTag]==="FormData"&&e[Symbol.iterator])}const Yh=e=>{const t=new Array(10),r=(n,o)=>{if(qn(n)){if(t.indexOf(n)>=0)return;if(!("toJSON"in n)){t[o]=n;const s=mr(n)?[]:{};return Wr(n,(i,a)=>{const l=r(i,o+1);!jr(l)&&(s[a]=l)}),t[o]=void 0,s}}return n};return r(e,0)},em=ut("AsyncFunction"),tm=e=>e&&(qn(e)||rt(e))&&rt(e.then)&&rt(e.catch),Hc=((e,t)=>e?setImmediate:t?((r,n)=>(Gt.addEventListener("message",({source:o,data:s})=>{o===Gt&&s===r&&n.length&&n.shift()()},!1),o=>{n.push(o),Gt.postMessage(r,"*")}))(`axios@${Math.random()}`,[]):r=>setTimeout(r))(typeof setImmediate=="function",rt(Gt.postMessage)),rm=typeof queueMicrotask<"u"?queueMicrotask.bind(Gt):typeof process<"u"&&process.nextTick||Hc,O={isArray:mr,isArrayBuffer:Fc,isBuffer:Ah,isFormData:Ch,isArrayBufferView:wh,isString:Sh,isNumber:Bc,isBoolean:Eh,isObject:qn,isPlainObject:nn,isReadableStream:Ih,isRequest:kh,isResponse:Dh,isHeaders:Nh,isUndefined:jr,isDate:_h,isFile:Rh,isBlob:Oh,isRegExp:Vh,isFunction:rt,isStream:Ph,isURLSearchParams:Th,isTypedArray:jh,isFileList:xh,forEach:Wr,merge:Ss,extend:Fh,trim:Mh,stripBOM:Bh,inherits:Lh,toFlatObject:Uh,kindOf:Ln,kindOfTest:ut,endsWith:qh,toArray:Hh,forEachEntry:$h,matchAll:Gh,isHTMLForm:Wh,hasOwnProperty:Ga,hasOwnProp:Ga,reduceDescriptors:qc,freezeMethods:zh,toObjectSet:Qh,toCamelCase:Kh,noop:Jh,toFiniteNumber:Xh,findKey:Lc,global:Gt,isContextDefined:Uc,isSpecCompliantForm:Zh,toJSONObject:Yh,isAsyncFn:em,isThenable:tm,setImmediate:Hc,asap:rm};function Y(e,t,r,n,o){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),r&&(this.config=r),n&&(this.request=n),o&&(this.response=o,this.status=o.status?o.status:null)}O.inherits(Y,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:O.toJSONObject(this.config),code:this.code,status:this.status}}});const jc=Y.prototype,$c={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{$c[e]={value:e}});Object.defineProperties(Y,$c);Object.defineProperty(jc,"isAxiosError",{value:!0});Y.from=(e,t,r,n,o,s)=>{const i=Object.create(jc);return O.toFlatObject(e,i,function(l){return l!==Error.prototype},a=>a!=="isAxiosError"),Y.call(i,e.message,t,r,n,o),i.cause=e,i.name=e.name,s&&Object.assign(i,s),i};const nm=null;function Es(e){return O.isPlainObject(e)||O.isArray(e)}function Gc(e){return O.endsWith(e,"[]")?e.slice(0,-2):e}function Wa(e,t,r){return e?e.concat(t).map(function(o,s){return o=Gc(o),!r&&s?"["+o+"]":o}).join(r?".":""):t}function om(e){return O.isArray(e)&&!e.some(Es)}const sm=O.toFlatObject(O,{},null,function(t){return/^is[A-Z]/.test(t)});function Hn(e,t,r){if(!O.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,r=O.toFlatObject(r,{metaTokens:!0,dots:!1,indexes:!1},!1,function(w,y){return!O.isUndefined(y[w])});const n=r.metaTokens,o=r.visitor||c,s=r.dots,i=r.indexes,l=(r.Blob||typeof Blob<"u"&&Blob)&&O.isSpecCompliantForm(t);if(!O.isFunction(o))throw new TypeError("visitor must be a function");function u(g){if(g===null)return"";if(O.isDate(g))return g.toISOString();if(!l&&O.isBlob(g))throw new Y("Blob is not supported. Use a Buffer instead.");return O.isArrayBuffer(g)||O.isTypedArray(g)?l&&typeof Blob=="function"?new Blob([g]):Buffer.from(g):g}function c(g,w,y){let v=g;if(g&&!y&&typeof g=="object"){if(O.endsWith(w,"{}"))w=n?w:w.slice(0,-2),g=JSON.stringify(g);else if(O.isArray(g)&&om(g)||(O.isFileList(g)||O.endsWith(w,"[]"))&&(v=O.toArray(g)))return w=Gc(w),v.forEach(function(R,_){!(O.isUndefined(R)||R===null)&&t.append(i===!0?Wa([w],_,s):i===null?w:w+"[]",u(R))}),!1}return Es(g)?!0:(t.append(Wa(y,w,s),u(g)),!1)}const f=[],h=Object.assign(sm,{defaultVisitor:c,convertValue:u,isVisitable:Es});function d(g,w){if(!O.isUndefined(g)){if(f.indexOf(g)!==-1)throw Error("Circular reference detected in "+w.join("."));f.push(g),O.forEach(g,function(v,b){(!(O.isUndefined(v)||v===null)&&o.call(t,v,O.isString(b)?b.trim():b,w,h))===!0&&d(v,w?w.concat(b):[b])}),f.pop()}}if(!O.isObject(e))throw new TypeError("data must be an object");return d(e),t}function Ka(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(n){return t[n]})}function Ys(e,t){this._pairs=[],e&&Hn(e,this,t)}const Wc=Ys.prototype;Wc.append=function(t,r){this._pairs.push([t,r])};Wc.toString=function(t){const r=t?function(n){return t.call(this,n,Ka)}:Ka;return this._pairs.map(function(o){return r(o[0])+"="+r(o[1])},"").join("&")};function im(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Kc(e,t,r){if(!t)return e;const n=r&&r.encode||im;O.isFunction(r)&&(r={serialize:r});const o=r&&r.serialize;let s;if(o?s=o(t,r):s=O.isURLSearchParams(t)?t.toString():new Ys(t,r).toString(n),s){const i=e.indexOf("#");i!==-1&&(e=e.slice(0,i)),e+=(e.indexOf("?")===-1?"?":"&")+s}return e}class Va{constructor(){this.handlers=[]}use(t,r,n){return this.handlers.push({fulfilled:t,rejected:r,synchronous:n?n.synchronous:!1,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){O.forEach(this.handlers,function(n){n!==null&&t(n)})}}const Vc={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},am=typeof URLSearchParams<"u"?URLSearchParams:Ys,lm=typeof FormData<"u"?FormData:null,cm=typeof Blob<"u"?Blob:null,um={isBrowser:!0,classes:{URLSearchParams:am,FormData:lm,Blob:cm},protocols:["http","https","file","blob","url","data"]},ei=typeof window<"u"&&typeof document<"u",_s=typeof navigator=="object"&&navigator||void 0,fm=ei&&(!_s||["ReactNative","NativeScript","NS"].indexOf(_s.product)<0),dm=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",pm=ei&&window.location.href||"http://localhost",hm=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:ei,hasStandardBrowserEnv:fm,hasStandardBrowserWebWorkerEnv:dm,navigator:_s,origin:pm},Symbol.toStringTag,{value:"Module"})),$e={...hm,...um};function mm(e,t){return Hn(e,new $e.classes.URLSearchParams,Object.assign({visitor:function(r,n,o,s){return $e.isNode&&O.isBuffer(r)?(this.append(n,r.toString("base64")),!1):s.defaultVisitor.apply(this,arguments)}},t))}function ym(e){return O.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function gm(e){const t={},r=Object.keys(e);let n;const o=r.length;let s;for(n=0;n<o;n++)s=r[n],t[s]=e[s];return t}function zc(e){function t(r,n,o,s){let i=r[s++];if(i==="__proto__")return!0;const a=Number.isFinite(+i),l=s>=r.length;return i=!i&&O.isArray(o)?o.length:i,l?(O.hasOwnProp(o,i)?o[i]=[o[i],n]:o[i]=n,!a):((!o[i]||!O.isObject(o[i]))&&(o[i]=[]),t(r,n,o[i],s)&&O.isArray(o[i])&&(o[i]=gm(o[i])),!a)}if(O.isFormData(e)&&O.isFunction(e.entries)){const r={};return O.forEachEntry(e,(n,o)=>{t(ym(n),o,r,0)}),r}return null}function vm(e,t,r){if(O.isString(e))try{return(t||JSON.parse)(e),O.trim(e)}catch(n){if(n.name!=="SyntaxError")throw n}return(r||JSON.stringify)(e)}const Kr={transitional:Vc,adapter:["xhr","http","fetch"],transformRequest:[function(t,r){const n=r.getContentType()||"",o=n.indexOf("application/json")>-1,s=O.isObject(t);if(s&&O.isHTMLForm(t)&&(t=new FormData(t)),O.isFormData(t))return o?JSON.stringify(zc(t)):t;if(O.isArrayBuffer(t)||O.isBuffer(t)||O.isStream(t)||O.isFile(t)||O.isBlob(t)||O.isReadableStream(t))return t;if(O.isArrayBufferView(t))return t.buffer;if(O.isURLSearchParams(t))return r.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let a;if(s){if(n.indexOf("application/x-www-form-urlencoded")>-1)return mm(t,this.formSerializer).toString();if((a=O.isFileList(t))||n.indexOf("multipart/form-data")>-1){const l=this.env&&this.env.FormData;return Hn(a?{"files[]":t}:t,l&&new l,this.formSerializer)}}return s||o?(r.setContentType("application/json",!1),vm(t)):t}],transformResponse:[function(t){const r=this.transitional||Kr.transitional,n=r&&r.forcedJSONParsing,o=this.responseType==="json";if(O.isResponse(t)||O.isReadableStream(t))return t;if(t&&O.isString(t)&&(n&&!this.responseType||o)){const i=!(r&&r.silentJSONParsing)&&o;try{return JSON.parse(t)}catch(a){if(i)throw a.name==="SyntaxError"?Y.from(a,Y.ERR_BAD_RESPONSE,this,null,this.response):a}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:$e.classes.FormData,Blob:$e.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};O.forEach(["delete","get","head","post","put","patch"],e=>{Kr.headers[e]={}});const bm=O.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Am=e=>{const t={};let r,n,o;return e&&e.split(`
`).forEach(function(i){o=i.indexOf(":"),r=i.substring(0,o).trim().toLowerCase(),n=i.substring(o+1).trim(),!(!r||t[r]&&bm[r])&&(r==="set-cookie"?t[r]?t[r].push(n):t[r]=[n]:t[r]=t[r]?t[r]+", "+n:n)}),t},za=Symbol("internals");function Sr(e){return e&&String(e).trim().toLowerCase()}function on(e){return e===!1||e==null?e:O.isArray(e)?e.map(on):String(e)}function wm(e){const t=Object.create(null),r=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let n;for(;n=r.exec(e);)t[n[1]]=n[2];return t}const Sm=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function os(e,t,r,n,o){if(O.isFunction(n))return n.call(this,t,r);if(o&&(t=r),!!O.isString(t)){if(O.isString(n))return t.indexOf(n)!==-1;if(O.isRegExp(n))return n.test(t)}}function Em(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,r,n)=>r.toUpperCase()+n)}function _m(e,t){const r=O.toCamelCase(" "+t);["get","set","has"].forEach(n=>{Object.defineProperty(e,n+r,{value:function(o,s,i){return this[n].call(this,t,o,s,i)},configurable:!0})})}let Je=class{constructor(t){t&&this.set(t)}set(t,r,n){const o=this;function s(a,l,u){const c=Sr(l);if(!c)throw new Error("header name must be a non-empty string");const f=O.findKey(o,c);(!f||o[f]===void 0||u===!0||u===void 0&&o[f]!==!1)&&(o[f||l]=on(a))}const i=(a,l)=>O.forEach(a,(u,c)=>s(u,c,l));if(O.isPlainObject(t)||t instanceof this.constructor)i(t,r);else if(O.isString(t)&&(t=t.trim())&&!Sm(t))i(Am(t),r);else if(O.isHeaders(t))for(const[a,l]of t.entries())s(l,a,n);else t!=null&&s(r,t,n);return this}get(t,r){if(t=Sr(t),t){const n=O.findKey(this,t);if(n){const o=this[n];if(!r)return o;if(r===!0)return wm(o);if(O.isFunction(r))return r.call(this,o,n);if(O.isRegExp(r))return r.exec(o);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,r){if(t=Sr(t),t){const n=O.findKey(this,t);return!!(n&&this[n]!==void 0&&(!r||os(this,this[n],n,r)))}return!1}delete(t,r){const n=this;let o=!1;function s(i){if(i=Sr(i),i){const a=O.findKey(n,i);a&&(!r||os(n,n[a],a,r))&&(delete n[a],o=!0)}}return O.isArray(t)?t.forEach(s):s(t),o}clear(t){const r=Object.keys(this);let n=r.length,o=!1;for(;n--;){const s=r[n];(!t||os(this,this[s],s,t,!0))&&(delete this[s],o=!0)}return o}normalize(t){const r=this,n={};return O.forEach(this,(o,s)=>{const i=O.findKey(n,s);if(i){r[i]=on(o),delete r[s];return}const a=t?Em(s):String(s).trim();a!==s&&delete r[s],r[a]=on(o),n[a]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const r=Object.create(null);return O.forEach(this,(n,o)=>{n!=null&&n!==!1&&(r[o]=t&&O.isArray(n)?n.join(", "):n)}),r}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,r])=>t+": "+r).join(`
`)}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...r){const n=new this(t);return r.forEach(o=>n.set(o)),n}static accessor(t){const n=(this[za]=this[za]={accessors:{}}).accessors,o=this.prototype;function s(i){const a=Sr(i);n[a]||(_m(o,i),n[a]=!0)}return O.isArray(t)?t.forEach(s):s(t),this}};Je.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);O.reduceDescriptors(Je.prototype,({value:e},t)=>{let r=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(n){this[r]=n}}});O.freezeMethods(Je);function ss(e,t){const r=this||Kr,n=t||r,o=Je.from(n.headers);let s=n.data;return O.forEach(e,function(a){s=a.call(r,s,o.normalize(),t?t.status:void 0)}),o.normalize(),s}function Qc(e){return!!(e&&e.__CANCEL__)}function yr(e,t,r){Y.call(this,e??"canceled",Y.ERR_CANCELED,t,r),this.name="CanceledError"}O.inherits(yr,Y,{__CANCEL__:!0});function Jc(e,t,r){const n=r.config.validateStatus;!r.status||!n||n(r.status)?e(r):t(new Y("Request failed with status code "+r.status,[Y.ERR_BAD_REQUEST,Y.ERR_BAD_RESPONSE][Math.floor(r.status/100)-4],r.config,r.request,r))}function Rm(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function Om(e,t){e=e||10;const r=new Array(e),n=new Array(e);let o=0,s=0,i;return t=t!==void 0?t:1e3,function(l){const u=Date.now(),c=n[s];i||(i=u),r[o]=l,n[o]=u;let f=s,h=0;for(;f!==o;)h+=r[f++],f=f%e;if(o=(o+1)%e,o===s&&(s=(s+1)%e),u-i<t)return;const d=c&&u-c;return d?Math.round(h*1e3/d):void 0}}function xm(e,t){let r=0,n=1e3/t,o,s;const i=(u,c=Date.now())=>{r=c,o=null,s&&(clearTimeout(s),s=null),e.apply(null,u)};return[(...u)=>{const c=Date.now(),f=c-r;f>=n?i(u,c):(o=u,s||(s=setTimeout(()=>{s=null,i(o)},n-f)))},()=>o&&i(o)]}const gn=(e,t,r=3)=>{let n=0;const o=Om(50,250);return xm(s=>{const i=s.loaded,a=s.lengthComputable?s.total:void 0,l=i-n,u=o(l),c=i<=a;n=i;const f={loaded:i,total:a,progress:a?i/a:void 0,bytes:l,rate:u||void 0,estimated:u&&a&&c?(a-i)/u:void 0,event:s,lengthComputable:a!=null,[t?"download":"upload"]:!0};e(f)},r)},Qa=(e,t)=>{const r=e!=null;return[n=>t[0]({lengthComputable:r,total:e,loaded:n}),t[1]]},Ja=e=>(...t)=>O.asap(()=>e(...t)),Pm=$e.hasStandardBrowserEnv?((e,t)=>r=>(r=new URL(r,$e.origin),e.protocol===r.protocol&&e.host===r.host&&(t||e.port===r.port)))(new URL($e.origin),$e.navigator&&/(msie|trident)/i.test($e.navigator.userAgent)):()=>!0,Cm=$e.hasStandardBrowserEnv?{write(e,t,r,n,o,s){const i=[e+"="+encodeURIComponent(t)];O.isNumber(r)&&i.push("expires="+new Date(r).toGMTString()),O.isString(n)&&i.push("path="+n),O.isString(o)&&i.push("domain="+o),s===!0&&i.push("secure"),document.cookie=i.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function Tm(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function Im(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function Xc(e,t,r){let n=!Tm(t);return e&&n||r==!1?Im(e,t):t}const Xa=e=>e instanceof Je?{...e}:e;function zt(e,t){t=t||{};const r={};function n(u,c,f,h){return O.isPlainObject(u)&&O.isPlainObject(c)?O.merge.call({caseless:h},u,c):O.isPlainObject(c)?O.merge({},c):O.isArray(c)?c.slice():c}function o(u,c,f,h){if(O.isUndefined(c)){if(!O.isUndefined(u))return n(void 0,u,f,h)}else return n(u,c,f,h)}function s(u,c){if(!O.isUndefined(c))return n(void 0,c)}function i(u,c){if(O.isUndefined(c)){if(!O.isUndefined(u))return n(void 0,u)}else return n(void 0,c)}function a(u,c,f){if(f in t)return n(u,c);if(f in e)return n(void 0,u)}const l={url:s,method:s,data:s,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,withXSRFToken:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,beforeRedirect:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:a,headers:(u,c,f)=>o(Xa(u),Xa(c),f,!0)};return O.forEach(Object.keys(Object.assign({},e,t)),function(c){const f=l[c]||o,h=f(e[c],t[c],c);O.isUndefined(h)&&f!==a||(r[c]=h)}),r}const Zc=e=>{const t=zt({},e);let{data:r,withXSRFToken:n,xsrfHeaderName:o,xsrfCookieName:s,headers:i,auth:a}=t;t.headers=i=Je.from(i),t.url=Kc(Xc(t.baseURL,t.url),e.params,e.paramsSerializer),a&&i.set("Authorization","Basic "+btoa((a.username||"")+":"+(a.password?unescape(encodeURIComponent(a.password)):"")));let l;if(O.isFormData(r)){if($e.hasStandardBrowserEnv||$e.hasStandardBrowserWebWorkerEnv)i.setContentType(void 0);else if((l=i.getContentType())!==!1){const[u,...c]=l?l.split(";").map(f=>f.trim()).filter(Boolean):[];i.setContentType([u||"multipart/form-data",...c].join("; "))}}if($e.hasStandardBrowserEnv&&(n&&O.isFunction(n)&&(n=n(t)),n||n!==!1&&Pm(t.url))){const u=o&&s&&Cm.read(s);u&&i.set(o,u)}return t},km=typeof XMLHttpRequest<"u",Dm=km&&function(e){return new Promise(function(r,n){const o=Zc(e);let s=o.data;const i=Je.from(o.headers).normalize();let{responseType:a,onUploadProgress:l,onDownloadProgress:u}=o,c,f,h,d,g;function w(){d&&d(),g&&g(),o.cancelToken&&o.cancelToken.unsubscribe(c),o.signal&&o.signal.removeEventListener("abort",c)}let y=new XMLHttpRequest;y.open(o.method.toUpperCase(),o.url,!0),y.timeout=o.timeout;function v(){if(!y)return;const R=Je.from("getAllResponseHeaders"in y&&y.getAllResponseHeaders()),k={data:!a||a==="text"||a==="json"?y.responseText:y.response,status:y.status,statusText:y.statusText,headers:R,config:e,request:y};Jc(function(B){r(B),w()},function(B){n(B),w()},k),y=null}"onloadend"in y?y.onloadend=v:y.onreadystatechange=function(){!y||y.readyState!==4||y.status===0&&!(y.responseURL&&y.responseURL.indexOf("file:")===0)||setTimeout(v)},y.onabort=function(){y&&(n(new Y("Request aborted",Y.ECONNABORTED,e,y)),y=null)},y.onerror=function(){n(new Y("Network Error",Y.ERR_NETWORK,e,y)),y=null},y.ontimeout=function(){let _=o.timeout?"timeout of "+o.timeout+"ms exceeded":"timeout exceeded";const k=o.transitional||Vc;o.timeoutErrorMessage&&(_=o.timeoutErrorMessage),n(new Y(_,k.clarifyTimeoutError?Y.ETIMEDOUT:Y.ECONNABORTED,e,y)),y=null},s===void 0&&i.setContentType(null),"setRequestHeader"in y&&O.forEach(i.toJSON(),function(_,k){y.setRequestHeader(k,_)}),O.isUndefined(o.withCredentials)||(y.withCredentials=!!o.withCredentials),a&&a!=="json"&&(y.responseType=o.responseType),u&&([h,g]=gn(u,!0),y.addEventListener("progress",h)),l&&y.upload&&([f,d]=gn(l),y.upload.addEventListener("progress",f),y.upload.addEventListener("loadend",d)),(o.cancelToken||o.signal)&&(c=R=>{y&&(n(!R||R.type?new yr(null,e,y):R),y.abort(),y=null)},o.cancelToken&&o.cancelToken.subscribe(c),o.signal&&(o.signal.aborted?c():o.signal.addEventListener("abort",c)));const b=Rm(o.url);if(b&&$e.protocols.indexOf(b)===-1){n(new Y("Unsupported protocol "+b+":",Y.ERR_BAD_REQUEST,e));return}y.send(s||null)})},Nm=(e,t)=>{const{length:r}=e=e?e.filter(Boolean):[];if(t||r){let n=new AbortController,o;const s=function(u){if(!o){o=!0,a();const c=u instanceof Error?u:this.reason;n.abort(c instanceof Y?c:new yr(c instanceof Error?c.message:c))}};let i=t&&setTimeout(()=>{i=null,s(new Y(`timeout ${t} of ms exceeded`,Y.ETIMEDOUT))},t);const a=()=>{e&&(i&&clearTimeout(i),i=null,e.forEach(u=>{u.unsubscribe?u.unsubscribe(s):u.removeEventListener("abort",s)}),e=null)};e.forEach(u=>u.addEventListener("abort",s));const{signal:l}=n;return l.unsubscribe=()=>O.asap(a),l}},Mm=function*(e,t){let r=e.byteLength;if(r<t){yield e;return}let n=0,o;for(;n<r;)o=n+t,yield e.slice(n,o),n=o},Fm=async function*(e,t){for await(const r of Bm(e))yield*Mm(r,t)},Bm=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:r,value:n}=await t.read();if(r)break;yield n}}finally{await t.cancel()}},Za=(e,t,r,n)=>{const o=Fm(e,t);let s=0,i,a=l=>{i||(i=!0,n&&n(l))};return new ReadableStream({async pull(l){try{const{done:u,value:c}=await o.next();if(u){a(),l.close();return}let f=c.byteLength;if(r){let h=s+=f;r(h)}l.enqueue(new Uint8Array(c))}catch(u){throw a(u),u}},cancel(l){return a(l),o.return()}},{highWaterMark:2})},jn=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",Yc=jn&&typeof ReadableStream=="function",Lm=jn&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),eu=(e,...t)=>{try{return!!e(...t)}catch{return!1}},Um=Yc&&eu(()=>{let e=!1;const t=new Request($e.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),Ya=64*1024,Rs=Yc&&eu(()=>O.isReadableStream(new Response("").body)),vn={stream:Rs&&(e=>e.body)};jn&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!vn[t]&&(vn[t]=O.isFunction(e[t])?r=>r[t]():(r,n)=>{throw new Y(`Response type '${t}' is not supported`,Y.ERR_NOT_SUPPORT,n)})})})(new Response);const qm=async e=>{if(e==null)return 0;if(O.isBlob(e))return e.size;if(O.isSpecCompliantForm(e))return(await new Request($e.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(O.isArrayBufferView(e)||O.isArrayBuffer(e))return e.byteLength;if(O.isURLSearchParams(e)&&(e=e+""),O.isString(e))return(await Lm(e)).byteLength},Hm=async(e,t)=>{const r=O.toFiniteNumber(e.getContentLength());return r??qm(t)},jm=jn&&(async e=>{let{url:t,method:r,data:n,signal:o,cancelToken:s,timeout:i,onDownloadProgress:a,onUploadProgress:l,responseType:u,headers:c,withCredentials:f="same-origin",fetchOptions:h}=Zc(e);u=u?(u+"").toLowerCase():"text";let d=Nm([o,s&&s.toAbortSignal()],i),g;const w=d&&d.unsubscribe&&(()=>{d.unsubscribe()});let y;try{if(l&&Um&&r!=="get"&&r!=="head"&&(y=await Hm(c,n))!==0){let k=new Request(t,{method:"POST",body:n,duplex:"half"}),U;if(O.isFormData(n)&&(U=k.headers.get("content-type"))&&c.setContentType(U),k.body){const[B,W]=Qa(y,gn(Ja(l)));n=Za(k.body,Ya,B,W)}}O.isString(f)||(f=f?"include":"omit");const v="credentials"in Request.prototype;g=new Request(t,{...h,signal:d,method:r.toUpperCase(),headers:c.normalize().toJSON(),body:n,duplex:"half",credentials:v?f:void 0});let b=await fetch(g);const R=Rs&&(u==="stream"||u==="response");if(Rs&&(a||R&&w)){const k={};["status","statusText","headers"].forEach(Z=>{k[Z]=b[Z]});const U=O.toFiniteNumber(b.headers.get("content-length")),[B,W]=a&&Qa(U,gn(Ja(a),!0))||[];b=new Response(Za(b.body,Ya,B,()=>{W&&W(),w&&w()}),k)}u=u||"text";let _=await vn[O.findKey(vn,u)||"text"](b,e);return!R&&w&&w(),await new Promise((k,U)=>{Jc(k,U,{data:_,headers:Je.from(b.headers),status:b.status,statusText:b.statusText,config:e,request:g})})}catch(v){throw w&&w(),v&&v.name==="TypeError"&&/fetch/i.test(v.message)?Object.assign(new Y("Network Error",Y.ERR_NETWORK,e,g),{cause:v.cause||v}):Y.from(v,v&&v.code,e,g)}}),Os={http:nm,xhr:Dm,fetch:jm};O.forEach(Os,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const el=e=>`- ${e}`,$m=e=>O.isFunction(e)||e===null||e===!1,tu={getAdapter:e=>{e=O.isArray(e)?e:[e];const{length:t}=e;let r,n;const o={};for(let s=0;s<t;s++){r=e[s];let i;if(n=r,!$m(r)&&(n=Os[(i=String(r)).toLowerCase()],n===void 0))throw new Y(`Unknown adapter '${i}'`);if(n)break;o[i||"#"+s]=n}if(!n){const s=Object.entries(o).map(([a,l])=>`adapter ${a} `+(l===!1?"is not supported by the environment":"is not available in the build"));let i=t?s.length>1?`since :
`+s.map(el).join(`
`):" "+el(s[0]):"as no adapter specified";throw new Y("There is no suitable adapter to dispatch the request "+i,"ERR_NOT_SUPPORT")}return n},adapters:Os};function is(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new yr(null,e)}function tl(e){return is(e),e.headers=Je.from(e.headers),e.data=ss.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),tu.getAdapter(e.adapter||Kr.adapter)(e).then(function(n){return is(e),n.data=ss.call(e,e.transformResponse,n),n.headers=Je.from(n.headers),n},function(n){return Qc(n)||(is(e),n&&n.response&&(n.response.data=ss.call(e,e.transformResponse,n.response),n.response.headers=Je.from(n.response.headers))),Promise.reject(n)})}const ru="1.8.2",$n={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{$n[e]=function(n){return typeof n===e||"a"+(t<1?"n ":" ")+e}});const rl={};$n.transitional=function(t,r,n){function o(s,i){return"[Axios v"+ru+"] Transitional option '"+s+"'"+i+(n?". "+n:"")}return(s,i,a)=>{if(t===!1)throw new Y(o(i," has been removed"+(r?" in "+r:"")),Y.ERR_DEPRECATED);return r&&!rl[i]&&(rl[i]=!0,console.warn(o(i," has been deprecated since v"+r+" and will be removed in the near future"))),t?t(s,i,a):!0}};$n.spelling=function(t){return(r,n)=>(console.warn(`${n} is likely a misspelling of ${t}`),!0)};function Gm(e,t,r){if(typeof e!="object")throw new Y("options must be an object",Y.ERR_BAD_OPTION_VALUE);const n=Object.keys(e);let o=n.length;for(;o-- >0;){const s=n[o],i=t[s];if(i){const a=e[s],l=a===void 0||i(a,s,e);if(l!==!0)throw new Y("option "+s+" must be "+l,Y.ERR_BAD_OPTION_VALUE);continue}if(r!==!0)throw new Y("Unknown option "+s,Y.ERR_BAD_OPTION)}}const sn={assertOptions:Gm,validators:$n},dt=sn.validators;let Kt=class{constructor(t){this.defaults=t,this.interceptors={request:new Va,response:new Va}}async request(t,r){try{return await this._request(t,r)}catch(n){if(n instanceof Error){let o={};Error.captureStackTrace?Error.captureStackTrace(o):o=new Error;const s=o.stack?o.stack.replace(/^.+\n/,""):"";try{n.stack?s&&!String(n.stack).endsWith(s.replace(/^.+\n.+\n/,""))&&(n.stack+=`
`+s):n.stack=s}catch{}}throw n}}_request(t,r){typeof t=="string"?(r=r||{},r.url=t):r=t||{},r=zt(this.defaults,r);const{transitional:n,paramsSerializer:o,headers:s}=r;n!==void 0&&sn.assertOptions(n,{silentJSONParsing:dt.transitional(dt.boolean),forcedJSONParsing:dt.transitional(dt.boolean),clarifyTimeoutError:dt.transitional(dt.boolean)},!1),o!=null&&(O.isFunction(o)?r.paramsSerializer={serialize:o}:sn.assertOptions(o,{encode:dt.function,serialize:dt.function},!0)),r.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?r.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:r.allowAbsoluteUrls=!0),sn.assertOptions(r,{baseUrl:dt.spelling("baseURL"),withXsrfToken:dt.spelling("withXSRFToken")},!0),r.method=(r.method||this.defaults.method||"get").toLowerCase();let i=s&&O.merge(s.common,s[r.method]);s&&O.forEach(["delete","get","head","post","put","patch","common"],g=>{delete s[g]}),r.headers=Je.concat(i,s);const a=[];let l=!0;this.interceptors.request.forEach(function(w){typeof w.runWhen=="function"&&w.runWhen(r)===!1||(l=l&&w.synchronous,a.unshift(w.fulfilled,w.rejected))});const u=[];this.interceptors.response.forEach(function(w){u.push(w.fulfilled,w.rejected)});let c,f=0,h;if(!l){const g=[tl.bind(this),void 0];for(g.unshift.apply(g,a),g.push.apply(g,u),h=g.length,c=Promise.resolve(r);f<h;)c=c.then(g[f++],g[f++]);return c}h=a.length;let d=r;for(f=0;f<h;){const g=a[f++],w=a[f++];try{d=g(d)}catch(y){w.call(this,y);break}}try{c=tl.call(this,d)}catch(g){return Promise.reject(g)}for(f=0,h=u.length;f<h;)c=c.then(u[f++],u[f++]);return c}getUri(t){t=zt(this.defaults,t);const r=Xc(t.baseURL,t.url,t.allowAbsoluteUrls);return Kc(r,t.params,t.paramsSerializer)}};O.forEach(["delete","get","head","options"],function(t){Kt.prototype[t]=function(r,n){return this.request(zt(n||{},{method:t,url:r,data:(n||{}).data}))}});O.forEach(["post","put","patch"],function(t){function r(n){return function(s,i,a){return this.request(zt(a||{},{method:t,headers:n?{"Content-Type":"multipart/form-data"}:{},url:s,data:i}))}}Kt.prototype[t]=r(),Kt.prototype[t+"Form"]=r(!0)});let Wm=class nu{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let r;this.promise=new Promise(function(s){r=s});const n=this;this.promise.then(o=>{if(!n._listeners)return;let s=n._listeners.length;for(;s-- >0;)n._listeners[s](o);n._listeners=null}),this.promise.then=o=>{let s;const i=new Promise(a=>{n.subscribe(a),s=a}).then(o);return i.cancel=function(){n.unsubscribe(s)},i},t(function(s,i,a){n.reason||(n.reason=new yr(s,i,a),r(n.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const r=this._listeners.indexOf(t);r!==-1&&this._listeners.splice(r,1)}toAbortSignal(){const t=new AbortController,r=n=>{t.abort(n)};return this.subscribe(r),t.signal.unsubscribe=()=>this.unsubscribe(r),t.signal}static source(){let t;return{token:new nu(function(o){t=o}),cancel:t}}};function Km(e){return function(r){return e.apply(null,r)}}function Vm(e){return O.isObject(e)&&e.isAxiosError===!0}const xs={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(xs).forEach(([e,t])=>{xs[t]=e});function ou(e){const t=new Kt(e),r=Mc(Kt.prototype.request,t);return O.extend(r,Kt.prototype,t,{allOwnKeys:!0}),O.extend(r,t,null,{allOwnKeys:!0}),r.create=function(o){return ou(zt(e,o))},r}const Ee=ou(Kr);Ee.Axios=Kt;Ee.CanceledError=yr;Ee.CancelToken=Wm;Ee.isCancel=Qc;Ee.VERSION=ru;Ee.toFormData=Hn;Ee.AxiosError=Y;Ee.Cancel=Ee.CanceledError;Ee.all=function(t){return Promise.all(t)};Ee.spread=Km;Ee.isAxiosError=Vm;Ee.mergeConfig=zt;Ee.AxiosHeaders=Je;Ee.formToJSON=e=>zc(O.isHTMLForm(e)?new FormData(e):e);Ee.getAdapter=tu.getAdapter;Ee.HttpStatusCode=xs;Ee.default=Ee;const{Axios:ev,AxiosError:tv,CanceledError:rv,isCancel:nv,CancelToken:ov,VERSION:sv,all:iv,Cancel:av,isAxiosError:lv,spread:cv,toFormData:uv,AxiosHeaders:fv,HttpStatusCode:dv,formToJSON:pv,getAdapter:hv,mergeConfig:mv}=Ee,zm={SUCCESS_CODE:0},Qm="https://m.kedou.com";Ee.defaults.headers.post["Content-Type"]="application/x-www-form-urlencoded";Ee.defaults.baseURL=Qm;Ee.interceptors.request.use(e=>{var n,o,s;const{method:t="get",data:r}=e;switch(e.headers.tockenid=dy(),t.toLocaleUpperCase()){case"POST":case"PUT":if((n=e==null?void 0:e.headers)!=null&&n.isBody)e.data=JSON.stringify(r);else if(((o=e==null?void 0:e.headers)==null?void 0:o["content-type"])==="multipart/form-data"){const i=new FormData;Object.keys(r).map(a=>{i.append(a,r[a])}),e.data=i}break;case"PATCH":case"DElETE":if(((s=e==null?void 0:e.headers)==null?void 0:s["content-type"])==="multipart/form-data"){const i=new FormData;Object.keys(r).map(a=>{i.append(a,r[a])}),e.data=i}break}return e},e=>Promise.reject(e));Ee.interceptors.response.use(e=>{var t;return e.data.code===zm.SUCCESS_CODE||((t=e==null?void 0:e.config)==null?void 0:t.responseType)==="blob",Promise.resolve(e.data)},e=>Promise.reject(e));function Jm(e){return Ee.request(e)}const Xm=async e=>new Promise((t,r)=>{const{callback:n,...o}=e||{};Jm(e).then(s=>{n&&n(s),t(s)}).catch(s=>{r(s)})}),Er="",Zm={},Ym={GetGameRank:Er+"/gameGuess/getGameRank",GetUserStreak:Er+"/gameGuess/getUserCurrentStreak",GetGameData:Er+"/gameGuess/processGameData",GetShareData:Er+"/gameGuess/share",PostReportData:Er+"/bigData/report"},as={post:Zm,get:Ym},dr={};Object.keys(as).forEach(e=>{Object.keys(as[e]).forEach((t,r)=>{dr[t]=async(n={})=>{const{data:o,urlData:s="",...i}=n;let a={url:as[e][t]+s,method:e.toUpperCase(),...i||{}};return e.toUpperCase()==="GET"?a.params=o||{}:a.data=o||{},await Xm(a)}})});const ey="https://static.kedou.com/marketingWeb/static/1.0.0/gameGuess/assets/ai-desk-guess0-DGLZJj3V.png",ty="https://static.kedou.com/marketingWeb/static/1.0.0/gameGuess/assets/ai-desk-guess1-DBUiR5NC.png",ry="https://static.kedou.com/marketingWeb/static/1.0.0/gameGuess/assets/ai-desk-guess2-9ldfLGsv.png",ny="https://static.kedou.com/marketingWeb/static/1.0.0/gameGuess/assets/user-desk-guess0-CzbhSpFd.png",oy="https://static.kedou.com/marketingWeb/static/1.0.0/gameGuess/assets/user-desk-guess1-DGXD8m_0.png",sy="https://static.kedou.com/marketingWeb/static/1.0.0/gameGuess/assets/user-desk-guess2-CyvaGai0.png",iy="https://static.kedou.com/marketingWeb/static/1.0.0/gameGuess/assets/guess-btn0-DDjQq3Ak.png",ay="https://static.kedou.com/marketingWeb/static/1.0.0/gameGuess/assets/guess-btn1-DLhZcC0L.png",ly="https://static.kedou.com/marketingWeb/static/1.0.0/gameGuess/assets/guess-btn2-rm83UsAG.png",su="tockenId",iu="pageInfo",cy=[{id:0,value:0,iconAi:ey,iconBtn:iy,iconUser:ny},{id:1,value:1,iconAi:ty,iconBtn:ay,iconUser:oy},{id:2,value:2,iconAi:ry,iconBtn:ly,iconUser:sy}],uy={0:1,1:2,2:0};function _r(){return vh.parse(window.location.search.split("?")[1])}function fy(e){return sessionStorage.setItem(su,e)}function dy(){return sessionStorage.getItem(su)}function py(e){return sessionStorage.setItem(iu,e)}function pr(){return{channel:"default",...JSON.parse(sessionStorage.getItem(iu)||"{}")}}const hy=async function(e){const t={projectId:"privilege",businessId:"mini",body:[e.moduleId,e.evt,e.id||"",e.pondId||"",pr().userId,"",pr().channel||"default",e.privilegeID||"",e.platform||"",e.strategy||"",e.barId||"",e.landingPageId||"",e.ruleTaskId||"",e.taskStatus||"",e.consumeTime||""].join("	")};dr.PostReportData({data:{...t}})},my="https://static.kedou.com/marketingWeb/static/1.0.0/gameGuess/assets/default-avatar-DOSpWYKo.png",yy="data:image/png;base64,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",gy={className:"home-desk-top"},vy={class:"user left"},by={class:"user-info"},Ay={class:"avatar left-avatar"},wy=["src"],Sy={class:"truncate"},Ey={class:"user-winning"},_y={xmlns:"http://www.w3.org/2000/svg",width:"50",height:"30"},Ry={class:"text stroke",x:"50%",y:"0","text-anchor":"middle","alignment-baseline":"text-before-edge"},Oy={class:"user right"},xy={class:"user-winning"},Py={xmlns:"http://www.w3.org/2000/svg",width:"50",height:"30"},Cy={class:"text stroke",x:"50%",y:"0","text-anchor":"middle","alignment-baseline":"text-before-edge"},Ty={__name:"top",setup(e){const{aiMaxStreak:t,userMaxStreak:r}=Ie("deskProvide"),{baseInfo:n}=Ie("gameProvide");return(o,s)=>{var i,a;return ie(),de("div",gy,[$("div",vy,[$("div",by,[$("div",Ay,[$("img",{src:((i=ne(n))==null?void 0:i.headImg)||ne(yy),alt:""},null,8,wy)]),$("p",Sy,Ue(((a=ne(n))==null?void 0:a.nickName)||"玩家"),1)]),$("div",Ey,[s[0]||(s[0]=$("p",null,"连胜",-1)),(ie(),de("svg",_y,[$("text",Ry,Ue(ne(r)||0),1)]))])]),$("div",Oy,[s[2]||(s[2]=$("div",{class:"user-info"},[$("div",{class:"avatar right-avatar"},[$("img",{src:my,alt:""})]),$("p",null,"小网酱")],-1)),$("div",xy,[s[1]||(s[1]=$("p",null,"连胜",-1)),(ie(),de("svg",Py,[$("text",Cy,Ue(ne(t)),1)]))])])])}}},Iy="https://static.kedou.com/marketingWeb/static/1.0.0/gameGuess/assets/icon-win-C1kBhfx9.png",ky="https://static.kedou.com/marketingWeb/static/1.0.0/gameGuess/assets/icon-lose-DfN8qBoL.png",Dy="https://static.kedou.com/marketingWeb/static/1.0.0/gameGuess/assets/icon-draw-D9YkQLnM.png",Ny="https://static.kedou.com/marketingWeb/static/1.0.0/gameGuess/assets/icon-vs-D0YpTd3M.png",My={className:"home-desk-content"},Fy={key:0,class:"desk-win game-result",src:Iy,alt:""},By={key:1,class:"desk-lose game-result",src:ky,alt:""},Ly={key:2,class:"desk-draw game-result",src:Dy,alt:""},Uy={key:3,class:"desk-vs",src:Ny,alt:""},qy={class:"desk-game"},Hy={class:"desk-ai"},jy={class:"ai-guess"},$y=["src"],Gy={class:"desk-user"},Wy={class:"user-guess"},Ky=["src"],Vy={__name:"content",setup(e){const{aiPlay:t,aiGuess:r,userGuess:n,guessResult:o}=Ie("deskProvide"),{guessOptions:s,gamePlay:i}=Ie("gameProvide");return Bt(()=>{}),(a,l)=>(ie(),de("div",My,[!ne(i)&&ne(o)===1?(ie(),de("img",Fy)):It("",!0),!ne(i)&&ne(o)===0?(ie(),de("img",By)):It("",!0),!ne(i)&&ne(o)===2?(ie(),de("img",Ly)):It("",!0),!ne(t)&&typeof ne(r)!="number"&&typeof ne(o)!="number"?(ie(),de("img",Uy)):It("",!0),$("div",qy,[$("div",Hy,[$("div",jy,[(ie(!0),de(Fe,null,pn(ne(s),u=>(ie(),de("img",{key:`aiGuess${u.value}`,class:yt({[`guess${u.value}`]:ne(t),"guess-selete":ne(r)===u.value}),src:u.iconAi,alt:""},null,10,$y))),128))]),l[0]||(l[0]=$("div",{class:"ai-name"},"小网酱出拳",-1))]),$("div",Gy,[l[1]||(l[1]=$("div",{class:"user-name"},"请您出拳",-1)),$("div",Wy,[(ie(!0),de(Fe,null,pn(ne(s),u=>(ie(),de("img",{key:`userGuess${u.value}`,class:yt({"guess-selete":ne(n)===u.value}),src:u.iconUser,alt:""},null,10,Ky))),128))])])])]))}},zy={className:"home-desk-bottom"},Qy={class:"guess-button flex justify-center"},Jy=["onClick"],Xy=["src"],Zy={key:1,class:"game-time"},Yy={__name:"bottom",setup(e){const{userGuess:t,changeAiState:r,changeAiGuess:n,changeUserGuess:o,getGameResult:s}=Ie("deskProvide"),{guessOptions:i,gamePlay:a,changeGameState:l,baseInfo:u}=Ie("gameProvide"),c=xe(!0),f=xe(0),h=()=>{c.value=!1,r(),o(),l(),setTimeout(()=>{const g=Math.floor(Math.random()*3);r(),n(g)},10);const d=setInterval(()=>{var g;f.value-=.01,f.value<=0&&(l(),clearInterval(d),f.value=0+(((g=u==null?void 0:u.value)==null?void 0:g.countDownTime)||2),s())},10)};return Bt(()=>{}),lt(u,d=>{f.value=0+((d==null?void 0:d.countDownTime)||2)}),(d,g)=>(ie(),de("div",zy,[$("div",Qy,[(ie(!0),de(Fe,null,pn(ne(i),w=>(ie(),de("div",{class:yt(["btn relative",{disabled:!ne(a),selete:ne(t)===w.value}]),key:`guessBtn${w.value}`,onClick:y=>ne(o)(w.value)},[$("img",{src:w.iconBtn,alt:""},null,8,Xy)],10,Jy))),128))]),ne(a)?(ie(),de("div",Zy," 剩余时间："+Ue(f.value.toFixed(2))+" 秒 ",1)):(ie(),de(Fe,{key:0},[$("div",{class:yt(["start-button",{first:c.value}]),onClick:h},Ue(c.value?"开始猜拳":"开始新回合"),3),g[0]||(g[0]=$("p",{class:"tips"},"点击开始，【小网酱】先出拳，2秒内请出拳",-1))],64))]))}},Vr=(e,t)=>{const r=e.__vccOpts||e;for(const[n,o]of t)r[n]=o;return r},eg={__name:"index",props:["maskClass","maskClick","position","customClass"],setup(e){return(t,r)=>(ie(),de("div",null,[$("div",{class:yt(["sw-modal-mask",e.maskClass]),onClick:r[0]||(r[0]=(...n)=>e.maskClick&&e.maskClick(...n))},null,2),$("div",{class:yt(["sw-modal-content",[e.position,e.customClass]])},[en(t.$slots,"content",{},void 0)],2)]))}},au=Vr(eg,[["__scopeId","data-v-eb494d05"]]),tg="https://static.kedou.com/marketingWeb/static/1.0.0/gameGuess/assets/share-dialog-BaQ9sHYA.png",rg="https://static.kedou.com/marketingWeb/static/1.0.0/gameGuess/assets/win-dialog-BXjg6a-M.png",ng={class:"draw-dialog"},og=["src"],sg={__name:"DrawDialog",setup(e){const{drawShow:t,changeDrawShow:r}=Ie("gameProvide"),n=()=>{var o;wx.miniProgram.navigateTo({url:`/pages/main/main?channel=${(o=pr())==null?void 0:o.channel}`})};return(o,s)=>(ie(),Br(au,{position:"center",maskClick:()=>ne(r)(!1),customClass:"draw-dialog-box"},{content:Hs(()=>[$("div",ng,[$("img",{onClick:s[0]||(s[0]=()=>ne(r)(!1)),src:ne(t)==="share"?ne(tg):ne(rg),alt:""},null,8,og),$("div",{class:"draw-button",onClick:n})])]),_:1},8,["maskClick"]))}},ig=Vr(sg,[["__scopeId","data-v-52580bfa"]]),ag={className:"home-desk-box"},lg={__name:"index",setup(e){const{gamePlay:t,baseInfo:r,drawShow:n,changeDrawShow:o,getBaseInfo:s}=Ie("gameProvide"),i=xe(!1),a=xe(),l=xe(),u=xe(),c=xe(0),f=xe(0),h=()=>{i.value=!i.value},d=v=>{a.value=v},g=v=>{l.value=v},w=()=>{if(typeof l.value!="number"){u.value=0,c.value+=1;return}if(l.value===a.value){u.value=2;return}uy[l.value]===a.value?(u.value=1,c.value=0):(u.value=0,c.value+=1)},y=async()=>{var v;await dr.GetGameData({data:{userChoice:l.value,robotChoice:a.value,channel:(v=pr())==null?void 0:v.channel}}).then(b=>{var R;f.value=((R=b==null?void 0:b.data)==null?void 0:R.currentStreak)||0}),await s()};return Bt(()=>{}),lt(t,v=>{v||y()}),lt(r,v=>{f.value=(v==null?void 0:v.currentStreak)||0}),lt(f,v=>{v&&v%10===0&&o("win")}),lr("deskProvide",{aiPlay:i,aiGuess:a,userGuess:l,guessResult:u,aiMaxStreak:c,userMaxStreak:f,changeAiState:h,changeAiGuess:d,changeUserGuess:g,getGameResult:w}),(v,b)=>(ie(),de("div",ag,[js(Se(ig,null,null,512),[[Ks,ne(n)]]),Se(Ty),Se(Vy),Se(Yy)]))}},cg="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGAAAAB+CAYAAADbRBTTAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAYKADAAQAAAABAAAAfgAAAAB243atAAAOJElEQVR4Ae2de6wdRR3He8urNIVCkZc8KrSFYhQhGsRHlIDgPxCVoAVBHoJoohKjpoREQxCMGCSoBMVGVAj4AoyIUCpEMBIFrEB5Sil4y7Mgr1JaKC29fr7nzpzO7tnZndmz59xze+eXfM/O/OY3v5n9/nZnZh/nnKFJDcnIyMjbcLUTmA6mgM3BpiTr2Zk3wErw/NDQ0AtN7NxQt04gfnt87Au26dbXOKu/iv4uJRAvddPvrgIA+TNpfA7oyk83OzDGdUdofxlBGK7bj9rEGfL3qdvwJlbv0bpBmFyHCDPs6MhPMsrAbDiZUYeMWgGgIY35tc+eOh0d8DriotZoEB0AIq3VzkSbcEPiv43hJsS2bRMdAGpqqZmkmIFobuoEYLvitpMWBnQNFCV1ArBVVAsTy1gXoFESFQDGuM3wvqld4UYRVmG8ueGowmxjcVQAqBYd4Y1NTZhUFEcpAM0fFykAzXMa5TEFIIqu5o1TAJrnNMpjCkAUXc0bpwA0z2mUxxSAKLqaN+5NALjA2JK+xi5bq3ZPj/X+BdZ5DNeivxO84ikvU6uO6spHkahNtf2/osIudJMNV0EuYgiNimxA6yJ/CQ8yRNTdIB8EEfdvyl9lew+ICYJs7zF1F5POB0Ft3W3avo90I8938WMlmKuxCoAlf4N6bIhyg2DJX23K9UA8NAiWfNWR7zVs3CBY8hVYlasPS0CTQRj4AKwyOy4OWuIEQaTryG+Rb4pFlAh9zeZLtquNbduEvA2C6uvIb5FvDcgrCHrI3pQEByD4qRbjmp746CF8U/I4O/5Y3hntoB7Sw+6MoJ+LYo+M0p95Ch8P+4uzJfiehWbvrLar3HLaXxriYayGIPVtb7PjmX42QL787Y7v/TKOPZkekK+Wgs+AsQyAOloYBBVYgaCYI99W07YyCD0iX22PmwCos7tBxBZK5AW9dmSXvD4ivzM+ti6yN23uVlTWgK7ZANBZzRW6DmhatNpZzLCTX4K22kGvVwHd1VFM+3a183pRJdOmuzoqMqur29JwVlk/dAhSRIMn7MpWRw0s+VqheAWitGKJDYIlP7PayTeCb7s6yl8n5E1j8+Iq6CyICUBsJ8rsveRz5Oi1l4xEBsFLvsd3r4Iw0AHQ+5QdRz4EzYX5A9lqWZgRE4T/ZpTFmWFjmyk1PuW7Y3Vk+rIsU6H7zEAHYC5E6K3qtpAX+Xad37E6olxnxux2BX9iFrY7usXk3XV+x+qIcvVF7TcpAx0AvV2ho7EVBLYu+ZaEdhAoF6HvASFDpmz2N3UmsXXJt77bQaBcfTgQqE9NSlAAgiZWOqkOdozNDfT2LXy8CHYq8bXClIeQ77rR7YXnQdkyVuU7gKbJx+WkFxjadP+qVELf8QmKZmlLxYXa8TLyVauMwGKvo1oFrKpuVdtl/qvKgjgLPaqCnFX1aIKVB3FWGQCGH50loWfKBOO4dHf1llwlb5UBoImgSJZ2ZeIWVnKXAtDbgyMFoLf8VnqvDEDlGEUTlU4qu9GsgR7W6GmZbmcIelKmG4V6bV53Pgepv5V9GS8BsGt6vcGg9XXreS/pDmHim4pSy0theodBfxXjPgA62lcA3TvSrelKMfd1hjEcJhi6yNKj1GlgLGRcB2ANjN0HobUfllP3RYJwB352B2Pxzc7uAkDng+9rN3x4vWTIL3xQE9MWQdBZ9CT7ojcidD+p8OlbjM8I2yni0PShsFrVMlSTW9D9okLv9ZS6P6NXR7om320efy+TvxO86ep7nBZ3pU8SqwJQeQo1vAMabh4oO2K6aQ+/ejy5BGhS75eUcjhIAdARfy8k6Q5pzwT/enPukZ410Ol43ATgMcgJWul07mOchnaeosbKuFq1rcdFADQ0iJR+yqN9amxcBECvKWq10jehPU3KWm31WgY+ACJeK5+xkOf60OjAB+BljkbvrYUeE9T0lzOKulsvAFxAaIVUuoYtaq2Grsn38qOaJ/C6mVf7SjuwMb0l511tegtwXhq5wMZDzApfHQyp2JBNP1ZeXi4HIQA6CsdS+tG+NwBlt6O9lRpma+uyU7ThtorclR2ERfZ1dF4uByEA766zR+OsjjcAZdH3VhpnOz8I3fVymQLQn/CkAPSHZ28rKQBeavpTUCsAfb030x8exqwVL5dlc0A/LlDGjJE+N+zlsiwA/bpf3mcuxqQ5L5dlARirO5RjwlCPG/Vy6Q0AN6p0k6zXN6p6vN8D4V6/i+G94egNgOm6np16J5CB2L3B7oS4E4deKQ0AkdNTo349uvN2chwX6I8dxKFXgt754WbZTDxM5L8q8RLoKdCRL/KXe8rb6qAAyJogbM9Gr/el/w4QIX7RvPlI1ZFvqwcHwFYgEPq2pH3zWFd4ZXdUbbVNeavHqVrna6nZ2N9bbcqEpX1LDCQGEgOJgcRAYiAxkBhIDCQGEgOJgcRAYiAxkBhIDCQGEgOJgcRAYiAxkBhIDCQGEgOJgcRAYiAxkBhIDCQGEgOJgcRAYqC/DPD+p/dbff3tSXxr9F0/fbAdKH31Pt5ztgb+e/GvG6ON4Pwq8Ax4X7ZZfw7bPcEnwI/AoX7L3pbQ9iVAMtvXEmULwQO2nPQScJvNV22xnQmeBleArgLRerMZJ0fS6FzTsH4jaB7QG7+HUnYIW1duIrMdOB7saaDvD9jX1vXnaXqV/a+gLfjZgcwpbUV3iRt5/fuhLlzoT4F2derPIK13+ivF7Mf1GO4CrqMfrV95RH8c+SMqHWw0OIu6K+yr5Z9Cf7QpE7n6XU39jORZRuduVpB5O/gSEMl/B1eBJ8AS8DCOVT8v6vAFeWXNvPrQTQBqNQvJCtwioB8YOYP9vNZxdBDpk518VfJ7GIwGAEenkjmVBj7H9grwTXQXkdd3Ad4knflLQPTz0Uu+SNmy0WTl5zAWZUfIwZR/BywA14AyaQ8fZUZNlrHPB+Dvj0Bn+/ns98U5/zpYz87pyrKvZQppYA54BVxtC0i/BdRoRtDNBxLvOJupEJDB10lyiEQPU9TZDxxmcB1byYlAugPAsWCdA5IjL9pukdZvS0tcm4VO+TzKVoP14GtW38TWzgH74OwWsC14lka+a5xPZquds3mp/wymKoH8nLI1o8mOz/s5Ss7s0PoVOr0ldX5I7+vUO61Ve+PH5SYpInVmXbmxaNJRpPPfDtKX6a5zbB5Umv2TnYZbfRPmGPapFRj0mjf+Am4F89EHzSHYZqQVADT6IvHOYC04DrjyDjKnO4ph0juZvFYANhgaF6eD202Z/tEiRroJgOage0xj89h+BJwDtF/LIecOtkJLIG8xib1M1m6ewO4Um7FbEYucQH466YekJ6+gKKDvBb+RDdvuBKeaWDOCzjcEXUNZ5uuX5G8EKzMOIjLUvQxI9o6o1mFK/ZBl6GLs8kPQvR3OPArqfgtItJxVhKaCGZHQwbrxC3ZE8Rm3PTklr0jvSFpH1e5gN/ArMAvcD4KE+h/HcE6FsY4myaexXz2aLP18kj67Q4bXGH8aSqc4BsrnZTJ29mxW2Tr8ayWYEWw+huIcoB99PUlHP3IhaQ1TMbIU431bQxAOTiTzSaBhyGIaackHDXTEPwl0Kh8AfgxC5WQMjw00Pj/Q7mbsggKA3eHgppzf/M8Wawh1A38e+W+7deDpMPLXAgXwQsjXECcRJ27wWkrn4/2k9RXfhcDOcStU3goAW43pM8HT4D6zVfoyoB09msZanTPR1jr/UhAqP8SwbGmpoF8C/gG2ASLjNPAK8IndeV/5ZvRVO/5OIL8/cAy13N7CySspYi53dLc7aY37x5P/BdjS6DVftgRuVM+ta0pGN9TVklUBOBfbf2YKyzJUzMwB5PWXHMPg1/l66GrPAdT9MpCcDm5ppUZG7ESfb8qbp94e4AZTf5XZ2iOuXQ991ByA/ZlgA9Ay9HogOaPtsCKB7cWtGiMjH8ib2jOgrcdQk4M9Oi4kvQydLsisHE5iWk6nst8DLcnqiIYnrSS0xP1MrAP6cp6pN8epqzH2T+AGRxedxLfOzvngTaAV4lbgSNCIdAQArxpXP5rz/rNc3pfVxHyBr7BIzw6+C/2HwF2cnroBWGRWpdsfg13BH8xWR9o8/C1jq+FjRzZaMluxB5jNa6tJ2A4vLT31dRfgOfTHSEH6VtKhc1nLR9VHUQBU5w2giTlGND5GCTszRIWfAm3PiaqcNf4K2RUiDJ+aSxQAVzQHzHYVpIsm4bWOjTjQP/O1iHf0jSZ9AVjPzlwd0xI7fmmMvbH9PNsPg0W0tzC0Pm3NwL5NIOknKuouoNwdRk8inz8L5E9nkJV1NtHLrS8AeqhxU2TDWr0EC/41zF0E1oNvBFccNVxEfd11DTpLscsMi9TVcnKvXJu6rvhCTtfzrC8AG2j50cjWDwm1h4CjsNWkraPwBHb8wdC6xk4TY2t4iKw3cOa+AKyFlK/G9BZSP1tlj40mOY3X3wca90X+b9m68pbJTHWVNo2P7UnrqnyR1Y3nrS8A+teHMyJ3bIrPHl9agWi4OBvMBKvAaZCvsyAvzxrFAur9jbQ7MeoK9Aig4N0CimQHo7SBLLLpt26aabB6XmGnbwMS9954SFp1dBXdIeivVCEiPz8B3ossyg4C+hPOMtEz3PakSlpz1kqgVZBEzzV8B5eWpVEXYu4OUVfPFiTeA5Sym4EuBNWPV4FEfdvW9aV0UScfRz+Fo/PgvHFZHueatDuuOk0dTbI6anUp/h+jK9xQfhe+9qRQl+4abnTUu6IzZCl2ujBqCenXqXMumTngGfA7dJrcfbKMgtedwsdIr3TyZck1FKqN1SVGv6RMZ73OwjfAcqDb1q+yTZIYSAwkBhIDiYEWA/8HIvLirpVXKFcAAAAASUVORK5CYII=",ug={className:"home-rank-box"},fg={class:"rank-mine rank"},dg={class:"light"},pg={class:"rank-item"},hg={class:"num"},mg={class:"name light truncate"},yg={class:"times"},gg={key:1,class:"rank-list rank"},vg={class:"num"},bg={class:"name truncate"},Ag={class:"times"},wg={key:2,class:"rank-empty"},Sg={__name:"index",setup(e){const{gamePlay:t}=Ie("gameProvide"),r=xe({}),n=xe([]),o=()=>{dr.GetGameRank({}).then(s=>{var i,a;n.value=((i=s==null?void 0:s.data)==null?void 0:i.top10Ranking)||[],r.value=((a=s==null?void 0:s.data)==null?void 0:a.userRanking)||{}})};return lt(t,s=>{s||setTimeout(()=>{o()},200)}),Bt(()=>{o()}),(s,i)=>{var a,l,u,c,f,h;return ie(),de("div",ug,[i[3]||(i[3]=$("div",{class:"rank-title"}," 排行榜 ",-1)),((a=r.value)==null?void 0:a.rank)<=10&&((l=r.value)==null?void 0:l.rank)!==-1?(ie(),de(Fe,{key:0},[$("div",fg,[$("p",null,[i[0]||(i[0]=sc("恭喜您获得：")),$("span",dg,"第"+Ue(((u=r.value)==null?void 0:u.rank)||0)+"名",1)]),$("div",pg,[$("div",hg,Ue(((c=r.value)==null?void 0:c.rank)||"--"),1),$("div",mg,Ue(((f=r.value)==null?void 0:f.nickname)||"玩家")+"（我自己）",1),$("div",yg,"连胜 "+Ue(((h=r.value)==null?void 0:h.maxStreak)||0)+" 局",1)])]),i[1]||(i[1]=$("div",{class:"line"},null,-1))],64)):It("",!0),n.value.length?(ie(),de("div",gg,[(ie(!0),de(Fe,null,pn(n.value,(d,g)=>(ie(),de("div",{class:"rank-item",key:`rank${d.rank}`},[$("div",vg,Ue(d.rank>3?d.rank<10?`0${d.rank}`:d.rank:""),1),$("div",bg,Ue((d==null?void 0:d.nickname)||`玩家${(d==null?void 0:d.userId)||g}`),1),$("div",Ag,"连胜 "+Ue((d==null?void 0:d.maxStreak)||0)+" 局",1)]))),128))])):(ie(),de("div",wg,i[2]||(i[2]=[$("img",{src:cg,alt:""},null,-1)])))])}}},Eg="https://static.kedou.com/marketingWeb/static/1.0.0/gameGuess/assets/share-bg-Bk6BVP1C.png",_g={__name:"ShareDialog",setup(e){const{changeShareShow:t}=Ie("gameProvide");return(r,n)=>(ie(),Br(au,{position:"top",maskClick:ne(t),customClass:"share-dialog-box"},{content:Hs(()=>n[0]||(n[0]=[$("div",{class:"share-dialog"},[$("img",{src:Eg,alt:""})],-1)])),_:1},8,["maskClick"]))}},Rg=Vr(_g,[["__scopeId","data-v-3eaef223"]]),Og={class:"home-container"},xg={class:"home-content"},Pg={class:"bottom"},Cg={__name:"HomeView",setup(e){const{proxy:t}=rd(),r=xe(!1),n=xe(!1),o=xe(!1),s=xe({}),i=()=>{r.value=!r.value},a=()=>{l()},l=()=>{n.value=!n.value},u=h=>{o.value=h},c=()=>{dr.GetUserStreak({}).then(h=>{s.value=h.data||{}})},f=()=>{var h;dr.GetShareData({data:{channel:(h=pr())==null?void 0:h.channel}}).then(d=>{d.code!==-2?u("share"):t.$message("分享成功，抽奖机会已达上限",{duration:5e3})})};return Bt(()=>{Object.keys(_r()).length&&(_r().tockenId&&fy(_r().tockenId),py(JSON.stringify(_r())),_r().shareTimes&&f(),hy({moduleId:"guess-game",evt:"pv"})),c()}),lt(s,h=>{var d;wx.miniProgram.postMessage({data:{...h,channel:(d=pr())==null?void 0:d.channel}})}),lr("gameProvide",{baseInfo:s,gamePlay:r,drawShow:o,guessOptions:cy,changeGameState:i,changeShareShow:l,changeDrawShow:u,getBaseInfo:c}),(h,d)=>(ie(),de("main",null,[js(Se(Rg,null,null,512),[[Ks,n.value]]),$("div",Og,[$("div",xg,[Se(lg),$("div",Pg,[Se(Sg)]),$("div",{class:"float",onClick:a},d[0]||(d[0]=[$("img",{src:Bp,alt:""},null,-1)]))])])]))}},Tg=Vr(Cg,[["__scopeId","data-v-50da0e02"]]),Ig=Dp({history:cp("/"),routes:[{path:"/",name:"home",component:Tg}]});function kg(e,t=null){if(!e)return!1;const r=e.getBoundingClientRect(),n=t?t.getBoundingClientRect():{top:0,left:0,bottom:window.innerHeight,right:window.innerWidth};return r.bottom>=n.top&&r.top<=n.bottom&&r.right>=n.left&&r.left<=n.right}async function Dg(e){return e?(await xn(),e.value instanceof HTMLElement?e.value:e.value?document.querySelector(e.value):null):null}function Ng(e){let t=`0px 0px ${e.distance}px 0px`;e.top&&(t=`${e.distance}px 0px 0px 0px`);const r=new IntersectionObserver(n=>{n[0].isIntersecting&&(e.firstload&&e.emit(),e.firstload=!0)},{root:e.parentEl,rootMargin:t});return e.infiniteLoading.value&&r.observe(e.infiniteLoading.value),r}async function nl(e,t){if(await xn(),!e.top)return;const r=e.parentEl||document.documentElement;r.scrollTop=r.scrollHeight-t}const lu=(e,t)=>{const r=e.__vccOpts||e;for(const[n,o]of t)r[n]=o;return r},Mg={},Fg=e=>(of("data-v-d3e37633"),e=e(),sf(),e),Bg={class:"container"},Lg=Fg(()=>$("div",{class:"spinner"},null,-1)),Ug=[Lg];function qg(e,t){return ie(),de("div",Bg,Ug)}const Hg=lu(Mg,[["render",qg],["__scopeId","data-v-d3e37633"]]),jg={class:"state-error"},$g=Cn({__name:"InfiniteLoading",props:{top:{type:Boolean,default:!1},target:{},distance:{default:0},identifier:{},firstload:{type:Boolean,default:!0},slots:{}},emits:["infinite"],setup(e,{emit:t}){const r=e;let n=null,o=0;const s=xe(null),i=xe(""),{top:a,firstload:l,distance:u}=r,{identifier:c,target:f}=zu(r),h={infiniteLoading:s,top:a,firstload:l,distance:u,parentEl:null,emit(){o=(h.parentEl||document.documentElement).scrollHeight,d.loading(),t("infinite",d)}},d={loading(){i.value="loading"},async loaded(){i.value="loaded",await nl(h,o),kg(s.value,h.parentEl)&&h.emit()},async complete(){i.value="complete",await nl(h,o),n==null||n.disconnect()},error(){i.value="error"}};function g(){n==null||n.disconnect(),n=Ng(h)}return lt(c,g),Bt(async()=>{h.parentEl=await Dg(f),g()}),In(()=>n==null?void 0:n.disconnect()),(w,y)=>(ie(),de("div",{ref_key:"infiniteLoading",ref:s,class:"v3-infinite-loading"},[js($("div",null,[en(w.$slots,"spinner",{},()=>[Se(Hg)])],512),[[Ks,i.value=="loading"]]),i.value=="complete"?en(w.$slots,"complete",{key:0},()=>{var v;return[$("span",null,Ue(((v=w.slots)==null?void 0:v.complete)||"No more results!"),1)]}):It("",!0),i.value=="error"?en(w.$slots,"error",{key:1,retry:h.emit},()=>{var v;return[$("span",jg,[$("span",null,Ue(((v=w.slots)==null?void 0:v.error)||"Oops something went wrong!"),1),$("button",{class:"retry",onClick:y[0]||(y[0]=(...b)=>h.emit&&h.emit(...b))},"retry")])]}):It("",!0)],512))}}),Gg=lu($g,[["__scopeId","data-v-4bdee133"]]),Wg={class:"message-content"},Kg=Cn({__name:"message",props:{message:{type:String,required:!0},duration:{type:Number,default:2e3},background:{type:String,default:"#333"},textColor:{type:String,default:"#fff"},position:{type:String,default:"center"}},setup(e){const t=e,r=xe(!1),n=xe(!1),o=xe(!1),s=xe(window.innerWidth<=768),i=ot(()=>`message-${t.position}`);Bt(()=>{r.value=!0,setTimeout(()=>{n.value=!0},10);const u=setTimeout(()=>{o.value=!0,setTimeout(()=>{r.value=!1},300)},t.duration);In(()=>{clearTimeout(u)})});const a=()=>{o.value=!0,setTimeout(()=>{r.value=!1},300)},l=()=>{s.value&&a()};return(u,c)=>r.value?(ie(),de("div",{key:0,class:yt(["message",[s.value?"message-mobile":"message-pc",i.value,{"message-fade-in":n.value,"message-fade-out":o.value}]]),style:En({backgroundColor:e.background,color:e.textColor}),onClick:l},[$("div",Wg,[$("span",null,Ue(e.message),1)])],6)):It("",!0)}}),Ps=Vr(Kg,[["__scopeId","data-v-85527d75"]]),an=document.createElement("div");document.body.appendChild(an);let $t=[],ls=null;const Vg=5,zg=window.innerWidth<=768;function Qg(){return(e,t={})=>{$t.push({content:e,options:t}),Jg()}}function Jg(){var e;if(zg){if(ls!==null)return;const{content:t="",options:r}=$t.shift(),n=Se(Ps,{message:t,duration:r.duration||2e3,background:r.background||"#333",textColor:r.textColor||"#fff",position:r.position||"center"});lo(n,an),ls=setTimeout(()=>{lo(null,an),ls=null,$t=[]},((e=n.props)==null?void 0:e.duration)||2e3)}else{$t.length>Vg&&$t.shift();const{content:t,options:r}=$t[$t.length-1],n=Se(Ps,{message:t,duration:r.duration||2e3,background:r.background||"#333",textColor:r.textColor||"#fff",position:r.position||"center"});lo(n,an)}}const zr=Cd(Fp),Xg=Qg();Array.prototype.at||(Array.prototype.at=function(e){return this[e]});zr.use(Ig);zr.component("infinite-loading",Gg);zr.component("v-message",Ps);zr.config.globalProperties.$message=Xg;zr.mount("#app");
