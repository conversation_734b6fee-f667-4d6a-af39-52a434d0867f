package com.shunwang.marketing.service.marketing.impl;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.shunwang.marketing.cacheService.CacheKeyConstant;
import com.shunwang.marketing.cacheService.RedisOperation;
import com.shunwang.marketing.context.ThreadLocalContext;
import com.shunwang.marketing.dao.BaseMapper;
import com.shunwang.marketing.dao.marketing.PondOrderMapper;
import com.shunwang.marketing.enums.RefundListEnum;
import com.shunwang.marketing.enums.marketing.MscOrderEnum;
import com.shunwang.marketing.enums.marketing.PondOrderEnum;
import com.shunwang.marketing.enums.marketing.StoreOrderEnum;
import com.shunwang.marketing.manager.request.swpay.RefundRequest;
import com.shunwang.marketing.manager.response.swpay.RefundResponse;
import com.shunwang.marketing.manager.service.SwpayServiceClient;
import com.shunwang.marketing.pojo.marketing.PayOrderRecord;
import com.shunwang.marketing.pojo.marketing.Pond;
import com.shunwang.marketing.pojo.marketing.PondOrder;
import com.shunwang.marketing.pojo.marketing.Prize;
import com.shunwang.marketing.query.marketing.PondOrderQuery;
import com.shunwang.marketing.service.impl.BaseServiceImpl;
import com.shunwang.marketing.service.marketing.LotteriesCntService;
import com.shunwang.marketing.service.marketing.PayOrderRecordService;
import com.shunwang.marketing.service.marketing.PondOrderService;
import com.shunwang.marketing.service.marketing.PrizeService;
import com.shunwang.marketing.util.MemberUtil;
import com.shunwang.util.lang.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Component
@Slf4j
public class PondOrderServiceImpl extends BaseServiceImpl<PondOrder> implements PondOrderService {
    @Autowired
    private PondOrderMapper pondOrderMapper;
    @Autowired
    PayOrderRecordService payOrderRecordService;
    @Autowired
    LotteriesCntService lotteriesCntService;
    @Autowired
    PrizeService prizeService;
    @Autowired
    RedisOperation redisOperation;
    @Autowired
    TransactionTemplate transactionTemplate;
    @Autowired
    PondOrderService pondOrderService;
    private final Gson gson=new Gson();

    @Override
    public BaseMapper getDao() {
        return pondOrderMapper;
    }


    @Override
    @Transactional(rollbackFor = Throwable.class)
    public PondOrder initAndSave(Pond pond, Integer times, String orderNo, String payOrderNo) {
        PondOrder order = new PondOrder();
        order.setMemberId(MemberUtil.getNotNullMemberId());
        order.setPondId(pond.getId());
        order.setName("抽奖:" + pond.getId() + "-" + times);
        order.setTimeAdd(new Date());
        order.setOrderNo(orderNo);
        order.setState(StoreOrderEnum.StateEnum.INIT.getValue());
        order.setPrice(pond.getExactPrice(times));
        order.setPayOrderNo(payOrderNo);
        order.setChannel(ThreadLocalContext.getChannel());
        order.appendRemark(PondOrder.REMARK_TIMES,times);
        pondOrderMapper.save(order);
        payOrderRecordService.createAndSavePayOrderRecord(payOrderNo, MemberUtil.getNotNullMemberId());
        return order;
    }

    @Override
    public PondOrder getByOrderNo(String orderNo) {
        PondOrderQuery query = new PondOrderQuery();
        query.setOrderNo(orderNo);
        return pondOrderMapper.findOne(query);
    }

    @Override
    public void finishOrder(String orderNo, String disburseNo) {
        Boolean result = transactionTemplate.execute(new TransactionCallback<Boolean>() {
            @Override
            public Boolean doInTransaction(TransactionStatus status) {
                PondOrder order = pondOrderMapper.getByOrderNoForUpdate(orderNo);
                if (order == null) {
                    log.error("抽奖未找到订单,orderNo:{}", orderNo);
                    return true;
                }
                if (StringUtil.isBlank(order.getPayOrderNo()) || !order.getPayOrderNo().equals(disburseNo)) {
                    log.error("抽奖回调消费单号不匹配,disburseNoInDb:{},disburseNo:{}", order.getPayOrderNo(), disburseNo);
                    return true;
                }
                if (!PondOrderEnum.StateEnum.INIT.getValue().equals(order.getState())) {
                    return true;
                }
                MemberUtil.loadMemberAndCache(order.getMemberId());
                try {
                    List<Prize> prizeList = prizeService.winPrize(null, order.getPondId(), order.getMemberId(), order.getChannel(), false, order.getTimes());
                    String allPrizeId = prizeList.stream().map(prize -> prize.getId().toString()).collect(Collectors.joining(","));
                    order.setState(PondOrderEnum.StateEnum.SUCCESS.getValue());
                    order.setFinishTime(new Date());
                    order.appendRemark(PondOrder.REMARK_PRIZE, allPrizeId);
                    update(order);

                    redisOperation.set(CacheKeyConstant.PRIZE_ORDER_RESULT + "_" + orderNo, prizeList, 1, TimeUnit.MINUTES);
                    return true;
                } catch (Exception e) {
                    status.setRollbackOnly();
                    log.error("抽奖订单失败,订单号:{}", orderNo, e);
                    return false;
                }
            }
        });
        if (!result){
            pondOrderService.refund(orderNo);
        }
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void refund(String orderNo) {
        PondOrder order = pondOrderMapper.getByOrderNoForUpdate(orderNo);
        if (!PondOrderEnum.StateEnum.INIT.getValue().equals(order.getState())) {
            log.info("抽奖订单:{}状态:{},无法退款", order.getOrderNo(), order.getState());
            return;
        }
        RefundRequest request = new RefundRequest();
        request.setOrderNo(order.getOrderNo());
        request.setMoney(order.getPrice().toString());
        request.setName(order.getName());
        request.setRefundType(RefundRequest.RefundTypeEnum.PRIZE.getValue());
        RefundResponse response = SwpayServiceClient.execute(request);
        if (response.isSuccess()) {
            PayOrderRecord record = payOrderRecordService.getByPayOrderNo(order.getPayOrderNo());
            record.setPayRefundNo(response.getRefundNo());
            payOrderRecordService.update(record);

            doRefund(response.getRefundState(),order.getOrderNo());
        }else{
            log.error("商品退款失败，订单号:{}，接口返回:{}", order.getOrderNo(), response.getMsg());
            order.setState(StoreOrderEnum.StateEnum.FAIL.getValue());
            order.setFinishTime(new Date());

            JsonObject remark = gson.fromJson(order.getRemark(), JsonObject.class);
            remark.addProperty("extMsg", response.getMsg());
            order.setRemark(gson.toJson(remark));
            update(order);
        }
    }

    @Transactional(rollbackFor = Throwable.class)
    public void doRefund(Integer refundState, String orderNo) {
        PondOrder order = pondOrderMapper.getByOrderNoForUpdate(orderNo);
        if (PondOrderEnum.StateEnum.SUCCESS.getValue().equals(order.getState())
                || PondOrderEnum.StateEnum.CLOSE.getValue().equals(order.getState())
                ||PondOrderEnum.StateEnum.REFUND.getValue().equals(order.getState())) {
            return;
        }
        if (RefundListEnum.RefundStateEnum.STATE_SUCCESS_3.getValue().equals(refundState)) {
            order.setState(StoreOrderEnum.StateEnum.REFUND.getValue());
        } else if (RefundListEnum.RefundStateEnum.STATE_FAIL_4.getValue().equals(refundState)) {
            order.setState(MscOrderEnum.StateEnum.REFUND_FAIL.getValue());
        } else {
            order.setState(MscOrderEnum.StateEnum.REFUNDING.getValue());
        }
        update(order);
    }
}
