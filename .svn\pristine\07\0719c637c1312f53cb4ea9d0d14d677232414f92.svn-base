package com.shunwang.marketing.pojo.marketing;

import com.shunwang.marketing.pojo.BaseObject;
import lombok.Data;

import java.util.Date;
@Data
public class TencentOrderNotice extends BaseObject {
	private static final long serialVersionUID = 1L;
	private Long id;
	private Integer goodsId;
	private String goodsName;
	private Integer welfareId;
	private String welfareName;
	private Integer memberId;
	private String memberName;
	private String orderNo;
	private String noticeNo;
	private Integer state;
	private Integer sendCnt;
	private Date sendTime;
	private Date successTime;
	private String remark;
	private Date addTime;
	private Date editTime;
	private String thirdActivityId;
}
