package com.shunwang.marketing.controller;

import com.shunwang.marketing.cacheService.CacheKeyConstant;
import com.shunwang.marketing.cacheService.RedisOperation;
import com.shunwang.marketing.context.ThreadLocalContext;
import com.shunwang.marketing.enums.marketing.ModuleDetailsEnum;
import com.shunwang.marketing.enums.marketing.WelfareEnum;
import com.shunwang.marketing.pojo.marketing.PrizeRecord;
import com.shunwang.marketing.pojo.marketing.vo.ModuleVo;
import com.shunwang.marketing.result.ApiResult;
import com.shunwang.marketing.result.Result;
import com.shunwang.marketing.result.ResultGenerator;
import com.shunwang.marketing.service.marketing.GuidBtnRecordService;
import com.shunwang.marketing.service.marketing.ModuleDetailsService;
import com.shunwang.marketing.service.marketing.ModuleService;
import com.shunwang.marketing.service.marketing.PrizeRecordService;
import com.shunwang.marketing.util.MemberUtil;
import com.shunwang.util.lang.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.shunwang.marketing.cacheService.CacheKeyConstant.MODULE_CDKEY_PREFIX;
import static com.shunwang.marketing.cacheService.CacheKeyConstant.MODULE_RED_PACKET_PREFIX;

/**
 * 模块相关接口
 *
 * @apiNote 模块相关接口
 */
@Controller
@RequestMapping("module")
public class ModuleController {
    @Autowired
    ModuleService moduleService;
    @Autowired
    ModuleDetailsService moduleDetailsService;
    @Autowired
    GuidBtnRecordService guidBtnRecordService;
    @Autowired
    RedisOperation redisOperation;
    @Autowired
    PrizeRecordService prizeRecordService;

    /**
     * 获取模块信息接口
     * (结构structure为1 2 3 4的数据（1：左右4个，2：左右5个，3：上下多个 4横排结构））
     * @param platform 归属平台（1：菜单特权，2：电酒权益，3：小程序）
     * @return
     */
    @Deprecated
    @ResponseBody
    @RequestMapping("/info")
    public Result<List<ModuleVo>> getModuleInfo(
            @RequestParam("platform")Integer platform,
            @RequestParam("guid")String guid,
            @RequestParam(value = "memberId",required = false)Integer memberId) {
        ThreadLocalContext.setTempData(ThreadLocalContext.MEMBER_ID,memberId);
        List<ModuleVo> moduleVoList = moduleService.getModuleVoByPlatform(platform, guid);
        return ResultGenerator.newSuccessResult(moduleVoList);
    }

    /**
     * 用户点击领取按钮操作
     * @param barId
     * @param guid
     * @param moduleDetailsId
     * @return
     */
    @ResponseBody
    @RequestMapping("/clickBtn")
    public Result toClickBtn(String barId, String guid, Integer moduleDetailsId){
        if (StringUtil.isBlank(barId) || StringUtil.isBlank(guid)){
            return ResultGenerator.newSystemErrorResult("参数错误");
        }
        guidBtnRecordService.toSaveRecord(barId, guid, moduleDetailsId);
        return ResultGenerator.newEmptySuccessResult();
    }

    /**
     * 模块详情领取cdkey
     *
     * @param memberId       用户ID
     * @param moduleDetailId 模块详情ID
     * @return cdkey
     */
    @RequestMapping("/welfare/cdkey")
    @ResponseBody
    public Result getCdkey(@RequestParam(value = "memberId") Integer memberId, @RequestParam("moduleDetailId") Integer moduleDetailId) {
        boolean absent = redisOperation.setIfAbsent(MODULE_CDKEY_PREFIX + memberId + CacheKeyConstant.CACHE_SPLIT + moduleDetailId, "1", 3, TimeUnit.SECONDS);
        if (!absent) {
            return ResultGenerator.newSystemErrorResult("操作频繁");
        }
        MemberUtil.loadMemberAndCache(memberId);
        String cdkey = moduleDetailsService.getCdkey(moduleDetailId);
        return ResultGenerator.newSuccessResult(cdkey);
    }

    /**
     * 模块详情领取权益
     *
     * @param memberId       用户ID
     * @param moduleDetailId 模块详情ID
     */
    @RequestMapping("/welfare/common")
    @ResponseBody
    public Result getWelfare(@RequestParam(value = "memberId") Integer memberId, @RequestParam("moduleDetailId") Integer moduleDetailId) {
        boolean absent = redisOperation.setIfAbsent(MODULE_CDKEY_PREFIX + memberId + CacheKeyConstant.CACHE_SPLIT + moduleDetailId, "1", 3, TimeUnit.SECONDS);
        if (!absent) {
            return ResultGenerator.newSystemErrorResult("操作频繁");
        }
        MemberUtil.loadMemberAndCache(memberId);
        moduleDetailsService.sendWelfare(moduleDetailId);
        return ResultGenerator.newEmptySuccessResult();
    }


}
