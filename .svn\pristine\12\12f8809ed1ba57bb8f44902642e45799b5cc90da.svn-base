<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shunwang.marketing.dao.marketing.BarLevelMapper">
    <resultMap id="BaseResultMap" type="barLevel">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="barLevelName" column="bar_level_name" jdbcType="VARCHAR"/>
        <result property="barLevelKey" column="bar_level_key" jdbcType="VARCHAR"/>
        <result property="onlineState" column="online_state" jdbcType="TINYINT"/>
        <result property="description" column="description" jdbcType="VARCHAR"/>
        <result property="userAdd" column="user_add" jdbcType="VARCHAR"/>
        <result property="timeAdd" column="time_add" jdbcType="TIMESTAMP"/>
        <result property="userEdit" column="user_edit" jdbcType="VARCHAR"/>
        <result property="timeEdit" column="time_edit" jdbcType="TIMESTAMP"/>
        <result property="barLevelImg" column="bar_level_img" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="baseColumn">
        id,
        bar_level_name,
        bar_level_key,
        online_state,
        description,
        user_add,
        time_add,
        user_edit,
        time_edit,
        bar_level_img
    </sql>
    <sql id="base_where" >
        <trim prefix="where" prefixOverrides="and|or">
            <if test="id != null ">
     	        and id = #{id,jdbcType=INTEGER}
            </if>
            <if test="barLevelName != null ">
     	        and bar_level_name = #{barLevelName,jdbcType=VARCHAR}
            </if>
            <if test="barLevelKey != null ">
     	        and bar_level_key = #{barLevelKey,jdbcType=VARCHAR}
            </if>
            <if test="onlineState != null ">
     	        and online_state = #{onlineState,jdbcType=TINYINT}
            </if>
            <if test="description != null ">
                and description = #{description, jdbcType=VARCHAR}
            </if>
            <if test="userAdd != null ">
     	        and user_add = #{userAdd,jdbcType=VARCHAR}
            </if>
            <if test="timeAdd != null ">
     	        and time_add = #{timeAdd,jdbcType=TIMESTAMP}
            </if>
            <if test="userEdit != null ">
     	        and user_edit = #{userEdit,jdbcType=VARCHAR}
            </if>
            <if test="timeEdit != null ">
     	        and time_edit = #{timeEdit,jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>
	<select id="find" parameterType="com.shunwang.marketing.query.BaseQuery" resultMap="BaseResultMap" >
		select 
        <include refid="baseColumn" />
		from marketing_bar_level t 
		<include refid="base_where" />
	</select>
    <select id="findAll" parameterType="com.shunwang.marketing.query.BaseQuery" resultMap="BaseResultMap" >
        select
        <include refid="baseColumn" />
        from marketing_bar_level t
    </select>

    <select id="findOne" parameterType="com.shunwang.marketing.query.BaseQuery" resultMap="BaseResultMap" >
        select
        <include refid="baseColumn" />
        from marketing_bar_level t
        <include refid="base_where" />
        limit 1
    </select>
    <select id="findCnt" parameterType="com.shunwang.marketing.query.BaseQuery" resultType="java.lang.Integer" >
        select
            count(*)
        from marketing_bar_level t
		<include refid="base_where" />
    </select>
    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.Integer">
		select
        <include refid="baseColumn" />
		from marketing_bar_level
		where id = #{id,jdbcType=INTEGER}
    </select>

    <select id="findOnlineByIds" parameterType="java.util.Collection" resultMap="BaseResultMap" >
        select
        <include refid="baseColumn" />
        from marketing_bar_level t
        where
            t.online_state = 1 and
            t.id in
            <foreach collection="ids" item="id" separator="," open="(" close=")">
                #{id,jdbcType=INTEGER}
            </foreach>
    </select>

    <select id="findData" resultMap="BaseResultMap">
        SELECT
        <include refid="baseColumn"/>
        FROM marketing_bar_level
        <include refid="base_where" />
        ORDER BY time_edit desc
        LIMIT #{firstResult:INTEGER}, #{limit:INTEGER}
    </select>
	<insert id="save" parameterType="barLevel" keyProperty="id" useGeneratedKeys="true">
		insert into marketing_bar_level 
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null">
                id,
            </if>
            <if test="barLevelName != null">
                bar_level_name,
            </if>
            <if test="barLevelKey != null">
                bar_level_key,
            </if>
            <if test="onlineState != null">
                online_state,
            </if>
            <if test="description != null">
                description,
            </if>
            <if test="userAdd != null">
                user_add,
            </if>
            <if test="timeAdd != null">
                time_add,
            </if>
            <if test="userEdit != null">
                user_edit,
            </if>
            <if test="timeEdit != null">
                time_edit,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="id != null">
                #{id,jdbcType=INTEGER},
            </if>
            <if test="barLevelName != null">
                #{barLevelName,jdbcType=VARCHAR},
            </if>
            <if test="barLevelKey != null">
                #{barLevelKey,jdbcType=VARCHAR},
            </if>
            <if test="onlineState != null">
                #{onlineState,jdbcType=TINYINT},
            </if>
            <if test="description != null ">
                #{description,jdbcType=VARCHAR},
            </if>
            <if test="userAdd != null">
                #{userAdd,jdbcType=VARCHAR},
            </if>
            <if test="timeAdd != null">
                #{timeAdd,jdbcType=TIMESTAMP},
            </if>
            <if test="userEdit != null">
                #{userEdit,jdbcType=VARCHAR},
            </if>
            <if test="timeEdit != null">
                #{timeEdit,jdbcType=TIMESTAMP},
            </if>
        </trim>
	</insert>
	<update id="update" parameterType="barLevel" >
		update marketing_bar_level
        <set>
            <if test="id != null">
     	        id = #{id,jdbcType=INTEGER},
            </if>
            <if test="barLevelName != null">
     	        bar_level_name = #{barLevelName,jdbcType=VARCHAR},
            </if>
            <if test="barLevelKey != null">
     	        bar_level_key = #{barLevelKey,jdbcType=VARCHAR},
            </if>
            <if test="onlineState != null">
     	        online_state = #{onlineState,jdbcType=TINYINT},
            </if>
            <if test="description != null ">
                description = #{description,jdbcType=VARCHAR},
            </if>
            <if test="userAdd != null">
     	        user_add = #{userAdd,jdbcType=VARCHAR},
            </if>
            <if test="timeAdd != null">
     	        time_add = #{timeAdd,jdbcType=TIMESTAMP},
            </if>
            <if test="userEdit != null">
     	        user_edit = #{userEdit,jdbcType=VARCHAR},
            </if>
            <if test="timeEdit != null">
     	        time_edit = #{timeEdit,jdbcType=TIMESTAMP},
            </if>
        </set>
		<trim prefix="where" prefixOverrides="and|or"> 
            id = #{id,jdbcType=INTEGER}
        </trim>
	</update>

    <delete id="delete" parameterType="integer">
        delete from marketing_bar_level where id = #{id,jdbcType=INTEGER}
    </delete>
</mapper>
