<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shunwang.marketing.dao.marketing.AiSessionMapper">
    <resultMap id="BaseResultMap" type="aiSession">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="sessionId" column="session_id" jdbcType="VARCHAR"/>
        <result property="type" column="type" jdbcType="INTEGER"/>
        <result property="memberId" column="member_id" jdbcType="INTEGER"/>
        <result property="modelId" column="model_id" jdbcType="INTEGER"/>
        <result property="addTime" column="add_time" jdbcType="TIMESTAMP"/>
        <result property="editTime" column="edit_time" jdbcType="TIMESTAMP"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="ext1" column="ext1" jdbcType="INTEGER"/>
        <result property="ext2" column="ext2" jdbcType="INTEGER"/>
        <result property="ext3" column="ext3" jdbcType="INTEGER"/>
    </resultMap>
    <sql id="baseColumn">
        id,
        session_id,
        type,
        member_id,
        model_id,
        add_time,
        edit_time,
        remark,
        ext1,
        ext2,
        ext3
    </sql>
    <sql id="base_where">
        <trim prefix="where" prefixOverrides="and|or">
            <if test="id != null">
                and id = #{id,jdbcType=INTEGER}
            </if>
            <if test="sessionId != null and sessionId != ''">
                and session_id = #{sessionId,jdbcType=VARCHAR}
            </if>
            <if test="type != null">
                and type = #{type,jdbcType=INTEGER}
            </if>
            <if test="memberId != null">
                and member_id = #{memberId,jdbcType=INTEGER}
            </if>
            <if test="modelId != null">
                and model_id = #{modelId,jdbcType=INTEGER}
            </if>
            <if test="addTime != null">
                and add_time = #{addTime,jdbcType=TIMESTAMP}
            </if>
            <if test="editTime != null">
                and edit_time = #{editTime,jdbcType=TIMESTAMP}
            </if>
            <if test="remark != null and remark != ''">
                and remark = #{remark,jdbcType=VARCHAR}
            </if>
            <if test="ext1 !=null">
                and ext1 = #{ext1,jdbcType=INTEGER}
            </if>
            <if test="ext2 !=null">
                and ext2 = #{ext2,jdbcType=INTEGER}
            </if>
            <if test="addTimeBegin!=null">
                and add_time &gt; #{addTimeBegin,jdbcType=TIMESTAMP}
            </if>
            <if test="addTimeEnd!=null">
                and add_time &lt; #{addTimeEnd,jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>
    <select id="find" parameterType="com.shunwang.marketing.query.BaseQuery" resultMap="BaseResultMap">
        select
        <include refid="baseColumn"/>
        from marketing_ai_session t
        <include refid="base_where"/>
    </select>
    <select id="findOne" parameterType="com.shunwang.marketing.query.BaseQuery" resultMap="BaseResultMap">
        select
        <include refid="baseColumn"/>
        from marketing_ai_session t
        <include refid="base_where"/>
        limit 1
    </select>
    <select id="findCnt" parameterType="com.shunwang.marketing.query.BaseQuery" resultType="java.lang.Integer">
        select
            count(*)
        from marketing_ai_session t
        <include refid="base_where"/>
    </select>
    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select
        <include refid="baseColumn"/>
        from marketing_ai_session
        where id = #{id,jdbcType=INTEGER}
    </select>
    <insert id="save" parameterType="aiSession" keyProperty="id" useGeneratedKeys="true">
        insert into marketing_ai_session
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="sessionId != null">
                session_id,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="memberId != null">
                member_id,
            </if>
            <if test="modelId != null">
                model_id,
            </if>
            <if test="addTime != null">
                add_time,
            </if>
            <if test="editTime != null">
                edit_time,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="ext1 != null">
                ext1,
            </if>
            <if test="ext2 != null">
                ext2,
            </if>
            <if test="ext3 != null">
                ext3,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="sessionId != null">
                #{sessionId,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                #{type,jdbcType=INTEGER},
            </if>
            <if test="memberId != null">
                #{memberId,jdbcType=INTEGER},
            </if>
            <if test="modelId != null">
                #{modelId,jdbcType=INTEGER},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="editTime != null">
                #{editTime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="ext1 != null">
                #{ext1,jdbcType=INTEGER},
            </if>
            <if test="ext2 != null">
                #{ext2,jdbcType=INTEGER},
            </if>
            <if test="ext3 != null">
                #{ext3,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <update id="update" parameterType="aiSession">
        update marketing_ai_session
        <set>
            <if test="sessionId != null">
                session_id = #{sessionId,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                type = #{type,jdbcType=INTEGER},
            </if>
            <if test="memberId != null">
                member_id = #{memberId,jdbcType=INTEGER},
            </if>
            <if test="modelId != null">
                model_id = #{modelId,jdbcType=INTEGER},
            </if>
            <if test="addTime != null">
                add_time = #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="editTime != null">
                edit_time = #{editTime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="ext1 != null">
                ext1 = #{ext1,jdbcType=INTEGER},
            </if>
            <if test="ext2 != null">
                ext2 = #{ext2,jdbcType=INTEGER},
            </if>
            <if test="ext3 != null">
                ext3 = #{ext3,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <select id="findData" parameterType="com.shunwang.marketing.query.BaseQuery" resultMap="BaseResultMap">
        SELECT a.*
        FROM marketing_ai_session a
        <include refid="base_where"></include>
        ORDER BY a.edit_time desc
        LIMIT #{firstResult:INTEGER}, #{limit:INTEGER}
    </select>
    <select id="findDataAggregate" parameterType="com.shunwang.marketing.query.BaseQuery" resultType="baseAggregate">
        SELECT
        COUNT(*) as cnt
        FROM marketing_ai_session a
        <include refid="base_where"></include>
    </select>
    <select id="findByGameAndTime" resultMap="BaseResultMap">
        SELECT a.*
        FROM marketing_ai_session a
        where a.member_id = #{memberId,jdbcType=INTEGER}
        and a.ext1 = #{ext1,jdbcType=INTEGER}
        and a.add_time >= #{time,jdbcType=TIMESTAMP}
    </select>
</mapper> 
