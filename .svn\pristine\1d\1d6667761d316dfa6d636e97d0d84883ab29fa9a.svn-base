package com.shunwang.marketing.service.marketing.impl;

import cn.hutool.core.date.DateUtil;
import com.shunwang.marketing.dao.BaseMapper;
import com.shunwang.marketing.dao.marketing.AiGameItemMapper;
import com.shunwang.marketing.enums.ResourcesEnum;
import com.shunwang.marketing.pojo.marketing.AiGameItem;
import com.shunwang.marketing.query.marketing.AiGameItemQuery;
import com.shunwang.marketing.service.config.ResourcesService;
import com.shunwang.marketing.service.impl.BaseServiceImpl;
import com.shunwang.marketing.service.marketing.AiGameItemService;
import com.shunwang.util.lang.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.Date;

@Component
public class AiGameItemServiceImpl extends BaseServiceImpl<AiGameItem> implements AiGameItemService {
    @Autowired
    private AiGameItemMapper aiGameItemMapper;
    @Autowired
    ResourcesService resourcesService;

    @Override
    public BaseMapper getDao() {
        return aiGameItemMapper;
    }

    @Override
    public AiGameItem findOnline(AiGameItemQuery aiGameItemQuery) {
        if (StringUtil.isBlank(aiGameItemQuery.getGameItemConfig())){
            return null;
        }
        return aiGameItemMapper.findOnline(aiGameItemQuery);
    }

    @Override
    public AiGameItem getPeriod() {
        AiGameItem result = new AiGameItem();
        Integer startConfig = resourcesService.getIntValueByTypeAndName(ResourcesEnum.TypeName.AI_GAME_START);
        Integer endConfig = resourcesService.getIntValueByTypeAndName(ResourcesEnum.TypeName.AI_GAME_END);

        if (endConfig < startConfig) {
            LocalDate date = LocalDate.now();
            LocalDateTime now = LocalDateTime.now();
            if (now.getHour() < endConfig) {
                LocalDateTime begin = LocalDateTime.of(date.minusDays(1), LocalTime.of(startConfig, 0));
                LocalDateTime end = LocalDateTime.of(date, LocalTime.of(endConfig, 0));
                result.setStartTime(Date.from(begin.atZone(ZoneId.systemDefault()).toInstant()));
                result.setEndTime(Date.from(end.atZone(ZoneId.systemDefault()).toInstant()));
            } else if (now.getHour() >= startConfig) {
                LocalDateTime begin = LocalDateTime.of(date, LocalTime.of(startConfig, 0));
                LocalDateTime end = LocalDateTime.of(date.plusDays(1), LocalTime.of(endConfig, 0));
                result.setStartTime(Date.from(begin.atZone(ZoneId.systemDefault()).toInstant()));
                result.setEndTime(Date.from(end.atZone(ZoneId.systemDefault()).toInstant()));
            } else {
                return null;
            }
        } else {
            LocalDate date = LocalDate.now();
            LocalDateTime begin = LocalDateTime.of(date, LocalTime.of(startConfig, 0));
            LocalDateTime end = LocalDateTime.of(date, LocalTime.of(endConfig, 0));
            result.setStartTime(Date.from(begin.atZone(ZoneId.systemDefault()).toInstant()));
            result.setEndTime(Date.from(end.atZone(ZoneId.systemDefault()).toInstant()));
        }
        return result;
    }
}
