package com.shunwang.marketing.service.marketing.impl;

import com.shunwang.marketing.aspect.asynctask.AiChatTaskExecutor;
import com.shunwang.marketing.cacheService.RedisOperation;
import com.shunwang.marketing.enums.ResourcesEnum;
import com.shunwang.marketing.enums.marketing.AiModelEnum;
import com.shunwang.marketing.enums.marketing.AiRecordEnum;
import com.shunwang.marketing.enums.marketing.AiSessionEnum;
import com.shunwang.marketing.enums.marketing.ContentEnum;
import com.shunwang.marketing.exception.CommonException;
import com.shunwang.marketing.pojo.ai.AiReplyMessage;
import com.shunwang.marketing.pojo.marketing.*;
import com.shunwang.marketing.pojo.util.SendMsg;
import com.shunwang.marketing.query.marketing.AiModelQuery;
import com.shunwang.marketing.query.marketing.AiRecordQuery;
import com.shunwang.marketing.query.marketing.ContentQuery;
import com.shunwang.marketing.send.email.EmailSender;
import com.shunwang.marketing.service.ai.AiSseEmitterWrapper;
import com.shunwang.marketing.service.config.ResourcesService;
import com.shunwang.marketing.service.marketing.*;
import com.shunwang.marketing.util.MemberUtil;
import com.shunwang.sms.utils.SMSInnerSenderUtil;
import com.shunwang.util.json.GsonUtil;
import com.shunwang.util.lang.StringUtil;
import com.shunwang.util.trace.TraceUtil;
import com.volcengine.ark.runtime.model.completion.chat.ChatCompletionRequest;
import com.volcengine.ark.runtime.model.completion.chat.ChatCompletionResult;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessage;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessageRole;
import com.volcengine.ark.runtime.service.ArkService;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static com.shunwang.marketing.enums.marketing.AiModelEnum.PlatformEnum.AI_DEPARTMENT;
import static com.shunwang.marketing.enums.marketing.AiModelEnum.PlatformEnum.HUOSHAN;

@Service
@Slf4j
public class AiServiceimpl implements AiService {
    @Autowired
    AiRecordService aiRecordService;
    @Autowired
    AiModelService aiModelService;
    Map<String, ArkService> arkServiceMap = new ConcurrentHashMap<>();
    @Autowired
    RedisOperation redisOperation;
    @Autowired
    ResourcesService resourcesService;
    @Autowired
    EmailSender emailSender;
    @Autowired
    @Lazy
    AiService aiService;
    @Autowired
    ContentService contentService;
    @Autowired
    TransactionTemplate transactionTemplate;
    @Autowired
    AiSessionService aiSessionService;
    @Autowired
    AiGameItemService aiGameItemService;

    public void checkToken(){
        checkToken(AiModelEnum.BussEnum.CHAT.getValue());
        checkToken(AiModelEnum.BussEnum.RIDDLE.getValue());
    }

    public void checkToken(String buss) {
        AiModelQuery query = new AiModelQuery();
        query.setState(AiModelEnum.StateEnum.ONLINE.getValue());
        // 根据业务类型设置查询条件
        List<String> bussList = new ArrayList<>();
        // 添加不区分的模型
        bussList.add(AiModelEnum.BussEnum.NO_DISTINGUISH.getValue());
        // 添加对应业务的模型
        bussList.add(buss);
        query.setBussList(bussList);
        List<AiModel> aiModelList = aiModelService.find(query);
        int sumRemainToken = aiModelList.stream()
                .filter((aiModel -> !AI_DEPARTMENT.getValue().equals(aiModel.getPlatform()) || aiModel.getRemainToken() == null))
                .mapToInt(AiModel::getRemainToken)
                .sum();
        if (sumRemainToken < 50000) {
            String bussDec="";
            for (AiModelEnum.BussEnum bussEnum : AiModelEnum.BussEnum.values()) {
                if (bussEnum.getValue().equals(buss)) {
                    bussDec = bussEnum.getValue();
                }
            }
            log.info("ai总token小于50000");
            if (redisOperation.setIfAbsent("ai_token_check_"+buss, 1, 6, TimeUnit.HOURS)) {
                log.info("ai:{}总token小于50000，发送提醒",buss);
                String emailReceiver = resourcesService.getStringValueByTypeAndNameEhCache(ResourcesEnum.TypeName.EMAIL_RECEIVER);
                if (StringUtil.isNotBlank(emailReceiver)) {
                    for (String receiver : emailReceiver.split(",")) {
                        SendMsg sendMsg = new SendMsg();
                        sendMsg.setTitle("AI Token余量不足");
                        sendMsg.setContent("AI总token数小于50000,业务:" + bussDec);
                        sendMsg.setNumber(receiver);
                        emailSender.sendEmail(sendMsg);
                    }
                }

                String smsReceiver = resourcesService.getStringValueByTypeAndNameEhCache(ResourcesEnum.TypeName.SMS_RECEIVER);
                if (StringUtil.isNotBlank(smsReceiver)) {
                    for (String receiver : smsReceiver.split(",")) {
                        SMSInnerSenderUtil.sendMsg(receiver, "AI总token数小于50000,业务:" + bussDec);
                    }
                }
            }
        }
    }

    private void chatForCommon(AiSseEmitterWrapper sseEmitterWrapper) throws IOException {
        if (judgeIsBar(sseEmitterWrapper.getQuestion())) {
            log.info("会话:{}问题为查询网费", sseEmitterWrapper.getRequestId());

            AiRecord questionRecord = buildQuestionRecord(sseEmitterWrapper);
            questionRecord.setRemark("网费查询问题");
            aiRecordService.save(questionRecord);

            AiRecord answerRecord = buildAnswerRecord(sseEmitterWrapper);
            answerRecord.setRemark("网费查询回复");
            answerRecord.setContent(getBarMoneyMsg());
            aiRecordService.save(answerRecord);
            sendInSegment(answerRecord.getContent(), sseEmitterWrapper);
            return;
        }

        ContentQuery query = new ContentQuery();
        query.setTitle(sseEmitterWrapper.getQuestion());
        query.setType(ContentEnum.TypeEnum.AI.getValue());
        Content basicQuestionContent = contentService.findOne(query);
        boolean isBasic = basicQuestionContent != null;
        if (isBasic) {
            log.info("会话:{}问题为基础问题", sseEmitterWrapper.getRequestId());
            AiRecord questionRecord = buildQuestionRecord(sseEmitterWrapper);
            questionRecord.setRemark("基础问题");
            aiRecordService.save(questionRecord);
            AiRecord answerRecord = buildAnswerRecord(sseEmitterWrapper);
            answerRecord.setRemark("基础问题回复");
            answerRecord.setContent(basicQuestionContent.getDetail());
            aiRecordService.save(answerRecord);
            sendInSegment(basicQuestionContent.getDetail(), sseEmitterWrapper);
        } else {
            AiRecord callResult = doChat(sseEmitterWrapper);

            AiRecord questionRecord = buildQuestionRecord(sseEmitterWrapper);
            questionRecord.setModelId(callResult.getModelId());
            questionRecord.setConsumeToken(callResult.getQuestionToken());

            AiRecord answerRecord = new AiRecord();
            answerRecord.setMemberId(sseEmitterWrapper.getMemberId());
            answerRecord.setType(AiRecordEnum.TypeEnum.ANSWER.getValue());
            answerRecord.setModelId(callResult.getModelId());
            answerRecord.setContent(callResult.getContent());
            answerRecord.setTimeAdd(new Date());
            answerRecord.setConsumeToken(callResult.getAnswerToken());

            transactionTemplate.execute(new TransactionCallbackWithoutResult() {
                @Override
                protected void doInTransactionWithoutResult(TransactionStatus status) {
                    aiModelService.decToken(callResult.getModelId(), answerRecord.getConsumeToken() + questionRecord.getConsumeToken());
                    aiRecordService.save(questionRecord);
                    answerRecord.setParentId(questionRecord.getId());
                    aiRecordService.save(answerRecord);
                }
            });
            checkToken();
        }
    }


    @Override
    public SseEmitter commonAiBuss(String question) {
        String requestId = UUID.randomUUID().toString();
        SseEmitter sseEmitter = aiService.createSseEmitter(MemberUtil.getNotNullMemberId(), requestId);
        AiSseEmitterWrapper aiSseEmitterWrapper = new AiSseEmitterWrapper();
        aiSseEmitterWrapper.setSseEmitter(sseEmitter);
        aiSseEmitterWrapper.setRequestId(requestId);
        aiSseEmitterWrapper.setQuestion(question);
        aiSseEmitterWrapper.setMemberId(MemberUtil.getNotNullMemberId());

        ChatTask chatTask = new CommonChatTask();
        chatTask.setAiSseEmitterWrapper(aiSseEmitterWrapper);
        chatTask.capture();
        AiChatTaskExecutor.submit(chatTask);
        return sseEmitter;
    }

    @Override
    public AiReplyMessage gameAiBuss(String question, String sessionId, boolean system) {
        AiSession session = aiSessionService.getBySessionId(sessionId);
        if (session.isFinish()) {
            return null;
        }
        AiRecordQuery query = new AiRecordQuery();
        query.setSession(session.getId());
        AiGameItem aiGameItem = aiGameItemService.get(session.getExt2());
        List<AiRecord> recordList = system ? new ArrayList<>() : aiRecordService.find(query);
        int times = ((int) recordList.stream().filter((item) -> AiRecordEnum.TypeEnum.USER.getValue().equals(item.getType())).count()) + (system ? 0 : 1);
        if (recordList.size() > 6) {
            List<AiRecord> resultRecordList = new ArrayList<>(recordList.subList(0, 2));
            resultRecordList.addAll(recordList.subList(recordList.size() - 4, recordList.size()));
            recordList = resultRecordList;
        }

        AiModel model = aiModelService.getModelForGame(MemberUtil.getNotNullMemberId(),sessionId);
        if (model == null) {
            log.info("不存在生效模型");
            throw new CommonException("系统异常");
        }
        AiRecord callResult = syncCallHuoShanModel(system ? aiGameItem.getPrompt() : question, system ? AiRecordEnum.TypeEnum.SYSTEM : AiRecordEnum.TypeEnum.USER, recordList, model);

        AiReplyMessage reply;
        try {
            reply = GsonUtil.fromJson(callResult.getContent(), AiReplyMessage.class);
        } catch (Exception e) {
            log.error("游戏ai回复内容解析异常:{}", callResult.getContent());
            throw new CommonException("系统解析异常");
        }

        AiRecord questionRecord = new AiRecord();
        questionRecord.setMemberId(MemberUtil.getNotNullMemberId());
        questionRecord.setType(system ? AiRecordEnum.TypeEnum.SYSTEM.getValue() : AiRecordEnum.TypeEnum.USER.getValue());
        questionRecord.setContent(system ? aiGameItem.getPrompt() : question);
        questionRecord.setTimeAdd(new Date());
        questionRecord.setModelId(model.getId());
        questionRecord.setConsumeToken(callResult.getQuestionToken());
        questionRecord.setSession(session.getId());
        questionRecord.setConsumeToken(callResult.getQuestionToken());

        AiRecord answerRecord = new AiRecord();
        answerRecord.setMemberId(MemberUtil.getNotNullMemberId());
        answerRecord.setType(AiRecordEnum.TypeEnum.ANSWER.getValue());
        answerRecord.setModelId(model.getId());
        answerRecord.setContent(callResult.getContent());
        answerRecord.setTimeAdd(new Date());
        answerRecord.setSession(session.getId());
        answerRecord.setConsumeToken(callResult.getAnswerToken());
        aiModelService.decToken(model.getId(), answerRecord.getConsumeToken() + questionRecord.getConsumeToken());

        transactionTemplate.execute(new TransactionCallbackWithoutResult() {
            @Override
            protected void doInTransactionWithoutResult(TransactionStatus status) {
                aiRecordService.save(questionRecord);
                answerRecord.setParentId(questionRecord.getId());
                aiRecordService.save(answerRecord);
                if (reply.getRight()) {
                    session.setExt3(AiSessionEnum.AiGameExt3Enum.FINISH.getValue());
                    aiSessionService.update(session);
                }
            }
        });
        reply.setTimes(times);
        reply.setMessageId(UUID.randomUUID().toString());
        checkToken();
        return reply;
    }

    private AiRecord buildQuestionRecord(AiSseEmitterWrapper sseEmitterWrapper) {
        AiRecord questionRecord = new AiRecord();
        questionRecord.setMemberId(sseEmitterWrapper.getMemberId());
        questionRecord.setType(AiRecordEnum.TypeEnum.USER.getValue());
        questionRecord.setContent(sseEmitterWrapper.getQuestion());
        questionRecord.setTimeAdd(new Date());
        questionRecord.setConsumeToken(0);
        return questionRecord;
    }

    private AiRecord buildAnswerRecord(AiSseEmitterWrapper sseEmitterWrapper) {
        AiRecord answerRecord = new AiRecord();
        answerRecord.setMemberId(sseEmitterWrapper.getMemberId());
        answerRecord.setType(AiRecordEnum.TypeEnum.ANSWER.getValue());
        answerRecord.setContent(sseEmitterWrapper.getQuestion());
        answerRecord.setTimeAdd(new Date());
        answerRecord.setConsumeToken(0);
        return answerRecord;
    }

    public void sendInSegment(String content, AiSseEmitterWrapper sseEmitterWrapper) throws IOException {
        int partSize = 10;
        int length = content.length();
        int sendTimes = content.length() / partSize + (length % partSize == 0 ? 0 : 1);
        String messageId = UUID.randomUUID().toString();

        for (int i = 0; i < sendTimes; i++) {
            int start = i * partSize;
            int end = Math.min(start + partSize, length);

            AiReplyMessage replyMessage = new AiReplyMessage();
            replyMessage.setCode(0);
            replyMessage.setMessageId(messageId);
            replyMessage.setFinish(i == sendTimes - 1);
            replyMessage.setContent(content.substring(start, end));
            sseEmitterWrapper.getSseEmitter().send(replyMessage);
            try {
                TimeUnit.MILLISECONDS.sleep(100);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
    }

    private boolean judgeIsBar(String question) {
        String barMoneyKeywords = resourcesService.getStringValueByTypeAndNameEhCache(ResourcesEnum.TypeName.AI_BAR_MONEY_KEYWORDS);
        if (StringUtil.isNotBlank(barMoneyKeywords)) {
            for (String keyWord : barMoneyKeywords.split(",")) {
                if (question.contains(keyWord)) {
                    return true;
                }
            }
        }
        return false;
    }

    public AiRecord doChat(AiSseEmitterWrapper sseEmitterWrapper) throws IOException {
        String messageId = UUID.randomUUID().toString();
        AiModel model = aiModelService.getModelForChat(sseEmitterWrapper.getMemberId());
        if (model == null) {
            log.info("会话:{}未获取到模型", sseEmitterWrapper.getRequestId());
            sseEmitterWrapper.getSseEmitter().send(AiReplyMessage.newFinishMsg(getFailMsg(), messageId));
            return null;
        }
        sseEmitterWrapper.setModel(model);
        Integer platform = sseEmitterWrapper.getModel().getPlatform();

        if (HUOSHAN.getValue().equals(platform)) {
            AiRecord callResult = streamCallHuoShanModel(sseEmitterWrapper);
            callResult.setModelId(model.getId());
            return callResult;
        } else {
            throw new CommonException("未配置的大模型类型");
        }
    }

    public String getFailMsg() {
        String msg = resourcesService.getStringValueByTypeAndNameEhCache(ResourcesEnum.TypeName.AI_FALLBACK);
        if (StringUtil.isBlank(msg)) {
            return "很抱歉，暂时无法为您提供服务，可能是由于网络不稳定或系统繁忙等原因。\n\n给您带来不便，我们深表歉意，感谢您的理解与支持！";
        }
        return msg;
    }

    public String getBarMoneyMsg() {
        String msg = resourcesService.getStringValueByTypeAndNameEhCache(ResourcesEnum.TypeName.AI_BAR_MONEY_REPLY);
        if (StringUtil.isBlank(msg)) {
            return "您好，为确保用户隐私信息安全，[网费查询]功能目前已关闭，如您需要查询网费信息，需向网吧前台直接确认，给您带来的不便非常抱歉，感谢您的支持与理解！";
        }
        return msg;
    }


    @Override
    public SseEmitter createSseEmitter(Integer memberId, String sessionId) {
        SseEmitter sseEmitter = new SseEmitter(3 * 60 * 1000L);
        sseEmitter.onCompletion(() -> {
            log.info("[{}]结束连接", sessionId);
        });
        sseEmitter.onTimeout(() -> {
            log.info("[{}]连接超时", sessionId);
        });
        sseEmitter.onError(throwable -> {
            log.info("[{}]连接异常，关闭连接", sessionId, throwable);
            sseEmitter.complete();
        });
        log.info("用户:{}创建sse连接:{}", memberId, sessionId);
        return sseEmitter;
    }

    private AiRecord streamCallHuoShanModel(AiSseEmitterWrapper sseEmitterWrapper) {
        AiRecord callResult = new AiRecord();
        String messageId = UUID.randomUUID().toString();
        AtomicReference<Boolean> isSseException = new AtomicReference<>(false);
        AiModel model = sseEmitterWrapper.getModel();
        ArkService service = getArkService(model);
        StringBuilder answer = new StringBuilder();
        final List<ChatMessage> streamMessages = new ArrayList<>();
        final ChatMessage streamUserMessage = ChatMessage.builder().role(ChatMessageRole.USER).content(sseEmitterWrapper.getQuestion()).build();
        streamMessages.add(streamUserMessage);

        ChatCompletionRequest streamChatCompletionRequest = ChatCompletionRequest.builder()
                .model(model.getEndPoint())
                .messages(streamMessages)
                .maxTokens(4096)
                .streamOptions(new ChatCompletionRequest.ChatCompletionRequestStreamOptions(true))
                .build();
        service.streamChatCompletion(streamChatCompletionRequest)
                .doOnError((throwable -> {
                    log.error("火山大模型调用异常:{}", sseEmitterWrapper.getRequestId(), throwable);
                    sseEmitterWrapper.getSseEmitter().send(AiReplyMessage.newFinishMsg(getFailMsg(), messageId));
                }))
                .blockingForEach(
                        choice -> {
                            if (!choice.getChoices().isEmpty()) {
                                String content = choice.getChoices().get(0).getMessage().getContent().toString();
                                //deepSeek深度思考存在很多为null的消息,content为null时，只发送finishReason不为空的消息
                                if (StringUtil.isNotEmpty(content) || StringUtil.isNotBlank(choice.getChoices().get(0).getFinishReason())) {
                                    answer.append(content);
                                    AiReplyMessage aiReplyMessage = new AiReplyMessage();
                                    aiReplyMessage.setCode(0);
                                    aiReplyMessage.setMessageId(messageId);
                                    aiReplyMessage.setFinish(StringUtil.isNotBlank(choice.getChoices().get(0).getFinishReason()));
                                    aiReplyMessage.setContent(content);
                                    try {
                                        sseEmitterWrapper.getSseEmitter().send(GsonUtil.toJson(aiReplyMessage));
                                    } catch (Exception e) {
                                        //用户直接退出页面会导致异常，捕获异常让程序继续执行，目的是线程保存回答,并且只对于第一次异常输出日志
                                        if (isSseException.get().equals(Boolean.FALSE)) {
                                            log.info("sse发送消息失败:{}", sseEmitterWrapper.getRequestId(), e);
                                            isSseException.set(true);
                                        }
                                    }
                                }
                            }
                            if (choice.getUsage() != null) {
                                callResult.setAnswerToken((int) choice.getUsage().getCompletionTokens());
                                callResult.setQuestionToken((int) choice.getUsage().getPromptTokens());
                            }
                        }
                );

        callResult.setContent(answer.toString());
        return callResult;
    }

    private AiRecord syncCallHuoShanModel(String question, AiRecordEnum.TypeEnum type, List<AiRecord> history, AiModel model) {
        AiRecord callResult = new AiRecord();
        StringBuilder answer = new StringBuilder();
        ArkService service = getArkService(model);
        List<ChatMessage> messages = history.stream().map(
                (item) -> ChatMessage.builder().role(recordTypeToHuoshanType(item.getType())).content(item.getContent()).build()
        ).collect(Collectors.toList());

        messages.add(ChatMessage.builder().role(AiRecordEnum.TypeEnum.USER.equals(type) ? ChatMessageRole.USER : ChatMessageRole.SYSTEM).content(question).build());

        ChatCompletionRequest chatCompletionRequest = ChatCompletionRequest.builder()
                .model(model.getEndPoint())
                .thinking(decideThinking(model))
                .messages(messages)
                .build();

        ChatCompletionResult chatCompletion = service.createChatCompletion(chatCompletionRequest);
        chatCompletion.getChoices().forEach((choice) -> {
            answer.append(choice.getMessage().getContent());
        });
        callResult.setContent(answer.toString());
        callResult.setAnswerToken((int) chatCompletion.getUsage().getCompletionTokens());
        callResult.setQuestionToken((int) chatCompletion.getUsage().getPromptTokens());
        return callResult;
    }

    private ChatCompletionRequest.ChatCompletionRequestThinking decideThinking(AiModel model) {
        ChatCompletionRequest.ChatCompletionRequestThinking thinking;
        if (AiModelEnum.ThinkingEnum.THINKING.getValue().equals(model.getThinking())) {
            thinking = new ChatCompletionRequest.ChatCompletionRequestThinking("enabled");
        } else if (AiModelEnum.ThinkingEnum.NO_THINKING.getValue().equals(model.getThinking())) {
            thinking = new ChatCompletionRequest.ChatCompletionRequestThinking("disabled");
        } else {
            thinking = new ChatCompletionRequest.ChatCompletionRequestThinking(null);
        }
        return thinking;
    }

    private ChatMessageRole recordTypeToHuoshanType(Integer type) {
        if (AiRecordEnum.TypeEnum.SYSTEM.getValue().equals(type)) {
            return ChatMessageRole.SYSTEM;
        } else if (AiRecordEnum.TypeEnum.USER.getValue().equals(type)) {
            return ChatMessageRole.USER;
        } else {
            return ChatMessageRole.ASSISTANT;
        }
    }

    private ArkService getArkService(AiModel model) {
        ArkService service = arkServiceMap.get(model.getApiKey());
        if (service == null) {
            synchronized (model.getApiKey().intern()) {
                service = arkServiceMap.get(model.getApiKey());
                if (service == null) {
                    service = ArkService.builder()
                            .apiKey(model.getApiKey())
                            .timeout(Duration.ofSeconds(120))
                            .connectTimeout(Duration.ofSeconds(10))
                            .baseUrl(model.getUrl())
                            .build();
                }
                arkServiceMap.put(model.getApiKey(), service);
            }
        }
        return service;
    }

    private abstract class ChatTask implements Runnable {
        private String traceId;
        private String spanId;
        @Setter
        private AiSseEmitterWrapper aiSseEmitterWrapper;

        @Override
        public void run() {
            try {
                release();
                doChat(aiSseEmitterWrapper);
            } catch (Exception e) {
                log.error("ai聊天发送消息异常", e);
            } finally {
                log.info("sse连接:{}关闭", aiSseEmitterWrapper.getRequestId());
                aiSseEmitterWrapper.getSseEmitter().complete();
            }
        }

        abstract void doChat(AiSseEmitterWrapper aiSseEmitterWrapper) throws IOException;

        /**
         * 在 A 线程捕获 trace
         */
        public void capture() {
            traceId = TraceUtil.getTraceId();
            spanId = TraceUtil.getSpanId();
        }

        /**
         * 在 B 线程恢复 trace
         */
        public void release() {
            if (StringUtil.isNotBlank(traceId) && StringUtil.isNotBlank(spanId)) {
                TraceUtil.putTrace(traceId, spanId);
            }
        }
    }

    private class CommonChatTask extends ChatTask {

        @Override
        void doChat(AiSseEmitterWrapper aiSseEmitterWrapper) throws IOException {
            chatForCommon(aiSseEmitterWrapper);
        }
    }
}
