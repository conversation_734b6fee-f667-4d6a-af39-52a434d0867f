package com.shunwang.marketing.service.marketing.promotionPopupWindow;

import com.shunwang.marketing.enums.marketing.PromotionPopupWindowEnum;
import com.shunwang.marketing.pojo.marketing.vo.PromotionPopupWindowVo;
import com.shunwang.marketing.service.marketing.MemberSurveyService;
import com.shunwang.marketing.util.MemberUtil;
import com.shunwang.util.lang.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class StrategyProcessor5 extends AbstractStrategyProcessor {
    @Autowired
    MemberSurveyService memberSurveyService;

    @Override
    public boolean doProcess(PromotionPopupWindowVo ppw) {
        String surveyId = ppw.getSurveyId();
        if (StringUtil.isBlank(surveyId)) return false;
        return memberSurveyService.getByMIdAndSurveyId(MemberUtil.getNotNullMemberId(), Integer.parseInt(surveyId)) == null;
    }

    @Override
    String strategyValue() {
        return PromotionPopupWindowEnum.StrategyEnum.NO_SURVEY.getValue();
    }

    @Override
    public boolean needLogin() {
        return true;
    }
}
