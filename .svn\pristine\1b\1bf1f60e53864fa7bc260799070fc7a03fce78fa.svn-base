package com.shunwang.marketing.service.marketing.impl;

import com.shunwang.marketing.cacheService.CacheKeyConstant;
import com.shunwang.marketing.cacheService.RedisOperation;
import com.shunwang.marketing.context.ThreadLocalContext;
import com.shunwang.marketing.dao.BaseMapper;
import com.shunwang.marketing.enums.marketing.ScoreRecordEnum;
import com.shunwang.marketing.enums.marketing.TaskEnum;
import com.shunwang.marketing.exception.CommonException;
import com.shunwang.marketing.manager.request.sso.OutSiteImportByWxRequest;
import com.shunwang.marketing.manager.response.sso.OutSiteImportByWxResponse;
import com.shunwang.marketing.manager.service.SsoServiceClient;
import com.shunwang.marketing.pojo.marketing.*;
import com.shunwang.marketing.query.marketing.MemberTaskQuery;
import com.shunwang.marketing.query.marketing.PondQuery;
import com.shunwang.marketing.service.bigData.BigDataService;
import com.shunwang.marketing.service.marketing.*;
import com.shunwang.marketing.util.DateUtil;
import com.shunwang.marketing.util.MemberUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import com.shunwang.marketing.dao.marketing.MemberTaskMapper;
import com.shunwang.marketing.service.impl.BaseServiceImpl;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Array;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.StringJoiner;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class MemberTaskServiceImpl extends BaseServiceImpl<MemberTask> implements MemberTaskService {
    @Autowired
    private MemberTaskMapper memberTaskMapper;
    @Autowired
    private LotteriesCntService lotteriesCntService;
    @Autowired
    private TaskRuleTaskService taskRuleTaskService;
    @Autowired
    private TaskService taskService;
    @Autowired
    private TaskRuleService taskRuleService;
    @Autowired
    private MoneySavingCardService moneySavingCardService;
    @Autowired
    private BigDataService bigDataService;
    @Autowired
    PondService pondService;
    @Value("${bigDataReport.bussId.mini}")
    private String businessIdMini;
    @Autowired
    RedisOperation redisOperation;
    @Lazy
    @Autowired
    MemberTaskService memberTaskService;
    @Autowired
    WelfareService welfareService;
    @Autowired
    GiftBagService giftBagService;
    @Autowired
    ScoreService scoreService;
    @Autowired
    ScoreRecordService scoreRecordService;
    @Autowired
    private PrizeRecordService prizeRecordService;
    @Autowired
    private MscOrderService mscOrderService;
    @Autowired
    private BagOrderService bagOrderService;

    @Override
    public BaseMapper getDao() {
        return memberTaskMapper;
    }


    /**
     * 该任务是否已经初始化过
     * @param taskRuleTaskId
     * @return
     */
    public MemberTask getSavedTaskToday(Integer taskRuleTaskId, Integer memberId, Integer taskType){
        Date todayStartTime = DateUtil.zeroConvertTime(new Date());
        List<MemberTask> list = memberTaskMapper.getSavedTaskToday(taskRuleTaskId, todayStartTime, memberId, taskType);
        if (list.isEmpty()){
            return null;
        }

        return list.get(0);
    }

    /**
     * 自动接取任务，并检查任务是否完成
     * @param task
     * @param memberId
     * @param taskRuleTaskId
     * @param pondId
     * @return
     */
    public MemberTask toStartMemberTask(Task task, Integer memberId, Integer taskRuleTaskId, Integer pondId) {
        boolean lock = redisOperation.setIfAbsent(CacheKeyConstant.MEMBER_TASK + "_" + memberId + "_" + taskRuleTaskId, 1, 3, TimeUnit.SECONDS);

        MemberTask exist = getSavedTaskToday(taskRuleTaskId, memberId, task.getType());
        //如果已经存在并且不是登录任务，则检查是否完成任务，并发放奖励
        if (exist != null && task.getCondition().intValue() != TaskEnum.ConditionEnum.LOGIN.getValue()) {
            return toCheckFinishTask(exist, task);
        }
        if (lock) {
            try {
                exist = getSavedTaskToday(taskRuleTaskId, memberId, task.getType());
                if (exist == null) {
                    MemberTask memberTask = new MemberTask();
                    memberTask.setMemberId(memberId);
                    memberTask.setRuleTaskId(taskRuleTaskId);
                    memberTask.setCondition(task.getCondition());
                    memberTask.setCntLimit(task.getCntLimit());
                    memberTask.setType(task.getType());
                    memberTask.setCreateTime(new Date());
                    memberTask.setPondId(pondId);
                    memberTask.setFinishCnt(0);
                    memberTaskMapper.save(memberTask);

                    //登录任务，直接完成，发放奖励
                    if (task.getCondition().intValue() == TaskEnum.ConditionEnum.LOGIN.getValue()) {
                        sendTaskRewards(memberTask, task);
                    }

                    return memberTask;
                } else {
                    return exist;
                }
            } finally {
                redisOperation.delete(CacheKeyConstant.MEMBER_TASK + "_" + memberId + "_" + taskRuleTaskId);
            }
        }
        throw new CommonException("系统繁忙");
    }

    /**
     * 第三方通知任务完成
     * @param unionId
     * @param key
     * @param appId
     * @param siteId
     */
    public boolean toFinishTask(String unionId, String key, String appId, String siteId){
        Integer memberId = getMemberByUnionId(unionId, appId, siteId);
        if (memberId == null){
            log.error("第三方通知任务完成报错，用户信息导入失败，unionId：[{}], key:[{}]", unionId, key);
            return false;
        }
        ThreadLocalContext.setTempData(ThreadLocalContext.MEMBER_ID, memberId);

        try {
            List<TaskRuleTask> ruleTasks = taskRuleTaskService.getByKey(key);
            for (TaskRuleTask taskRuleTask : ruleTasks) {
                memberTaskService.finishRuleTask(taskRuleTask);
            }
        } catch (Exception e){
            e.printStackTrace();
            log.error("第三方通知任务完成发生异常，任务完成操作处理失败，memberId：[{}], key:[{}]", memberId, key);
            return false;
        }

        return true;
    }

    @Override
    public boolean finishRuleTask(TaskRuleTask taskRuleTask) {
        Task task = taskService.get(taskRuleTask.getTaskId());
        Pond pond = isPondTask(taskRuleTask);
        MemberTask memberTask = toStartMemberTask(task, MemberUtil.getNotNullMemberId(), taskRuleTask.getId(), pond == null ? null : pond.getId());
        return memberTaskService.finishMemberTask(memberTask);
    }

    @Transactional(rollbackFor = Throwable.class)
    public boolean finishMemberTask(MemberTask memberTask) {
        TaskRuleTask certainTask = taskRuleTaskService.get(memberTask.getRuleTaskId());
        Task baseTask = taskService.get(certainTask.getTaskId());

        sendTaskRewards(memberTask, baseTask);
        return true;
    }

    /**
     * 完成广告任务
     * @param memberId
     * @param ruleTaskId
     * @return
     */
    public boolean toFinishAdTask(Integer memberId, Integer ruleTaskId){
        TaskRuleTask ruleTask = taskRuleTaskService.get(ruleTaskId);
        if (ruleTask == null){
            return false;
        }
        return memberTaskService.finishRuleTask(ruleTask);
    }

    private Integer getMemberByUnionId(String unionId, String appId, String siteId){
        OutSiteImportByWxRequest importByWxRequest = new OutSiteImportByWxRequest();
        importByWxRequest.setUserId(unionId);
        importByWxRequest.setAppId(appId);
        importByWxRequest.setSiteId(siteId);
        OutSiteImportByWxResponse importByWxResponse = SsoServiceClient.executeForJson(importByWxRequest);
        if (importByWxResponse.isSuccess()){
            return importByWxResponse.getMemberId();
        }

        return null;
    }

    public MemberTask getByRuleTaskId(Integer ruleTaskId, Integer memberId){
        MemberTaskQuery query = new MemberTaskQuery();
        query.setTaskRuleId(ruleTaskId);
        query.setMemberId(memberId);

        List<MemberTask> list = memberTaskMapper.find(query);
        if (list.isEmpty())
            return null;

        return list.get(0);
    }

    /**
     * 判断任务是否是省钱卡任务
     * @return
     */
    public MoneySavingCard isCardTask(Integer ruleTaskId){
        TaskRuleTask ruleTask = taskRuleTaskService.get(ruleTaskId);
        if (ruleTask == null){
            return null;
        }
        TaskRule rule = taskRuleService.get(ruleTask.getRuleId());
        if (rule == null){
            return null;
        }
        return moneySavingCardService.getByTaskRule(rule.getId());

    }

    private Pond isPondTask(TaskRuleTask certainTask){
        TaskRule rule = taskRuleService.get(certainTask.getRuleId());
        PondQuery query = new PondQuery();
        query.setTaskRuleId(rule.getId());
        return pondService.findOne(query);
    }

    /**
     * 省钱卡任务完成，数据上报内容
     * @param memberId
     * @param cardId
     * @param channel
     * @return
     */
    private String buildReportBody(Integer memberId, Integer cardId, String channel) {
        StringJoiner joiner = new StringJoiner("\t");
        joiner.add("savingcard-task-success").add("finish").add(cardId + "").add("").add(memberId.toString()).add("").add(channel);
        return joiner.toString();
    }

    /**
     * 发放任务奖励
     * @param memberTask
     * @param baseTask
     */
    public void sendTaskRewards(MemberTask memberTask, Task baseTask) {
        //已达任务完成上限
        if (memberTask.getFinishCnt() >= memberTask.getCntLimit()) {
            return;
        }
        memberTask.setFinishCnt(memberTask.getFinishCnt() + 1);
        if (memberTask.getFinishCnt() == memberTask.getCntLimit()) {
            memberTask.setFinishTime(new Date());
        }
        memberTaskMapper.update(memberTask);
        log.info("用户:{}完成任务:{},发放奖励", MemberUtil.getNotNullMemberId(), baseTask.getId());
        //发放任务奖励，抽奖次数奖励
        if (baseTask.getRewardType().intValue() == TaskEnum.RewardTypeEnum.CNT.getValue()) {
            //添加抽奖次数
            int reward = Integer.parseInt(baseTask.getReward());
            log.info("发放抽奖次数:{},奖池ID:{}", reward, memberTask.getPondId());
            if (reward > 0 && memberTask.getPondId() != null) {
                lotteriesCntService.addTaskCnt(MemberUtil.getNotNullMemberId(), memberTask.getPondId(), reward);
            }
        }
        //权益类型奖励
        else if (baseTask.getRewardType().intValue() == TaskEnum.RewardTypeEnum.WELFARE.getValue()) {
            //发放权益
            Welfare welfare = welfareService.get(Integer.parseInt(baseTask.getReward()));
            if (welfare == null) {
                return;
            }
            welfareService.sendWelfare(welfare, "任务", ScoreRecordEnum.SourceTypeEnum.TASK, true);
        }
        //积分类型奖励
        else if (baseTask.getRewardType().intValue() == TaskEnum.RewardTypeEnum.SCORE.getValue()) {
            //发放积分
            int reward = Integer.parseInt(baseTask.getReward());
            if (reward > 0) {
                TaskRuleTask taskRuleTask = taskRuleTaskService.get(memberTask.getRuleTaskId());
                String showTitle = taskRuleTask == null ? baseTask.getTitle() : taskRuleTask.getShowTitle();
                scoreService.updateScoreAndTakeRecord(reward, ScoreRecordEnum.TypeEnum.ADD, ScoreRecordEnum.SourceTypeEnum.TASK.getValue() , "完成【" + showTitle + "】任务");
            }
        }
        //礼包类型奖励
        else if (baseTask.getRewardType().intValue() == TaskEnum.RewardTypeEnum.GIFT_BAG.getValue()) {
            //发放礼包
            giftBagService.receiveGiftBag(Integer.parseInt(baseTask.getReward()));
        }

        //省钱卡任务完成，进行数据上报
        MoneySavingCard moneySavingCard = isCardTask(memberTask.getRuleTaskId());
        if (moneySavingCard != null) {
            bigDataService.bigDataReport(buildReportBody(MemberUtil.getNotNullMemberId(), moneySavingCard.getId(), moneySavingCard.getChannel()), businessIdMini);
        }
    }

    /**
     * 判断任务是否完成，并发奖（目前只检查登录任务和互动任务）
     * 支付任务，尚未开发，不处理
     * 广告任务，由小程序直接调用接口完成
     * 业务任务，此处不处理，一般是业务方通过回调通知任务是否完成
     * @param memberTask
     * @param baseTask
     * @return
     */
    public MemberTask toCheckFinishTask(MemberTask memberTask, Task baseTask) {
        if (memberTask.getFinishCnt() >= memberTask.getCntLimit()) {
            return memberTask;
        }
        boolean isFinished = false;
        //互动任务
        if (baseTask.getCondition().intValue() == TaskEnum.ConditionEnum.INTERACTION.getValue()){
            //日常任务
            if(baseTask.getType().intValue() == TaskEnum.TypeEnum.COMMON.getValue()){
                Date zeroToday = DateUtil.zeroConvertTime(memberTask.getCreateTime());
                if (baseTask.getTool().intValue() == TaskEnum.ToolEnum.DRAW_PRIZE.getValue()){
                    isFinished = prizeRecordService.getUserPondPrizeCntToday(MemberUtil.getNotNullMemberId(), Integer.valueOf(baseTask.getToolCondition()), zeroToday) > 0;
                } else if (baseTask.getTool().intValue() == TaskEnum.ToolEnum.BUY_MONEY_SAVING_CARD.getValue()){
                    //查询是否购买配置的省钱卡
                    String toolCondition = baseTask.getToolCondition();
                    List<String> mscIdList = Arrays.asList(toolCondition.split("\\|"));
                    isFinished = mscIdList.stream()
                            .anyMatch((mscId) -> mscOrderService.getTodayMscOrder(MemberUtil.getNotNullMemberId(), Integer.valueOf(mscId), zeroToday) != null);
                } else if (baseTask.getTool().intValue() == TaskEnum.ToolEnum.BUY_GIFT_BAG.getValue()){
                    //查询当日是否购买配置的礼包
                    isFinished = bagOrderService.getTodayGiftBag(MemberUtil.getNotNullMemberId(), Integer.valueOf(baseTask.getToolCondition()),zeroToday) != null;
                }
            //赏金任务
            } else if (baseTask.getType().intValue() == TaskEnum.TypeEnum.NO_COMMON.getValue()) {
                //查看是否完成
                if (baseTask.getTool().intValue() == TaskEnum.ToolEnum.DRAW_PRIZE.getValue()){
                    isFinished = prizeRecordService.getUserPondPrizeCntToday(MemberUtil.getNotNullMemberId(), Integer.valueOf(baseTask.getToolCondition()), memberTask.getCreateTime()) > 0;
                } else if (baseTask.getTool().intValue() == TaskEnum.ToolEnum.BUY_MONEY_SAVING_CARD.getValue()){
                    //查询是否购买配置的省钱卡
                    isFinished = mscOrderService.getTodayMscOrder(MemberUtil.getNotNullMemberId(), Integer.valueOf(baseTask.getToolCondition()), memberTask.getCreateTime()) != null;
                } else if (baseTask.getTool().intValue() == TaskEnum.ToolEnum.BUY_GIFT_BAG.getValue()){
                    //查询当日是否购买配置的礼包
                    isFinished = bagOrderService.getTodayGiftBag(MemberUtil.getNotNullMemberId(), Integer.valueOf(baseTask.getToolCondition()), memberTask.getCreateTime()) != null;
                }
            }
        }
        //登录任务，直接完成
        else if (baseTask.getCondition().intValue() == TaskEnum.ConditionEnum.LOGIN.getValue()){
            isFinished = true;
        }
        //如果任务完成发放奖励
        if (isFinished) {
            sendTaskRewards(memberTask, baseTask);
        }

        return memberTask;
    }

}
