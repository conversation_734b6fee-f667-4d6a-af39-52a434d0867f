package com.shunwang.marketing.pojo.marketing;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.shunwang.marketing.enums.marketing.ResourcePrivilegeContentEnum;
import com.shunwang.marketing.pojo.BaseObject;
import lombok.Data;

import java.util.Objects;

@Data
public class ResourcePrivilegeContent extends BaseObject {
	private static final long serialVersionUID = 1L;
	private Integer id;
	private Integer level;
	private Integer privilegeType;
	private Integer privilegeRelateId;
	private Integer resourcePrivilegeId;

	private String privilegeName;

	@JsonIgnore
	public ResourcePrivilegeContentEnum.PrivilegeTypeEnum getType(){
		for(ResourcePrivilegeContentEnum.PrivilegeTypeEnum privilegeTypeEnum : ResourcePrivilegeContentEnum.PrivilegeTypeEnum.values()){
			if(Objects.equals(privilegeTypeEnum.getValue(), privilegeType)){
				return privilegeTypeEnum;
			}
		}
		return null;
	}

	public String getPrivilegeTypeName(){
		for(ResourcePrivilegeContentEnum.PrivilegeTypeEnum privilegeTypeEnum : ResourcePrivilegeContentEnum.PrivilegeTypeEnum.values()){
			if(Objects.equals(privilegeTypeEnum.getValue(), privilegeType)){
				return privilegeTypeEnum.getName();
			}
		}
		return "";
	}

	@JsonIgnore
	public String getResourceIdAndId() {
		return "contentId:" + id + ",resourceId:" + resourcePrivilegeId;
	}
}
