package com.shunwang.marketing.service.marketing;

import com.shunwang.marketing.pojo.marketing.MoneySavingCard;
import com.shunwang.marketing.pojo.marketing.MscOrder;
import com.shunwang.marketing.service.BaseService;

import java.util.Date;
import java.util.List;

public interface MscOrderService extends BaseService<MscOrder> {

    /**
     * 初始化订单
     * @param moneySavingCard
     * @return
     */
    MscOrder buildOrder(MoneySavingCard moneySavingCard);

    /**
     * 根据订单号获取订单
     * @param orderNo
     * @return
     */
    MscOrder getByOrderNo(String orderNo);

    void finishOrder(MscOrder order) throws Exception;

    /**
     * 获取已经购买成功且生效中的省钱卡记录
     * @return
     */
    MscOrder getEffectiveTimeRecord(Integer cardId, String channel);

    void unLock(MscOrder mscOrder);

    /**
     * 根据省钱卡id获取用户生效中的省钱卡
     * @param memberId
     * @param savingCardId
     * @return
     */
    MscOrder getEffectiveTimeRecordByCardId(Integer memberId, Integer savingCardId);

    MscOrder getByPayOrderNo(String payOrderNo);

    void sendRefundWxTemplate(MscOrder mscOrder);


    void doRefund(MscOrder mscOrder,String refundNo,Integer refundState);

    void afterCreateOrder(MscOrder mscOrder,String disburseNo);

    MscOrder getTodayMscOrder(Integer memberId, Integer savingCardId, Date time);
}
