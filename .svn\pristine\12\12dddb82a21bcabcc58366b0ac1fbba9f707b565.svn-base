package com.shunwang.marketing.manager.response.weixin;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.shunwang.marketing.manager.IResponse;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;


/**
 * {
 *     "errcode":0,
 *     "errmsg":"ok",
 *     "phone_info": {
 *         "phoneNumber":"xxxxxx", //用户绑定的手机号（国外手机号会有区号）
 *         "purePhoneNumber": "xxxxxx", //没有区号的手机号
 *         "countryCode": 86, //区号
 *         "watermark": { //数据水印
 *             "timestamp": 1637744274,
 *             "appid": "xxxx" //小程序appid
 *         }
 *     }
 * }
 */
@Slf4j
@Getter
public class GetUserPhoneNumberResponse implements IResponse {

    public static final int SUCCESS = 0;

    private String json;

    private String phoneNumber;
    private String purePhoneNumber;
    private int errorCode;
    private String msg;

    @Override
    public void setResponse(String resp) {
        this.json = resp;
    }

    @Override
    public GetUserPhoneNumberResponse parse() {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode node = objectMapper.readTree(json);

            if (node.has("errcode")) {
                errorCode = node.get("errcode").asInt();
                if (errorCode == SUCCESS) {
                    JsonNode phoneInfo = node.get("phone_info");
                    phoneNumber = phoneInfo.get("phoneNumber").asText();
                    purePhoneNumber = phoneInfo.get("purePhoneNumber").asText();
                } else {
                    msg = node.get("errmsg").asText();
                }
            }
        } catch (Exception e) {
            log.error("解析出错[{}]", json, e);
        }
        return this;
    }

    public boolean isSuccess() {
        return errorCode == SUCCESS;
    }

    public boolean isInvalidToken(){
        return errorCode == 40001;
    }


    public String getMsg() {
        if (isSuccess()) {
            return "";
        }
        if (40029 == getErrorCode()) {
            return "code 无效";
        }
        if (45011 == getErrorCode()) {
            return "频率限制，每个用户每分钟100次";
        }
        return msg;
    }
}
