package com.shunwang.marketing.manager.response.tencent;

import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.shunwang.marketing.exception.CommonException;
import com.shunwang.marketing.manager.IResponse;
import com.shunwang.util.lang.StringUtil;
import lombok.Data;

@Data
public class GiveGameGiftResponse implements IResponse {

    private int iRet;
    private int v;
    private String apiName;
    private String sMsg;
    private String tid;
    /**json格式，解析获取v7**/
    private JsonObject jData;
    /**绑定的用户id**/
    private String v7;

    private String json;

    public static final int SUCCESS = 0;

    public boolean isSuccess() {
        return iRet == SUCCESS;
    }

    @Override
    public void setResponse(String resp) {
        this.json = resp;
    }

    @Override
    public GiveGameGiftResponse parse() throws Exception {
        try {
            if (StringUtil.isBlank(json)) {
                return null;
            }
            JsonObject jsonObject = JsonParser.parseString(json).getAsJsonObject();
            iRet = jsonObject.get("ret").getAsInt();
            v = jsonObject.get("v").getAsInt();
            apiName = jsonObject.get("apiName").getAsString();
            sMsg = jsonObject.get("sMsg").getAsString();
            tid = jsonObject.get("tid").getAsString();

            if (isSuccess()) {
                jData = jsonObject.get("jData").getAsJsonObject();
                v7 = jData.get("v7").getAsString();
            }
            return this;
        } catch (Exception e) {
            throw new CommonException("腾讯游戏tokenToUid解析失败,响应内容:" + json);
        }
    }
}
