package com.shunwang.marketing.interceptor;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class WebConfig implements WebMvcConfigurer {
    @Autowired
    private LogInterceptor logInterceptor;
    @Autowired
    private SignInterceptor signInterceptor;
    @Autowired
    private NewSignInterceptor newSignInterceptor;
    @Autowired
    private ThreadLocalInterceptor threadLocalInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(logInterceptor);
        registry.addInterceptor(signInterceptor).addPathPatterns(
                "/barPrivilege/promotionData",
                "/h5/welfare/gameId",
                "/aiTask/receiptNotify"
        );
        registry.addInterceptor(newSignInterceptor).addPathPatterns(
                "/ad/barPush",
                "/ad/gamePush",
                "/ad/qqGamePush",
                "/wangwei/queryPrivilegeContent",
                "/wangwei/privilege",
                "/wangwei/barStatistics",
                "/wangwei/imageResources"
        );
        registry.addInterceptor(threadLocalInterceptor);
    }
}
