package com.shunwang.marketing.pojo.marketing;

import com.shunwang.marketing.constant.UploadContstant;
import com.shunwang.marketing.context.DomainContext;
import com.shunwang.marketing.pojo.BaseObject;
import com.shunwang.util.lang.StringUtil;
import lombok.Data;

@Data
public class BussImg extends BaseObject {
	private static final long serialVersionUID = 1L;
	
	private Integer id;
	private Integer bussType; // 业务类型 1云菜单特权道具图片
	private Integer bussId; // 业务id
	private String name; // 名称
	private String url; // 图片地址
	private Integer sort; // 排序
	private String remark; // 备注

	public String getUrl() {
		if (StringUtil.isNotBlank(url)){
			return DomainContext.getBackStaticServer() + UploadContstant.UPLOAD_DIRECTORY + url;
		}
		return null;
	}
}
