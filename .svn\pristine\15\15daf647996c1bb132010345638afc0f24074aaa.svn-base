package com.shunwang.marketing.enums.marketing;

import lombok.Getter;
import lombok.Setter;

public interface ModuleDetailsEnum {

     enum TypeEnum {
         BANNER(1,"banner"),
         WELFARE(2,"权益"),
         EMBEDDED_WEB(3,"内嵌页"),
         LINK(4,"活动链接")
;
        TypeEnum(Integer value, String name) {
            this.value = value;
            this.name = name;
        }

        private @Getter @Setter Integer value;
        private @Getter @Setter String name;
    }

    enum ModeEnum {
        HAND(1, "手动"),
        AUTO(2, "自动");


        ModeEnum(Integer value, String name) {
            this.value = value;
            this.name = name;
        }

        private @Getter Integer value;
        private @Getter String name;
    }

     enum WelfareTypeEnum {
;
        WelfareTypeEnum(int value, String name) {
            this.value = value;
            this.name = name;
        }

        private @Getter @Setter int value;
        private @Getter @Setter String name;
    }

     enum BtnOpenTypeEnum {
;
        BtnOpenTypeEnum(int value, String name) {
            this.value = value;
            this.name = name;
        }

        private @Getter @Setter int value;
        private @Getter @Setter String name;
    }



}
