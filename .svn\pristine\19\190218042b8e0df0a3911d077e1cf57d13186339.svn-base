package com.shunwang.marketing.service.marketing.impl;

import cn.hutool.core.util.RandomUtil;
import com.shunwang.marketing.cacheService.RedisOperation;
import com.shunwang.marketing.enums.ResourcesEnum;
import com.shunwang.marketing.enums.marketing.AiRecordEnum;
import com.shunwang.marketing.pojo.ai.AiReplyMessage;
import com.shunwang.marketing.pojo.marketing.AiModel;
import com.shunwang.marketing.pojo.marketing.AiRecord;
import com.shunwang.marketing.pojo.util.SendMsg;
import com.shunwang.marketing.query.marketing.AiModelQuery;
import com.shunwang.marketing.send.email.EmailSender;
import com.shunwang.marketing.service.ai.AiSseEmitterWrapper;
import com.shunwang.marketing.service.config.ResourcesService;
import com.shunwang.marketing.service.marketing.AiModelService;
import com.shunwang.marketing.service.marketing.AiRecordService;
import com.shunwang.marketing.service.marketing.AiService;
import com.shunwang.sms.utils.SMSInnerSenderUtil;
import com.shunwang.util.json.GsonUtil;
import com.shunwang.util.lang.StringUtil;
import com.volcengine.ark.runtime.model.completion.chat.ChatCompletionRequest;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessage;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessageRole;
import com.volcengine.ark.runtime.service.ArkService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.shunwang.marketing.enums.marketing.AiModelEnum.PlatformEnum.AI_DEPARTMENT;
import static com.shunwang.marketing.enums.marketing.AiModelEnum.PlatformEnum.HUOSHAN;

@Service
@Slf4j
public class AiServiceimpl implements AiService {
    @Autowired
    AiRecordService aiRecordService;
    @Autowired
    AiModelService aiModelService;
    Map<String, ArkService> arkServiceMap = new ConcurrentHashMap<>();
    @Autowired
    RedisOperation redisOperation;
    @Autowired
    ResourcesService resourcesService;
    @Autowired
    EmailSender emailSender;

    private AiModel getModel() {
        List<AiModel> aiModelList = aiModelService.find(new AiModelQuery());
        if (CollectionUtils.isEmpty(aiModelList)) {
            return null;
        }
        aiModelList = aiModelList.stream().filter(AiModel::isEffective).collect(Collectors.toList());
        //优先使用外部模型，外部模型失效 使用ai部门模型
        Map<Boolean, List<AiModel>> isAiDepartment = aiModelList.stream().collect(Collectors.groupingBy((aiModel -> AI_DEPARTMENT.getValue().equals(aiModel.getPlatform()))));
        if (CollectionUtils.isEmpty(isAiDepartment.get(false))) {
            return RandomUtil.randomEle(isAiDepartment.get(true));
        } else {
            return RandomUtil.randomEle(isAiDepartment.get(false));
        }
    }

    public void checkToken() {
        List<AiModel> aiModelList = aiModelService.find(new AiModelQuery());
        int sumRemainToken = aiModelList.stream()
                .filter((aiModel -> !AI_DEPARTMENT.getValue().equals(aiModel.getPlatform()) || aiModel.getRemainToken() == null))
                .mapToInt(AiModel::getRemainToken)
                .sum();
        if (sumRemainToken < 50000) {
            log.info("ai总token小于50000");
            if (redisOperation.setIfAbsent("ai_token_check", 1, 3, TimeUnit.HOURS)) {
                log.info("ai总token小于50000，发送提醒");
                String emailReceiver = resourcesService.getStringValueByTypeAndNameEhCache(ResourcesEnum.TypeName.EMAIL_RECEIVER);
                if (StringUtil.isNotBlank(emailReceiver)) {
                    for (String receiver : emailReceiver.split(",")) {
                        SendMsg sendMsg = new SendMsg();
                        sendMsg.setTitle("AI Token余量不足");
                        sendMsg.setContent("AI总token数小于50000");
                        sendMsg.setNumber(receiver);
                        emailSender.sendEmail(sendMsg);
                    }
                }

                String smsReceiver = resourcesService.getStringValueByTypeAndNameEhCache(ResourcesEnum.TypeName.SMS_RECEIVER);
                if (StringUtil.isNotBlank(smsReceiver)) {
                    for (String receiver : smsReceiver.split(",")) {
                        SMSInnerSenderUtil.sendMsg(receiver, "AI总token数小于50000");
                    }
                }
            }
        }
    }

    @Override
    public void chat(AiSseEmitterWrapper sseEmitterWrapper) throws IOException {
        AiModel model = getModel();
        if (model == null) {
            log.info("会话:{}未获取到模型", sseEmitterWrapper.getSessionId());
            sseEmitterWrapper.getSseEmitter().send(sseMessageEvent(AiReplyMessage.newErrorJsonMsg("未获取到模型")));
            return;
        }

        AiRecord questionRecord = new AiRecord();
        questionRecord.setMemberId(sseEmitterWrapper.getMemberId());
        questionRecord.setType(AiRecordEnum.TypeEnum.USER.getValue());
        questionRecord.setContent(sseEmitterWrapper.getQuestion());
        questionRecord.setTimeAdd(new Date());
        questionRecord.setConsumeToken(0);
        questionRecord.setModelId(model.getId());
        aiRecordService.save(questionRecord);
        Integer platform = model.getPlatform();
        String messageId = UUID.randomUUID().toString();
        if (HUOSHAN.getValue().equals(platform)) {
            callHuoShanModel(sseEmitterWrapper, model, questionRecord, messageId);
        }
        checkToken();
    }


    @Override
    public SseEmitter createSseEmitter(Integer memberId, String sessionId) {
        SseEmitter sseEmitter = new SseEmitter(3 * 60 * 1000L);
        sseEmitter.onCompletion(() -> {
            log.info("[{}]结束连接", sessionId);
        });
        sseEmitter.onTimeout(() -> {
            log.info("[{}]连接超时", sessionId);
        });
        sseEmitter.onError(throwable -> {
            log.info("[{}]连接异常，关闭连接", sessionId, throwable);
            sseEmitter.complete();
        });
        log.info("用户:{}创建sse连接:{}", memberId, sessionId);
        return sseEmitter;
    }

    private void callHuoShanModel(AiSseEmitterWrapper sseEmitterWrapper, AiModel model, AiRecord questionRecord, String messageId) {
        ArkService service = getArkService(model);
        StringBuilder answer = new StringBuilder();
        AiRecord answerRecord = new AiRecord();
        answerRecord.setMemberId(sseEmitterWrapper.getMemberId());
        answerRecord.setType(AiRecordEnum.TypeEnum.SYSTEM.getValue());
        answerRecord.setModelId(model.getId());
        answerRecord.setParentId(questionRecord.getId());
        final List<ChatMessage> streamMessages = new ArrayList<>();
        final ChatMessage streamUserMessage = ChatMessage.builder().role(ChatMessageRole.USER).content(sseEmitterWrapper.getQuestion()).build();
        streamMessages.add(streamUserMessage);

        ChatCompletionRequest streamChatCompletionRequest = ChatCompletionRequest.builder()
                .model(model.getEndPoint())
                .messages(streamMessages)
                .maxTokens(4096)
                .streamOptions(new ChatCompletionRequest.ChatCompletionRequestStreamOptions(true))
                .build();
        service.streamChatCompletion(streamChatCompletionRequest)
                .doOnError((throwable -> {
                    log.error("火山大模型调用异常:{}", sseEmitterWrapper.getSessionId(), throwable);
                    sseEmitterWrapper.getSseEmitter().send(sseMessageEvent(AiReplyMessage.newErrorJsonMsg("大模型调用异常")));
                }))
                .blockingForEach(
                        choice -> {
                            if (!choice.getChoices().isEmpty()) {
                                String content = choice.getChoices().get(0).getMessage().getContent().toString();
                                //deepSeek深度思考存在很多为null的消息,content为null时，只发送finishReason不为空的消息
                                if (StringUtil.isNotEmpty(content) || StringUtil.isNotBlank(choice.getChoices().get(0).getFinishReason())) {
                                    answer.append(content);
                                    AiReplyMessage aiReplyMessage = new AiReplyMessage();
                                    aiReplyMessage.setCode(0);
                                    aiReplyMessage.setMessageId(messageId);
                                    aiReplyMessage.setFinish(StringUtil.isNotBlank(choice.getChoices().get(0).getFinishReason()));
                                    aiReplyMessage.setContent(content);
                                    try {
                                        sseEmitterWrapper.getSseEmitter().send(GsonUtil.toJson(aiReplyMessage));
                                    } catch (IOException e) {
                                        //用户直接退出页面会导致异常，捕获异常让程序继续执行，目的是线程保存回答
                                        log.info("sse发送消息失败:{},原因:{}", sseEmitterWrapper.getSessionId(), e.getMessage());
                                    }
                                }
                            }
                            if (choice.getUsage() != null) {
                                answerRecord.setConsumeToken((int) choice.getUsage().getCompletionTokens());
                                questionRecord.setConsumeToken((int) choice.getUsage().getPromptTokens());
                            }
                        }
                );
        //感觉不需要事务，尽可能去操作
        //回答记录
        answerRecord.setContent(answer.toString());
        answerRecord.setTimeAdd(new Date());
        aiRecordService.save(answerRecord);

        aiRecordService.update(questionRecord);

        //减少模型的token
        aiModelService.decToken(model.getId(), answerRecord.getConsumeToken() + questionRecord.getConsumeToken());
    }

    private ArkService getArkService(AiModel model) {
        ArkService service = arkServiceMap.get(model.getApiKey());
        if (service == null) {
            synchronized (model.getApiKey().intern()) {
                service = arkServiceMap.get(model.getApiKey());
                if (service == null) {
                    service = ArkService.builder()
                            .apiKey(model.getApiKey())
                            .timeout(Duration.ofSeconds(120))
                            .connectTimeout(Duration.ofSeconds(10))
                            .baseUrl(model.getUrl())
                            .build();
                }
                arkServiceMap.put(model.getApiKey(), service);
            }
        }
        return service;
    }

    private SseEmitter.SseEventBuilder sseMessageEvent(String message) {
        return SseEmitter.event()
                .name("message")
                .data(message)
                .reconnectTime(5000);
    }
}
